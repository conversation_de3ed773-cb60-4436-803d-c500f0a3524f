"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/raw-body@2.5.2";
exports.ids = ["vendor-chunks/raw-body@2.5.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * raw-body\n * Copyright(c) 2013-2014 Jonathan Ong\n * Copyright(c) 2014-2022 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar asyncHooks = tryRequireAsyncHooks()\nvar bytes = __webpack_require__(/*! bytes */ \"(rsc)/./node_modules/.pnpm/bytes@3.1.2/node_modules/bytes/index.js\")\nvar createError = __webpack_require__(/*! http-errors */ \"(rsc)/./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js\")\nvar iconv = __webpack_require__(/*! iconv-lite */ \"(rsc)/./node_modules/.pnpm/iconv-lite@0.4.24/node_modules/iconv-lite/lib/index.js\")\nvar unpipe = __webpack_require__(/*! unpipe */ \"(rsc)/./node_modules/.pnpm/unpipe@1.0.0/node_modules/unpipe/index.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = getRawBody\n\n/**\n * Module variables.\n * @private\n */\n\nvar ICONV_ENCODING_MESSAGE_REGEXP = /^Encoding not recognized: /\n\n/**\n * Get the decoder for a given encoding.\n *\n * @param {string} encoding\n * @private\n */\n\nfunction getDecoder (encoding) {\n  if (!encoding) return null\n\n  try {\n    return iconv.getDecoder(encoding)\n  } catch (e) {\n    // error getting decoder\n    if (!ICONV_ENCODING_MESSAGE_REGEXP.test(e.message)) throw e\n\n    // the encoding was not found\n    throw createError(415, 'specified encoding unsupported', {\n      encoding: encoding,\n      type: 'encoding.unsupported'\n    })\n  }\n}\n\n/**\n * Get the raw body of a stream (typically HTTP).\n *\n * @param {object} stream\n * @param {object|string|function} [options]\n * @param {function} [callback]\n * @public\n */\n\nfunction getRawBody (stream, options, callback) {\n  var done = callback\n  var opts = options || {}\n\n  // light validation\n  if (stream === undefined) {\n    throw new TypeError('argument stream is required')\n  } else if (typeof stream !== 'object' || stream === null || typeof stream.on !== 'function') {\n    throw new TypeError('argument stream must be a stream')\n  }\n\n  if (options === true || typeof options === 'string') {\n    // short cut for encoding\n    opts = {\n      encoding: options\n    }\n  }\n\n  if (typeof options === 'function') {\n    done = options\n    opts = {}\n  }\n\n  // validate callback is a function, if provided\n  if (done !== undefined && typeof done !== 'function') {\n    throw new TypeError('argument callback must be a function')\n  }\n\n  // require the callback without promises\n  if (!done && !global.Promise) {\n    throw new TypeError('argument callback is required')\n  }\n\n  // get encoding\n  var encoding = opts.encoding !== true\n    ? opts.encoding\n    : 'utf-8'\n\n  // convert the limit to an integer\n  var limit = bytes.parse(opts.limit)\n\n  // convert the expected length to an integer\n  var length = opts.length != null && !isNaN(opts.length)\n    ? parseInt(opts.length, 10)\n    : null\n\n  if (done) {\n    // classic callback style\n    return readStream(stream, encoding, length, limit, wrap(done))\n  }\n\n  return new Promise(function executor (resolve, reject) {\n    readStream(stream, encoding, length, limit, function onRead (err, buf) {\n      if (err) return reject(err)\n      resolve(buf)\n    })\n  })\n}\n\n/**\n * Halt a stream.\n *\n * @param {Object} stream\n * @private\n */\n\nfunction halt (stream) {\n  // unpipe everything from the stream\n  unpipe(stream)\n\n  // pause stream\n  if (typeof stream.pause === 'function') {\n    stream.pause()\n  }\n}\n\n/**\n * Read the data from the stream.\n *\n * @param {object} stream\n * @param {string} encoding\n * @param {number} length\n * @param {number} limit\n * @param {function} callback\n * @public\n */\n\nfunction readStream (stream, encoding, length, limit, callback) {\n  var complete = false\n  var sync = true\n\n  // check the length and limit options.\n  // note: we intentionally leave the stream paused,\n  // so users should handle the stream themselves.\n  if (limit !== null && length !== null && length > limit) {\n    return done(createError(413, 'request entity too large', {\n      expected: length,\n      length: length,\n      limit: limit,\n      type: 'entity.too.large'\n    }))\n  }\n\n  // streams1: assert request encoding is buffer.\n  // streams2+: assert the stream encoding is buffer.\n  //   stream._decoder: streams1\n  //   state.encoding: streams2\n  //   state.decoder: streams2, specifically < 0.10.6\n  var state = stream._readableState\n  if (stream._decoder || (state && (state.encoding || state.decoder))) {\n    // developer error\n    return done(createError(500, 'stream encoding should not be set', {\n      type: 'stream.encoding.set'\n    }))\n  }\n\n  if (typeof stream.readable !== 'undefined' && !stream.readable) {\n    return done(createError(500, 'stream is not readable', {\n      type: 'stream.not.readable'\n    }))\n  }\n\n  var received = 0\n  var decoder\n\n  try {\n    decoder = getDecoder(encoding)\n  } catch (err) {\n    return done(err)\n  }\n\n  var buffer = decoder\n    ? ''\n    : []\n\n  // attach listeners\n  stream.on('aborted', onAborted)\n  stream.on('close', cleanup)\n  stream.on('data', onData)\n  stream.on('end', onEnd)\n  stream.on('error', onEnd)\n\n  // mark sync section complete\n  sync = false\n\n  function done () {\n    var args = new Array(arguments.length)\n\n    // copy arguments\n    for (var i = 0; i < args.length; i++) {\n      args[i] = arguments[i]\n    }\n\n    // mark complete\n    complete = true\n\n    if (sync) {\n      process.nextTick(invokeCallback)\n    } else {\n      invokeCallback()\n    }\n\n    function invokeCallback () {\n      cleanup()\n\n      if (args[0]) {\n        // halt the stream on error\n        halt(stream)\n      }\n\n      callback.apply(null, args)\n    }\n  }\n\n  function onAborted () {\n    if (complete) return\n\n    done(createError(400, 'request aborted', {\n      code: 'ECONNABORTED',\n      expected: length,\n      length: length,\n      received: received,\n      type: 'request.aborted'\n    }))\n  }\n\n  function onData (chunk) {\n    if (complete) return\n\n    received += chunk.length\n\n    if (limit !== null && received > limit) {\n      done(createError(413, 'request entity too large', {\n        limit: limit,\n        received: received,\n        type: 'entity.too.large'\n      }))\n    } else if (decoder) {\n      buffer += decoder.write(chunk)\n    } else {\n      buffer.push(chunk)\n    }\n  }\n\n  function onEnd (err) {\n    if (complete) return\n    if (err) return done(err)\n\n    if (length !== null && received !== length) {\n      done(createError(400, 'request size did not match content length', {\n        expected: length,\n        length: length,\n        received: received,\n        type: 'request.size.invalid'\n      }))\n    } else {\n      var string = decoder\n        ? buffer + (decoder.end() || '')\n        : Buffer.concat(buffer)\n      done(null, string)\n    }\n  }\n\n  function cleanup () {\n    buffer = null\n\n    stream.removeListener('aborted', onAborted)\n    stream.removeListener('data', onData)\n    stream.removeListener('end', onEnd)\n    stream.removeListener('error', onEnd)\n    stream.removeListener('close', cleanup)\n  }\n}\n\n/**\n * Try to require async_hooks\n * @private\n */\n\nfunction tryRequireAsyncHooks () {\n  try {\n    return __webpack_require__(/*! async_hooks */ \"async_hooks\")\n  } catch (e) {\n    return {}\n  }\n}\n\n/**\n * Wrap function with async resource, if possible.\n * AsyncResource.bind static method backported.\n * @private\n */\n\nfunction wrap (fn) {\n  var res\n\n  // create anonymous resource\n  if (asyncHooks.AsyncResource) {\n    res = new asyncHooks.AsyncResource(fn.name || 'bound-anonymous-fn')\n  }\n\n  // incompatible node.js\n  if (!res || !res.runInAsyncScope) {\n    return fn\n  }\n\n  // return bound function\n  return res.runInAsyncScope.bind(res, fn, null)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/raw-body@2.5.2/node_modules/raw-body/index.js\n");

/***/ })

};
;