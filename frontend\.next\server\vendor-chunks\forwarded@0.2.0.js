"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/forwarded@0.2.0";
exports.ids = ["vendor-chunks/forwarded@0.2.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/forwarded@0.2.0/node_modules/forwarded/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/forwarded@0.2.0/node_modules/forwarded/index.js ***!
  \****************************************************************************/
/***/ ((module) => {

eval("/*!\n * forwarded\n * Copyright(c) 2014-2017 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = forwarded\n\n/**\n * Get all addresses in the request, using the `X-Forwarded-For` header.\n *\n * @param {object} req\n * @return {array}\n * @public\n */\n\nfunction forwarded (req) {\n  if (!req) {\n    throw new TypeError('argument req is required')\n  }\n\n  // simple header parsing\n  var proxyAddrs = parse(req.headers['x-forwarded-for'] || '')\n  var socketAddr = getSocketAddr(req)\n  var addrs = [socketAddr].concat(proxyAddrs)\n\n  // return all addresses\n  return addrs\n}\n\n/**\n * Get the socket address for a request.\n *\n * @param {object} req\n * @return {string}\n * @private\n */\n\nfunction getSocketAddr (req) {\n  return req.socket\n    ? req.socket.remoteAddress\n    : req.connection.remoteAddress\n}\n\n/**\n * Parse the X-Forwarded-For header.\n *\n * @param {string} header\n * @private\n */\n\nfunction parse (header) {\n  var end = header.length\n  var list = []\n  var start = header.length\n\n  // gather addresses, backwards\n  for (var i = header.length - 1; i >= 0; i--) {\n    switch (header.charCodeAt(i)) {\n      case 0x20: /*   */\n        if (start === end) {\n          start = end = i\n        }\n        break\n      case 0x2c: /* , */\n        if (start !== end) {\n          list.push(header.substring(start, end))\n        }\n        start = end = i\n        break\n      default:\n        start = i\n        break\n    }\n  }\n\n  // final address\n  if (start !== end) {\n    list.push(header.substring(start, end))\n  }\n\n  return list\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/forwarded@0.2.0/node_modules/forwarded/index.js\n");

/***/ })

};
;