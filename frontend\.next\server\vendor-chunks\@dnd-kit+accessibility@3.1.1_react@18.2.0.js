"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit+accessibility@3.1.1_react@18.2.0";
exports.ids = ["vendor-chunks/@dnd-kit+accessibility@3.1.1_react@18.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@18.2.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@18.2.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HiddenText: () => (/* binding */ HiddenText),\n/* harmony export */   LiveRegion: () => (/* binding */ LiveRegion),\n/* harmony export */   useAnnouncement: () => (/* binding */ useAnnouncement)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nconst hiddenStyles = {\n  display: 'none'\n};\nfunction HiddenText(_ref) {\n  let {\n    id,\n    value\n  } = _ref;\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: hiddenStyles\n  }, value);\n}\n\nfunction LiveRegion(_ref) {\n  let {\n    id,\n    announcement,\n    ariaLiveType = \"assertive\"\n  } = _ref;\n  // Hide element visually but keep it readable by screen readers\n  const visuallyHidden = {\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    width: 1,\n    height: 1,\n    margin: -1,\n    border: 0,\n    padding: 0,\n    overflow: 'hidden',\n    clip: 'rect(0 0 0 0)',\n    clipPath: 'inset(100%)',\n    whiteSpace: 'nowrap'\n  };\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(\"div\", {\n    id: id,\n    style: visuallyHidden,\n    role: \"status\",\n    \"aria-live\": ariaLiveType,\n    \"aria-atomic\": true\n  }, announcement);\n}\n\nfunction useAnnouncement() {\n  const [announcement, setAnnouncement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const announce = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(value => {\n    if (value != null) {\n      setAnnouncement(value);\n    }\n  }, []);\n  return {\n    announce,\n    announcement\n  };\n}\n\n\n//# sourceMappingURL=accessibility.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@dnd-kit+accessibility@3.1.1_react@18.2.0/node_modules/@dnd-kit/accessibility/dist/accessibility.esm.js\n");

/***/ })

};
;