#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/bin/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/bin/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules/cssesc/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/cssesc@3.0.0/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../cssesc@3.0.0/node_modules/cssesc/bin/cssesc" "$@"
else
  exec node  "$basedir/../../../../../cssesc@3.0.0/node_modules/cssesc/bin/cssesc" "$@"
fi
