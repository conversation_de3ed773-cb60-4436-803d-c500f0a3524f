"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/content-disposition@0.5.4";
exports.ids = ["vendor-chunks/content-disposition@0.5.4"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/content-disposition@0.5.4/node_modules/content-disposition/index.js":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/content-disposition@0.5.4/node_modules/content-disposition/index.js ***!
  \************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * content-disposition\n * Copyright(c) 2014-2017 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = contentDisposition\nmodule.exports.parse = parse\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar basename = (__webpack_require__(/*! path */ \"path\").basename)\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(rsc)/./node_modules/.pnpm/safe-buffer@5.2.1/node_modules/safe-buffer/index.js\").Buffer)\n\n/**\n * RegExp to match non attr-char, *after* encodeURIComponent (i.e. not including \"%\")\n * @private\n */\n\nvar ENCODE_URL_ATTR_CHAR_REGEXP = /[\\x00-\\x20\"'()*,/:;<=>?@[\\\\\\]{}\\x7f]/g // eslint-disable-line no-control-regex\n\n/**\n * RegExp to match percent encoding escape.\n * @private\n */\n\nvar HEX_ESCAPE_REGEXP = /%[0-9A-Fa-f]{2}/\nvar HEX_ESCAPE_REPLACE_REGEXP = /%([0-9A-Fa-f]{2})/g\n\n/**\n * RegExp to match non-latin1 characters.\n * @private\n */\n\nvar NON_LATIN1_REGEXP = /[^\\x20-\\x7e\\xa0-\\xff]/g\n\n/**\n * RegExp to match quoted-pair in RFC 2616\n *\n * quoted-pair = \"\\\" CHAR\n * CHAR        = <any US-ASCII character (octets 0 - 127)>\n * @private\n */\n\nvar QESC_REGEXP = /\\\\([\\u0000-\\u007f])/g // eslint-disable-line no-control-regex\n\n/**\n * RegExp to match chars that must be quoted-pair in RFC 2616\n * @private\n */\n\nvar QUOTE_REGEXP = /([\\\\\"])/g\n\n/**\n * RegExp for various RFC 2616 grammar\n *\n * parameter     = token \"=\" ( token | quoted-string )\n * token         = 1*<any CHAR except CTLs or separators>\n * separators    = \"(\" | \")\" | \"<\" | \">\" | \"@\"\n *               | \",\" | \";\" | \":\" | \"\\\" | <\">\n *               | \"/\" | \"[\" | \"]\" | \"?\" | \"=\"\n *               | \"{\" | \"}\" | SP | HT\n * quoted-string = ( <\"> *(qdtext | quoted-pair ) <\"> )\n * qdtext        = <any TEXT except <\">>\n * quoted-pair   = \"\\\" CHAR\n * CHAR          = <any US-ASCII character (octets 0 - 127)>\n * TEXT          = <any OCTET except CTLs, but including LWS>\n * LWS           = [CRLF] 1*( SP | HT )\n * CRLF          = CR LF\n * CR            = <US-ASCII CR, carriage return (13)>\n * LF            = <US-ASCII LF, linefeed (10)>\n * SP            = <US-ASCII SP, space (32)>\n * HT            = <US-ASCII HT, horizontal-tab (9)>\n * CTL           = <any US-ASCII control character (octets 0 - 31) and DEL (127)>\n * OCTET         = <any 8-bit sequence of data>\n * @private\n */\n\nvar PARAM_REGEXP = /;[\\x09\\x20]*([!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\\x09\\x20]*=[\\x09\\x20]*(\"(?:[\\x20!\\x23-\\x5b\\x5d-\\x7e\\x80-\\xff]|\\\\[\\x20-\\x7e])*\"|[!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\\x09\\x20]*/g // eslint-disable-line no-control-regex\nvar TEXT_REGEXP = /^[\\x20-\\x7e\\x80-\\xff]+$/\nvar TOKEN_REGEXP = /^[!#$%&'*+.0-9A-Z^_`a-z|~-]+$/\n\n/**\n * RegExp for various RFC 5987 grammar\n *\n * ext-value     = charset  \"'\" [ language ] \"'\" value-chars\n * charset       = \"UTF-8\" / \"ISO-8859-1\" / mime-charset\n * mime-charset  = 1*mime-charsetc\n * mime-charsetc = ALPHA / DIGIT\n *               / \"!\" / \"#\" / \"$\" / \"%\" / \"&\"\n *               / \"+\" / \"-\" / \"^\" / \"_\" / \"`\"\n *               / \"{\" / \"}\" / \"~\"\n * language      = ( 2*3ALPHA [ extlang ] )\n *               / 4ALPHA\n *               / 5*8ALPHA\n * extlang       = *3( \"-\" 3ALPHA )\n * value-chars   = *( pct-encoded / attr-char )\n * pct-encoded   = \"%\" HEXDIG HEXDIG\n * attr-char     = ALPHA / DIGIT\n *               / \"!\" / \"#\" / \"$\" / \"&\" / \"+\" / \"-\" / \".\"\n *               / \"^\" / \"_\" / \"`\" / \"|\" / \"~\"\n * @private\n */\n\nvar EXT_VALUE_REGEXP = /^([A-Za-z0-9!#$%&+\\-^_`{}~]+)'(?:[A-Za-z]{2,3}(?:-[A-Za-z]{3}){0,3}|[A-Za-z]{4,8}|)'((?:%[0-9A-Fa-f]{2}|[A-Za-z0-9!#$&+.^_`|~-])+)$/\n\n/**\n * RegExp for various RFC 6266 grammar\n *\n * disposition-type = \"inline\" | \"attachment\" | disp-ext-type\n * disp-ext-type    = token\n * disposition-parm = filename-parm | disp-ext-parm\n * filename-parm    = \"filename\" \"=\" value\n *                  | \"filename*\" \"=\" ext-value\n * disp-ext-parm    = token \"=\" value\n *                  | ext-token \"=\" ext-value\n * ext-token        = <the characters in token, followed by \"*\">\n * @private\n */\n\nvar DISPOSITION_TYPE_REGEXP = /^([!#$%&'*+.0-9A-Z^_`a-z|~-]+)[\\x09\\x20]*(?:$|;)/ // eslint-disable-line no-control-regex\n\n/**\n * Create an attachment Content-Disposition header.\n *\n * @param {string} [filename]\n * @param {object} [options]\n * @param {string} [options.type=attachment]\n * @param {string|boolean} [options.fallback=true]\n * @return {string}\n * @public\n */\n\nfunction contentDisposition (filename, options) {\n  var opts = options || {}\n\n  // get type\n  var type = opts.type || 'attachment'\n\n  // get parameters\n  var params = createparams(filename, opts.fallback)\n\n  // format into string\n  return format(new ContentDisposition(type, params))\n}\n\n/**\n * Create parameters object from filename and fallback.\n *\n * @param {string} [filename]\n * @param {string|boolean} [fallback=true]\n * @return {object}\n * @private\n */\n\nfunction createparams (filename, fallback) {\n  if (filename === undefined) {\n    return\n  }\n\n  var params = {}\n\n  if (typeof filename !== 'string') {\n    throw new TypeError('filename must be a string')\n  }\n\n  // fallback defaults to true\n  if (fallback === undefined) {\n    fallback = true\n  }\n\n  if (typeof fallback !== 'string' && typeof fallback !== 'boolean') {\n    throw new TypeError('fallback must be a string or boolean')\n  }\n\n  if (typeof fallback === 'string' && NON_LATIN1_REGEXP.test(fallback)) {\n    throw new TypeError('fallback must be ISO-8859-1 string')\n  }\n\n  // restrict to file base name\n  var name = basename(filename)\n\n  // determine if name is suitable for quoted string\n  var isQuotedString = TEXT_REGEXP.test(name)\n\n  // generate fallback name\n  var fallbackName = typeof fallback !== 'string'\n    ? fallback && getlatin1(name)\n    : basename(fallback)\n  var hasFallback = typeof fallbackName === 'string' && fallbackName !== name\n\n  // set extended filename parameter\n  if (hasFallback || !isQuotedString || HEX_ESCAPE_REGEXP.test(name)) {\n    params['filename*'] = name\n  }\n\n  // set filename parameter\n  if (isQuotedString || hasFallback) {\n    params.filename = hasFallback\n      ? fallbackName\n      : name\n  }\n\n  return params\n}\n\n/**\n * Format object to Content-Disposition header.\n *\n * @param {object} obj\n * @param {string} obj.type\n * @param {object} [obj.parameters]\n * @return {string}\n * @private\n */\n\nfunction format (obj) {\n  var parameters = obj.parameters\n  var type = obj.type\n\n  if (!type || typeof type !== 'string' || !TOKEN_REGEXP.test(type)) {\n    throw new TypeError('invalid type')\n  }\n\n  // start with normalized type\n  var string = String(type).toLowerCase()\n\n  // append parameters\n  if (parameters && typeof parameters === 'object') {\n    var param\n    var params = Object.keys(parameters).sort()\n\n    for (var i = 0; i < params.length; i++) {\n      param = params[i]\n\n      var val = param.substr(-1) === '*'\n        ? ustring(parameters[param])\n        : qstring(parameters[param])\n\n      string += '; ' + param + '=' + val\n    }\n  }\n\n  return string\n}\n\n/**\n * Decode a RFC 5987 field value (gracefully).\n *\n * @param {string} str\n * @return {string}\n * @private\n */\n\nfunction decodefield (str) {\n  var match = EXT_VALUE_REGEXP.exec(str)\n\n  if (!match) {\n    throw new TypeError('invalid extended field value')\n  }\n\n  var charset = match[1].toLowerCase()\n  var encoded = match[2]\n  var value\n\n  // to binary string\n  var binary = encoded.replace(HEX_ESCAPE_REPLACE_REGEXP, pdecode)\n\n  switch (charset) {\n    case 'iso-8859-1':\n      value = getlatin1(binary)\n      break\n    case 'utf-8':\n      value = Buffer.from(binary, 'binary').toString('utf8')\n      break\n    default:\n      throw new TypeError('unsupported charset in extended field')\n  }\n\n  return value\n}\n\n/**\n * Get ISO-8859-1 version of string.\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction getlatin1 (val) {\n  // simple Unicode -> ISO-8859-1 transformation\n  return String(val).replace(NON_LATIN1_REGEXP, '?')\n}\n\n/**\n * Parse Content-Disposition header string.\n *\n * @param {string} string\n * @return {object}\n * @public\n */\n\nfunction parse (string) {\n  if (!string || typeof string !== 'string') {\n    throw new TypeError('argument string is required')\n  }\n\n  var match = DISPOSITION_TYPE_REGEXP.exec(string)\n\n  if (!match) {\n    throw new TypeError('invalid type format')\n  }\n\n  // normalize type\n  var index = match[0].length\n  var type = match[1].toLowerCase()\n\n  var key\n  var names = []\n  var params = {}\n  var value\n\n  // calculate index to start at\n  index = PARAM_REGEXP.lastIndex = match[0].substr(-1) === ';'\n    ? index - 1\n    : index\n\n  // match parameters\n  while ((match = PARAM_REGEXP.exec(string))) {\n    if (match.index !== index) {\n      throw new TypeError('invalid parameter format')\n    }\n\n    index += match[0].length\n    key = match[1].toLowerCase()\n    value = match[2]\n\n    if (names.indexOf(key) !== -1) {\n      throw new TypeError('invalid duplicate parameter')\n    }\n\n    names.push(key)\n\n    if (key.indexOf('*') + 1 === key.length) {\n      // decode extended value\n      key = key.slice(0, -1)\n      value = decodefield(value)\n\n      // overwrite existing value\n      params[key] = value\n      continue\n    }\n\n    if (typeof params[key] === 'string') {\n      continue\n    }\n\n    if (value[0] === '\"') {\n      // remove quotes and escapes\n      value = value\n        .substr(1, value.length - 2)\n        .replace(QESC_REGEXP, '$1')\n    }\n\n    params[key] = value\n  }\n\n  if (index !== -1 && index !== string.length) {\n    throw new TypeError('invalid parameter format')\n  }\n\n  return new ContentDisposition(type, params)\n}\n\n/**\n * Percent decode a single character.\n *\n * @param {string} str\n * @param {string} hex\n * @return {string}\n * @private\n */\n\nfunction pdecode (str, hex) {\n  return String.fromCharCode(parseInt(hex, 16))\n}\n\n/**\n * Percent encode a single character.\n *\n * @param {string} char\n * @return {string}\n * @private\n */\n\nfunction pencode (char) {\n  return '%' + String(char)\n    .charCodeAt(0)\n    .toString(16)\n    .toUpperCase()\n}\n\n/**\n * Quote a string for HTTP.\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction qstring (val) {\n  var str = String(val)\n\n  return '\"' + str.replace(QUOTE_REGEXP, '\\\\$1') + '\"'\n}\n\n/**\n * Encode a Unicode string for HTTP (RFC 5987).\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction ustring (val) {\n  var str = String(val)\n\n  // percent encode as UTF-8\n  var encoded = encodeURIComponent(str)\n    .replace(ENCODE_URL_ATTR_CHAR_REGEXP, pencode)\n\n  return 'UTF-8\\'\\'' + encoded\n}\n\n/**\n * Class for parsed Content-Disposition header for v8 optimization\n *\n * @public\n * @param {string} type\n * @param {object} parameters\n * @constructor\n */\n\nfunction ContentDisposition (type, parameters) {\n  this.type = type\n  this.parameters = parameters\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/content-disposition@0.5.4/node_modules/content-disposition/index.js\n");

/***/ })

};
;