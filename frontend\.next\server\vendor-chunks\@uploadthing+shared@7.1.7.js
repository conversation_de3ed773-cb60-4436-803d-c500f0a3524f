"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uploadthing+shared@7.1.7";
exports.ids = ["vendor-chunks/@uploadthing+shared@7.1.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_FILE_TYPES: () => (/* binding */ ALLOWED_FILE_TYPES),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   FILESIZE_UNITS: () => (/* binding */ FILESIZE_UNITS),\n/* harmony export */   FetchContext: () => (/* binding */ FetchContext),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   INTERNAL_DO_NOT_USE__fatalClientError: () => (/* binding */ INTERNAL_DO_NOT_USE__fatalClientError),\n/* harmony export */   INTERNAL_doFormatting: () => (/* binding */ INTERNAL_doFormatting),\n/* harmony export */   InvalidFileSizeError: () => (/* binding */ InvalidFileSizeError),\n/* harmony export */   InvalidFileTypeError: () => (/* binding */ InvalidFileTypeError),\n/* harmony export */   InvalidJsonError: () => (/* binding */ InvalidJsonError),\n/* harmony export */   InvalidRouteConfigError: () => (/* binding */ InvalidRouteConfigError),\n/* harmony export */   InvalidURLError: () => (/* binding */ InvalidURLError),\n/* harmony export */   RetryError: () => (/* binding */ RetryError),\n/* harmony export */   UnknownFileTypeError: () => (/* binding */ UnknownFileTypeError),\n/* harmony export */   UploadAbortedError: () => (/* binding */ UploadAbortedError),\n/* harmony export */   UploadPausedError: () => (/* binding */ UploadPausedError),\n/* harmony export */   UploadThingError: () => (/* binding */ UploadThingError),\n/* harmony export */   ValidACLs: () => (/* binding */ ValidACLs),\n/* harmony export */   ValidContentDispositions: () => (/* binding */ ValidContentDispositions),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   allowedContentTextLabelGenerator: () => (/* binding */ allowedContentTextLabelGenerator),\n/* harmony export */   bytesToFileSize: () => (/* binding */ bytesToFileSize),\n/* harmony export */   capitalizeStart: () => (/* binding */ capitalizeStart),\n/* harmony export */   contentFieldToContent: () => (/* binding */ contentFieldToContent),\n/* harmony export */   createIdentityProxy: () => (/* binding */ createIdentityProxy),\n/* harmony export */   defaultClassListMerger: () => (/* binding */ defaultClassListMerger),\n/* harmony export */   fetchEff: () => (/* binding */ fetchEff),\n/* harmony export */   fileSizeToBytes: () => (/* binding */ fileSizeToBytes),\n/* harmony export */   fillInputRouteConfig: () => (/* binding */ fillInputRouteConfig),\n/* harmony export */   filterDefinedObjectValues: () => (/* binding */ filterDefinedObjectValues),\n/* harmony export */   generateClientDropzoneAccept: () => (/* binding */ generateClientDropzoneAccept),\n/* harmony export */   generateKey: () => (/* binding */ generateKey),\n/* harmony export */   generateMimeTypes: () => (/* binding */ generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* binding */ generatePermittedFileTypes),\n/* harmony export */   generateSignedURL: () => (/* binding */ generateSignedURL),\n/* harmony export */   getDefaultRouteConfigValues: () => (/* binding */ getDefaultRouteConfigValues),\n/* harmony export */   getDefaultSizeForType: () => (/* binding */ getDefaultSizeForType),\n/* harmony export */   getErrorTypeFromStatusCode: () => (/* binding */ getErrorTypeFromStatusCode),\n/* harmony export */   getFilesFromClipboardEvent: () => (/* binding */ getFilesFromClipboardEvent),\n/* harmony export */   getFullApiUrl: () => (/* binding */ getFullApiUrl),\n/* harmony export */   getRequestUrl: () => (/* binding */ getRequestUrl),\n/* harmony export */   getStatusCodeFromError: () => (/* binding */ getStatusCodeFromError),\n/* harmony export */   initialState: () => (/* binding */ initialState),\n/* harmony export */   isEnterOrSpace: () => (/* binding */ isEnterOrSpace),\n/* harmony export */   isEventWithFiles: () => (/* binding */ isEventWithFiles),\n/* harmony export */   isFileAccepted: () => (/* binding */ isFileAccepted),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isRouteArray: () => (/* binding */ isRouteArray),\n/* harmony export */   isValidQuantity: () => (/* binding */ isValidQuantity),\n/* harmony export */   isValidSize: () => (/* binding */ isValidSize),\n/* harmony export */   matchFileType: () => (/* binding */ matchFileType),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   objectKeys: () => (/* binding */ objectKeys),\n/* harmony export */   parseResponseJson: () => (/* binding */ parseResponseJson),\n/* harmony export */   parseTimeToSeconds: () => (/* binding */ parseTimeToSeconds),\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   resolveMaybeUrlArg: () => (/* binding */ resolveMaybeUrlArg),\n/* harmony export */   roundProgress: () => (/* binding */ roundProgress),\n/* harmony export */   safeNumberReplacer: () => (/* binding */ safeNumberReplacer),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   semverLite: () => (/* binding */ semverLite),\n/* harmony export */   signPayload: () => (/* binding */ signPayload),\n/* harmony export */   styleFieldToClassName: () => (/* binding */ styleFieldToClassName),\n/* harmony export */   styleFieldToCssObject: () => (/* binding */ styleFieldToCssObject),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   verifyKey: () => (/* binding */ verifyKey),\n/* harmony export */   verifySignature: () => (/* binding */ verifySignature),\n/* harmony export */   warnIfInvalidPeerDependency: () => (/* binding */ warnIfInvalidPeerDependency)\n/* harmony export */ });\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @uploadthing/mime-types */ \"(ssr)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/dist/index.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Predicate */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var effect_Context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Context */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Context.js\");\n/* harmony import */ var _uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @uploadthing/mime-types/audio */ \"(ssr)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/audio/index.js\");\n/* harmony import */ var _uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @uploadthing/mime-types/image */ \"(ssr)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/image/index.js\");\n/* harmony import */ var _uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uploadthing/mime-types/text */ \"(ssr)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/text/index.js\");\n/* harmony import */ var _uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @uploadthing/mime-types/video */ \"(ssr)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/video/index.js\");\n/* harmony import */ var effect_Encoding__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! effect/Encoding */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Encoding.js\");\n/* harmony import */ var effect_Hash__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! effect/Hash */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Hash.js\");\n/* harmony import */ var effect_Redacted__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! effect/Redacted */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Redacted.js\");\n/* harmony import */ var sqids__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqids */ \"(ssr)/./node_modules/.pnpm/sqids@0.3.0/node_modules/sqids/esm/sqids.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ValidContentDispositions = [\n    \"inline\",\n    \"attachment\"\n];\nconst ValidACLs = [\n    \"public-read\",\n    \"private\"\n];\n\nclass InvalidRouteConfigError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidRouteConfig\") {\n    constructor(type, field){\n        const reason = field ? `Expected route config to have a ${field} for key ${type} but none was found.` : `Encountered an invalid route config during backfilling. ${type} was not found.`;\n        super({\n            reason\n        });\n    }\n}\nclass UnknownFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UnknownFileType\") {\n    constructor(fileName){\n        const reason = `Could not determine type for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileType\") {\n    constructor(fileType, fileName){\n        const reason = `File type ${fileType} not allowed for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileSizeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileSize\") {\n    constructor(fileSize){\n        const reason = `Invalid file size: ${fileSize}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidURLError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidURL\") {\n    constructor(attemptedUrl){\n        super({\n            reason: `Failed to parse '${attemptedUrl}' as a URL.`\n        });\n    }\n}\nclass RetryError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"RetryError\") {\n}\nclass FetchError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"FetchError\") {\n}\nclass InvalidJsonError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidJson\") {\n}\nclass BadRequestError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"BadRequestError\") {\n    getMessage() {\n        if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(this.json)) {\n            if (typeof this.json.message === \"string\") return this.json.message;\n        }\n        return this.message;\n    }\n}\nclass UploadPausedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\nclass UploadAbortedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\n\nfunction isRouteArray(routeConfig) {\n    return Array.isArray(routeConfig);\n}\nfunction getDefaultSizeForType(fileType) {\n    if (fileType === \"image\") return \"4MB\";\n    if (fileType === \"video\") return \"16MB\";\n    if (fileType === \"audio\") return \"8MB\";\n    if (fileType === \"blob\") return \"8MB\";\n    if (fileType === \"pdf\") return \"4MB\";\n    if (fileType === \"text\") return \"64KB\";\n    return \"4MB\";\n}\nfunction getDefaultRouteConfigValues(type) {\n    return {\n        maxFileSize: getDefaultSizeForType(type),\n        maxFileCount: 1,\n        minFileCount: 1,\n        contentDisposition: \"inline\"\n    };\n}\n/**\n * This function takes in the user's input and \"upscales\" it to a full config\n * Additionally, it replaces numbers with \"safe\" equivalents\n *\n * Example:\n * ```ts\n * [\"image\"] => { image: { maxFileSize: \"4MB\", limit: 1 } }\n * ```\n */ const fillInputRouteConfig = (routeConfig)=>{\n    // If array, apply defaults\n    if (isRouteArray(routeConfig)) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(routeConfig.reduce((acc, fileType)=>{\n            acc[fileType] = getDefaultRouteConfigValues(fileType);\n            return acc;\n        }, {}));\n    }\n    // Backfill defaults onto config\n    const newConfig = {};\n    for (const key of objectKeys(routeConfig)){\n        const value = routeConfig[key];\n        if (!value) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidRouteConfigError(key));\n        newConfig[key] = {\n            ...getDefaultRouteConfigValues(key),\n            ...value\n        };\n    }\n    // we know that the config is valid, so we can stringify it and parse it back\n    // this allows us to replace numbers with \"safe\" equivalents\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(JSON.parse(JSON.stringify(newConfig, safeNumberReplacer)));\n};\n/**\n * Match the file's type for a given allow list e.g. `image/png => image`\n * Prefers the file's type, then falls back to a extension-based lookup\n */ const matchFileType = (file, allowedTypes)=>{\n    // Type might be \"\" if the browser doesn't recognize the mime type\n    const mimeType = file.type || (0,_uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__.lookup)(file.name);\n    if (!mimeType) {\n        if (allowedTypes.includes(\"blob\")) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new UnknownFileTypeError(file.name));\n    }\n    // If the user has specified a specific mime type, use that\n    if (allowedTypes.some((type)=>type.includes(\"/\"))) {\n        if (allowedTypes.includes(mimeType)) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(mimeType);\n        }\n    }\n    // Otherwise, we have a \"magic\" type eg. \"image\" or \"video\"\n    const type = mimeType.toLowerCase() === \"application/pdf\" ? \"pdf\" : mimeType.split(\"/\")[0];\n    if (!allowedTypes.includes(type)) {\n        // Blob is a catch-all for any file type not explicitly supported\n        if (allowedTypes.includes(\"blob\")) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        } else {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileTypeError(type, file.name));\n        }\n    }\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(type);\n};\nconst FILESIZE_UNITS = [\n    \"B\",\n    \"KB\",\n    \"MB\",\n    \"GB\",\n    \"TB\"\n];\nconst fileSizeToBytes = (fileSize)=>{\n    const regex = new RegExp(`^(\\\\d+)(\\\\.\\\\d+)?\\\\s*(${FILESIZE_UNITS.join(\"|\")})$`, \"i\");\n    // make sure the string is in the format of 123KB\n    const match = fileSize.match(regex);\n    if (!match?.[1] || !match[3]) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileSizeError(fileSize));\n    }\n    const sizeValue = parseFloat(match[1]);\n    const sizeUnit = match[3].toUpperCase();\n    const bytes = sizeValue * Math.pow(1024, FILESIZE_UNITS.indexOf(sizeUnit));\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(Math.floor(bytes));\n};\nconst bytesToFileSize = (bytes)=>{\n    if (bytes === 0 || bytes === -1) {\n        return \"0B\";\n    }\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(2)}${FILESIZE_UNITS[i]}`;\n};\nasync function safeParseJSON(input) {\n    const text = await input.text();\n    try {\n        return JSON.parse(text);\n    } catch (err) {\n        // eslint-disable-next-line no-console\n        console.error(`Error parsing JSON, got '${text}'`, err);\n        return new Error(`Error parsing JSON, got '${text}'`);\n    }\n}\n/** typesafe Object.keys */ function objectKeys(obj) {\n    return Object.keys(obj);\n}\nfunction filterDefinedObjectValues(obj) {\n    return Object.fromEntries(Object.entries(obj).filter((pair)=>pair[1] != null));\n}\nfunction semverLite(required, toCheck) {\n    // Pull out numbers from strings like `6.0.0`, `^6.4`, `~6.4.0`\n    const semverRegex = /(\\d+)\\.?(\\d+)?\\.?(\\d+)?/;\n    const requiredMatch = semverRegex.exec(required);\n    if (!requiredMatch?.[0]) {\n        throw new Error(`Invalid semver requirement: ${required}`);\n    }\n    const toCheckMatch = semverRegex.exec(toCheck);\n    if (!toCheckMatch?.[0]) {\n        throw new Error(`Invalid semver to check: ${toCheck}`);\n    }\n    const [_1, rMajor, rMinor, rPatch] = requiredMatch;\n    const [_2, cMajor, cMinor, cPatch] = toCheckMatch;\n    if (required.startsWith(\"^\")) {\n        // Major must be equal, minor must be greater or equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor && cMinor && rMinor > cMinor) return false;\n        return true;\n    }\n    if (required.startsWith(\"~\")) {\n        // Major must be equal, minor must be equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor !== cMinor) return false;\n        return true;\n    }\n    // Exact match\n    return rMajor === cMajor && rMinor === cMinor && rPatch === cPatch;\n}\nfunction warnIfInvalidPeerDependency(pkg, required, toCheck) {\n    if (!semverLite(required, toCheck)) {\n        // eslint-disable-next-line no-console\n        console.warn(`!!!WARNING::: ${pkg} requires \"uploadthing@${required}\", but version \"${toCheck}\" is installed`);\n    }\n}\nconst getRequestUrl = (req)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const host = req.headers.get(\"x-forwarded-host\") ?? req.headers.get(\"host\");\n        const proto = req.headers.get(\"x-forwarded-proto\") ?? \"https\";\n        const protocol = proto.endsWith(\":\") ? proto : `${proto}:`;\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(req.url, `${protocol}//${host}`),\n            catch: ()=>new InvalidURLError(req.url)\n        });\n        url.search = \"\";\n        return url;\n    });\nconst getFullApiUrl = (maybeUrl)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const base = (()=>{\n            if (typeof window !== \"undefined\") return window.location.origin;\n            if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n            return \"http://localhost:3000\";\n        })();\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(maybeUrl ?? \"/api/uploadthing\", base),\n            catch: ()=>new InvalidURLError(maybeUrl ?? \"/api/uploadthing\")\n        });\n        if (url.pathname === \"/\") {\n            url.pathname = \"/api/uploadthing\";\n        }\n        return url;\n    });\n/*\n * Returns a full URL to the dev's uploadthing endpoint\n * Can take either an origin, or a pathname, or a full URL\n * and will return the \"closest\" url matching the default\n * `<VERCEL_URL || localhost>/api/uploadthing`\n */ const resolveMaybeUrlArg = (maybeUrl)=>{\n    return maybeUrl instanceof URL ? maybeUrl : effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync(getFullApiUrl(maybeUrl));\n};\nfunction parseTimeToSeconds(time) {\n    if (typeof time === \"number\") return time;\n    const match = time.split(/(\\d+)/).filter(Boolean);\n    const num = Number(match[0]);\n    const unit = (match[1] ?? \"s\").trim().slice(0, 1);\n    const multiplier = {\n        s: 1,\n        m: 60,\n        h: 3600,\n        d: 86400\n    }[unit];\n    return num * multiplier;\n}\n/**\n * Replacer for JSON.stringify that will replace numbers that cannot be\n * serialized to JSON with \"reasonable equivalents\".\n *\n * Infinity and -Infinity are replaced by MAX_SAFE_INTEGER and MIN_SAFE_INTEGER\n * NaN is replaced by 0\n *\n */ const safeNumberReplacer = (_, value)=>{\n    if (typeof value !== \"number\") return value;\n    if (Number.isSafeInteger(value) || value <= Number.MAX_SAFE_INTEGER && value >= Number.MIN_SAFE_INTEGER) {\n        return value;\n    }\n    if (value === Infinity) return Number.MAX_SAFE_INTEGER;\n    if (value === -Infinity) return Number.MIN_SAFE_INTEGER;\n    if (Number.isNaN(value)) return 0;\n};\nfunction noop() {\n// noop\n}\nfunction createIdentityProxy() {\n    return new Proxy(noop, {\n        get: (_, prop)=>prop\n    });\n}\nfunction unwrap(x, ...args) {\n    return typeof x === \"function\" ? x(...args) : x;\n}\n\nconst ALLOWED_FILE_TYPES = [\n    \"image\",\n    \"video\",\n    \"audio\",\n    \"pdf\",\n    \"text\",\n    \"blob\"\n];\n\nconst ERROR_CODES = {\n    // Generic\n    BAD_REQUEST: 400,\n    NOT_FOUND: 404,\n    FORBIDDEN: 403,\n    INTERNAL_SERVER_ERROR: 500,\n    INTERNAL_CLIENT_ERROR: 500,\n    // S3 specific\n    TOO_LARGE: 413,\n    TOO_SMALL: 400,\n    TOO_MANY_FILES: 400,\n    KEY_TOO_LONG: 400,\n    // UploadThing specific\n    URL_GENERATION_FAILED: 500,\n    UPLOAD_FAILED: 500,\n    MISSING_ENV: 500,\n    INVALID_SERVER_CONFIG: 500,\n    FILE_LIMIT_EXCEEDED: 500\n};\nfunction messageFromUnknown(cause, fallback) {\n    if (typeof cause === \"string\") {\n        return cause;\n    }\n    if (cause instanceof Error) {\n        return cause.message;\n    }\n    if (cause && typeof cause === \"object\" && \"message\" in cause && typeof cause.message === \"string\") {\n        return cause.message;\n    }\n    return fallback ?? \"An unknown error occurred\";\n}\nclass UploadThingError extends effect_Micro__WEBPACK_IMPORTED_MODULE_1__.Error {\n    constructor(initOpts){\n        const opts = typeof initOpts === \"string\" ? {\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: initOpts\n        } : initOpts;\n        const message = opts.message ?? messageFromUnknown(opts.cause, opts.code);\n        super({\n            message\n        }), this._tag = \"UploadThingError\", this.name = \"UploadThingError\";\n        this.code = opts.code;\n        this.data = opts.data;\n        if (opts.cause instanceof Error) {\n            this.cause = opts.cause;\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(opts.cause) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isNumber(opts.cause.status) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause.statusText)) {\n            this.cause = new Error(`Response ${opts.cause.status} ${opts.cause.statusText}`);\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause)) {\n            this.cause = new Error(opts.cause);\n        } else {\n            this.cause = opts.cause;\n        }\n    }\n    static toObject(error) {\n        return {\n            code: error.code,\n            message: error.message,\n            data: error.data\n        };\n    }\n    static serialize(error) {\n        return JSON.stringify(UploadThingError.toObject(error));\n    }\n}\nfunction getErrorTypeFromStatusCode(statusCode) {\n    for (const [code, status] of Object.entries(ERROR_CODES)){\n        if (status === statusCode) {\n            return code;\n        }\n    }\n    return \"INTERNAL_SERVER_ERROR\";\n}\nfunction getStatusCodeFromError(error) {\n    return ERROR_CODES[error.code];\n}\nconst INTERNAL_DO_NOT_USE__fatalClientError = (e)=>new UploadThingError({\n        code: \"INTERNAL_CLIENT_ERROR\",\n        message: \"Something went wrong. Please report this to UploadThing.\",\n        cause: e\n    });\n\nclass FetchContext extends /** #__PURE__ */ effect_Context__WEBPACK_IMPORTED_MODULE_4__.Tag(\"uploadthing/Fetch\")() {\n}\n// Temporary Effect wrappers below.\n// Only for use in the browser.\n// On the server, use `@effect/platform.HttpClient` instead.\nconst fetchEff = (input, init)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.service(FetchContext), (fetch)=>{\n        const headers = new Headers(init?.headers ?? []);\n        const reqInfo = {\n            url: input.toString(),\n            method: init?.method,\n            body: init?.body,\n            headers: Object.fromEntries(headers)\n        };\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: (signal)=>fetch(input, {\n                    ...init,\n                    headers,\n                    signal\n                }),\n            catch: (error)=>new FetchError({\n                    error: error instanceof Error ? {\n                        ...error,\n                        name: error.name,\n                        message: error.message,\n                        stack: error.stack\n                    } : error,\n                    input: reqInfo\n                })\n        }).pipe(// eslint-disable-next-line no-console\n        effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tapError((e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>console.error(e.input))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((res)=>Object.assign(res, {\n                requestUrl: reqInfo.url\n            })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"fetch\"));\n    });\nconst parseResponseJson = (res)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n        try: async ()=>{\n            const json = await res.json();\n            return {\n                json,\n                ok: res.ok,\n                status: res.status\n            };\n        },\n        catch: (error)=>new InvalidJsonError({\n                error,\n                input: res.requestUrl\n            })\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFail(({ ok })=>ok, ({ json, status })=>new BadRequestError({\n            status,\n            message: `Request to ${res.requestUrl} failed with status ${status}`,\n            json\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(({ json })=>json), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"parseJson\"));\n\nconst roundProgress = (progress, granularity)=>{\n    if (granularity === \"all\") return progress;\n    if (granularity === \"fine\") return Math.round(progress);\n    return Math.floor(progress / 10) * 10;\n};\nconst generateMimeTypes = (typesOrRouteConfig)=>{\n    const fileTypes = Array.isArray(typesOrRouteConfig) ? typesOrRouteConfig : objectKeys(typesOrRouteConfig);\n    if (fileTypes.includes(\"blob\")) return [];\n    return fileTypes.map((type)=>{\n        if (type === \"pdf\") return \"application/pdf\";\n        if (type.includes(\"/\")) return type;\n        // Add wildcard to support all subtypes, e.g. image => \"image/*\"\n        // But some browsers/OSes don't support it, so we'll also dump all the mime types\n        // we know that starts with the type, e.g. image => \"image/png, image/jpeg, ...\"\n        if (type === \"audio\") return [\n            \"audio/*\",\n            ...objectKeys(_uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__.audio)\n        ].join(\", \");\n        if (type === \"image\") return [\n            \"image/*\",\n            ...objectKeys(_uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__.image)\n        ].join(\", \");\n        if (type === \"text\") return [\n            \"text/*\",\n            ...objectKeys(_uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__.text)\n        ].join(\", \");\n        if (type === \"video\") return [\n            \"video/*\",\n            ...objectKeys(_uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__.video)\n        ].join(\", \");\n        return `${type}/*`;\n    });\n};\nconst generateClientDropzoneAccept = (fileTypes)=>{\n    const mimeTypes = generateMimeTypes(fileTypes);\n    return Object.fromEntries(mimeTypes.map((type)=>[\n            type,\n            []\n        ]));\n};\nfunction getFilesFromClipboardEvent(event) {\n    const dataTransferItems = event.clipboardData?.items;\n    if (!dataTransferItems) return;\n    const files = Array.from(dataTransferItems).reduce((acc, curr)=>{\n        const f = curr.getAsFile();\n        return f ? [\n            ...acc,\n            f\n        ] : acc;\n    }, []);\n    return files;\n}\n/**\n * Shared helpers for our premade components that's reusable by multiple frameworks\n */ const generatePermittedFileTypes = (config)=>{\n    const fileTypes = config ? objectKeys(config) : [];\n    const maxFileCount = config ? Object.values(config).map((v)=>v.maxFileCount) : [];\n    return {\n        fileTypes,\n        multiple: maxFileCount.some((v)=>v && v > 1)\n    };\n};\nconst capitalizeStart = (str)=>{\n    return str.charAt(0).toUpperCase() + str.slice(1);\n};\nconst INTERNAL_doFormatting = (config)=>{\n    if (!config) return \"\";\n    const allowedTypes = objectKeys(config);\n    const formattedTypes = allowedTypes.map((f)=>f === \"blob\" ? \"file\" : f);\n    // Format multi-type uploader label as \"Supports videos, images and files\";\n    if (formattedTypes.length > 1) {\n        const lastType = formattedTypes.pop();\n        return `${formattedTypes.join(\"s, \")} and ${lastType}s`;\n    }\n    // Single type uploader label\n    const key = allowedTypes[0];\n    const formattedKey = formattedTypes[0];\n    if (!key || !formattedKey) return \"\";\n    const { maxFileSize, maxFileCount, minFileCount } = config[key];\n    if (maxFileCount && maxFileCount > 1) {\n        if (minFileCount > 1) {\n            return `${minFileCount} - ${maxFileCount} ${formattedKey}s up to ${maxFileSize}`;\n        } else {\n            return `${formattedKey}s up to ${maxFileSize}, max ${maxFileCount}`;\n        }\n    } else {\n        return `${formattedKey} (${maxFileSize})`;\n    }\n};\nconst allowedContentTextLabelGenerator = (config)=>{\n    return capitalizeStart(INTERNAL_doFormatting(config));\n};\nconst styleFieldToClassName = (styleField, args)=>{\n    if (typeof styleField === \"string\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"string\") return result;\n    }\n    return \"\";\n};\nconst styleFieldToCssObject = (styleField, args)=>{\n    if (typeof styleField === \"object\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"object\") return result;\n    }\n    return {};\n};\nconst contentFieldToContent = (contentField, arg)=>{\n    if (!contentField) return null;\n    if (typeof contentField !== \"function\") return contentField;\n    if (typeof contentField === \"function\") {\n        const result = contentField(arg);\n        return result;\n    }\n};\nconst defaultClassListMerger = (...classes)=>{\n    return classes.filter(Boolean).join(\" \");\n};\n\nconst signaturePrefix = \"hmac-sha256=\";\nconst algorithm = {\n    name: \"HMAC\",\n    hash: \"SHA-256\"\n};\nconst encoder = new TextEncoder();\nfunction shuffle(str, seed) {\n    const chars = str.split(\"\");\n    const seedNum = effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(seed);\n    let temp;\n    let j;\n    for(let i = 0; i < chars.length; i++){\n        j = (seedNum % (i + 1) + i) % chars.length;\n        temp = chars[i];\n        chars[i] = chars[j];\n        chars[j] = temp;\n    }\n    return chars.join(\"\");\n}\nconst signPayload = (payload, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.importKey(\"raw\", encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret)), algorithm, false, [\n                    \"sign\"\n                ]),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid signing secret\",\n                    cause: e\n                })\n        });\n        const signature = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.sign(algorithm, signingKey, encoder.encode(payload)),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    cause: e\n                })\n        }), (arrayBuffer)=>effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.encodeHex(new Uint8Array(arrayBuffer)));\n        return `${signaturePrefix}${signature}`;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"signPayload\"));\nconst verifySignature = (payload, signature, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const sig = signature?.slice(signaturePrefix.length);\n        if (!sig) return false;\n        const secretBytes = encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret));\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.importKey(\"raw\", secretBytes, algorithm, false, [\n                \"verify\"\n            ]));\n        const sigBytes = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fromEither(effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.decodeHex(sig));\n        const payloadBytes = encoder.encode(payload);\n        return yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.verify(algorithm, signingKey, sigBytes, payloadBytes));\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifySignature\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateKey = (file, appId, getHashParts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        // Get the parts of which we should hash to constuct the key\n        // This allows the user to customize the hashing algorithm\n        // If they for example want to generate the same key for the\n        // same file whenever it was uploaded\n        const hashParts = JSON.stringify(getHashParts?.(file) ?? [\n            file.name,\n            file.size,\n            file.type,\n            file.lastModified,\n            Date.now()\n        ]);\n        // Hash and Encode the parts and appId as sqids\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const encodedFileSeed = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 36\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(hashParts))\n        ]);\n        const encodedAppId = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        // Concatenate them\n        return encodedAppId + encodedFileSeed;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateKey\"));\n// Verify that the key was generated with the same appId\nconst verifyKey = (key, appId)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const expectedPrefix = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        return key.startsWith(expectedPrefix);\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifyKey\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateSignedURL = (url, secretKey, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const parsedURL = new URL(url);\n        const ttl = opts.ttlInSeconds ? parseTimeToSeconds(opts.ttlInSeconds) : 60 * 60;\n        const expirationTime = Date.now() + ttl * 1000;\n        parsedURL.searchParams.append(\"expires\", expirationTime.toString());\n        if (opts.data) {\n            Object.entries(opts.data).forEach(([key, value])=>{\n                if (value == null) return;\n                const encoded = encodeURIComponent(value);\n                parsedURL.searchParams.append(key, encoded);\n            });\n        }\n        const signature = yield* signPayload(parsedURL.toString(), secretKey);\n        parsedURL.searchParams.append(\"signature\", signature);\n        return parsedURL.href;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateSignedURL\"));\n\n/**\n * Copyright (c) (MIT License) 2015 Andrey Okonetchnikov\n * https://github.com/react-dropzone/attr-accept/blob/master/src/index.js\n */ function accepts(file, acceptedFiles) {\n    if (acceptedFiles) {\n        const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(\",\");\n        const fileName = file.name;\n        const mimeType = file.type.toLowerCase();\n        const baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n        return acceptedFilesArray.some((type)=>{\n            const validType = type.trim().toLowerCase();\n            if (validType.startsWith(\".\")) {\n                return fileName.toLowerCase().endsWith(validType);\n            } else if (validType.endsWith(\"/*\")) {\n                // This is something like a image/* mime type\n                return baseMimeType === validType.replace(/\\/.*$/, \"\");\n            }\n            return mimeType === validType;\n        });\n    }\n    return true;\n}\nconst isPropagationStopped = (event)=>{\n    if (typeof event.isPropagationStopped === \"function\") {\n        return event.isPropagationStopped();\n    }\n    if (typeof event.cancelBubble !== \"undefined\") {\n        return event.cancelBubble;\n    }\n    return false;\n};\n// Firefox versions prior to 53 return a bogus MIME type for every file drag, so dragovers with\n// that MIME type will always be accepted\nfunction isFileAccepted(file, accept) {\n    return file.type === \"application/x-moz-file\" || accepts(file, accept);\n}\nfunction isEnterOrSpace(event) {\n    return \"key\" in event && (event.key === \" \" || event.key === \"Enter\") || \"keyCode\" in event && (event.keyCode === 32 || event.keyCode === 13);\n}\nconst isDefined = (v)=>v != null;\nfunction isValidSize(file, minSize, maxSize) {\n    if (!isDefined(file.size)) return true;\n    if (isDefined(minSize) && isDefined(maxSize)) {\n        return file.size >= minSize && file.size <= maxSize;\n    }\n    if (isDefined(minSize) && file.size < minSize) return false;\n    if (isDefined(maxSize) && file.size > maxSize) return false;\n    return true;\n}\nfunction isValidQuantity(files, multiple, maxFiles) {\n    if (!multiple && files.length > 1) return false;\n    if (multiple && maxFiles >= 1 && files.length > maxFiles) return false;\n    return true;\n}\nfunction allFilesAccepted({ files, accept, minSize, maxSize, multiple, maxFiles }) {\n    if (!isValidQuantity(files, multiple, maxFiles)) return false;\n    return files.every((file)=>isFileAccepted(file, accept) && isValidSize(file, minSize, maxSize));\n}\nfunction isEventWithFiles(event) {\n    if (!(\"dataTransfer\" in event && event.dataTransfer !== null)) {\n        return !!event.target && \"files\" in event.target && !!event.target.files;\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n    return Array.prototype.some.call(// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    event.dataTransfer?.types, (type)=>type === \"Files\" || type === \"application/x-moz-file\");\n}\nfunction isIeOrEdge(ua = window.navigator.userAgent) {\n    return ua.includes(\"MSIE \") || ua.includes(\"Trident/\") || ua.includes(\"Edge/\");\n}\nfunction isMIMEType(v) {\n    return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\nfunction isExt(v) {\n    return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n */ function acceptPropAsAcceptAttr(accept) {\n    if (isDefined(accept)) {\n        return Object.entries(accept).reduce((a, [mimeType, ext])=>[\n                ...a,\n                mimeType,\n                ...ext\n            ], [])// Silently discard invalid entries as pickerOptionsFromAccept warns about these\n        .filter((v)=>isMIMEType(v) || isExt(v)).join(\",\");\n    }\n    return undefined;\n}\nconst initialState = {\n    isFocused: false,\n    isFileDialogActive: false,\n    isDragActive: false,\n    isDragAccept: false,\n    isDragReject: false,\n    acceptedFiles: []\n};\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"focus\":\n            return {\n                ...state,\n                isFocused: true\n            };\n        case \"blur\":\n            return {\n                ...state,\n                isFocused: false\n            };\n        case \"openDialog\":\n            return {\n                ...initialState,\n                isFileDialogActive: true\n            };\n        case \"closeDialog\":\n            return {\n                ...state,\n                isFileDialogActive: false\n            };\n        case \"setDraggedFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"setFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"reset\":\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALLOWED_FILE_TYPES: () => (/* binding */ ALLOWED_FILE_TYPES),\n/* harmony export */   BadRequestError: () => (/* binding */ BadRequestError),\n/* harmony export */   FILESIZE_UNITS: () => (/* binding */ FILESIZE_UNITS),\n/* harmony export */   FetchContext: () => (/* binding */ FetchContext),\n/* harmony export */   FetchError: () => (/* binding */ FetchError),\n/* harmony export */   INTERNAL_DO_NOT_USE__fatalClientError: () => (/* binding */ INTERNAL_DO_NOT_USE__fatalClientError),\n/* harmony export */   INTERNAL_doFormatting: () => (/* binding */ INTERNAL_doFormatting),\n/* harmony export */   InvalidFileSizeError: () => (/* binding */ InvalidFileSizeError),\n/* harmony export */   InvalidFileTypeError: () => (/* binding */ InvalidFileTypeError),\n/* harmony export */   InvalidJsonError: () => (/* binding */ InvalidJsonError),\n/* harmony export */   InvalidRouteConfigError: () => (/* binding */ InvalidRouteConfigError),\n/* harmony export */   InvalidURLError: () => (/* binding */ InvalidURLError),\n/* harmony export */   RetryError: () => (/* binding */ RetryError),\n/* harmony export */   UnknownFileTypeError: () => (/* binding */ UnknownFileTypeError),\n/* harmony export */   UploadAbortedError: () => (/* binding */ UploadAbortedError),\n/* harmony export */   UploadPausedError: () => (/* binding */ UploadPausedError),\n/* harmony export */   UploadThingError: () => (/* binding */ UploadThingError),\n/* harmony export */   ValidACLs: () => (/* binding */ ValidACLs),\n/* harmony export */   ValidContentDispositions: () => (/* binding */ ValidContentDispositions),\n/* harmony export */   acceptPropAsAcceptAttr: () => (/* binding */ acceptPropAsAcceptAttr),\n/* harmony export */   allFilesAccepted: () => (/* binding */ allFilesAccepted),\n/* harmony export */   allowedContentTextLabelGenerator: () => (/* binding */ allowedContentTextLabelGenerator),\n/* harmony export */   bytesToFileSize: () => (/* binding */ bytesToFileSize),\n/* harmony export */   capitalizeStart: () => (/* binding */ capitalizeStart),\n/* harmony export */   contentFieldToContent: () => (/* binding */ contentFieldToContent),\n/* harmony export */   createIdentityProxy: () => (/* binding */ createIdentityProxy),\n/* harmony export */   defaultClassListMerger: () => (/* binding */ defaultClassListMerger),\n/* harmony export */   fetchEff: () => (/* binding */ fetchEff),\n/* harmony export */   fileSizeToBytes: () => (/* binding */ fileSizeToBytes),\n/* harmony export */   fillInputRouteConfig: () => (/* binding */ fillInputRouteConfig),\n/* harmony export */   filterDefinedObjectValues: () => (/* binding */ filterDefinedObjectValues),\n/* harmony export */   generateClientDropzoneAccept: () => (/* binding */ generateClientDropzoneAccept),\n/* harmony export */   generateKey: () => (/* binding */ generateKey),\n/* harmony export */   generateMimeTypes: () => (/* binding */ generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* binding */ generatePermittedFileTypes),\n/* harmony export */   generateSignedURL: () => (/* binding */ generateSignedURL),\n/* harmony export */   getDefaultRouteConfigValues: () => (/* binding */ getDefaultRouteConfigValues),\n/* harmony export */   getDefaultSizeForType: () => (/* binding */ getDefaultSizeForType),\n/* harmony export */   getErrorTypeFromStatusCode: () => (/* binding */ getErrorTypeFromStatusCode),\n/* harmony export */   getFilesFromClipboardEvent: () => (/* binding */ getFilesFromClipboardEvent),\n/* harmony export */   getFullApiUrl: () => (/* binding */ getFullApiUrl),\n/* harmony export */   getRequestUrl: () => (/* binding */ getRequestUrl),\n/* harmony export */   getStatusCodeFromError: () => (/* binding */ getStatusCodeFromError),\n/* harmony export */   initialState: () => (/* binding */ initialState),\n/* harmony export */   isEnterOrSpace: () => (/* binding */ isEnterOrSpace),\n/* harmony export */   isEventWithFiles: () => (/* binding */ isEventWithFiles),\n/* harmony export */   isFileAccepted: () => (/* binding */ isFileAccepted),\n/* harmony export */   isIeOrEdge: () => (/* binding */ isIeOrEdge),\n/* harmony export */   isPropagationStopped: () => (/* binding */ isPropagationStopped),\n/* harmony export */   isRouteArray: () => (/* binding */ isRouteArray),\n/* harmony export */   isValidQuantity: () => (/* binding */ isValidQuantity),\n/* harmony export */   isValidSize: () => (/* binding */ isValidSize),\n/* harmony export */   matchFileType: () => (/* binding */ matchFileType),\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   objectKeys: () => (/* binding */ objectKeys),\n/* harmony export */   parseResponseJson: () => (/* binding */ parseResponseJson),\n/* harmony export */   parseTimeToSeconds: () => (/* binding */ parseTimeToSeconds),\n/* harmony export */   reducer: () => (/* binding */ reducer),\n/* harmony export */   resolveMaybeUrlArg: () => (/* binding */ resolveMaybeUrlArg),\n/* harmony export */   roundProgress: () => (/* binding */ roundProgress),\n/* harmony export */   safeNumberReplacer: () => (/* binding */ safeNumberReplacer),\n/* harmony export */   safeParseJSON: () => (/* binding */ safeParseJSON),\n/* harmony export */   semverLite: () => (/* binding */ semverLite),\n/* harmony export */   signPayload: () => (/* binding */ signPayload),\n/* harmony export */   styleFieldToClassName: () => (/* binding */ styleFieldToClassName),\n/* harmony export */   styleFieldToCssObject: () => (/* binding */ styleFieldToCssObject),\n/* harmony export */   unwrap: () => (/* binding */ unwrap),\n/* harmony export */   verifyKey: () => (/* binding */ verifyKey),\n/* harmony export */   verifySignature: () => (/* binding */ verifySignature),\n/* harmony export */   warnIfInvalidPeerDependency: () => (/* binding */ warnIfInvalidPeerDependency)\n/* harmony export */ });\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @uploadthing/mime-types */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/dist/index.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Predicate */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var effect_Context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Context */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Context.js\");\n/* harmony import */ var _uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @uploadthing/mime-types/audio */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/audio/index.js\");\n/* harmony import */ var _uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @uploadthing/mime-types/image */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/image/index.js\");\n/* harmony import */ var _uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uploadthing/mime-types/text */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/text/index.js\");\n/* harmony import */ var _uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @uploadthing/mime-types/video */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/video/index.js\");\n/* harmony import */ var effect_Encoding__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! effect/Encoding */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Encoding.js\");\n/* harmony import */ var effect_Hash__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! effect/Hash */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Hash.js\");\n/* harmony import */ var effect_Redacted__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! effect/Redacted */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Redacted.js\");\n/* harmony import */ var sqids__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sqids */ \"(action-browser)/./node_modules/.pnpm/sqids@0.3.0/node_modules/sqids/esm/sqids.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst ValidContentDispositions = [\n    \"inline\",\n    \"attachment\"\n];\nconst ValidACLs = [\n    \"public-read\",\n    \"private\"\n];\n\nclass InvalidRouteConfigError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidRouteConfig\") {\n    constructor(type, field){\n        const reason = field ? `Expected route config to have a ${field} for key ${type} but none was found.` : `Encountered an invalid route config during backfilling. ${type} was not found.`;\n        super({\n            reason\n        });\n    }\n}\nclass UnknownFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UnknownFileType\") {\n    constructor(fileName){\n        const reason = `Could not determine type for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileTypeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileType\") {\n    constructor(fileType, fileName){\n        const reason = `File type ${fileType} not allowed for ${fileName}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidFileSizeError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidFileSize\") {\n    constructor(fileSize){\n        const reason = `Invalid file size: ${fileSize}`;\n        super({\n            reason\n        });\n    }\n}\nclass InvalidURLError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidURL\") {\n    constructor(attemptedUrl){\n        super({\n            reason: `Failed to parse '${attemptedUrl}' as a URL.`\n        });\n    }\n}\nclass RetryError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"RetryError\") {\n}\nclass FetchError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"FetchError\") {\n}\nclass InvalidJsonError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"InvalidJson\") {\n}\nclass BadRequestError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"BadRequestError\") {\n    getMessage() {\n        if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(this.json)) {\n            if (typeof this.json.message === \"string\") return this.json.message;\n        }\n        return this.message;\n    }\n}\nclass UploadPausedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\nclass UploadAbortedError extends /** #__PURE__ */ effect_Micro__WEBPACK_IMPORTED_MODULE_1__.TaggedError(\"UploadAborted\") {\n}\n\nfunction isRouteArray(routeConfig) {\n    return Array.isArray(routeConfig);\n}\nfunction getDefaultSizeForType(fileType) {\n    if (fileType === \"image\") return \"4MB\";\n    if (fileType === \"video\") return \"16MB\";\n    if (fileType === \"audio\") return \"8MB\";\n    if (fileType === \"blob\") return \"8MB\";\n    if (fileType === \"pdf\") return \"4MB\";\n    if (fileType === \"text\") return \"64KB\";\n    return \"4MB\";\n}\nfunction getDefaultRouteConfigValues(type) {\n    return {\n        maxFileSize: getDefaultSizeForType(type),\n        maxFileCount: 1,\n        minFileCount: 1,\n        contentDisposition: \"inline\"\n    };\n}\n/**\n * This function takes in the user's input and \"upscales\" it to a full config\n * Additionally, it replaces numbers with \"safe\" equivalents\n *\n * Example:\n * ```ts\n * [\"image\"] => { image: { maxFileSize: \"4MB\", limit: 1 } }\n * ```\n */ const fillInputRouteConfig = (routeConfig)=>{\n    // If array, apply defaults\n    if (isRouteArray(routeConfig)) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(routeConfig.reduce((acc, fileType)=>{\n            acc[fileType] = getDefaultRouteConfigValues(fileType);\n            return acc;\n        }, {}));\n    }\n    // Backfill defaults onto config\n    const newConfig = {};\n    for (const key of objectKeys(routeConfig)){\n        const value = routeConfig[key];\n        if (!value) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidRouteConfigError(key));\n        newConfig[key] = {\n            ...getDefaultRouteConfigValues(key),\n            ...value\n        };\n    }\n    // we know that the config is valid, so we can stringify it and parse it back\n    // this allows us to replace numbers with \"safe\" equivalents\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(JSON.parse(JSON.stringify(newConfig, safeNumberReplacer)));\n};\n/**\n * Match the file's type for a given allow list e.g. `image/png => image`\n * Prefers the file's type, then falls back to a extension-based lookup\n */ const matchFileType = (file, allowedTypes)=>{\n    // Type might be \"\" if the browser doesn't recognize the mime type\n    const mimeType = file.type || (0,_uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_3__.lookup)(file.name);\n    if (!mimeType) {\n        if (allowedTypes.includes(\"blob\")) return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new UnknownFileTypeError(file.name));\n    }\n    // If the user has specified a specific mime type, use that\n    if (allowedTypes.some((type)=>type.includes(\"/\"))) {\n        if (allowedTypes.includes(mimeType)) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(mimeType);\n        }\n    }\n    // Otherwise, we have a \"magic\" type eg. \"image\" or \"video\"\n    const type = mimeType.toLowerCase() === \"application/pdf\" ? \"pdf\" : mimeType.split(\"/\")[0];\n    if (!allowedTypes.includes(type)) {\n        // Blob is a catch-all for any file type not explicitly supported\n        if (allowedTypes.includes(\"blob\")) {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(\"blob\");\n        } else {\n            return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileTypeError(type, file.name));\n        }\n    }\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(type);\n};\nconst FILESIZE_UNITS = [\n    \"B\",\n    \"KB\",\n    \"MB\",\n    \"GB\",\n    \"TB\"\n];\nconst fileSizeToBytes = (fileSize)=>{\n    const regex = new RegExp(`^(\\\\d+)(\\\\.\\\\d+)?\\\\s*(${FILESIZE_UNITS.join(\"|\")})$`, \"i\");\n    // make sure the string is in the format of 123KB\n    const match = fileSize.match(regex);\n    if (!match?.[1] || !match[3]) {\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fail(new InvalidFileSizeError(fileSize));\n    }\n    const sizeValue = parseFloat(match[1]);\n    const sizeUnit = match[3].toUpperCase();\n    const bytes = sizeValue * Math.pow(1024, FILESIZE_UNITS.indexOf(sizeUnit));\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.succeed(Math.floor(bytes));\n};\nconst bytesToFileSize = (bytes)=>{\n    if (bytes === 0 || bytes === -1) {\n        return \"0B\";\n    }\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return `${(bytes / Math.pow(1024, i)).toFixed(2)}${FILESIZE_UNITS[i]}`;\n};\nasync function safeParseJSON(input) {\n    const text = await input.text();\n    try {\n        return JSON.parse(text);\n    } catch (err) {\n        // eslint-disable-next-line no-console\n        console.error(`Error parsing JSON, got '${text}'`, err);\n        return new Error(`Error parsing JSON, got '${text}'`);\n    }\n}\n/** typesafe Object.keys */ function objectKeys(obj) {\n    return Object.keys(obj);\n}\nfunction filterDefinedObjectValues(obj) {\n    return Object.fromEntries(Object.entries(obj).filter((pair)=>pair[1] != null));\n}\nfunction semverLite(required, toCheck) {\n    // Pull out numbers from strings like `6.0.0`, `^6.4`, `~6.4.0`\n    const semverRegex = /(\\d+)\\.?(\\d+)?\\.?(\\d+)?/;\n    const requiredMatch = semverRegex.exec(required);\n    if (!requiredMatch?.[0]) {\n        throw new Error(`Invalid semver requirement: ${required}`);\n    }\n    const toCheckMatch = semverRegex.exec(toCheck);\n    if (!toCheckMatch?.[0]) {\n        throw new Error(`Invalid semver to check: ${toCheck}`);\n    }\n    const [_1, rMajor, rMinor, rPatch] = requiredMatch;\n    const [_2, cMajor, cMinor, cPatch] = toCheckMatch;\n    if (required.startsWith(\"^\")) {\n        // Major must be equal, minor must be greater or equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor && cMinor && rMinor > cMinor) return false;\n        return true;\n    }\n    if (required.startsWith(\"~\")) {\n        // Major must be equal, minor must be equal\n        if (rMajor !== cMajor) return false;\n        if (rMinor !== cMinor) return false;\n        return true;\n    }\n    // Exact match\n    return rMajor === cMajor && rMinor === cMinor && rPatch === cPatch;\n}\nfunction warnIfInvalidPeerDependency(pkg, required, toCheck) {\n    if (!semverLite(required, toCheck)) {\n        // eslint-disable-next-line no-console\n        console.warn(`!!!WARNING::: ${pkg} requires \"uploadthing@${required}\", but version \"${toCheck}\" is installed`);\n    }\n}\nconst getRequestUrl = (req)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const host = req.headers.get(\"x-forwarded-host\") ?? req.headers.get(\"host\");\n        const proto = req.headers.get(\"x-forwarded-proto\") ?? \"https\";\n        const protocol = proto.endsWith(\":\") ? proto : `${proto}:`;\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(req.url, `${protocol}//${host}`),\n            catch: ()=>new InvalidURLError(req.url)\n        });\n        url.search = \"\";\n        return url;\n    });\nconst getFullApiUrl = (maybeUrl)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const base = (()=>{\n            if (typeof window !== \"undefined\") return window.location.origin;\n            if (process.env.VERCEL_URL) return `https://${process.env.VERCEL_URL}`;\n            return \"http://localhost:3000\";\n        })();\n        const url = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__[\"try\"]({\n            try: ()=>new URL(maybeUrl ?? \"/api/uploadthing\", base),\n            catch: ()=>new InvalidURLError(maybeUrl ?? \"/api/uploadthing\")\n        });\n        if (url.pathname === \"/\") {\n            url.pathname = \"/api/uploadthing\";\n        }\n        return url;\n    });\n/*\n * Returns a full URL to the dev's uploadthing endpoint\n * Can take either an origin, or a pathname, or a full URL\n * and will return the \"closest\" url matching the default\n * `<VERCEL_URL || localhost>/api/uploadthing`\n */ const resolveMaybeUrlArg = (maybeUrl)=>{\n    return maybeUrl instanceof URL ? maybeUrl : effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync(getFullApiUrl(maybeUrl));\n};\nfunction parseTimeToSeconds(time) {\n    if (typeof time === \"number\") return time;\n    const match = time.split(/(\\d+)/).filter(Boolean);\n    const num = Number(match[0]);\n    const unit = (match[1] ?? \"s\").trim().slice(0, 1);\n    const multiplier = {\n        s: 1,\n        m: 60,\n        h: 3600,\n        d: 86400\n    }[unit];\n    return num * multiplier;\n}\n/**\n * Replacer for JSON.stringify that will replace numbers that cannot be\n * serialized to JSON with \"reasonable equivalents\".\n *\n * Infinity and -Infinity are replaced by MAX_SAFE_INTEGER and MIN_SAFE_INTEGER\n * NaN is replaced by 0\n *\n */ const safeNumberReplacer = (_, value)=>{\n    if (typeof value !== \"number\") return value;\n    if (Number.isSafeInteger(value) || value <= Number.MAX_SAFE_INTEGER && value >= Number.MIN_SAFE_INTEGER) {\n        return value;\n    }\n    if (value === Infinity) return Number.MAX_SAFE_INTEGER;\n    if (value === -Infinity) return Number.MIN_SAFE_INTEGER;\n    if (Number.isNaN(value)) return 0;\n};\nfunction noop() {\n// noop\n}\nfunction createIdentityProxy() {\n    return new Proxy(noop, {\n        get: (_, prop)=>prop\n    });\n}\nfunction unwrap(x, ...args) {\n    return typeof x === \"function\" ? x(...args) : x;\n}\n\nconst ALLOWED_FILE_TYPES = [\n    \"image\",\n    \"video\",\n    \"audio\",\n    \"pdf\",\n    \"text\",\n    \"blob\"\n];\n\nconst ERROR_CODES = {\n    // Generic\n    BAD_REQUEST: 400,\n    NOT_FOUND: 404,\n    FORBIDDEN: 403,\n    INTERNAL_SERVER_ERROR: 500,\n    INTERNAL_CLIENT_ERROR: 500,\n    // S3 specific\n    TOO_LARGE: 413,\n    TOO_SMALL: 400,\n    TOO_MANY_FILES: 400,\n    KEY_TOO_LONG: 400,\n    // UploadThing specific\n    URL_GENERATION_FAILED: 500,\n    UPLOAD_FAILED: 500,\n    MISSING_ENV: 500,\n    INVALID_SERVER_CONFIG: 500,\n    FILE_LIMIT_EXCEEDED: 500\n};\nfunction messageFromUnknown(cause, fallback) {\n    if (typeof cause === \"string\") {\n        return cause;\n    }\n    if (cause instanceof Error) {\n        return cause.message;\n    }\n    if (cause && typeof cause === \"object\" && \"message\" in cause && typeof cause.message === \"string\") {\n        return cause.message;\n    }\n    return fallback ?? \"An unknown error occurred\";\n}\nclass UploadThingError extends effect_Micro__WEBPACK_IMPORTED_MODULE_1__.Error {\n    constructor(initOpts){\n        const opts = typeof initOpts === \"string\" ? {\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: initOpts\n        } : initOpts;\n        const message = opts.message ?? messageFromUnknown(opts.cause, opts.code);\n        super({\n            message\n        }), this._tag = \"UploadThingError\", this.name = \"UploadThingError\";\n        this.code = opts.code;\n        this.data = opts.data;\n        if (opts.cause instanceof Error) {\n            this.cause = opts.cause;\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isRecord(opts.cause) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isNumber(opts.cause.status) && effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause.statusText)) {\n            this.cause = new Error(`Response ${opts.cause.status} ${opts.cause.statusText}`);\n        } else if (effect_Predicate__WEBPACK_IMPORTED_MODULE_2__.isString(opts.cause)) {\n            this.cause = new Error(opts.cause);\n        } else {\n            this.cause = opts.cause;\n        }\n    }\n    static toObject(error) {\n        return {\n            code: error.code,\n            message: error.message,\n            data: error.data\n        };\n    }\n    static serialize(error) {\n        return JSON.stringify(UploadThingError.toObject(error));\n    }\n}\nfunction getErrorTypeFromStatusCode(statusCode) {\n    for (const [code, status] of Object.entries(ERROR_CODES)){\n        if (status === statusCode) {\n            return code;\n        }\n    }\n    return \"INTERNAL_SERVER_ERROR\";\n}\nfunction getStatusCodeFromError(error) {\n    return ERROR_CODES[error.code];\n}\nconst INTERNAL_DO_NOT_USE__fatalClientError = (e)=>new UploadThingError({\n        code: \"INTERNAL_CLIENT_ERROR\",\n        message: \"Something went wrong. Please report this to UploadThing.\",\n        cause: e\n    });\n\nclass FetchContext extends /** #__PURE__ */ effect_Context__WEBPACK_IMPORTED_MODULE_4__.Tag(\"uploadthing/Fetch\")() {\n}\n// Temporary Effect wrappers below.\n// Only for use in the browser.\n// On the server, use `@effect/platform.HttpClient` instead.\nconst fetchEff = (input, init)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.service(FetchContext), (fetch)=>{\n        const headers = new Headers(init?.headers ?? []);\n        const reqInfo = {\n            url: input.toString(),\n            method: init?.method,\n            body: init?.body,\n            headers: Object.fromEntries(headers)\n        };\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: (signal)=>fetch(input, {\n                    ...init,\n                    headers,\n                    signal\n                }),\n            catch: (error)=>new FetchError({\n                    error: error instanceof Error ? {\n                        ...error,\n                        name: error.name,\n                        message: error.message,\n                        stack: error.stack\n                    } : error,\n                    input: reqInfo\n                })\n        }).pipe(// eslint-disable-next-line no-console\n        effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tapError((e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>console.error(e.input))), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((res)=>Object.assign(res, {\n                requestUrl: reqInfo.url\n            })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"fetch\"));\n    });\nconst parseResponseJson = (res)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n        try: async ()=>{\n            const json = await res.json();\n            return {\n                json,\n                ok: res.ok,\n                status: res.status\n            };\n        },\n        catch: (error)=>new InvalidJsonError({\n                error,\n                input: res.requestUrl\n            })\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.filterOrFail(({ ok })=>ok, ({ json, status })=>new BadRequestError({\n            status,\n            message: `Request to ${res.requestUrl} failed with status ${status}`,\n            json\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(({ json })=>json), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"parseJson\"));\n\nconst roundProgress = (progress, granularity)=>{\n    if (granularity === \"all\") return progress;\n    if (granularity === \"fine\") return Math.round(progress);\n    return Math.floor(progress / 10) * 10;\n};\nconst generateMimeTypes = (typesOrRouteConfig)=>{\n    const fileTypes = Array.isArray(typesOrRouteConfig) ? typesOrRouteConfig : objectKeys(typesOrRouteConfig);\n    if (fileTypes.includes(\"blob\")) return [];\n    return fileTypes.map((type)=>{\n        if (type === \"pdf\") return \"application/pdf\";\n        if (type.includes(\"/\")) return type;\n        // Add wildcard to support all subtypes, e.g. image => \"image/*\"\n        // But some browsers/OSes don't support it, so we'll also dump all the mime types\n        // we know that starts with the type, e.g. image => \"image/png, image/jpeg, ...\"\n        if (type === \"audio\") return [\n            \"audio/*\",\n            ...objectKeys(_uploadthing_mime_types_audio__WEBPACK_IMPORTED_MODULE_5__.audio)\n        ].join(\", \");\n        if (type === \"image\") return [\n            \"image/*\",\n            ...objectKeys(_uploadthing_mime_types_image__WEBPACK_IMPORTED_MODULE_6__.image)\n        ].join(\", \");\n        if (type === \"text\") return [\n            \"text/*\",\n            ...objectKeys(_uploadthing_mime_types_text__WEBPACK_IMPORTED_MODULE_7__.text)\n        ].join(\", \");\n        if (type === \"video\") return [\n            \"video/*\",\n            ...objectKeys(_uploadthing_mime_types_video__WEBPACK_IMPORTED_MODULE_8__.video)\n        ].join(\", \");\n        return `${type}/*`;\n    });\n};\nconst generateClientDropzoneAccept = (fileTypes)=>{\n    const mimeTypes = generateMimeTypes(fileTypes);\n    return Object.fromEntries(mimeTypes.map((type)=>[\n            type,\n            []\n        ]));\n};\nfunction getFilesFromClipboardEvent(event) {\n    const dataTransferItems = event.clipboardData?.items;\n    if (!dataTransferItems) return;\n    const files = Array.from(dataTransferItems).reduce((acc, curr)=>{\n        const f = curr.getAsFile();\n        return f ? [\n            ...acc,\n            f\n        ] : acc;\n    }, []);\n    return files;\n}\n/**\n * Shared helpers for our premade components that's reusable by multiple frameworks\n */ const generatePermittedFileTypes = (config)=>{\n    const fileTypes = config ? objectKeys(config) : [];\n    const maxFileCount = config ? Object.values(config).map((v)=>v.maxFileCount) : [];\n    return {\n        fileTypes,\n        multiple: maxFileCount.some((v)=>v && v > 1)\n    };\n};\nconst capitalizeStart = (str)=>{\n    return str.charAt(0).toUpperCase() + str.slice(1);\n};\nconst INTERNAL_doFormatting = (config)=>{\n    if (!config) return \"\";\n    const allowedTypes = objectKeys(config);\n    const formattedTypes = allowedTypes.map((f)=>f === \"blob\" ? \"file\" : f);\n    // Format multi-type uploader label as \"Supports videos, images and files\";\n    if (formattedTypes.length > 1) {\n        const lastType = formattedTypes.pop();\n        return `${formattedTypes.join(\"s, \")} and ${lastType}s`;\n    }\n    // Single type uploader label\n    const key = allowedTypes[0];\n    const formattedKey = formattedTypes[0];\n    if (!key || !formattedKey) return \"\";\n    const { maxFileSize, maxFileCount, minFileCount } = config[key];\n    if (maxFileCount && maxFileCount > 1) {\n        if (minFileCount > 1) {\n            return `${minFileCount} - ${maxFileCount} ${formattedKey}s up to ${maxFileSize}`;\n        } else {\n            return `${formattedKey}s up to ${maxFileSize}, max ${maxFileCount}`;\n        }\n    } else {\n        return `${formattedKey} (${maxFileSize})`;\n    }\n};\nconst allowedContentTextLabelGenerator = (config)=>{\n    return capitalizeStart(INTERNAL_doFormatting(config));\n};\nconst styleFieldToClassName = (styleField, args)=>{\n    if (typeof styleField === \"string\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"string\") return result;\n    }\n    return \"\";\n};\nconst styleFieldToCssObject = (styleField, args)=>{\n    if (typeof styleField === \"object\") return styleField;\n    if (typeof styleField === \"function\") {\n        const result = styleField(args);\n        if (typeof result === \"object\") return result;\n    }\n    return {};\n};\nconst contentFieldToContent = (contentField, arg)=>{\n    if (!contentField) return null;\n    if (typeof contentField !== \"function\") return contentField;\n    if (typeof contentField === \"function\") {\n        const result = contentField(arg);\n        return result;\n    }\n};\nconst defaultClassListMerger = (...classes)=>{\n    return classes.filter(Boolean).join(\" \");\n};\n\nconst signaturePrefix = \"hmac-sha256=\";\nconst algorithm = {\n    name: \"HMAC\",\n    hash: \"SHA-256\"\n};\nconst encoder = new TextEncoder();\nfunction shuffle(str, seed) {\n    const chars = str.split(\"\");\n    const seedNum = effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(seed);\n    let temp;\n    let j;\n    for(let i = 0; i < chars.length; i++){\n        j = (seedNum % (i + 1) + i) % chars.length;\n        temp = chars[i];\n        chars[i] = chars[j];\n        chars[j] = temp;\n    }\n    return chars.join(\"\");\n}\nconst signPayload = (payload, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.importKey(\"raw\", encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret)), algorithm, false, [\n                    \"sign\"\n                ]),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid signing secret\",\n                    cause: e\n                })\n        });\n        const signature = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.tryPromise({\n            try: ()=>crypto.subtle.sign(algorithm, signingKey, encoder.encode(payload)),\n            catch: (e)=>new UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    cause: e\n                })\n        }), (arrayBuffer)=>effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.encodeHex(new Uint8Array(arrayBuffer)));\n        return `${signaturePrefix}${signature}`;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"signPayload\"));\nconst verifySignature = (payload, signature, secret)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const sig = signature?.slice(signaturePrefix.length);\n        if (!sig) return false;\n        const secretBytes = encoder.encode(effect_Redacted__WEBPACK_IMPORTED_MODULE_10__.value(secret));\n        const signingKey = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.importKey(\"raw\", secretBytes, algorithm, false, [\n                \"verify\"\n            ]));\n        const sigBytes = yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.fromEither(effect_Encoding__WEBPACK_IMPORTED_MODULE_11__.decodeHex(sig));\n        const payloadBytes = encoder.encode(payload);\n        return yield* effect_Micro__WEBPACK_IMPORTED_MODULE_1__.promise(()=>crypto.subtle.verify(algorithm, signingKey, sigBytes, payloadBytes));\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifySignature\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateKey = (file, appId, getHashParts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        // Get the parts of which we should hash to constuct the key\n        // This allows the user to customize the hashing algorithm\n        // If they for example want to generate the same key for the\n        // same file whenever it was uploaded\n        const hashParts = JSON.stringify(getHashParts?.(file) ?? [\n            file.name,\n            file.size,\n            file.type,\n            file.lastModified,\n            Date.now()\n        ]);\n        // Hash and Encode the parts and appId as sqids\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const encodedFileSeed = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 36\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(hashParts))\n        ]);\n        const encodedAppId = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        // Concatenate them\n        return encodedAppId + encodedFileSeed;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateKey\"));\n// Verify that the key was generated with the same appId\nconst verifyKey = (key, appId)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.sync(()=>{\n        const alphabet = shuffle(sqids__WEBPACK_IMPORTED_MODULE_0__.defaultOptions.alphabet, appId);\n        const expectedPrefix = new sqids__WEBPACK_IMPORTED_MODULE_0__[\"default\"]({\n            alphabet,\n            minLength: 12\n        }).encode([\n            Math.abs(effect_Hash__WEBPACK_IMPORTED_MODULE_9__.string(appId))\n        ]);\n        return key.startsWith(expectedPrefix);\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"verifyKey\"), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false));\nconst generateSignedURL = (url, secretKey, opts)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n        const parsedURL = new URL(url);\n        const ttl = opts.ttlInSeconds ? parseTimeToSeconds(opts.ttlInSeconds) : 60 * 60;\n        const expirationTime = Date.now() + ttl * 1000;\n        parsedURL.searchParams.append(\"expires\", expirationTime.toString());\n        if (opts.data) {\n            Object.entries(opts.data).forEach(([key, value])=>{\n                if (value == null) return;\n                const encoded = encodeURIComponent(value);\n                parsedURL.searchParams.append(key, encoded);\n            });\n        }\n        const signature = yield* signPayload(parsedURL.toString(), secretKey);\n        parsedURL.searchParams.append(\"signature\", signature);\n        return parsedURL.href;\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.withTrace(\"generateSignedURL\"));\n\n/**\n * Copyright (c) (MIT License) 2015 Andrey Okonetchnikov\n * https://github.com/react-dropzone/attr-accept/blob/master/src/index.js\n */ function accepts(file, acceptedFiles) {\n    if (acceptedFiles) {\n        const acceptedFilesArray = Array.isArray(acceptedFiles) ? acceptedFiles : acceptedFiles.split(\",\");\n        const fileName = file.name;\n        const mimeType = file.type.toLowerCase();\n        const baseMimeType = mimeType.replace(/\\/.*$/, \"\");\n        return acceptedFilesArray.some((type)=>{\n            const validType = type.trim().toLowerCase();\n            if (validType.startsWith(\".\")) {\n                return fileName.toLowerCase().endsWith(validType);\n            } else if (validType.endsWith(\"/*\")) {\n                // This is something like a image/* mime type\n                return baseMimeType === validType.replace(/\\/.*$/, \"\");\n            }\n            return mimeType === validType;\n        });\n    }\n    return true;\n}\nconst isPropagationStopped = (event)=>{\n    if (typeof event.isPropagationStopped === \"function\") {\n        return event.isPropagationStopped();\n    }\n    if (typeof event.cancelBubble !== \"undefined\") {\n        return event.cancelBubble;\n    }\n    return false;\n};\n// Firefox versions prior to 53 return a bogus MIME type for every file drag, so dragovers with\n// that MIME type will always be accepted\nfunction isFileAccepted(file, accept) {\n    return file.type === \"application/x-moz-file\" || accepts(file, accept);\n}\nfunction isEnterOrSpace(event) {\n    return \"key\" in event && (event.key === \" \" || event.key === \"Enter\") || \"keyCode\" in event && (event.keyCode === 32 || event.keyCode === 13);\n}\nconst isDefined = (v)=>v != null;\nfunction isValidSize(file, minSize, maxSize) {\n    if (!isDefined(file.size)) return true;\n    if (isDefined(minSize) && isDefined(maxSize)) {\n        return file.size >= minSize && file.size <= maxSize;\n    }\n    if (isDefined(minSize) && file.size < minSize) return false;\n    if (isDefined(maxSize) && file.size > maxSize) return false;\n    return true;\n}\nfunction isValidQuantity(files, multiple, maxFiles) {\n    if (!multiple && files.length > 1) return false;\n    if (multiple && maxFiles >= 1 && files.length > maxFiles) return false;\n    return true;\n}\nfunction allFilesAccepted({ files, accept, minSize, maxSize, multiple, maxFiles }) {\n    if (!isValidQuantity(files, multiple, maxFiles)) return false;\n    return files.every((file)=>isFileAccepted(file, accept) && isValidSize(file, minSize, maxSize));\n}\nfunction isEventWithFiles(event) {\n    if (!(\"dataTransfer\" in event && event.dataTransfer !== null)) {\n        return !!event.target && \"files\" in event.target && !!event.target.files;\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/API/DataTransfer/types\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTML_Drag_and_Drop_API/Recommended_drag_types#file\n    return Array.prototype.some.call(// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n    event.dataTransfer?.types, (type)=>type === \"Files\" || type === \"application/x-moz-file\");\n}\nfunction isIeOrEdge(ua = window.navigator.userAgent) {\n    return ua.includes(\"MSIE \") || ua.includes(\"Trident/\") || ua.includes(\"Edge/\");\n}\nfunction isMIMEType(v) {\n    return v === \"audio/*\" || v === \"video/*\" || v === \"image/*\" || v === \"text/*\" || /\\w+\\/[-+.\\w]+/g.test(v);\n}\nfunction isExt(v) {\n    return /^.*\\.[\\w]+$/.test(v);\n}\n/**\n * Convert the `{accept}` dropzone prop to an array of MIME types/extensions.\n */ function acceptPropAsAcceptAttr(accept) {\n    if (isDefined(accept)) {\n        return Object.entries(accept).reduce((a, [mimeType, ext])=>[\n                ...a,\n                mimeType,\n                ...ext\n            ], [])// Silently discard invalid entries as pickerOptionsFromAccept warns about these\n        .filter((v)=>isMIMEType(v) || isExt(v)).join(\",\");\n    }\n    return undefined;\n}\nconst initialState = {\n    isFocused: false,\n    isFileDialogActive: false,\n    isDragActive: false,\n    isDragAccept: false,\n    isDragReject: false,\n    acceptedFiles: []\n};\nfunction reducer(state, action) {\n    switch(action.type){\n        case \"focus\":\n            return {\n                ...state,\n                isFocused: true\n            };\n        case \"blur\":\n            return {\n                ...state,\n                isFocused: false\n            };\n        case \"openDialog\":\n            return {\n                ...initialState,\n                isFileDialogActive: true\n            };\n        case \"closeDialog\":\n            return {\n                ...state,\n                isFileDialogActive: false\n            };\n        case \"setDraggedFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"setFiles\":\n            return {\n                ...state,\n                ...action.payload\n            };\n        case \"reset\":\n            return initialState;\n        default:\n            return state;\n    }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\n");

/***/ })

};
;