"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+slate-react@41.0.5_7f240204dacc266af17b00aab8336def";
exports.ids = ["vendor-chunks/@udecode+slate-react@41.0.5_7f240204dacc266af17b00aab8336def"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+slate-react@41.0.5_7f240204dacc266af17b00aab8336def/node_modules/@udecode/slate-react/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+slate-react@41.0.5_7f240204dacc266af17b00aab8336def/node_modules/@udecode/slate-react/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   blurEditor: () => (/* binding */ blurEditor),\n/* harmony export */   deselectEditor: () => (/* binding */ deselectEditor),\n/* harmony export */   findEditorDocumentOrShadowRoot: () => (/* binding */ findEditorDocumentOrShadowRoot),\n/* harmony export */   findEventRange: () => (/* binding */ findEventRange),\n/* harmony export */   findNodeKey: () => (/* binding */ findNodeKey),\n/* harmony export */   findPath: () => (/* binding */ findPath),\n/* harmony export */   focusEditor: () => (/* binding */ focusEditor),\n/* harmony export */   focusEditorEdge: () => (/* binding */ focusEditorEdge),\n/* harmony export */   getEditorWindow: () => (/* binding */ getEditorWindow),\n/* harmony export */   hasEditorDOMNode: () => (/* binding */ hasEditorDOMNode),\n/* harmony export */   hasEditorEditableTarget: () => (/* binding */ hasEditorEditableTarget),\n/* harmony export */   hasEditorSelectableTarget: () => (/* binding */ hasEditorSelectableTarget),\n/* harmony export */   hasEditorTarget: () => (/* binding */ hasEditorTarget),\n/* harmony export */   insertData: () => (/* binding */ insertData),\n/* harmony export */   isComposing: () => (/* binding */ isComposing),\n/* harmony export */   isEditorFocused: () => (/* binding */ isEditorFocused),\n/* harmony export */   isEditorReadOnly: () => (/* binding */ isEditorReadOnly),\n/* harmony export */   isTargetInsideNonReadonlyVoid: () => (/* binding */ isTargetInsideNonReadonlyVoid),\n/* harmony export */   setFragmentData: () => (/* binding */ setFragmentData),\n/* harmony export */   setNode: () => (/* binding */ setNode),\n/* harmony export */   toDOMNode: () => (/* binding */ toDOMNode),\n/* harmony export */   toDOMPoint: () => (/* binding */ toDOMPoint),\n/* harmony export */   toDOMRange: () => (/* binding */ toDOMRange),\n/* harmony export */   toSlateNode: () => (/* binding */ toSlateNode),\n/* harmony export */   toSlatePoint: () => (/* binding */ toSlatePoint),\n/* harmony export */   toSlateRange: () => (/* binding */ toSlateRange)\n/* harmony export */ });\n/* harmony import */ var slate_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! slate-react */ \"(ssr)/./node_modules/.pnpm/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4/node_modules/slate-react/dist/index.es.js\");\n/* harmony import */ var _udecode_slate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @udecode/slate */ \"(ssr)/./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\n\n// src/react-editor/blurEditor.ts\n\nvar blurEditor = (editor) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.blur(editor);\n\n// src/react-editor/deselectEditor.ts\n\nvar deselectEditor = (editor) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.deselect(editor);\n\n// src/react-editor/findEditorDocumentOrShadowRoot.ts\n\nvar findEditorDocumentOrShadowRoot = (editor) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.findDocumentOrShadowRoot(editor);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/findEventRange.ts\n\nvar findEventRange = (editor, event) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.findEventRange(editor, event);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/findNodeKey.ts\n\nvar findNodeKey = (editor, node) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.findKey(editor, node);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/findPath.ts\n\nvar findPath = (editor, node) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.findPath(editor, node);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/focusEditor.ts\n\n\nvar focusEditor = (editor, target) => {\n  if (target) {\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n      (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.deselect)(editor);\n      (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.select)(editor, target);\n    });\n  }\n  slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.focus(editor);\n};\n\n// src/react-editor/getEditorWindow.ts\n\nvar getEditorWindow = (editor) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.getWindow(editor);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/hasEditorDOMNode.ts\n\nvar hasEditorDOMNode = (editor, target, options) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.hasDOMNode(editor, target, options);\n  } catch (error) {\n  }\n  return false;\n};\n\n// src/react-editor/hasEditorEditableTarget.ts\n\nvar hasEditorEditableTarget = (editor, target) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.hasEditableTarget(editor, target);\n  } catch (error) {\n  }\n  return false;\n};\n\n// src/react-editor/hasEditorSelectableTarget.ts\n\nvar hasEditorSelectableTarget = (editor, target) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.hasSelectableTarget(editor, target);\n  } catch (error) {\n  }\n  return false;\n};\n\n// src/react-editor/hasEditorTarget.ts\n\nvar hasEditorTarget = (editor, target) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.hasTarget(editor, target);\n  } catch (error) {\n  }\n  return false;\n};\n\n// src/react-editor/insertData.ts\n\nvar insertData = (editor, data) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.insertData(editor, data);\n\n// src/react-editor/isComposing.ts\n\nvar isComposing = (editor) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.isComposing(editor);\n\n// src/react-editor/isEditorFocused.ts\n\nvar isEditorFocused = (editor) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.isFocused(editor);\n\n// src/react-editor/isEditorReadOnly.ts\n\nvar isEditorReadOnly = (editor) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.isReadOnly(editor);\n\n// src/react-editor/isTargetInsideNonReadonlyVoidEditor.ts\n\nvar isTargetInsideNonReadonlyVoid = (editor, target) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.isTargetInsideNonReadonlyVoid(editor, target);\n  } catch (error) {\n  }\n  return false;\n};\n\n// src/react-editor/setFragmentData.ts\n\nvar setFragmentData = (editor, data) => slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.setFragmentData(editor, data);\n\n// src/react-editor/toDOMNode.ts\n\nvar toDOMNode = (editor, node) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toDOMNode(editor, node);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/toDOMPoint.ts\n\nvar toDOMPoint = (editor, point) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toDOMPoint(editor, point);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/toDOMRange.ts\n\nvar toDOMRange = (editor, range) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toDOMRange(editor, range);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/toSlateNode.ts\n\nvar toSlateNode = (editor, domNode) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toSlateNode(editor, domNode);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/toSlatePoint.ts\n\nvar toSlatePoint = (editor, domPoint, options) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toSlatePoint(editor, domPoint, options);\n  } catch (error) {\n  }\n};\n\n// src/react-editor/toSlateRange.ts\n\nvar toSlateRange = (editor, domRange, options) => {\n  try {\n    return slate_react__WEBPACK_IMPORTED_MODULE_0__.ReactEditor.toSlateRange(editor, domRange, options);\n  } catch (error) {\n  }\n};\n\n// src/utils/focusEditorEdge.ts\n\nvar focusEditorEdge = (editor, {\n  edge = \"start\"\n} = {}) => {\n  const target = edge === \"start\" ? (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getStartPoint)(editor, []) : (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEndPoint)(editor, []);\n  focusEditor(editor, target);\n};\n\n// src/utils/setNode.ts\nvar setNode = (editor, node, props, options) => {\n  const path = findPath(editor, node);\n  if (!path) return;\n  editor.setNodes(props, __spreadProps(__spreadValues({}, options), {\n    at: path\n  }));\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+slate-react@41.0.5_7f240204dacc266af17b00aab8336def/node_modules/@udecode/slate-react/dist/index.mjs\n");

/***/ })

};
;