{"name": "react-icons-picker", "version": "1.0.9", "description": "", "main": "dist/index.js", "module": "dist/index.es.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "build-lib": "rollup -c"}, "author": "Nyambayar Saintogtokh", "license": "MIT", "devDependencies": {"@babel/core": "^7.18.9", "@rollup/plugin-node-resolve": "^13.3.0", "@storybook/addon-actions": "^6.5.9", "@storybook/addon-essentials": "^6.5.9", "@storybook/addon-interactions": "^6.5.9", "@storybook/addon-links": "^6.5.9", "@storybook/builder-webpack4": "^6.5.9", "@storybook/manager-webpack4": "^6.5.9", "@storybook/react": "^6.5.9", "@storybook/testing-library": "0.0.13", "babel-loader": "^8.2.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.4.0", "rollup": "^2.77.2", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-terser": "^7.0.2"}, "peerDependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.4.0"}}