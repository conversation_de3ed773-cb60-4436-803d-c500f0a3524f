"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encodeurl@2.0.0";
exports.ids = ["vendor-chunks/encodeurl@2.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js ***!
  \****************************************************************************/
/***/ ((module) => {

eval("/*!\n * encodeurl\n * Copyright(c) 2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = encodeUrl\n\n/**\n * RegExp to match non-URL code points, *after* encoding (i.e. not including \"%\")\n * and including invalid escape sequences.\n * @private\n */\n\nvar ENCODE_CHARS_REGEXP = /(?:[^\\x21\\x23-\\x3B\\x3D\\x3F-\\x5F\\x61-\\x7A\\x7C\\x7E]|%(?:[^0-9A-Fa-f]|[0-9A-Fa-f][^0-9A-Fa-f]|$))+/g\n\n/**\n * RegExp to match unmatched surrogate pair.\n * @private\n */\n\nvar UNMATCHED_SURROGATE_PAIR_REGEXP = /(^|[^\\uD800-\\uDBFF])[\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF]([^\\uDC00-\\uDFFF]|$)/g\n\n/**\n * String to replace unmatched surrogate pair with.\n * @private\n */\n\nvar UNMATCHED_SURROGATE_PAIR_REPLACE = '$1\\uFFFD$2'\n\n/**\n * Encode a URL to a percent-encoded form, excluding already-encoded sequences.\n *\n * This function will take an already-encoded URL and encode all the non-URL\n * code points. This function will not encode the \"%\" character unless it is\n * not part of a valid sequence (`%20` will be left as-is, but `%foo` will\n * be encoded as `%25foo`).\n *\n * This encode is meant to be \"safe\" and does not throw errors. It will try as\n * hard as it can to properly encode the given URL, including replacing any raw,\n * unpaired surrogate pairs with the Unicode replacement character prior to\n * encoding.\n *\n * @param {string} url\n * @return {string}\n * @public\n */\n\nfunction encodeUrl (url) {\n  return String(url)\n    .replace(UNMATCHED_SURROGATE_PAIR_REGEXP, UNMATCHED_SURROGATE_PAIR_REPLACE)\n    .replace(ENCODE_CHARS_REGEXP, encodeURI)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js\n");

/***/ })

};
;