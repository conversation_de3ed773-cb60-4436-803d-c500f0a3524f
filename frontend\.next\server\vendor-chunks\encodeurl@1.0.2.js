"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/encodeurl@1.0.2";
exports.ids = ["vendor-chunks/encodeurl@1.0.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/encodeurl@1.0.2/node_modules/encodeurl/index.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/encodeurl@1.0.2/node_modules/encodeurl/index.js ***!
  \****************************************************************************/
/***/ ((module) => {

eval("/*!\n * encodeurl\n * Copyright(c) 2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = encodeUrl\n\n/**\n * RegExp to match non-URL code points, *after* encoding (i.e. not including \"%\")\n * and including invalid escape sequences.\n * @private\n */\n\nvar ENCODE_CHARS_REGEXP = /(?:[^\\x21\\x25\\x26-\\x3B\\x3D\\x3F-\\x5B\\x5D\\x5F\\x61-\\x7A\\x7E]|%(?:[^0-9A-Fa-f]|[0-9A-Fa-f][^0-9A-Fa-f]|$))+/g\n\n/**\n * RegExp to match unmatched surrogate pair.\n * @private\n */\n\nvar UNMATCHED_SURROGATE_PAIR_REGEXP = /(^|[^\\uD800-\\uDBFF])[\\uDC00-\\uDFFF]|[\\uD800-\\uDBFF]([^\\uDC00-\\uDFFF]|$)/g\n\n/**\n * String to replace unmatched surrogate pair with.\n * @private\n */\n\nvar UNMATCHED_SURROGATE_PAIR_REPLACE = '$1\\uFFFD$2'\n\n/**\n * Encode a URL to a percent-encoded form, excluding already-encoded sequences.\n *\n * This function will take an already-encoded URL and encode all the non-URL\n * code points. This function will not encode the \"%\" character unless it is\n * not part of a valid sequence (`%20` will be left as-is, but `%foo` will\n * be encoded as `%25foo`).\n *\n * This encode is meant to be \"safe\" and does not throw errors. It will try as\n * hard as it can to properly encode the given URL, including replacing any raw,\n * unpaired surrogate pairs with the Unicode replacement character prior to\n * encoding.\n *\n * @param {string} url\n * @return {string}\n * @public\n */\n\nfunction encodeUrl (url) {\n  return String(url)\n    .replace(UNMATCHED_SURROGATE_PAIR_REGEXP, UNMATCHED_SURROGATE_PAIR_REPLACE)\n    .replace(ENCODE_CHARS_REGEXP, encodeURI)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/encodeurl@1.0.2/node_modules/encodeurl/index.js\n");

/***/ })

};
;