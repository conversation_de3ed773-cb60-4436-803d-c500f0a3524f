
/**
 * Client
**/

import * as runtime from '@prisma/client/runtime/library.js';
import $Types = runtime.Types // general types
import $Public = runtime.Types.Public
import $Utils = runtime.Types.Utils
import $Extensions = runtime.Types.Extensions
import $Result = runtime.Types.Result

export type PrismaPromise<T> = $Public.PrismaPromise<T>


/**
 * Model User
 * 
 */
export type User = $Result.DefaultSelection<Prisma.$UserPayload>
/**
 * Model BaseDocument
 * 
 */
export type BaseDocument = $Result.DefaultSelection<Prisma.$BaseDocumentPayload>
/**
 * Model Presentation
 * 
 */
export type Presentation = $Result.DefaultSelection<Prisma.$PresentationPayload>
/**
 * Model CustomTheme
 * 
 */
export type CustomTheme = $Result.DefaultSelection<Prisma.$CustomThemePayload>
/**
 * Model FavoriteDocument
 * 
 */
export type FavoriteDocument = $Result.DefaultSelection<Prisma.$FavoriteDocumentPayload>
/**
 * Model GeneratedImage
 * 
 */
export type GeneratedImage = $Result.DefaultSelection<Prisma.$GeneratedImagePayload>

/**
 * Enums
 */
export namespace $Enums {
  export const UserRole: {
  ADMIN: 'ADMIN',
  USER: 'USER'
};

export type UserRole = (typeof UserRole)[keyof typeof UserRole]


export const DocumentType: {
  NOTE: 'NOTE',
  DOCUMENT: 'DOCUMENT',
  DRAWING: 'DRAWING',
  DESIGN: 'DESIGN',
  STICKY_NOTES: 'STICKY_NOTES',
  MIND_MAP: 'MIND_MAP',
  RAG: 'RAG',
  RESEARCH_PAPER: 'RESEARCH_PAPER',
  FLIPBOOK: 'FLIPBOOK',
  PRESENTATION: 'PRESENTATION'
};

export type DocumentType = (typeof DocumentType)[keyof typeof DocumentType]

}

export type UserRole = $Enums.UserRole

export const UserRole: typeof $Enums.UserRole

export type DocumentType = $Enums.DocumentType

export const DocumentType: typeof $Enums.DocumentType

/**
 * ##  Prisma Client ʲˢ
 * 
 * Type-safe database client for TypeScript & Node.js
 * @example
 * ```
 * const prisma = new PrismaClient()
 * // Fetch zero or more Users
 * const users = await prisma.user.findMany()
 * ```
 *
 * 
 * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
 */
export class PrismaClient<
  ClientOptions extends Prisma.PrismaClientOptions = Prisma.PrismaClientOptions,
  U = 'log' extends keyof ClientOptions ? ClientOptions['log'] extends Array<Prisma.LogLevel | Prisma.LogDefinition> ? Prisma.GetEvents<ClientOptions['log']> : never : never,
  ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs
> {
  [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['other'] }

    /**
   * ##  Prisma Client ʲˢ
   * 
   * Type-safe database client for TypeScript & Node.js
   * @example
   * ```
   * const prisma = new PrismaClient()
   * // Fetch zero or more Users
   * const users = await prisma.user.findMany()
   * ```
   *
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client).
   */

  constructor(optionsArg ?: Prisma.Subset<ClientOptions, Prisma.PrismaClientOptions>);
  $on<V extends U>(eventType: V, callback: (event: V extends 'query' ? Prisma.QueryEvent : Prisma.LogEvent) => void): void;

  /**
   * Connect with the database
   */
  $connect(): $Utils.JsPromise<void>;

  /**
   * Disconnect from the database
   */
  $disconnect(): $Utils.JsPromise<void>;

  /**
   * Add a middleware
   * @deprecated since 4.16.0. For new code, prefer client extensions instead.
   * @see https://pris.ly/d/extensions
   */
  $use(cb: Prisma.Middleware): void

/**
   * Executes a prepared raw query and returns the number of affected rows.
   * @example
   * ```
   * const result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Executes a raw query and returns the number of affected rows.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$executeRawUnsafe('UPDATE User SET cool = $1 WHERE email = $2 ;', true, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $executeRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<number>;

  /**
   * Performs a prepared raw query and returns the `SELECT` data.
   * @example
   * ```
   * const result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRaw<T = unknown>(query: TemplateStringsArray | Prisma.Sql, ...values: any[]): Prisma.PrismaPromise<T>;

  /**
   * Performs a raw query and returns the `SELECT` data.
   * Susceptible to SQL injections, see documentation.
   * @example
   * ```
   * const result = await prisma.$queryRawUnsafe('SELECT * FROM User WHERE id = $1 OR email = $2;', 1, '<EMAIL>')
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/raw-database-access).
   */
  $queryRawUnsafe<T = unknown>(query: string, ...values: any[]): Prisma.PrismaPromise<T>;


  /**
   * Allows the running of a sequence of read/write operations that are guaranteed to either succeed or fail as a whole.
   * @example
   * ```
   * const [george, bob, alice] = await prisma.$transaction([
   *   prisma.user.create({ data: { name: 'George' } }),
   *   prisma.user.create({ data: { name: 'Bob' } }),
   *   prisma.user.create({ data: { name: 'Alice' } }),
   * ])
   * ```
   * 
   * Read more in our [docs](https://www.prisma.io/docs/concepts/components/prisma-client/transactions).
   */
  $transaction<P extends Prisma.PrismaPromise<any>[]>(arg: [...P], options?: { isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<runtime.Types.Utils.UnwrapTuple<P>>

  $transaction<R>(fn: (prisma: Omit<PrismaClient, runtime.ITXClientDenyList>) => $Utils.JsPromise<R>, options?: { maxWait?: number, timeout?: number, isolationLevel?: Prisma.TransactionIsolationLevel }): $Utils.JsPromise<R>


  $extends: $Extensions.ExtendsHook<"extends", Prisma.TypeMapCb, ExtArgs>

      /**
   * `prisma.user`: Exposes CRUD operations for the **User** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Users
    * const users = await prisma.user.findMany()
    * ```
    */
  get user(): Prisma.UserDelegate<ExtArgs>;

  /**
   * `prisma.baseDocument`: Exposes CRUD operations for the **BaseDocument** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more BaseDocuments
    * const baseDocuments = await prisma.baseDocument.findMany()
    * ```
    */
  get baseDocument(): Prisma.BaseDocumentDelegate<ExtArgs>;

  /**
   * `prisma.presentation`: Exposes CRUD operations for the **Presentation** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more Presentations
    * const presentations = await prisma.presentation.findMany()
    * ```
    */
  get presentation(): Prisma.PresentationDelegate<ExtArgs>;

  /**
   * `prisma.customTheme`: Exposes CRUD operations for the **CustomTheme** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more CustomThemes
    * const customThemes = await prisma.customTheme.findMany()
    * ```
    */
  get customTheme(): Prisma.CustomThemeDelegate<ExtArgs>;

  /**
   * `prisma.favoriteDocument`: Exposes CRUD operations for the **FavoriteDocument** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more FavoriteDocuments
    * const favoriteDocuments = await prisma.favoriteDocument.findMany()
    * ```
    */
  get favoriteDocument(): Prisma.FavoriteDocumentDelegate<ExtArgs>;

  /**
   * `prisma.generatedImage`: Exposes CRUD operations for the **GeneratedImage** model.
    * Example usage:
    * ```ts
    * // Fetch zero or more GeneratedImages
    * const generatedImages = await prisma.generatedImage.findMany()
    * ```
    */
  get generatedImage(): Prisma.GeneratedImageDelegate<ExtArgs>;
}

export namespace Prisma {
  export import DMMF = runtime.DMMF

  export type PrismaPromise<T> = $Public.PrismaPromise<T>

  /**
   * Validator
   */
  export import validator = runtime.Public.validator

  /**
   * Prisma Errors
   */
  export import PrismaClientKnownRequestError = runtime.PrismaClientKnownRequestError
  export import PrismaClientUnknownRequestError = runtime.PrismaClientUnknownRequestError
  export import PrismaClientRustPanicError = runtime.PrismaClientRustPanicError
  export import PrismaClientInitializationError = runtime.PrismaClientInitializationError
  export import PrismaClientValidationError = runtime.PrismaClientValidationError
  export import NotFoundError = runtime.NotFoundError

  /**
   * Re-export of sql-template-tag
   */
  export import sql = runtime.sqltag
  export import empty = runtime.empty
  export import join = runtime.join
  export import raw = runtime.raw
  export import Sql = runtime.Sql



  /**
   * Decimal.js
   */
  export import Decimal = runtime.Decimal

  export type DecimalJsLike = runtime.DecimalJsLike

  /**
   * Metrics 
   */
  export type Metrics = runtime.Metrics
  export type Metric<T> = runtime.Metric<T>
  export type MetricHistogram = runtime.MetricHistogram
  export type MetricHistogramBucket = runtime.MetricHistogramBucket

  /**
  * Extensions
  */
  export import Extension = $Extensions.UserArgs
  export import getExtensionContext = runtime.Extensions.getExtensionContext
  export import Args = $Public.Args
  export import Payload = $Public.Payload
  export import Result = $Public.Result
  export import Exact = $Public.Exact

  /**
   * Prisma Client JS version: 5.22.0
   * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
   */
  export type PrismaVersion = {
    client: string
  }

  export const prismaVersion: PrismaVersion 

  /**
   * Utility Types
   */


  export import JsonObject = runtime.JsonObject
  export import JsonArray = runtime.JsonArray
  export import JsonValue = runtime.JsonValue
  export import InputJsonObject = runtime.InputJsonObject
  export import InputJsonArray = runtime.InputJsonArray
  export import InputJsonValue = runtime.InputJsonValue

  /**
   * Types of the values used to represent different kinds of `null` values when working with JSON fields.
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  namespace NullTypes {
    /**
    * Type of `Prisma.DbNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.DbNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class DbNull {
      private DbNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.JsonNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.JsonNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class JsonNull {
      private JsonNull: never
      private constructor()
    }

    /**
    * Type of `Prisma.AnyNull`.
    * 
    * You cannot use other instances of this class. Please use the `Prisma.AnyNull` value.
    * 
    * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
    */
    class AnyNull {
      private AnyNull: never
      private constructor()
    }
  }

  /**
   * Helper for filtering JSON entries that have `null` on the database (empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const DbNull: NullTypes.DbNull

  /**
   * Helper for filtering JSON entries that have JSON `null` values (not empty on the db)
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const JsonNull: NullTypes.JsonNull

  /**
   * Helper for filtering JSON entries that are `Prisma.DbNull` or `Prisma.JsonNull`
   * 
   * @see https://www.prisma.io/docs/concepts/components/prisma-client/working-with-fields/working-with-json-fields#filtering-on-a-json-field
   */
  export const AnyNull: NullTypes.AnyNull

  type SelectAndInclude = {
    select: any
    include: any
  }

  type SelectAndOmit = {
    select: any
    omit: any
  }

  /**
   * Get the type of the value, that the Promise holds.
   */
  export type PromiseType<T extends PromiseLike<any>> = T extends PromiseLike<infer U> ? U : T;

  /**
   * Get the return type of a function which returns a Promise.
   */
  export type PromiseReturnType<T extends (...args: any) => $Utils.JsPromise<any>> = PromiseType<ReturnType<T>>

  /**
   * From T, pick a set of properties whose keys are in the union K
   */
  type Prisma__Pick<T, K extends keyof T> = {
      [P in K]: T[P];
  };


  export type Enumerable<T> = T | Array<T>;

  export type RequiredKeys<T> = {
    [K in keyof T]-?: {} extends Prisma__Pick<T, K> ? never : K
  }[keyof T]

  export type TruthyKeys<T> = keyof {
    [K in keyof T as T[K] extends false | undefined | null ? never : K]: K
  }

  export type TrueKeys<T> = TruthyKeys<Prisma__Pick<T, RequiredKeys<T>>>

  /**
   * Subset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection
   */
  export type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
  };

  /**
   * SelectSubset
   * @desc From `T` pick properties that exist in `U`. Simple version of Intersection.
   * Additionally, it validates, if both select and include are present. If the case, it errors.
   */
  export type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    (T extends SelectAndInclude
      ? 'Please either choose `select` or `include`.'
      : T extends SelectAndOmit
        ? 'Please either choose `select` or `omit`.'
        : {})

  /**
   * Subset + Intersection
   * @desc From `T` pick properties that exist in `U` and intersect `K`
   */
  export type SubsetIntersection<T, U, K> = {
    [key in keyof T]: key extends keyof U ? T[key] : never
  } &
    K

  type Without<T, U> = { [P in Exclude<keyof T, keyof U>]?: never };

  /**
   * XOR is needed to have a real mutually exclusive union type
   * https://stackoverflow.com/questions/42123407/does-typescript-support-mutually-exclusive-types
   */
  type XOR<T, U> =
    T extends object ?
    U extends object ?
      (Without<T, U> & U) | (Without<U, T> & T)
    : U : T


  /**
   * Is T a Record?
   */
  type IsObject<T extends any> = T extends Array<any>
  ? False
  : T extends Date
  ? False
  : T extends Uint8Array
  ? False
  : T extends BigInt
  ? False
  : T extends object
  ? True
  : False


  /**
   * If it's T[], return T
   */
  export type UnEnumerate<T extends unknown> = T extends Array<infer U> ? U : T

  /**
   * From ts-toolbelt
   */

  type __Either<O extends object, K extends Key> = Omit<O, K> &
    {
      // Merge all but K
      [P in K]: Prisma__Pick<O, P & keyof O> // With K possibilities
    }[K]

  type EitherStrict<O extends object, K extends Key> = Strict<__Either<O, K>>

  type EitherLoose<O extends object, K extends Key> = ComputeRaw<__Either<O, K>>

  type _Either<
    O extends object,
    K extends Key,
    strict extends Boolean
  > = {
    1: EitherStrict<O, K>
    0: EitherLoose<O, K>
  }[strict]

  type Either<
    O extends object,
    K extends Key,
    strict extends Boolean = 1
  > = O extends unknown ? _Either<O, K, strict> : never

  export type Union = any

  type PatchUndefined<O extends object, O1 extends object> = {
    [K in keyof O]: O[K] extends undefined ? At<O1, K> : O[K]
  } & {}

  /** Helper Types for "Merge" **/
  export type IntersectOf<U extends Union> = (
    U extends unknown ? (k: U) => void : never
  ) extends (k: infer I) => void
    ? I
    : never

  export type Overwrite<O extends object, O1 extends object> = {
      [K in keyof O]: K extends keyof O1 ? O1[K] : O[K];
  } & {};

  type _Merge<U extends object> = IntersectOf<Overwrite<U, {
      [K in keyof U]-?: At<U, K>;
  }>>;

  type Key = string | number | symbol;
  type AtBasic<O extends object, K extends Key> = K extends keyof O ? O[K] : never;
  type AtStrict<O extends object, K extends Key> = O[K & keyof O];
  type AtLoose<O extends object, K extends Key> = O extends unknown ? AtStrict<O, K> : never;
  export type At<O extends object, K extends Key, strict extends Boolean = 1> = {
      1: AtStrict<O, K>;
      0: AtLoose<O, K>;
  }[strict];

  export type ComputeRaw<A extends any> = A extends Function ? A : {
    [K in keyof A]: A[K];
  } & {};

  export type OptionalFlat<O> = {
    [K in keyof O]?: O[K];
  } & {};

  type _Record<K extends keyof any, T> = {
    [P in K]: T;
  };

  // cause typescript not to expand types and preserve names
  type NoExpand<T> = T extends unknown ? T : never;

  // this type assumes the passed object is entirely optional
  type AtLeast<O extends object, K extends string> = NoExpand<
    O extends unknown
    ? | (K extends keyof O ? { [P in K]: O[P] } & O : O)
      | {[P in keyof O as P extends K ? K : never]-?: O[P]} & O
    : never>;

  type _Strict<U, _U = U> = U extends unknown ? U & OptionalFlat<_Record<Exclude<Keys<_U>, keyof U>, never>> : never;

  export type Strict<U extends object> = ComputeRaw<_Strict<U>>;
  /** End Helper Types for "Merge" **/

  export type Merge<U extends object> = ComputeRaw<_Merge<Strict<U>>>;

  /**
  A [[Boolean]]
  */
  export type Boolean = True | False

  // /**
  // 1
  // */
  export type True = 1

  /**
  0
  */
  export type False = 0

  export type Not<B extends Boolean> = {
    0: 1
    1: 0
  }[B]

  export type Extends<A1 extends any, A2 extends any> = [A1] extends [never]
    ? 0 // anything `never` is false
    : A1 extends A2
    ? 1
    : 0

  export type Has<U extends Union, U1 extends Union> = Not<
    Extends<Exclude<U1, U>, U1>
  >

  export type Or<B1 extends Boolean, B2 extends Boolean> = {
    0: {
      0: 0
      1: 1
    }
    1: {
      0: 1
      1: 1
    }
  }[B1][B2]

  export type Keys<U extends Union> = U extends unknown ? keyof U : never

  type Cast<A, B> = A extends B ? A : B;

  export const type: unique symbol;



  /**
   * Used by group by
   */

  export type GetScalarType<T, O> = O extends object ? {
    [P in keyof T]: P extends keyof O
      ? O[P]
      : never
  } : never

  type FieldPaths<
    T,
    U = Omit<T, '_avg' | '_sum' | '_count' | '_min' | '_max'>
  > = IsObject<T> extends True ? U : T

  type GetHavingFields<T> = {
    [K in keyof T]: Or<
      Or<Extends<'OR', K>, Extends<'AND', K>>,
      Extends<'NOT', K>
    > extends True
      ? // infer is only needed to not hit TS limit
        // based on the brilliant idea of Pierre-Antoine Mills
        // https://github.com/microsoft/TypeScript/issues/30188#issuecomment-478938437
        T[K] extends infer TK
        ? GetHavingFields<UnEnumerate<TK> extends object ? Merge<UnEnumerate<TK>> : never>
        : never
      : {} extends FieldPaths<T[K]>
      ? never
      : K
  }[keyof T]

  /**
   * Convert tuple to union
   */
  type _TupleToUnion<T> = T extends (infer E)[] ? E : never
  type TupleToUnion<K extends readonly any[]> = _TupleToUnion<K>
  type MaybeTupleToUnion<T> = T extends any[] ? TupleToUnion<T> : T

  /**
   * Like `Pick`, but additionally can also accept an array of keys
   */
  type PickEnumerable<T, K extends Enumerable<keyof T> | keyof T> = Prisma__Pick<T, MaybeTupleToUnion<K>>

  /**
   * Exclude all keys with underscores
   */
  type ExcludeUnderscoreKeys<T extends string> = T extends `_${string}` ? never : T


  export type FieldRef<Model, FieldType> = runtime.FieldRef<Model, FieldType>

  type FieldRefInputType<Model, FieldType> = Model extends never ? never : FieldRef<Model, FieldType>


  export const ModelName: {
    User: 'User',
    BaseDocument: 'BaseDocument',
    Presentation: 'Presentation',
    CustomTheme: 'CustomTheme',
    FavoriteDocument: 'FavoriteDocument',
    GeneratedImage: 'GeneratedImage'
  };

  export type ModelName = (typeof ModelName)[keyof typeof ModelName]


  export type Datasources = {
    db?: Datasource
  }

  interface TypeMapCb extends $Utils.Fn<{extArgs: $Extensions.InternalArgs, clientOptions: PrismaClientOptions }, $Utils.Record<string, any>> {
    returns: Prisma.TypeMap<this['params']['extArgs'], this['params']['clientOptions']>
  }

  export type TypeMap<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs, ClientOptions = {}> = {
    meta: {
      modelProps: "user" | "baseDocument" | "presentation" | "customTheme" | "favoriteDocument" | "generatedImage"
      txIsolationLevel: Prisma.TransactionIsolationLevel
    }
    model: {
      User: {
        payload: Prisma.$UserPayload<ExtArgs>
        fields: Prisma.UserFieldRefs
        operations: {
          findUnique: {
            args: Prisma.UserFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.UserFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findFirst: {
            args: Prisma.UserFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.UserFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          findMany: {
            args: Prisma.UserFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          create: {
            args: Prisma.UserCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          createMany: {
            args: Prisma.UserCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.UserCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>[]
          }
          delete: {
            args: Prisma.UserDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          update: {
            args: Prisma.UserUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          deleteMany: {
            args: Prisma.UserDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.UserUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.UserUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$UserPayload>
          }
          aggregate: {
            args: Prisma.UserAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateUser>
          }
          groupBy: {
            args: Prisma.UserGroupByArgs<ExtArgs>
            result: $Utils.Optional<UserGroupByOutputType>[]
          }
          count: {
            args: Prisma.UserCountArgs<ExtArgs>
            result: $Utils.Optional<UserCountAggregateOutputType> | number
          }
        }
      }
      BaseDocument: {
        payload: Prisma.$BaseDocumentPayload<ExtArgs>
        fields: Prisma.BaseDocumentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.BaseDocumentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.BaseDocumentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          findFirst: {
            args: Prisma.BaseDocumentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.BaseDocumentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          findMany: {
            args: Prisma.BaseDocumentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>[]
          }
          create: {
            args: Prisma.BaseDocumentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          createMany: {
            args: Prisma.BaseDocumentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.BaseDocumentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>[]
          }
          delete: {
            args: Prisma.BaseDocumentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          update: {
            args: Prisma.BaseDocumentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          deleteMany: {
            args: Prisma.BaseDocumentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.BaseDocumentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.BaseDocumentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$BaseDocumentPayload>
          }
          aggregate: {
            args: Prisma.BaseDocumentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateBaseDocument>
          }
          groupBy: {
            args: Prisma.BaseDocumentGroupByArgs<ExtArgs>
            result: $Utils.Optional<BaseDocumentGroupByOutputType>[]
          }
          count: {
            args: Prisma.BaseDocumentCountArgs<ExtArgs>
            result: $Utils.Optional<BaseDocumentCountAggregateOutputType> | number
          }
        }
      }
      Presentation: {
        payload: Prisma.$PresentationPayload<ExtArgs>
        fields: Prisma.PresentationFieldRefs
        operations: {
          findUnique: {
            args: Prisma.PresentationFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.PresentationFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          findFirst: {
            args: Prisma.PresentationFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.PresentationFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          findMany: {
            args: Prisma.PresentationFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>[]
          }
          create: {
            args: Prisma.PresentationCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          createMany: {
            args: Prisma.PresentationCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.PresentationCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>[]
          }
          delete: {
            args: Prisma.PresentationDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          update: {
            args: Prisma.PresentationUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          deleteMany: {
            args: Prisma.PresentationDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.PresentationUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.PresentationUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$PresentationPayload>
          }
          aggregate: {
            args: Prisma.PresentationAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregatePresentation>
          }
          groupBy: {
            args: Prisma.PresentationGroupByArgs<ExtArgs>
            result: $Utils.Optional<PresentationGroupByOutputType>[]
          }
          count: {
            args: Prisma.PresentationCountArgs<ExtArgs>
            result: $Utils.Optional<PresentationCountAggregateOutputType> | number
          }
        }
      }
      CustomTheme: {
        payload: Prisma.$CustomThemePayload<ExtArgs>
        fields: Prisma.CustomThemeFieldRefs
        operations: {
          findUnique: {
            args: Prisma.CustomThemeFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.CustomThemeFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          findFirst: {
            args: Prisma.CustomThemeFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.CustomThemeFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          findMany: {
            args: Prisma.CustomThemeFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>[]
          }
          create: {
            args: Prisma.CustomThemeCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          createMany: {
            args: Prisma.CustomThemeCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.CustomThemeCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>[]
          }
          delete: {
            args: Prisma.CustomThemeDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          update: {
            args: Prisma.CustomThemeUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          deleteMany: {
            args: Prisma.CustomThemeDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.CustomThemeUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.CustomThemeUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$CustomThemePayload>
          }
          aggregate: {
            args: Prisma.CustomThemeAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateCustomTheme>
          }
          groupBy: {
            args: Prisma.CustomThemeGroupByArgs<ExtArgs>
            result: $Utils.Optional<CustomThemeGroupByOutputType>[]
          }
          count: {
            args: Prisma.CustomThemeCountArgs<ExtArgs>
            result: $Utils.Optional<CustomThemeCountAggregateOutputType> | number
          }
        }
      }
      FavoriteDocument: {
        payload: Prisma.$FavoriteDocumentPayload<ExtArgs>
        fields: Prisma.FavoriteDocumentFieldRefs
        operations: {
          findUnique: {
            args: Prisma.FavoriteDocumentFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.FavoriteDocumentFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          findFirst: {
            args: Prisma.FavoriteDocumentFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.FavoriteDocumentFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          findMany: {
            args: Prisma.FavoriteDocumentFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>[]
          }
          create: {
            args: Prisma.FavoriteDocumentCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          createMany: {
            args: Prisma.FavoriteDocumentCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.FavoriteDocumentCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>[]
          }
          delete: {
            args: Prisma.FavoriteDocumentDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          update: {
            args: Prisma.FavoriteDocumentUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          deleteMany: {
            args: Prisma.FavoriteDocumentDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.FavoriteDocumentUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.FavoriteDocumentUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$FavoriteDocumentPayload>
          }
          aggregate: {
            args: Prisma.FavoriteDocumentAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateFavoriteDocument>
          }
          groupBy: {
            args: Prisma.FavoriteDocumentGroupByArgs<ExtArgs>
            result: $Utils.Optional<FavoriteDocumentGroupByOutputType>[]
          }
          count: {
            args: Prisma.FavoriteDocumentCountArgs<ExtArgs>
            result: $Utils.Optional<FavoriteDocumentCountAggregateOutputType> | number
          }
        }
      }
      GeneratedImage: {
        payload: Prisma.$GeneratedImagePayload<ExtArgs>
        fields: Prisma.GeneratedImageFieldRefs
        operations: {
          findUnique: {
            args: Prisma.GeneratedImageFindUniqueArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload> | null
          }
          findUniqueOrThrow: {
            args: Prisma.GeneratedImageFindUniqueOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          findFirst: {
            args: Prisma.GeneratedImageFindFirstArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload> | null
          }
          findFirstOrThrow: {
            args: Prisma.GeneratedImageFindFirstOrThrowArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          findMany: {
            args: Prisma.GeneratedImageFindManyArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>[]
          }
          create: {
            args: Prisma.GeneratedImageCreateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          createMany: {
            args: Prisma.GeneratedImageCreateManyArgs<ExtArgs>
            result: BatchPayload
          }
          createManyAndReturn: {
            args: Prisma.GeneratedImageCreateManyAndReturnArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>[]
          }
          delete: {
            args: Prisma.GeneratedImageDeleteArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          update: {
            args: Prisma.GeneratedImageUpdateArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          deleteMany: {
            args: Prisma.GeneratedImageDeleteManyArgs<ExtArgs>
            result: BatchPayload
          }
          updateMany: {
            args: Prisma.GeneratedImageUpdateManyArgs<ExtArgs>
            result: BatchPayload
          }
          upsert: {
            args: Prisma.GeneratedImageUpsertArgs<ExtArgs>
            result: $Utils.PayloadToResult<Prisma.$GeneratedImagePayload>
          }
          aggregate: {
            args: Prisma.GeneratedImageAggregateArgs<ExtArgs>
            result: $Utils.Optional<AggregateGeneratedImage>
          }
          groupBy: {
            args: Prisma.GeneratedImageGroupByArgs<ExtArgs>
            result: $Utils.Optional<GeneratedImageGroupByOutputType>[]
          }
          count: {
            args: Prisma.GeneratedImageCountArgs<ExtArgs>
            result: $Utils.Optional<GeneratedImageCountAggregateOutputType> | number
          }
        }
      }
    }
  } & {
    other: {
      payload: any
      operations: {
        $executeRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $executeRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
        $queryRaw: {
          args: [query: TemplateStringsArray | Prisma.Sql, ...values: any[]],
          result: any
        }
        $queryRawUnsafe: {
          args: [query: string, ...values: any[]],
          result: any
        }
      }
    }
  }
  export const defineExtension: $Extensions.ExtendsHook<"define", Prisma.TypeMapCb, $Extensions.DefaultArgs>
  export type DefaultPrismaClient = PrismaClient
  export type ErrorFormat = 'pretty' | 'colorless' | 'minimal'
  export interface PrismaClientOptions {
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasources?: Datasources
    /**
     * Overwrites the datasource url from your schema.prisma file
     */
    datasourceUrl?: string
    /**
     * @default "colorless"
     */
    errorFormat?: ErrorFormat
    /**
     * @example
     * ```
     * // Defaults to stdout
     * log: ['query', 'info', 'warn', 'error']
     * 
     * // Emit as events
     * log: [
     *   { emit: 'stdout', level: 'query' },
     *   { emit: 'stdout', level: 'info' },
     *   { emit: 'stdout', level: 'warn' }
     *   { emit: 'stdout', level: 'error' }
     * ]
     * ```
     * Read more in our [docs](https://www.prisma.io/docs/reference/tools-and-interfaces/prisma-client/logging#the-log-option).
     */
    log?: (LogLevel | LogDefinition)[]
    /**
     * The default values for transactionOptions
     * maxWait ?= 2000
     * timeout ?= 5000
     */
    transactionOptions?: {
      maxWait?: number
      timeout?: number
      isolationLevel?: Prisma.TransactionIsolationLevel
    }
  }


  /* Types for Logging */
  export type LogLevel = 'info' | 'query' | 'warn' | 'error'
  export type LogDefinition = {
    level: LogLevel
    emit: 'stdout' | 'event'
  }

  export type GetLogType<T extends LogLevel | LogDefinition> = T extends LogDefinition ? T['emit'] extends 'event' ? T['level'] : never : never
  export type GetEvents<T extends any> = T extends Array<LogLevel | LogDefinition> ?
    GetLogType<T[0]> | GetLogType<T[1]> | GetLogType<T[2]> | GetLogType<T[3]>
    : never

  export type QueryEvent = {
    timestamp: Date
    query: string
    params: string
    duration: number
    target: string
  }

  export type LogEvent = {
    timestamp: Date
    message: string
    target: string
  }
  /* End Types for Logging */


  export type PrismaAction =
    | 'findUnique'
    | 'findUniqueOrThrow'
    | 'findMany'
    | 'findFirst'
    | 'findFirstOrThrow'
    | 'create'
    | 'createMany'
    | 'createManyAndReturn'
    | 'update'
    | 'updateMany'
    | 'upsert'
    | 'delete'
    | 'deleteMany'
    | 'executeRaw'
    | 'queryRaw'
    | 'aggregate'
    | 'count'
    | 'runCommandRaw'
    | 'findRaw'
    | 'groupBy'

  /**
   * These options are being passed into the middleware as "params"
   */
  export type MiddlewareParams = {
    model?: ModelName
    action: PrismaAction
    args: any
    dataPath: string[]
    runInTransaction: boolean
  }

  /**
   * The `T` type makes sure, that the `return proceed` is not forgotten in the middleware implementation
   */
  export type Middleware<T = any> = (
    params: MiddlewareParams,
    next: (params: MiddlewareParams) => $Utils.JsPromise<T>,
  ) => $Utils.JsPromise<T>

  // tested in getLogLevel.test.ts
  export function getLogLevel(log: Array<LogLevel | LogDefinition>): LogLevel | undefined;

  /**
   * `PrismaClient` proxy available in interactive transactions.
   */
  export type TransactionClient = Omit<Prisma.DefaultPrismaClient, runtime.ITXClientDenyList>

  export type Datasource = {
    url?: string
  }

  /**
   * Count Types
   */


  /**
   * Count Type UserCountOutputType
   */

  export type UserCountOutputType = {
    documents: number
    favorites: number
    CustomTheme: number
    GeneratedImage: number
  }

  export type UserCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    documents?: boolean | UserCountOutputTypeCountDocumentsArgs
    favorites?: boolean | UserCountOutputTypeCountFavoritesArgs
    CustomTheme?: boolean | UserCountOutputTypeCountCustomThemeArgs
    GeneratedImage?: boolean | UserCountOutputTypeCountGeneratedImageArgs
  }

  // Custom InputTypes
  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the UserCountOutputType
     */
    select?: UserCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountDocumentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: BaseDocumentWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountFavoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: FavoriteDocumentWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountCustomThemeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CustomThemeWhereInput
  }

  /**
   * UserCountOutputType without action
   */
  export type UserCountOutputTypeCountGeneratedImageArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GeneratedImageWhereInput
  }


  /**
   * Count Type BaseDocumentCountOutputType
   */

  export type BaseDocumentCountOutputType = {
    favorites: number
  }

  export type BaseDocumentCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    favorites?: boolean | BaseDocumentCountOutputTypeCountFavoritesArgs
  }

  // Custom InputTypes
  /**
   * BaseDocumentCountOutputType without action
   */
  export type BaseDocumentCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocumentCountOutputType
     */
    select?: BaseDocumentCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * BaseDocumentCountOutputType without action
   */
  export type BaseDocumentCountOutputTypeCountFavoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: FavoriteDocumentWhereInput
  }


  /**
   * Count Type CustomThemeCountOutputType
   */

  export type CustomThemeCountOutputType = {
    presentations: number
  }

  export type CustomThemeCountOutputTypeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    presentations?: boolean | CustomThemeCountOutputTypeCountPresentationsArgs
  }

  // Custom InputTypes
  /**
   * CustomThemeCountOutputType without action
   */
  export type CustomThemeCountOutputTypeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomThemeCountOutputType
     */
    select?: CustomThemeCountOutputTypeSelect<ExtArgs> | null
  }

  /**
   * CustomThemeCountOutputType without action
   */
  export type CustomThemeCountOutputTypeCountPresentationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PresentationWhereInput
  }


  /**
   * Models
   */

  /**
   * Model User
   */

  export type AggregateUser = {
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  export type UserMinAggregateOutputType = {
    id: string | null
    name: string | null
    email: string | null
    password: string | null
    emailVerified: Date | null
    image: string | null
    createdAt: Date | null
    updatedAt: Date | null
    headline: string | null
    bio: string | null
    location: string | null
    website: string | null
    role: $Enums.UserRole | null
    hasAccess: boolean | null
  }

  export type UserMaxAggregateOutputType = {
    id: string | null
    name: string | null
    email: string | null
    password: string | null
    emailVerified: Date | null
    image: string | null
    createdAt: Date | null
    updatedAt: Date | null
    headline: string | null
    bio: string | null
    location: string | null
    website: string | null
    role: $Enums.UserRole | null
    hasAccess: boolean | null
  }

  export type UserCountAggregateOutputType = {
    id: number
    name: number
    email: number
    password: number
    emailVerified: number
    image: number
    createdAt: number
    updatedAt: number
    headline: number
    bio: number
    interests: number
    location: number
    website: number
    role: number
    hasAccess: number
    _all: number
  }


  export type UserMinAggregateInputType = {
    id?: true
    name?: true
    email?: true
    password?: true
    emailVerified?: true
    image?: true
    createdAt?: true
    updatedAt?: true
    headline?: true
    bio?: true
    location?: true
    website?: true
    role?: true
    hasAccess?: true
  }

  export type UserMaxAggregateInputType = {
    id?: true
    name?: true
    email?: true
    password?: true
    emailVerified?: true
    image?: true
    createdAt?: true
    updatedAt?: true
    headline?: true
    bio?: true
    location?: true
    website?: true
    role?: true
    hasAccess?: true
  }

  export type UserCountAggregateInputType = {
    id?: true
    name?: true
    email?: true
    password?: true
    emailVerified?: true
    image?: true
    createdAt?: true
    updatedAt?: true
    headline?: true
    bio?: true
    interests?: true
    location?: true
    website?: true
    role?: true
    hasAccess?: true
    _all?: true
  }

  export type UserAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which User to aggregate.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Users
    **/
    _count?: true | UserCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: UserMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: UserMaxAggregateInputType
  }

  export type GetUserAggregateType<T extends UserAggregateArgs> = {
        [P in keyof T & keyof AggregateUser]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateUser[P]>
      : GetScalarType<T[P], AggregateUser[P]>
  }




  export type UserGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: UserWhereInput
    orderBy?: UserOrderByWithAggregationInput | UserOrderByWithAggregationInput[]
    by: UserScalarFieldEnum[] | UserScalarFieldEnum
    having?: UserScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: UserCountAggregateInputType | true
    _min?: UserMinAggregateInputType
    _max?: UserMaxAggregateInputType
  }

  export type UserGroupByOutputType = {
    id: string
    name: string | null
    email: string | null
    password: string | null
    emailVerified: Date | null
    image: string | null
    createdAt: Date
    updatedAt: Date
    headline: string | null
    bio: string | null
    interests: string[]
    location: string | null
    website: string | null
    role: $Enums.UserRole
    hasAccess: boolean
    _count: UserCountAggregateOutputType | null
    _min: UserMinAggregateOutputType | null
    _max: UserMaxAggregateOutputType | null
  }

  type GetUserGroupByPayload<T extends UserGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<UserGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof UserGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], UserGroupByOutputType[P]>
            : GetScalarType<T[P], UserGroupByOutputType[P]>
        }
      >
    >


  export type UserSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    email?: boolean
    password?: boolean
    emailVerified?: boolean
    image?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    headline?: boolean
    bio?: boolean
    interests?: boolean
    location?: boolean
    website?: boolean
    role?: boolean
    hasAccess?: boolean
    documents?: boolean | User$documentsArgs<ExtArgs>
    favorites?: boolean | User$favoritesArgs<ExtArgs>
    CustomTheme?: boolean | User$CustomThemeArgs<ExtArgs>
    GeneratedImage?: boolean | User$GeneratedImageArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["user"]>

  export type UserSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    email?: boolean
    password?: boolean
    emailVerified?: boolean
    image?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    headline?: boolean
    bio?: boolean
    interests?: boolean
    location?: boolean
    website?: boolean
    role?: boolean
    hasAccess?: boolean
  }, ExtArgs["result"]["user"]>

  export type UserSelectScalar = {
    id?: boolean
    name?: boolean
    email?: boolean
    password?: boolean
    emailVerified?: boolean
    image?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    headline?: boolean
    bio?: boolean
    interests?: boolean
    location?: boolean
    website?: boolean
    role?: boolean
    hasAccess?: boolean
  }

  export type UserInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    documents?: boolean | User$documentsArgs<ExtArgs>
    favorites?: boolean | User$favoritesArgs<ExtArgs>
    CustomTheme?: boolean | User$CustomThemeArgs<ExtArgs>
    GeneratedImage?: boolean | User$GeneratedImageArgs<ExtArgs>
    _count?: boolean | UserCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type UserIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {}

  export type $UserPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "User"
    objects: {
      documents: Prisma.$BaseDocumentPayload<ExtArgs>[]
      favorites: Prisma.$FavoriteDocumentPayload<ExtArgs>[]
      CustomTheme: Prisma.$CustomThemePayload<ExtArgs>[]
      GeneratedImage: Prisma.$GeneratedImagePayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string | null
      email: string | null
      password: string | null
      emailVerified: Date | null
      image: string | null
      createdAt: Date
      updatedAt: Date
      headline: string | null
      bio: string | null
      interests: string[]
      location: string | null
      website: string | null
      role: $Enums.UserRole
      hasAccess: boolean
    }, ExtArgs["result"]["user"]>
    composites: {}
  }

  type UserGetPayload<S extends boolean | null | undefined | UserDefaultArgs> = $Result.GetResult<Prisma.$UserPayload, S>

  type UserCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<UserFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: UserCountAggregateInputType | true
    }

  export interface UserDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['User'], meta: { name: 'User' } }
    /**
     * Find zero or one User that matches the filter.
     * @param {UserFindUniqueArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends UserFindUniqueArgs>(args: SelectSubset<T, UserFindUniqueArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one User that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {UserFindUniqueOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends UserFindUniqueOrThrowArgs>(args: SelectSubset<T, UserFindUniqueOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first User that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends UserFindFirstArgs>(args?: SelectSubset<T, UserFindFirstArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first User that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindFirstOrThrowArgs} args - Arguments to find a User
     * @example
     * // Get one User
     * const user = await prisma.user.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends UserFindFirstOrThrowArgs>(args?: SelectSubset<T, UserFindFirstOrThrowArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Users that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Users
     * const users = await prisma.user.findMany()
     * 
     * // Get first 10 Users
     * const users = await prisma.user.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const userWithIdOnly = await prisma.user.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends UserFindManyArgs>(args?: SelectSubset<T, UserFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a User.
     * @param {UserCreateArgs} args - Arguments to create a User.
     * @example
     * // Create one User
     * const User = await prisma.user.create({
     *   data: {
     *     // ... data to create a User
     *   }
     * })
     * 
     */
    create<T extends UserCreateArgs>(args: SelectSubset<T, UserCreateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Users.
     * @param {UserCreateManyArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends UserCreateManyArgs>(args?: SelectSubset<T, UserCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Users and returns the data saved in the database.
     * @param {UserCreateManyAndReturnArgs} args - Arguments to create many Users.
     * @example
     * // Create many Users
     * const user = await prisma.user.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Users and only return the `id`
     * const userWithIdOnly = await prisma.user.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends UserCreateManyAndReturnArgs>(args?: SelectSubset<T, UserCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a User.
     * @param {UserDeleteArgs} args - Arguments to delete one User.
     * @example
     * // Delete one User
     * const User = await prisma.user.delete({
     *   where: {
     *     // ... filter to delete one User
     *   }
     * })
     * 
     */
    delete<T extends UserDeleteArgs>(args: SelectSubset<T, UserDeleteArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one User.
     * @param {UserUpdateArgs} args - Arguments to update one User.
     * @example
     * // Update one User
     * const user = await prisma.user.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends UserUpdateArgs>(args: SelectSubset<T, UserUpdateArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Users.
     * @param {UserDeleteManyArgs} args - Arguments to filter Users to delete.
     * @example
     * // Delete a few Users
     * const { count } = await prisma.user.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends UserDeleteManyArgs>(args?: SelectSubset<T, UserDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Users
     * const user = await prisma.user.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends UserUpdateManyArgs>(args: SelectSubset<T, UserUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one User.
     * @param {UserUpsertArgs} args - Arguments to update or create a User.
     * @example
     * // Update or create a User
     * const user = await prisma.user.upsert({
     *   create: {
     *     // ... data to create a User
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the User we want to update
     *   }
     * })
     */
    upsert<T extends UserUpsertArgs>(args: SelectSubset<T, UserUpsertArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Users.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserCountArgs} args - Arguments to filter Users to count.
     * @example
     * // Count the number of Users
     * const count = await prisma.user.count({
     *   where: {
     *     // ... the filter for the Users we want to count
     *   }
     * })
    **/
    count<T extends UserCountArgs>(
      args?: Subset<T, UserCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], UserCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends UserAggregateArgs>(args: Subset<T, UserAggregateArgs>): Prisma.PrismaPromise<GetUserAggregateType<T>>

    /**
     * Group by User.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {UserGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends UserGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: UserGroupByArgs['orderBy'] }
        : { orderBy?: UserGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, UserGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetUserGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the User model
   */
  readonly fields: UserFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for User.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__UserClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    documents<T extends User$documentsArgs<ExtArgs> = {}>(args?: Subset<T, User$documentsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findMany"> | Null>
    favorites<T extends User$favoritesArgs<ExtArgs> = {}>(args?: Subset<T, User$favoritesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findMany"> | Null>
    CustomTheme<T extends User$CustomThemeArgs<ExtArgs> = {}>(args?: Subset<T, User$CustomThemeArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findMany"> | Null>
    GeneratedImage<T extends User$GeneratedImageArgs<ExtArgs> = {}>(args?: Subset<T, User$GeneratedImageArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the User model
   */ 
  interface UserFieldRefs {
    readonly id: FieldRef<"User", 'String'>
    readonly name: FieldRef<"User", 'String'>
    readonly email: FieldRef<"User", 'String'>
    readonly password: FieldRef<"User", 'String'>
    readonly emailVerified: FieldRef<"User", 'DateTime'>
    readonly image: FieldRef<"User", 'String'>
    readonly createdAt: FieldRef<"User", 'DateTime'>
    readonly updatedAt: FieldRef<"User", 'DateTime'>
    readonly headline: FieldRef<"User", 'String'>
    readonly bio: FieldRef<"User", 'String'>
    readonly interests: FieldRef<"User", 'String[]'>
    readonly location: FieldRef<"User", 'String'>
    readonly website: FieldRef<"User", 'String'>
    readonly role: FieldRef<"User", 'UserRole'>
    readonly hasAccess: FieldRef<"User", 'Boolean'>
  }
    

  // Custom InputTypes
  /**
   * User findUnique
   */
  export type UserFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findUniqueOrThrow
   */
  export type UserFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User findFirst
   */
  export type UserFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findFirstOrThrow
   */
  export type UserFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which User to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Users.
     */
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User findMany
   */
  export type UserFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter, which Users to fetch.
     */
    where?: UserWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Users to fetch.
     */
    orderBy?: UserOrderByWithRelationInput | UserOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Users.
     */
    cursor?: UserWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Users from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Users.
     */
    skip?: number
    distinct?: UserScalarFieldEnum | UserScalarFieldEnum[]
  }

  /**
   * User create
   */
  export type UserCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to create a User.
     */
    data?: XOR<UserCreateInput, UserUncheckedCreateInput>
  }

  /**
   * User createMany
   */
  export type UserCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User createManyAndReturn
   */
  export type UserCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Users.
     */
    data: UserCreateManyInput | UserCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * User update
   */
  export type UserUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The data needed to update a User.
     */
    data: XOR<UserUpdateInput, UserUncheckedUpdateInput>
    /**
     * Choose, which User to update.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User updateMany
   */
  export type UserUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Users.
     */
    data: XOR<UserUpdateManyMutationInput, UserUncheckedUpdateManyInput>
    /**
     * Filter which Users to update
     */
    where?: UserWhereInput
  }

  /**
   * User upsert
   */
  export type UserUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * The filter to search for the User to update in case it exists.
     */
    where: UserWhereUniqueInput
    /**
     * In case the User found by the `where` argument doesn't exist, create a new User with this data.
     */
    create: XOR<UserCreateInput, UserUncheckedCreateInput>
    /**
     * In case the User was found with the provided `where` argument, update it with this data.
     */
    update: XOR<UserUpdateInput, UserUncheckedUpdateInput>
  }

  /**
   * User delete
   */
  export type UserDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
    /**
     * Filter which User to delete.
     */
    where: UserWhereUniqueInput
  }

  /**
   * User deleteMany
   */
  export type UserDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Users to delete
     */
    where?: UserWhereInput
  }

  /**
   * User.documents
   */
  export type User$documentsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    where?: BaseDocumentWhereInput
    orderBy?: BaseDocumentOrderByWithRelationInput | BaseDocumentOrderByWithRelationInput[]
    cursor?: BaseDocumentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: BaseDocumentScalarFieldEnum | BaseDocumentScalarFieldEnum[]
  }

  /**
   * User.favorites
   */
  export type User$favoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    where?: FavoriteDocumentWhereInput
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    cursor?: FavoriteDocumentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: FavoriteDocumentScalarFieldEnum | FavoriteDocumentScalarFieldEnum[]
  }

  /**
   * User.CustomTheme
   */
  export type User$CustomThemeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    where?: CustomThemeWhereInput
    orderBy?: CustomThemeOrderByWithRelationInput | CustomThemeOrderByWithRelationInput[]
    cursor?: CustomThemeWhereUniqueInput
    take?: number
    skip?: number
    distinct?: CustomThemeScalarFieldEnum | CustomThemeScalarFieldEnum[]
  }

  /**
   * User.GeneratedImage
   */
  export type User$GeneratedImageArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    where?: GeneratedImageWhereInput
    orderBy?: GeneratedImageOrderByWithRelationInput | GeneratedImageOrderByWithRelationInput[]
    cursor?: GeneratedImageWhereUniqueInput
    take?: number
    skip?: number
    distinct?: GeneratedImageScalarFieldEnum | GeneratedImageScalarFieldEnum[]
  }

  /**
   * User without action
   */
  export type UserDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the User
     */
    select?: UserSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: UserInclude<ExtArgs> | null
  }


  /**
   * Model BaseDocument
   */

  export type AggregateBaseDocument = {
    _count: BaseDocumentCountAggregateOutputType | null
    _min: BaseDocumentMinAggregateOutputType | null
    _max: BaseDocumentMaxAggregateOutputType | null
  }

  export type BaseDocumentMinAggregateOutputType = {
    id: string | null
    title: string | null
    type: $Enums.DocumentType | null
    userId: string | null
    thumbnailUrl: string | null
    createdAt: Date | null
    updatedAt: Date | null
    isPublic: boolean | null
    documentType: string | null
  }

  export type BaseDocumentMaxAggregateOutputType = {
    id: string | null
    title: string | null
    type: $Enums.DocumentType | null
    userId: string | null
    thumbnailUrl: string | null
    createdAt: Date | null
    updatedAt: Date | null
    isPublic: boolean | null
    documentType: string | null
  }

  export type BaseDocumentCountAggregateOutputType = {
    id: number
    title: number
    type: number
    userId: number
    thumbnailUrl: number
    createdAt: number
    updatedAt: number
    isPublic: number
    documentType: number
    _all: number
  }


  export type BaseDocumentMinAggregateInputType = {
    id?: true
    title?: true
    type?: true
    userId?: true
    thumbnailUrl?: true
    createdAt?: true
    updatedAt?: true
    isPublic?: true
    documentType?: true
  }

  export type BaseDocumentMaxAggregateInputType = {
    id?: true
    title?: true
    type?: true
    userId?: true
    thumbnailUrl?: true
    createdAt?: true
    updatedAt?: true
    isPublic?: true
    documentType?: true
  }

  export type BaseDocumentCountAggregateInputType = {
    id?: true
    title?: true
    type?: true
    userId?: true
    thumbnailUrl?: true
    createdAt?: true
    updatedAt?: true
    isPublic?: true
    documentType?: true
    _all?: true
  }

  export type BaseDocumentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which BaseDocument to aggregate.
     */
    where?: BaseDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of BaseDocuments to fetch.
     */
    orderBy?: BaseDocumentOrderByWithRelationInput | BaseDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: BaseDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` BaseDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` BaseDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned BaseDocuments
    **/
    _count?: true | BaseDocumentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: BaseDocumentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: BaseDocumentMaxAggregateInputType
  }

  export type GetBaseDocumentAggregateType<T extends BaseDocumentAggregateArgs> = {
        [P in keyof T & keyof AggregateBaseDocument]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateBaseDocument[P]>
      : GetScalarType<T[P], AggregateBaseDocument[P]>
  }




  export type BaseDocumentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: BaseDocumentWhereInput
    orderBy?: BaseDocumentOrderByWithAggregationInput | BaseDocumentOrderByWithAggregationInput[]
    by: BaseDocumentScalarFieldEnum[] | BaseDocumentScalarFieldEnum
    having?: BaseDocumentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: BaseDocumentCountAggregateInputType | true
    _min?: BaseDocumentMinAggregateInputType
    _max?: BaseDocumentMaxAggregateInputType
  }

  export type BaseDocumentGroupByOutputType = {
    id: string
    title: string
    type: $Enums.DocumentType
    userId: string
    thumbnailUrl: string | null
    createdAt: Date
    updatedAt: Date
    isPublic: boolean
    documentType: string
    _count: BaseDocumentCountAggregateOutputType | null
    _min: BaseDocumentMinAggregateOutputType | null
    _max: BaseDocumentMaxAggregateOutputType | null
  }

  type GetBaseDocumentGroupByPayload<T extends BaseDocumentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<BaseDocumentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof BaseDocumentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], BaseDocumentGroupByOutputType[P]>
            : GetScalarType<T[P], BaseDocumentGroupByOutputType[P]>
        }
      >
    >


  export type BaseDocumentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    title?: boolean
    type?: boolean
    userId?: boolean
    thumbnailUrl?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    isPublic?: boolean
    documentType?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    presentation?: boolean | BaseDocument$presentationArgs<ExtArgs>
    favorites?: boolean | BaseDocument$favoritesArgs<ExtArgs>
    _count?: boolean | BaseDocumentCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["baseDocument"]>

  export type BaseDocumentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    title?: boolean
    type?: boolean
    userId?: boolean
    thumbnailUrl?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    isPublic?: boolean
    documentType?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["baseDocument"]>

  export type BaseDocumentSelectScalar = {
    id?: boolean
    title?: boolean
    type?: boolean
    userId?: boolean
    thumbnailUrl?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    isPublic?: boolean
    documentType?: boolean
  }

  export type BaseDocumentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    presentation?: boolean | BaseDocument$presentationArgs<ExtArgs>
    favorites?: boolean | BaseDocument$favoritesArgs<ExtArgs>
    _count?: boolean | BaseDocumentCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type BaseDocumentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $BaseDocumentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "BaseDocument"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      presentation: Prisma.$PresentationPayload<ExtArgs> | null
      favorites: Prisma.$FavoriteDocumentPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      title: string
      type: $Enums.DocumentType
      userId: string
      thumbnailUrl: string | null
      createdAt: Date
      updatedAt: Date
      isPublic: boolean
      documentType: string
    }, ExtArgs["result"]["baseDocument"]>
    composites: {}
  }

  type BaseDocumentGetPayload<S extends boolean | null | undefined | BaseDocumentDefaultArgs> = $Result.GetResult<Prisma.$BaseDocumentPayload, S>

  type BaseDocumentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<BaseDocumentFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: BaseDocumentCountAggregateInputType | true
    }

  export interface BaseDocumentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['BaseDocument'], meta: { name: 'BaseDocument' } }
    /**
     * Find zero or one BaseDocument that matches the filter.
     * @param {BaseDocumentFindUniqueArgs} args - Arguments to find a BaseDocument
     * @example
     * // Get one BaseDocument
     * const baseDocument = await prisma.baseDocument.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends BaseDocumentFindUniqueArgs>(args: SelectSubset<T, BaseDocumentFindUniqueArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one BaseDocument that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {BaseDocumentFindUniqueOrThrowArgs} args - Arguments to find a BaseDocument
     * @example
     * // Get one BaseDocument
     * const baseDocument = await prisma.baseDocument.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends BaseDocumentFindUniqueOrThrowArgs>(args: SelectSubset<T, BaseDocumentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first BaseDocument that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentFindFirstArgs} args - Arguments to find a BaseDocument
     * @example
     * // Get one BaseDocument
     * const baseDocument = await prisma.baseDocument.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends BaseDocumentFindFirstArgs>(args?: SelectSubset<T, BaseDocumentFindFirstArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first BaseDocument that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentFindFirstOrThrowArgs} args - Arguments to find a BaseDocument
     * @example
     * // Get one BaseDocument
     * const baseDocument = await prisma.baseDocument.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends BaseDocumentFindFirstOrThrowArgs>(args?: SelectSubset<T, BaseDocumentFindFirstOrThrowArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more BaseDocuments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all BaseDocuments
     * const baseDocuments = await prisma.baseDocument.findMany()
     * 
     * // Get first 10 BaseDocuments
     * const baseDocuments = await prisma.baseDocument.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const baseDocumentWithIdOnly = await prisma.baseDocument.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends BaseDocumentFindManyArgs>(args?: SelectSubset<T, BaseDocumentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a BaseDocument.
     * @param {BaseDocumentCreateArgs} args - Arguments to create a BaseDocument.
     * @example
     * // Create one BaseDocument
     * const BaseDocument = await prisma.baseDocument.create({
     *   data: {
     *     // ... data to create a BaseDocument
     *   }
     * })
     * 
     */
    create<T extends BaseDocumentCreateArgs>(args: SelectSubset<T, BaseDocumentCreateArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many BaseDocuments.
     * @param {BaseDocumentCreateManyArgs} args - Arguments to create many BaseDocuments.
     * @example
     * // Create many BaseDocuments
     * const baseDocument = await prisma.baseDocument.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends BaseDocumentCreateManyArgs>(args?: SelectSubset<T, BaseDocumentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many BaseDocuments and returns the data saved in the database.
     * @param {BaseDocumentCreateManyAndReturnArgs} args - Arguments to create many BaseDocuments.
     * @example
     * // Create many BaseDocuments
     * const baseDocument = await prisma.baseDocument.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many BaseDocuments and only return the `id`
     * const baseDocumentWithIdOnly = await prisma.baseDocument.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends BaseDocumentCreateManyAndReturnArgs>(args?: SelectSubset<T, BaseDocumentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a BaseDocument.
     * @param {BaseDocumentDeleteArgs} args - Arguments to delete one BaseDocument.
     * @example
     * // Delete one BaseDocument
     * const BaseDocument = await prisma.baseDocument.delete({
     *   where: {
     *     // ... filter to delete one BaseDocument
     *   }
     * })
     * 
     */
    delete<T extends BaseDocumentDeleteArgs>(args: SelectSubset<T, BaseDocumentDeleteArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one BaseDocument.
     * @param {BaseDocumentUpdateArgs} args - Arguments to update one BaseDocument.
     * @example
     * // Update one BaseDocument
     * const baseDocument = await prisma.baseDocument.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends BaseDocumentUpdateArgs>(args: SelectSubset<T, BaseDocumentUpdateArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more BaseDocuments.
     * @param {BaseDocumentDeleteManyArgs} args - Arguments to filter BaseDocuments to delete.
     * @example
     * // Delete a few BaseDocuments
     * const { count } = await prisma.baseDocument.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends BaseDocumentDeleteManyArgs>(args?: SelectSubset<T, BaseDocumentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more BaseDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many BaseDocuments
     * const baseDocument = await prisma.baseDocument.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends BaseDocumentUpdateManyArgs>(args: SelectSubset<T, BaseDocumentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one BaseDocument.
     * @param {BaseDocumentUpsertArgs} args - Arguments to update or create a BaseDocument.
     * @example
     * // Update or create a BaseDocument
     * const baseDocument = await prisma.baseDocument.upsert({
     *   create: {
     *     // ... data to create a BaseDocument
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the BaseDocument we want to update
     *   }
     * })
     */
    upsert<T extends BaseDocumentUpsertArgs>(args: SelectSubset<T, BaseDocumentUpsertArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of BaseDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentCountArgs} args - Arguments to filter BaseDocuments to count.
     * @example
     * // Count the number of BaseDocuments
     * const count = await prisma.baseDocument.count({
     *   where: {
     *     // ... the filter for the BaseDocuments we want to count
     *   }
     * })
    **/
    count<T extends BaseDocumentCountArgs>(
      args?: Subset<T, BaseDocumentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], BaseDocumentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a BaseDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends BaseDocumentAggregateArgs>(args: Subset<T, BaseDocumentAggregateArgs>): Prisma.PrismaPromise<GetBaseDocumentAggregateType<T>>

    /**
     * Group by BaseDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {BaseDocumentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends BaseDocumentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: BaseDocumentGroupByArgs['orderBy'] }
        : { orderBy?: BaseDocumentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, BaseDocumentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetBaseDocumentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the BaseDocument model
   */
  readonly fields: BaseDocumentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for BaseDocument.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__BaseDocumentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    presentation<T extends BaseDocument$presentationArgs<ExtArgs> = {}>(args?: Subset<T, BaseDocument$presentationArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    favorites<T extends BaseDocument$favoritesArgs<ExtArgs> = {}>(args?: Subset<T, BaseDocument$favoritesArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the BaseDocument model
   */ 
  interface BaseDocumentFieldRefs {
    readonly id: FieldRef<"BaseDocument", 'String'>
    readonly title: FieldRef<"BaseDocument", 'String'>
    readonly type: FieldRef<"BaseDocument", 'DocumentType'>
    readonly userId: FieldRef<"BaseDocument", 'String'>
    readonly thumbnailUrl: FieldRef<"BaseDocument", 'String'>
    readonly createdAt: FieldRef<"BaseDocument", 'DateTime'>
    readonly updatedAt: FieldRef<"BaseDocument", 'DateTime'>
    readonly isPublic: FieldRef<"BaseDocument", 'Boolean'>
    readonly documentType: FieldRef<"BaseDocument", 'String'>
  }
    

  // Custom InputTypes
  /**
   * BaseDocument findUnique
   */
  export type BaseDocumentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter, which BaseDocument to fetch.
     */
    where: BaseDocumentWhereUniqueInput
  }

  /**
   * BaseDocument findUniqueOrThrow
   */
  export type BaseDocumentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter, which BaseDocument to fetch.
     */
    where: BaseDocumentWhereUniqueInput
  }

  /**
   * BaseDocument findFirst
   */
  export type BaseDocumentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter, which BaseDocument to fetch.
     */
    where?: BaseDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of BaseDocuments to fetch.
     */
    orderBy?: BaseDocumentOrderByWithRelationInput | BaseDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for BaseDocuments.
     */
    cursor?: BaseDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` BaseDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` BaseDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of BaseDocuments.
     */
    distinct?: BaseDocumentScalarFieldEnum | BaseDocumentScalarFieldEnum[]
  }

  /**
   * BaseDocument findFirstOrThrow
   */
  export type BaseDocumentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter, which BaseDocument to fetch.
     */
    where?: BaseDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of BaseDocuments to fetch.
     */
    orderBy?: BaseDocumentOrderByWithRelationInput | BaseDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for BaseDocuments.
     */
    cursor?: BaseDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` BaseDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` BaseDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of BaseDocuments.
     */
    distinct?: BaseDocumentScalarFieldEnum | BaseDocumentScalarFieldEnum[]
  }

  /**
   * BaseDocument findMany
   */
  export type BaseDocumentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter, which BaseDocuments to fetch.
     */
    where?: BaseDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of BaseDocuments to fetch.
     */
    orderBy?: BaseDocumentOrderByWithRelationInput | BaseDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing BaseDocuments.
     */
    cursor?: BaseDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` BaseDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` BaseDocuments.
     */
    skip?: number
    distinct?: BaseDocumentScalarFieldEnum | BaseDocumentScalarFieldEnum[]
  }

  /**
   * BaseDocument create
   */
  export type BaseDocumentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * The data needed to create a BaseDocument.
     */
    data: XOR<BaseDocumentCreateInput, BaseDocumentUncheckedCreateInput>
  }

  /**
   * BaseDocument createMany
   */
  export type BaseDocumentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many BaseDocuments.
     */
    data: BaseDocumentCreateManyInput | BaseDocumentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * BaseDocument createManyAndReturn
   */
  export type BaseDocumentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many BaseDocuments.
     */
    data: BaseDocumentCreateManyInput | BaseDocumentCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * BaseDocument update
   */
  export type BaseDocumentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * The data needed to update a BaseDocument.
     */
    data: XOR<BaseDocumentUpdateInput, BaseDocumentUncheckedUpdateInput>
    /**
     * Choose, which BaseDocument to update.
     */
    where: BaseDocumentWhereUniqueInput
  }

  /**
   * BaseDocument updateMany
   */
  export type BaseDocumentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update BaseDocuments.
     */
    data: XOR<BaseDocumentUpdateManyMutationInput, BaseDocumentUncheckedUpdateManyInput>
    /**
     * Filter which BaseDocuments to update
     */
    where?: BaseDocumentWhereInput
  }

  /**
   * BaseDocument upsert
   */
  export type BaseDocumentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * The filter to search for the BaseDocument to update in case it exists.
     */
    where: BaseDocumentWhereUniqueInput
    /**
     * In case the BaseDocument found by the `where` argument doesn't exist, create a new BaseDocument with this data.
     */
    create: XOR<BaseDocumentCreateInput, BaseDocumentUncheckedCreateInput>
    /**
     * In case the BaseDocument was found with the provided `where` argument, update it with this data.
     */
    update: XOR<BaseDocumentUpdateInput, BaseDocumentUncheckedUpdateInput>
  }

  /**
   * BaseDocument delete
   */
  export type BaseDocumentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
    /**
     * Filter which BaseDocument to delete.
     */
    where: BaseDocumentWhereUniqueInput
  }

  /**
   * BaseDocument deleteMany
   */
  export type BaseDocumentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which BaseDocuments to delete
     */
    where?: BaseDocumentWhereInput
  }

  /**
   * BaseDocument.presentation
   */
  export type BaseDocument$presentationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    where?: PresentationWhereInput
  }

  /**
   * BaseDocument.favorites
   */
  export type BaseDocument$favoritesArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    where?: FavoriteDocumentWhereInput
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    cursor?: FavoriteDocumentWhereUniqueInput
    take?: number
    skip?: number
    distinct?: FavoriteDocumentScalarFieldEnum | FavoriteDocumentScalarFieldEnum[]
  }

  /**
   * BaseDocument without action
   */
  export type BaseDocumentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the BaseDocument
     */
    select?: BaseDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: BaseDocumentInclude<ExtArgs> | null
  }


  /**
   * Model Presentation
   */

  export type AggregatePresentation = {
    _count: PresentationCountAggregateOutputType | null
    _min: PresentationMinAggregateOutputType | null
    _max: PresentationMaxAggregateOutputType | null
  }

  export type PresentationMinAggregateOutputType = {
    id: string | null
    theme: string | null
    imageModel: string | null
    presentationStyle: string | null
    language: string | null
    templateId: string | null
    customThemeId: string | null
  }

  export type PresentationMaxAggregateOutputType = {
    id: string | null
    theme: string | null
    imageModel: string | null
    presentationStyle: string | null
    language: string | null
    templateId: string | null
    customThemeId: string | null
  }

  export type PresentationCountAggregateOutputType = {
    id: number
    content: number
    theme: number
    imageModel: number
    presentationStyle: number
    language: number
    outline: number
    templateId: number
    customThemeId: number
    _all: number
  }


  export type PresentationMinAggregateInputType = {
    id?: true
    theme?: true
    imageModel?: true
    presentationStyle?: true
    language?: true
    templateId?: true
    customThemeId?: true
  }

  export type PresentationMaxAggregateInputType = {
    id?: true
    theme?: true
    imageModel?: true
    presentationStyle?: true
    language?: true
    templateId?: true
    customThemeId?: true
  }

  export type PresentationCountAggregateInputType = {
    id?: true
    content?: true
    theme?: true
    imageModel?: true
    presentationStyle?: true
    language?: true
    outline?: true
    templateId?: true
    customThemeId?: true
    _all?: true
  }

  export type PresentationAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Presentation to aggregate.
     */
    where?: PresentationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Presentations to fetch.
     */
    orderBy?: PresentationOrderByWithRelationInput | PresentationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: PresentationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Presentations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Presentations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned Presentations
    **/
    _count?: true | PresentationCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: PresentationMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: PresentationMaxAggregateInputType
  }

  export type GetPresentationAggregateType<T extends PresentationAggregateArgs> = {
        [P in keyof T & keyof AggregatePresentation]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregatePresentation[P]>
      : GetScalarType<T[P], AggregatePresentation[P]>
  }




  export type PresentationGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: PresentationWhereInput
    orderBy?: PresentationOrderByWithAggregationInput | PresentationOrderByWithAggregationInput[]
    by: PresentationScalarFieldEnum[] | PresentationScalarFieldEnum
    having?: PresentationScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: PresentationCountAggregateInputType | true
    _min?: PresentationMinAggregateInputType
    _max?: PresentationMaxAggregateInputType
  }

  export type PresentationGroupByOutputType = {
    id: string
    content: JsonValue
    theme: string
    imageModel: string | null
    presentationStyle: string | null
    language: string | null
    outline: string[]
    templateId: string | null
    customThemeId: string | null
    _count: PresentationCountAggregateOutputType | null
    _min: PresentationMinAggregateOutputType | null
    _max: PresentationMaxAggregateOutputType | null
  }

  type GetPresentationGroupByPayload<T extends PresentationGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<PresentationGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof PresentationGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], PresentationGroupByOutputType[P]>
            : GetScalarType<T[P], PresentationGroupByOutputType[P]>
        }
      >
    >


  export type PresentationSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    theme?: boolean
    imageModel?: boolean
    presentationStyle?: boolean
    language?: boolean
    outline?: boolean
    templateId?: boolean
    customThemeId?: boolean
    base?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    customTheme?: boolean | Presentation$customThemeArgs<ExtArgs>
  }, ExtArgs["result"]["presentation"]>

  export type PresentationSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    content?: boolean
    theme?: boolean
    imageModel?: boolean
    presentationStyle?: boolean
    language?: boolean
    outline?: boolean
    templateId?: boolean
    customThemeId?: boolean
    base?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    customTheme?: boolean | Presentation$customThemeArgs<ExtArgs>
  }, ExtArgs["result"]["presentation"]>

  export type PresentationSelectScalar = {
    id?: boolean
    content?: boolean
    theme?: boolean
    imageModel?: boolean
    presentationStyle?: boolean
    language?: boolean
    outline?: boolean
    templateId?: boolean
    customThemeId?: boolean
  }

  export type PresentationInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    base?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    customTheme?: boolean | Presentation$customThemeArgs<ExtArgs>
  }
  export type PresentationIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    base?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    customTheme?: boolean | Presentation$customThemeArgs<ExtArgs>
  }

  export type $PresentationPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "Presentation"
    objects: {
      base: Prisma.$BaseDocumentPayload<ExtArgs>
      customTheme: Prisma.$CustomThemePayload<ExtArgs> | null
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      content: Prisma.JsonValue
      theme: string
      imageModel: string | null
      presentationStyle: string | null
      language: string | null
      outline: string[]
      templateId: string | null
      customThemeId: string | null
    }, ExtArgs["result"]["presentation"]>
    composites: {}
  }

  type PresentationGetPayload<S extends boolean | null | undefined | PresentationDefaultArgs> = $Result.GetResult<Prisma.$PresentationPayload, S>

  type PresentationCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<PresentationFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: PresentationCountAggregateInputType | true
    }

  export interface PresentationDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['Presentation'], meta: { name: 'Presentation' } }
    /**
     * Find zero or one Presentation that matches the filter.
     * @param {PresentationFindUniqueArgs} args - Arguments to find a Presentation
     * @example
     * // Get one Presentation
     * const presentation = await prisma.presentation.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends PresentationFindUniqueArgs>(args: SelectSubset<T, PresentationFindUniqueArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one Presentation that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {PresentationFindUniqueOrThrowArgs} args - Arguments to find a Presentation
     * @example
     * // Get one Presentation
     * const presentation = await prisma.presentation.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends PresentationFindUniqueOrThrowArgs>(args: SelectSubset<T, PresentationFindUniqueOrThrowArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first Presentation that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationFindFirstArgs} args - Arguments to find a Presentation
     * @example
     * // Get one Presentation
     * const presentation = await prisma.presentation.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends PresentationFindFirstArgs>(args?: SelectSubset<T, PresentationFindFirstArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first Presentation that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationFindFirstOrThrowArgs} args - Arguments to find a Presentation
     * @example
     * // Get one Presentation
     * const presentation = await prisma.presentation.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends PresentationFindFirstOrThrowArgs>(args?: SelectSubset<T, PresentationFindFirstOrThrowArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more Presentations that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all Presentations
     * const presentations = await prisma.presentation.findMany()
     * 
     * // Get first 10 Presentations
     * const presentations = await prisma.presentation.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const presentationWithIdOnly = await prisma.presentation.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends PresentationFindManyArgs>(args?: SelectSubset<T, PresentationFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a Presentation.
     * @param {PresentationCreateArgs} args - Arguments to create a Presentation.
     * @example
     * // Create one Presentation
     * const Presentation = await prisma.presentation.create({
     *   data: {
     *     // ... data to create a Presentation
     *   }
     * })
     * 
     */
    create<T extends PresentationCreateArgs>(args: SelectSubset<T, PresentationCreateArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many Presentations.
     * @param {PresentationCreateManyArgs} args - Arguments to create many Presentations.
     * @example
     * // Create many Presentations
     * const presentation = await prisma.presentation.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends PresentationCreateManyArgs>(args?: SelectSubset<T, PresentationCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many Presentations and returns the data saved in the database.
     * @param {PresentationCreateManyAndReturnArgs} args - Arguments to create many Presentations.
     * @example
     * // Create many Presentations
     * const presentation = await prisma.presentation.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many Presentations and only return the `id`
     * const presentationWithIdOnly = await prisma.presentation.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends PresentationCreateManyAndReturnArgs>(args?: SelectSubset<T, PresentationCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a Presentation.
     * @param {PresentationDeleteArgs} args - Arguments to delete one Presentation.
     * @example
     * // Delete one Presentation
     * const Presentation = await prisma.presentation.delete({
     *   where: {
     *     // ... filter to delete one Presentation
     *   }
     * })
     * 
     */
    delete<T extends PresentationDeleteArgs>(args: SelectSubset<T, PresentationDeleteArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one Presentation.
     * @param {PresentationUpdateArgs} args - Arguments to update one Presentation.
     * @example
     * // Update one Presentation
     * const presentation = await prisma.presentation.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends PresentationUpdateArgs>(args: SelectSubset<T, PresentationUpdateArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more Presentations.
     * @param {PresentationDeleteManyArgs} args - Arguments to filter Presentations to delete.
     * @example
     * // Delete a few Presentations
     * const { count } = await prisma.presentation.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends PresentationDeleteManyArgs>(args?: SelectSubset<T, PresentationDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more Presentations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many Presentations
     * const presentation = await prisma.presentation.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends PresentationUpdateManyArgs>(args: SelectSubset<T, PresentationUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one Presentation.
     * @param {PresentationUpsertArgs} args - Arguments to update or create a Presentation.
     * @example
     * // Update or create a Presentation
     * const presentation = await prisma.presentation.upsert({
     *   create: {
     *     // ... data to create a Presentation
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the Presentation we want to update
     *   }
     * })
     */
    upsert<T extends PresentationUpsertArgs>(args: SelectSubset<T, PresentationUpsertArgs<ExtArgs>>): Prisma__PresentationClient<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of Presentations.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationCountArgs} args - Arguments to filter Presentations to count.
     * @example
     * // Count the number of Presentations
     * const count = await prisma.presentation.count({
     *   where: {
     *     // ... the filter for the Presentations we want to count
     *   }
     * })
    **/
    count<T extends PresentationCountArgs>(
      args?: Subset<T, PresentationCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], PresentationCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a Presentation.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends PresentationAggregateArgs>(args: Subset<T, PresentationAggregateArgs>): Prisma.PrismaPromise<GetPresentationAggregateType<T>>

    /**
     * Group by Presentation.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {PresentationGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends PresentationGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: PresentationGroupByArgs['orderBy'] }
        : { orderBy?: PresentationGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, PresentationGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetPresentationGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the Presentation model
   */
  readonly fields: PresentationFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for Presentation.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__PresentationClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    base<T extends BaseDocumentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, BaseDocumentDefaultArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    customTheme<T extends Presentation$customThemeArgs<ExtArgs> = {}>(args?: Subset<T, Presentation$customThemeArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findUniqueOrThrow"> | null, null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the Presentation model
   */ 
  interface PresentationFieldRefs {
    readonly id: FieldRef<"Presentation", 'String'>
    readonly content: FieldRef<"Presentation", 'Json'>
    readonly theme: FieldRef<"Presentation", 'String'>
    readonly imageModel: FieldRef<"Presentation", 'String'>
    readonly presentationStyle: FieldRef<"Presentation", 'String'>
    readonly language: FieldRef<"Presentation", 'String'>
    readonly outline: FieldRef<"Presentation", 'String[]'>
    readonly templateId: FieldRef<"Presentation", 'String'>
    readonly customThemeId: FieldRef<"Presentation", 'String'>
  }
    

  // Custom InputTypes
  /**
   * Presentation findUnique
   */
  export type PresentationFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter, which Presentation to fetch.
     */
    where: PresentationWhereUniqueInput
  }

  /**
   * Presentation findUniqueOrThrow
   */
  export type PresentationFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter, which Presentation to fetch.
     */
    where: PresentationWhereUniqueInput
  }

  /**
   * Presentation findFirst
   */
  export type PresentationFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter, which Presentation to fetch.
     */
    where?: PresentationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Presentations to fetch.
     */
    orderBy?: PresentationOrderByWithRelationInput | PresentationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Presentations.
     */
    cursor?: PresentationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Presentations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Presentations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Presentations.
     */
    distinct?: PresentationScalarFieldEnum | PresentationScalarFieldEnum[]
  }

  /**
   * Presentation findFirstOrThrow
   */
  export type PresentationFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter, which Presentation to fetch.
     */
    where?: PresentationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Presentations to fetch.
     */
    orderBy?: PresentationOrderByWithRelationInput | PresentationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for Presentations.
     */
    cursor?: PresentationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Presentations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Presentations.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of Presentations.
     */
    distinct?: PresentationScalarFieldEnum | PresentationScalarFieldEnum[]
  }

  /**
   * Presentation findMany
   */
  export type PresentationFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter, which Presentations to fetch.
     */
    where?: PresentationWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of Presentations to fetch.
     */
    orderBy?: PresentationOrderByWithRelationInput | PresentationOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing Presentations.
     */
    cursor?: PresentationWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` Presentations from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` Presentations.
     */
    skip?: number
    distinct?: PresentationScalarFieldEnum | PresentationScalarFieldEnum[]
  }

  /**
   * Presentation create
   */
  export type PresentationCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * The data needed to create a Presentation.
     */
    data: XOR<PresentationCreateInput, PresentationUncheckedCreateInput>
  }

  /**
   * Presentation createMany
   */
  export type PresentationCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many Presentations.
     */
    data: PresentationCreateManyInput | PresentationCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * Presentation createManyAndReturn
   */
  export type PresentationCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many Presentations.
     */
    data: PresentationCreateManyInput | PresentationCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * Presentation update
   */
  export type PresentationUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * The data needed to update a Presentation.
     */
    data: XOR<PresentationUpdateInput, PresentationUncheckedUpdateInput>
    /**
     * Choose, which Presentation to update.
     */
    where: PresentationWhereUniqueInput
  }

  /**
   * Presentation updateMany
   */
  export type PresentationUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update Presentations.
     */
    data: XOR<PresentationUpdateManyMutationInput, PresentationUncheckedUpdateManyInput>
    /**
     * Filter which Presentations to update
     */
    where?: PresentationWhereInput
  }

  /**
   * Presentation upsert
   */
  export type PresentationUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * The filter to search for the Presentation to update in case it exists.
     */
    where: PresentationWhereUniqueInput
    /**
     * In case the Presentation found by the `where` argument doesn't exist, create a new Presentation with this data.
     */
    create: XOR<PresentationCreateInput, PresentationUncheckedCreateInput>
    /**
     * In case the Presentation was found with the provided `where` argument, update it with this data.
     */
    update: XOR<PresentationUpdateInput, PresentationUncheckedUpdateInput>
  }

  /**
   * Presentation delete
   */
  export type PresentationDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    /**
     * Filter which Presentation to delete.
     */
    where: PresentationWhereUniqueInput
  }

  /**
   * Presentation deleteMany
   */
  export type PresentationDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which Presentations to delete
     */
    where?: PresentationWhereInput
  }

  /**
   * Presentation.customTheme
   */
  export type Presentation$customThemeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    where?: CustomThemeWhereInput
  }

  /**
   * Presentation without action
   */
  export type PresentationDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
  }


  /**
   * Model CustomTheme
   */

  export type AggregateCustomTheme = {
    _count: CustomThemeCountAggregateOutputType | null
    _min: CustomThemeMinAggregateOutputType | null
    _max: CustomThemeMaxAggregateOutputType | null
  }

  export type CustomThemeMinAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    userId: string | null
    logoUrl: string | null
    isPublic: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CustomThemeMaxAggregateOutputType = {
    id: string | null
    name: string | null
    description: string | null
    userId: string | null
    logoUrl: string | null
    isPublic: boolean | null
    createdAt: Date | null
    updatedAt: Date | null
  }

  export type CustomThemeCountAggregateOutputType = {
    id: number
    name: number
    description: number
    userId: number
    logoUrl: number
    isPublic: number
    createdAt: number
    updatedAt: number
    themeData: number
    _all: number
  }


  export type CustomThemeMinAggregateInputType = {
    id?: true
    name?: true
    description?: true
    userId?: true
    logoUrl?: true
    isPublic?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CustomThemeMaxAggregateInputType = {
    id?: true
    name?: true
    description?: true
    userId?: true
    logoUrl?: true
    isPublic?: true
    createdAt?: true
    updatedAt?: true
  }

  export type CustomThemeCountAggregateInputType = {
    id?: true
    name?: true
    description?: true
    userId?: true
    logoUrl?: true
    isPublic?: true
    createdAt?: true
    updatedAt?: true
    themeData?: true
    _all?: true
  }

  export type CustomThemeAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CustomTheme to aggregate.
     */
    where?: CustomThemeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CustomThemes to fetch.
     */
    orderBy?: CustomThemeOrderByWithRelationInput | CustomThemeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: CustomThemeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CustomThemes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CustomThemes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned CustomThemes
    **/
    _count?: true | CustomThemeCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: CustomThemeMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: CustomThemeMaxAggregateInputType
  }

  export type GetCustomThemeAggregateType<T extends CustomThemeAggregateArgs> = {
        [P in keyof T & keyof AggregateCustomTheme]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateCustomTheme[P]>
      : GetScalarType<T[P], AggregateCustomTheme[P]>
  }




  export type CustomThemeGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: CustomThemeWhereInput
    orderBy?: CustomThemeOrderByWithAggregationInput | CustomThemeOrderByWithAggregationInput[]
    by: CustomThemeScalarFieldEnum[] | CustomThemeScalarFieldEnum
    having?: CustomThemeScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: CustomThemeCountAggregateInputType | true
    _min?: CustomThemeMinAggregateInputType
    _max?: CustomThemeMaxAggregateInputType
  }

  export type CustomThemeGroupByOutputType = {
    id: string
    name: string
    description: string | null
    userId: string
    logoUrl: string | null
    isPublic: boolean
    createdAt: Date
    updatedAt: Date
    themeData: JsonValue
    _count: CustomThemeCountAggregateOutputType | null
    _min: CustomThemeMinAggregateOutputType | null
    _max: CustomThemeMaxAggregateOutputType | null
  }

  type GetCustomThemeGroupByPayload<T extends CustomThemeGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<CustomThemeGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof CustomThemeGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], CustomThemeGroupByOutputType[P]>
            : GetScalarType<T[P], CustomThemeGroupByOutputType[P]>
        }
      >
    >


  export type CustomThemeSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    userId?: boolean
    logoUrl?: boolean
    isPublic?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    themeData?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
    presentations?: boolean | CustomTheme$presentationsArgs<ExtArgs>
    _count?: boolean | CustomThemeCountOutputTypeDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["customTheme"]>

  export type CustomThemeSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    name?: boolean
    description?: boolean
    userId?: boolean
    logoUrl?: boolean
    isPublic?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    themeData?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["customTheme"]>

  export type CustomThemeSelectScalar = {
    id?: boolean
    name?: boolean
    description?: boolean
    userId?: boolean
    logoUrl?: boolean
    isPublic?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    themeData?: boolean
  }

  export type CustomThemeInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
    presentations?: boolean | CustomTheme$presentationsArgs<ExtArgs>
    _count?: boolean | CustomThemeCountOutputTypeDefaultArgs<ExtArgs>
  }
  export type CustomThemeIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $CustomThemePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "CustomTheme"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
      presentations: Prisma.$PresentationPayload<ExtArgs>[]
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      name: string
      description: string | null
      userId: string
      logoUrl: string | null
      isPublic: boolean
      createdAt: Date
      updatedAt: Date
      themeData: Prisma.JsonValue
    }, ExtArgs["result"]["customTheme"]>
    composites: {}
  }

  type CustomThemeGetPayload<S extends boolean | null | undefined | CustomThemeDefaultArgs> = $Result.GetResult<Prisma.$CustomThemePayload, S>

  type CustomThemeCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<CustomThemeFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: CustomThemeCountAggregateInputType | true
    }

  export interface CustomThemeDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['CustomTheme'], meta: { name: 'CustomTheme' } }
    /**
     * Find zero or one CustomTheme that matches the filter.
     * @param {CustomThemeFindUniqueArgs} args - Arguments to find a CustomTheme
     * @example
     * // Get one CustomTheme
     * const customTheme = await prisma.customTheme.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends CustomThemeFindUniqueArgs>(args: SelectSubset<T, CustomThemeFindUniqueArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one CustomTheme that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {CustomThemeFindUniqueOrThrowArgs} args - Arguments to find a CustomTheme
     * @example
     * // Get one CustomTheme
     * const customTheme = await prisma.customTheme.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends CustomThemeFindUniqueOrThrowArgs>(args: SelectSubset<T, CustomThemeFindUniqueOrThrowArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first CustomTheme that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeFindFirstArgs} args - Arguments to find a CustomTheme
     * @example
     * // Get one CustomTheme
     * const customTheme = await prisma.customTheme.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends CustomThemeFindFirstArgs>(args?: SelectSubset<T, CustomThemeFindFirstArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first CustomTheme that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeFindFirstOrThrowArgs} args - Arguments to find a CustomTheme
     * @example
     * // Get one CustomTheme
     * const customTheme = await prisma.customTheme.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends CustomThemeFindFirstOrThrowArgs>(args?: SelectSubset<T, CustomThemeFindFirstOrThrowArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more CustomThemes that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all CustomThemes
     * const customThemes = await prisma.customTheme.findMany()
     * 
     * // Get first 10 CustomThemes
     * const customThemes = await prisma.customTheme.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const customThemeWithIdOnly = await prisma.customTheme.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends CustomThemeFindManyArgs>(args?: SelectSubset<T, CustomThemeFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "findMany">>

    /**
     * Create a CustomTheme.
     * @param {CustomThemeCreateArgs} args - Arguments to create a CustomTheme.
     * @example
     * // Create one CustomTheme
     * const CustomTheme = await prisma.customTheme.create({
     *   data: {
     *     // ... data to create a CustomTheme
     *   }
     * })
     * 
     */
    create<T extends CustomThemeCreateArgs>(args: SelectSubset<T, CustomThemeCreateArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many CustomThemes.
     * @param {CustomThemeCreateManyArgs} args - Arguments to create many CustomThemes.
     * @example
     * // Create many CustomThemes
     * const customTheme = await prisma.customTheme.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends CustomThemeCreateManyArgs>(args?: SelectSubset<T, CustomThemeCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many CustomThemes and returns the data saved in the database.
     * @param {CustomThemeCreateManyAndReturnArgs} args - Arguments to create many CustomThemes.
     * @example
     * // Create many CustomThemes
     * const customTheme = await prisma.customTheme.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many CustomThemes and only return the `id`
     * const customThemeWithIdOnly = await prisma.customTheme.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends CustomThemeCreateManyAndReturnArgs>(args?: SelectSubset<T, CustomThemeCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a CustomTheme.
     * @param {CustomThemeDeleteArgs} args - Arguments to delete one CustomTheme.
     * @example
     * // Delete one CustomTheme
     * const CustomTheme = await prisma.customTheme.delete({
     *   where: {
     *     // ... filter to delete one CustomTheme
     *   }
     * })
     * 
     */
    delete<T extends CustomThemeDeleteArgs>(args: SelectSubset<T, CustomThemeDeleteArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one CustomTheme.
     * @param {CustomThemeUpdateArgs} args - Arguments to update one CustomTheme.
     * @example
     * // Update one CustomTheme
     * const customTheme = await prisma.customTheme.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends CustomThemeUpdateArgs>(args: SelectSubset<T, CustomThemeUpdateArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more CustomThemes.
     * @param {CustomThemeDeleteManyArgs} args - Arguments to filter CustomThemes to delete.
     * @example
     * // Delete a few CustomThemes
     * const { count } = await prisma.customTheme.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends CustomThemeDeleteManyArgs>(args?: SelectSubset<T, CustomThemeDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more CustomThemes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many CustomThemes
     * const customTheme = await prisma.customTheme.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends CustomThemeUpdateManyArgs>(args: SelectSubset<T, CustomThemeUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one CustomTheme.
     * @param {CustomThemeUpsertArgs} args - Arguments to update or create a CustomTheme.
     * @example
     * // Update or create a CustomTheme
     * const customTheme = await prisma.customTheme.upsert({
     *   create: {
     *     // ... data to create a CustomTheme
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the CustomTheme we want to update
     *   }
     * })
     */
    upsert<T extends CustomThemeUpsertArgs>(args: SelectSubset<T, CustomThemeUpsertArgs<ExtArgs>>): Prisma__CustomThemeClient<$Result.GetResult<Prisma.$CustomThemePayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of CustomThemes.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeCountArgs} args - Arguments to filter CustomThemes to count.
     * @example
     * // Count the number of CustomThemes
     * const count = await prisma.customTheme.count({
     *   where: {
     *     // ... the filter for the CustomThemes we want to count
     *   }
     * })
    **/
    count<T extends CustomThemeCountArgs>(
      args?: Subset<T, CustomThemeCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], CustomThemeCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a CustomTheme.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends CustomThemeAggregateArgs>(args: Subset<T, CustomThemeAggregateArgs>): Prisma.PrismaPromise<GetCustomThemeAggregateType<T>>

    /**
     * Group by CustomTheme.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {CustomThemeGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends CustomThemeGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: CustomThemeGroupByArgs['orderBy'] }
        : { orderBy?: CustomThemeGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, CustomThemeGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetCustomThemeGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the CustomTheme model
   */
  readonly fields: CustomThemeFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for CustomTheme.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__CustomThemeClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    presentations<T extends CustomTheme$presentationsArgs<ExtArgs> = {}>(args?: Subset<T, CustomTheme$presentationsArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$PresentationPayload<ExtArgs>, T, "findMany"> | Null>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the CustomTheme model
   */ 
  interface CustomThemeFieldRefs {
    readonly id: FieldRef<"CustomTheme", 'String'>
    readonly name: FieldRef<"CustomTheme", 'String'>
    readonly description: FieldRef<"CustomTheme", 'String'>
    readonly userId: FieldRef<"CustomTheme", 'String'>
    readonly logoUrl: FieldRef<"CustomTheme", 'String'>
    readonly isPublic: FieldRef<"CustomTheme", 'Boolean'>
    readonly createdAt: FieldRef<"CustomTheme", 'DateTime'>
    readonly updatedAt: FieldRef<"CustomTheme", 'DateTime'>
    readonly themeData: FieldRef<"CustomTheme", 'Json'>
  }
    

  // Custom InputTypes
  /**
   * CustomTheme findUnique
   */
  export type CustomThemeFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter, which CustomTheme to fetch.
     */
    where: CustomThemeWhereUniqueInput
  }

  /**
   * CustomTheme findUniqueOrThrow
   */
  export type CustomThemeFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter, which CustomTheme to fetch.
     */
    where: CustomThemeWhereUniqueInput
  }

  /**
   * CustomTheme findFirst
   */
  export type CustomThemeFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter, which CustomTheme to fetch.
     */
    where?: CustomThemeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CustomThemes to fetch.
     */
    orderBy?: CustomThemeOrderByWithRelationInput | CustomThemeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CustomThemes.
     */
    cursor?: CustomThemeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CustomThemes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CustomThemes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CustomThemes.
     */
    distinct?: CustomThemeScalarFieldEnum | CustomThemeScalarFieldEnum[]
  }

  /**
   * CustomTheme findFirstOrThrow
   */
  export type CustomThemeFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter, which CustomTheme to fetch.
     */
    where?: CustomThemeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CustomThemes to fetch.
     */
    orderBy?: CustomThemeOrderByWithRelationInput | CustomThemeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for CustomThemes.
     */
    cursor?: CustomThemeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CustomThemes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CustomThemes.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of CustomThemes.
     */
    distinct?: CustomThemeScalarFieldEnum | CustomThemeScalarFieldEnum[]
  }

  /**
   * CustomTheme findMany
   */
  export type CustomThemeFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter, which CustomThemes to fetch.
     */
    where?: CustomThemeWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of CustomThemes to fetch.
     */
    orderBy?: CustomThemeOrderByWithRelationInput | CustomThemeOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing CustomThemes.
     */
    cursor?: CustomThemeWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` CustomThemes from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` CustomThemes.
     */
    skip?: number
    distinct?: CustomThemeScalarFieldEnum | CustomThemeScalarFieldEnum[]
  }

  /**
   * CustomTheme create
   */
  export type CustomThemeCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * The data needed to create a CustomTheme.
     */
    data: XOR<CustomThemeCreateInput, CustomThemeUncheckedCreateInput>
  }

  /**
   * CustomTheme createMany
   */
  export type CustomThemeCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many CustomThemes.
     */
    data: CustomThemeCreateManyInput | CustomThemeCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * CustomTheme createManyAndReturn
   */
  export type CustomThemeCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many CustomThemes.
     */
    data: CustomThemeCreateManyInput | CustomThemeCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * CustomTheme update
   */
  export type CustomThemeUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * The data needed to update a CustomTheme.
     */
    data: XOR<CustomThemeUpdateInput, CustomThemeUncheckedUpdateInput>
    /**
     * Choose, which CustomTheme to update.
     */
    where: CustomThemeWhereUniqueInput
  }

  /**
   * CustomTheme updateMany
   */
  export type CustomThemeUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update CustomThemes.
     */
    data: XOR<CustomThemeUpdateManyMutationInput, CustomThemeUncheckedUpdateManyInput>
    /**
     * Filter which CustomThemes to update
     */
    where?: CustomThemeWhereInput
  }

  /**
   * CustomTheme upsert
   */
  export type CustomThemeUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * The filter to search for the CustomTheme to update in case it exists.
     */
    where: CustomThemeWhereUniqueInput
    /**
     * In case the CustomTheme found by the `where` argument doesn't exist, create a new CustomTheme with this data.
     */
    create: XOR<CustomThemeCreateInput, CustomThemeUncheckedCreateInput>
    /**
     * In case the CustomTheme was found with the provided `where` argument, update it with this data.
     */
    update: XOR<CustomThemeUpdateInput, CustomThemeUncheckedUpdateInput>
  }

  /**
   * CustomTheme delete
   */
  export type CustomThemeDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
    /**
     * Filter which CustomTheme to delete.
     */
    where: CustomThemeWhereUniqueInput
  }

  /**
   * CustomTheme deleteMany
   */
  export type CustomThemeDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which CustomThemes to delete
     */
    where?: CustomThemeWhereInput
  }

  /**
   * CustomTheme.presentations
   */
  export type CustomTheme$presentationsArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the Presentation
     */
    select?: PresentationSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: PresentationInclude<ExtArgs> | null
    where?: PresentationWhereInput
    orderBy?: PresentationOrderByWithRelationInput | PresentationOrderByWithRelationInput[]
    cursor?: PresentationWhereUniqueInput
    take?: number
    skip?: number
    distinct?: PresentationScalarFieldEnum | PresentationScalarFieldEnum[]
  }

  /**
   * CustomTheme without action
   */
  export type CustomThemeDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the CustomTheme
     */
    select?: CustomThemeSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: CustomThemeInclude<ExtArgs> | null
  }


  /**
   * Model FavoriteDocument
   */

  export type AggregateFavoriteDocument = {
    _count: FavoriteDocumentCountAggregateOutputType | null
    _min: FavoriteDocumentMinAggregateOutputType | null
    _max: FavoriteDocumentMaxAggregateOutputType | null
  }

  export type FavoriteDocumentMinAggregateOutputType = {
    id: string | null
    documentId: string | null
    userId: string | null
  }

  export type FavoriteDocumentMaxAggregateOutputType = {
    id: string | null
    documentId: string | null
    userId: string | null
  }

  export type FavoriteDocumentCountAggregateOutputType = {
    id: number
    documentId: number
    userId: number
    _all: number
  }


  export type FavoriteDocumentMinAggregateInputType = {
    id?: true
    documentId?: true
    userId?: true
  }

  export type FavoriteDocumentMaxAggregateInputType = {
    id?: true
    documentId?: true
    userId?: true
  }

  export type FavoriteDocumentCountAggregateInputType = {
    id?: true
    documentId?: true
    userId?: true
    _all?: true
  }

  export type FavoriteDocumentAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which FavoriteDocument to aggregate.
     */
    where?: FavoriteDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FavoriteDocuments to fetch.
     */
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: FavoriteDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FavoriteDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FavoriteDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned FavoriteDocuments
    **/
    _count?: true | FavoriteDocumentCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: FavoriteDocumentMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: FavoriteDocumentMaxAggregateInputType
  }

  export type GetFavoriteDocumentAggregateType<T extends FavoriteDocumentAggregateArgs> = {
        [P in keyof T & keyof AggregateFavoriteDocument]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateFavoriteDocument[P]>
      : GetScalarType<T[P], AggregateFavoriteDocument[P]>
  }




  export type FavoriteDocumentGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: FavoriteDocumentWhereInput
    orderBy?: FavoriteDocumentOrderByWithAggregationInput | FavoriteDocumentOrderByWithAggregationInput[]
    by: FavoriteDocumentScalarFieldEnum[] | FavoriteDocumentScalarFieldEnum
    having?: FavoriteDocumentScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: FavoriteDocumentCountAggregateInputType | true
    _min?: FavoriteDocumentMinAggregateInputType
    _max?: FavoriteDocumentMaxAggregateInputType
  }

  export type FavoriteDocumentGroupByOutputType = {
    id: string
    documentId: string
    userId: string
    _count: FavoriteDocumentCountAggregateOutputType | null
    _min: FavoriteDocumentMinAggregateOutputType | null
    _max: FavoriteDocumentMaxAggregateOutputType | null
  }

  type GetFavoriteDocumentGroupByPayload<T extends FavoriteDocumentGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<FavoriteDocumentGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof FavoriteDocumentGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], FavoriteDocumentGroupByOutputType[P]>
            : GetScalarType<T[P], FavoriteDocumentGroupByOutputType[P]>
        }
      >
    >


  export type FavoriteDocumentSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    documentId?: boolean
    userId?: boolean
    document?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["favoriteDocument"]>

  export type FavoriteDocumentSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    documentId?: boolean
    userId?: boolean
    document?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["favoriteDocument"]>

  export type FavoriteDocumentSelectScalar = {
    id?: boolean
    documentId?: boolean
    userId?: boolean
  }

  export type FavoriteDocumentInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    document?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type FavoriteDocumentIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    document?: boolean | BaseDocumentDefaultArgs<ExtArgs>
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $FavoriteDocumentPayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "FavoriteDocument"
    objects: {
      document: Prisma.$BaseDocumentPayload<ExtArgs>
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      documentId: string
      userId: string
    }, ExtArgs["result"]["favoriteDocument"]>
    composites: {}
  }

  type FavoriteDocumentGetPayload<S extends boolean | null | undefined | FavoriteDocumentDefaultArgs> = $Result.GetResult<Prisma.$FavoriteDocumentPayload, S>

  type FavoriteDocumentCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<FavoriteDocumentFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: FavoriteDocumentCountAggregateInputType | true
    }

  export interface FavoriteDocumentDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['FavoriteDocument'], meta: { name: 'FavoriteDocument' } }
    /**
     * Find zero or one FavoriteDocument that matches the filter.
     * @param {FavoriteDocumentFindUniqueArgs} args - Arguments to find a FavoriteDocument
     * @example
     * // Get one FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends FavoriteDocumentFindUniqueArgs>(args: SelectSubset<T, FavoriteDocumentFindUniqueArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one FavoriteDocument that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {FavoriteDocumentFindUniqueOrThrowArgs} args - Arguments to find a FavoriteDocument
     * @example
     * // Get one FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends FavoriteDocumentFindUniqueOrThrowArgs>(args: SelectSubset<T, FavoriteDocumentFindUniqueOrThrowArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first FavoriteDocument that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentFindFirstArgs} args - Arguments to find a FavoriteDocument
     * @example
     * // Get one FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends FavoriteDocumentFindFirstArgs>(args?: SelectSubset<T, FavoriteDocumentFindFirstArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first FavoriteDocument that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentFindFirstOrThrowArgs} args - Arguments to find a FavoriteDocument
     * @example
     * // Get one FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends FavoriteDocumentFindFirstOrThrowArgs>(args?: SelectSubset<T, FavoriteDocumentFindFirstOrThrowArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more FavoriteDocuments that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all FavoriteDocuments
     * const favoriteDocuments = await prisma.favoriteDocument.findMany()
     * 
     * // Get first 10 FavoriteDocuments
     * const favoriteDocuments = await prisma.favoriteDocument.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const favoriteDocumentWithIdOnly = await prisma.favoriteDocument.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends FavoriteDocumentFindManyArgs>(args?: SelectSubset<T, FavoriteDocumentFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "findMany">>

    /**
     * Create a FavoriteDocument.
     * @param {FavoriteDocumentCreateArgs} args - Arguments to create a FavoriteDocument.
     * @example
     * // Create one FavoriteDocument
     * const FavoriteDocument = await prisma.favoriteDocument.create({
     *   data: {
     *     // ... data to create a FavoriteDocument
     *   }
     * })
     * 
     */
    create<T extends FavoriteDocumentCreateArgs>(args: SelectSubset<T, FavoriteDocumentCreateArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many FavoriteDocuments.
     * @param {FavoriteDocumentCreateManyArgs} args - Arguments to create many FavoriteDocuments.
     * @example
     * // Create many FavoriteDocuments
     * const favoriteDocument = await prisma.favoriteDocument.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends FavoriteDocumentCreateManyArgs>(args?: SelectSubset<T, FavoriteDocumentCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many FavoriteDocuments and returns the data saved in the database.
     * @param {FavoriteDocumentCreateManyAndReturnArgs} args - Arguments to create many FavoriteDocuments.
     * @example
     * // Create many FavoriteDocuments
     * const favoriteDocument = await prisma.favoriteDocument.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many FavoriteDocuments and only return the `id`
     * const favoriteDocumentWithIdOnly = await prisma.favoriteDocument.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends FavoriteDocumentCreateManyAndReturnArgs>(args?: SelectSubset<T, FavoriteDocumentCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a FavoriteDocument.
     * @param {FavoriteDocumentDeleteArgs} args - Arguments to delete one FavoriteDocument.
     * @example
     * // Delete one FavoriteDocument
     * const FavoriteDocument = await prisma.favoriteDocument.delete({
     *   where: {
     *     // ... filter to delete one FavoriteDocument
     *   }
     * })
     * 
     */
    delete<T extends FavoriteDocumentDeleteArgs>(args: SelectSubset<T, FavoriteDocumentDeleteArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one FavoriteDocument.
     * @param {FavoriteDocumentUpdateArgs} args - Arguments to update one FavoriteDocument.
     * @example
     * // Update one FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends FavoriteDocumentUpdateArgs>(args: SelectSubset<T, FavoriteDocumentUpdateArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more FavoriteDocuments.
     * @param {FavoriteDocumentDeleteManyArgs} args - Arguments to filter FavoriteDocuments to delete.
     * @example
     * // Delete a few FavoriteDocuments
     * const { count } = await prisma.favoriteDocument.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends FavoriteDocumentDeleteManyArgs>(args?: SelectSubset<T, FavoriteDocumentDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more FavoriteDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many FavoriteDocuments
     * const favoriteDocument = await prisma.favoriteDocument.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends FavoriteDocumentUpdateManyArgs>(args: SelectSubset<T, FavoriteDocumentUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one FavoriteDocument.
     * @param {FavoriteDocumentUpsertArgs} args - Arguments to update or create a FavoriteDocument.
     * @example
     * // Update or create a FavoriteDocument
     * const favoriteDocument = await prisma.favoriteDocument.upsert({
     *   create: {
     *     // ... data to create a FavoriteDocument
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the FavoriteDocument we want to update
     *   }
     * })
     */
    upsert<T extends FavoriteDocumentUpsertArgs>(args: SelectSubset<T, FavoriteDocumentUpsertArgs<ExtArgs>>): Prisma__FavoriteDocumentClient<$Result.GetResult<Prisma.$FavoriteDocumentPayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of FavoriteDocuments.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentCountArgs} args - Arguments to filter FavoriteDocuments to count.
     * @example
     * // Count the number of FavoriteDocuments
     * const count = await prisma.favoriteDocument.count({
     *   where: {
     *     // ... the filter for the FavoriteDocuments we want to count
     *   }
     * })
    **/
    count<T extends FavoriteDocumentCountArgs>(
      args?: Subset<T, FavoriteDocumentCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], FavoriteDocumentCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a FavoriteDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends FavoriteDocumentAggregateArgs>(args: Subset<T, FavoriteDocumentAggregateArgs>): Prisma.PrismaPromise<GetFavoriteDocumentAggregateType<T>>

    /**
     * Group by FavoriteDocument.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {FavoriteDocumentGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends FavoriteDocumentGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: FavoriteDocumentGroupByArgs['orderBy'] }
        : { orderBy?: FavoriteDocumentGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, FavoriteDocumentGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetFavoriteDocumentGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the FavoriteDocument model
   */
  readonly fields: FavoriteDocumentFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for FavoriteDocument.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__FavoriteDocumentClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    document<T extends BaseDocumentDefaultArgs<ExtArgs> = {}>(args?: Subset<T, BaseDocumentDefaultArgs<ExtArgs>>): Prisma__BaseDocumentClient<$Result.GetResult<Prisma.$BaseDocumentPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the FavoriteDocument model
   */ 
  interface FavoriteDocumentFieldRefs {
    readonly id: FieldRef<"FavoriteDocument", 'String'>
    readonly documentId: FieldRef<"FavoriteDocument", 'String'>
    readonly userId: FieldRef<"FavoriteDocument", 'String'>
  }
    

  // Custom InputTypes
  /**
   * FavoriteDocument findUnique
   */
  export type FavoriteDocumentFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter, which FavoriteDocument to fetch.
     */
    where: FavoriteDocumentWhereUniqueInput
  }

  /**
   * FavoriteDocument findUniqueOrThrow
   */
  export type FavoriteDocumentFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter, which FavoriteDocument to fetch.
     */
    where: FavoriteDocumentWhereUniqueInput
  }

  /**
   * FavoriteDocument findFirst
   */
  export type FavoriteDocumentFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter, which FavoriteDocument to fetch.
     */
    where?: FavoriteDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FavoriteDocuments to fetch.
     */
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for FavoriteDocuments.
     */
    cursor?: FavoriteDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FavoriteDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FavoriteDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of FavoriteDocuments.
     */
    distinct?: FavoriteDocumentScalarFieldEnum | FavoriteDocumentScalarFieldEnum[]
  }

  /**
   * FavoriteDocument findFirstOrThrow
   */
  export type FavoriteDocumentFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter, which FavoriteDocument to fetch.
     */
    where?: FavoriteDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FavoriteDocuments to fetch.
     */
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for FavoriteDocuments.
     */
    cursor?: FavoriteDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FavoriteDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FavoriteDocuments.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of FavoriteDocuments.
     */
    distinct?: FavoriteDocumentScalarFieldEnum | FavoriteDocumentScalarFieldEnum[]
  }

  /**
   * FavoriteDocument findMany
   */
  export type FavoriteDocumentFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter, which FavoriteDocuments to fetch.
     */
    where?: FavoriteDocumentWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of FavoriteDocuments to fetch.
     */
    orderBy?: FavoriteDocumentOrderByWithRelationInput | FavoriteDocumentOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing FavoriteDocuments.
     */
    cursor?: FavoriteDocumentWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` FavoriteDocuments from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` FavoriteDocuments.
     */
    skip?: number
    distinct?: FavoriteDocumentScalarFieldEnum | FavoriteDocumentScalarFieldEnum[]
  }

  /**
   * FavoriteDocument create
   */
  export type FavoriteDocumentCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * The data needed to create a FavoriteDocument.
     */
    data: XOR<FavoriteDocumentCreateInput, FavoriteDocumentUncheckedCreateInput>
  }

  /**
   * FavoriteDocument createMany
   */
  export type FavoriteDocumentCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many FavoriteDocuments.
     */
    data: FavoriteDocumentCreateManyInput | FavoriteDocumentCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * FavoriteDocument createManyAndReturn
   */
  export type FavoriteDocumentCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many FavoriteDocuments.
     */
    data: FavoriteDocumentCreateManyInput | FavoriteDocumentCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * FavoriteDocument update
   */
  export type FavoriteDocumentUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * The data needed to update a FavoriteDocument.
     */
    data: XOR<FavoriteDocumentUpdateInput, FavoriteDocumentUncheckedUpdateInput>
    /**
     * Choose, which FavoriteDocument to update.
     */
    where: FavoriteDocumentWhereUniqueInput
  }

  /**
   * FavoriteDocument updateMany
   */
  export type FavoriteDocumentUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update FavoriteDocuments.
     */
    data: XOR<FavoriteDocumentUpdateManyMutationInput, FavoriteDocumentUncheckedUpdateManyInput>
    /**
     * Filter which FavoriteDocuments to update
     */
    where?: FavoriteDocumentWhereInput
  }

  /**
   * FavoriteDocument upsert
   */
  export type FavoriteDocumentUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * The filter to search for the FavoriteDocument to update in case it exists.
     */
    where: FavoriteDocumentWhereUniqueInput
    /**
     * In case the FavoriteDocument found by the `where` argument doesn't exist, create a new FavoriteDocument with this data.
     */
    create: XOR<FavoriteDocumentCreateInput, FavoriteDocumentUncheckedCreateInput>
    /**
     * In case the FavoriteDocument was found with the provided `where` argument, update it with this data.
     */
    update: XOR<FavoriteDocumentUpdateInput, FavoriteDocumentUncheckedUpdateInput>
  }

  /**
   * FavoriteDocument delete
   */
  export type FavoriteDocumentDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
    /**
     * Filter which FavoriteDocument to delete.
     */
    where: FavoriteDocumentWhereUniqueInput
  }

  /**
   * FavoriteDocument deleteMany
   */
  export type FavoriteDocumentDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which FavoriteDocuments to delete
     */
    where?: FavoriteDocumentWhereInput
  }

  /**
   * FavoriteDocument without action
   */
  export type FavoriteDocumentDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the FavoriteDocument
     */
    select?: FavoriteDocumentSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: FavoriteDocumentInclude<ExtArgs> | null
  }


  /**
   * Model GeneratedImage
   */

  export type AggregateGeneratedImage = {
    _count: GeneratedImageCountAggregateOutputType | null
    _min: GeneratedImageMinAggregateOutputType | null
    _max: GeneratedImageMaxAggregateOutputType | null
  }

  export type GeneratedImageMinAggregateOutputType = {
    id: string | null
    url: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: string | null
    prompt: string | null
  }

  export type GeneratedImageMaxAggregateOutputType = {
    id: string | null
    url: string | null
    createdAt: Date | null
    updatedAt: Date | null
    userId: string | null
    prompt: string | null
  }

  export type GeneratedImageCountAggregateOutputType = {
    id: number
    url: number
    createdAt: number
    updatedAt: number
    userId: number
    prompt: number
    _all: number
  }


  export type GeneratedImageMinAggregateInputType = {
    id?: true
    url?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    prompt?: true
  }

  export type GeneratedImageMaxAggregateInputType = {
    id?: true
    url?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    prompt?: true
  }

  export type GeneratedImageCountAggregateInputType = {
    id?: true
    url?: true
    createdAt?: true
    updatedAt?: true
    userId?: true
    prompt?: true
    _all?: true
  }

  export type GeneratedImageAggregateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GeneratedImage to aggregate.
     */
    where?: GeneratedImageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GeneratedImages to fetch.
     */
    orderBy?: GeneratedImageOrderByWithRelationInput | GeneratedImageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the start position
     */
    cursor?: GeneratedImageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GeneratedImages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GeneratedImages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Count returned GeneratedImages
    **/
    _count?: true | GeneratedImageCountAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the minimum value
    **/
    _min?: GeneratedImageMinAggregateInputType
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/aggregations Aggregation Docs}
     * 
     * Select which fields to find the maximum value
    **/
    _max?: GeneratedImageMaxAggregateInputType
  }

  export type GetGeneratedImageAggregateType<T extends GeneratedImageAggregateArgs> = {
        [P in keyof T & keyof AggregateGeneratedImage]: P extends '_count' | 'count'
      ? T[P] extends true
        ? number
        : GetScalarType<T[P], AggregateGeneratedImage[P]>
      : GetScalarType<T[P], AggregateGeneratedImage[P]>
  }




  export type GeneratedImageGroupByArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    where?: GeneratedImageWhereInput
    orderBy?: GeneratedImageOrderByWithAggregationInput | GeneratedImageOrderByWithAggregationInput[]
    by: GeneratedImageScalarFieldEnum[] | GeneratedImageScalarFieldEnum
    having?: GeneratedImageScalarWhereWithAggregatesInput
    take?: number
    skip?: number
    _count?: GeneratedImageCountAggregateInputType | true
    _min?: GeneratedImageMinAggregateInputType
    _max?: GeneratedImageMaxAggregateInputType
  }

  export type GeneratedImageGroupByOutputType = {
    id: string
    url: string
    createdAt: Date
    updatedAt: Date
    userId: string
    prompt: string
    _count: GeneratedImageCountAggregateOutputType | null
    _min: GeneratedImageMinAggregateOutputType | null
    _max: GeneratedImageMaxAggregateOutputType | null
  }

  type GetGeneratedImageGroupByPayload<T extends GeneratedImageGroupByArgs> = Prisma.PrismaPromise<
    Array<
      PickEnumerable<GeneratedImageGroupByOutputType, T['by']> &
        {
          [P in ((keyof T) & (keyof GeneratedImageGroupByOutputType))]: P extends '_count'
            ? T[P] extends boolean
              ? number
              : GetScalarType<T[P], GeneratedImageGroupByOutputType[P]>
            : GetScalarType<T[P], GeneratedImageGroupByOutputType[P]>
        }
      >
    >


  export type GeneratedImageSelect<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    url?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    prompt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["generatedImage"]>

  export type GeneratedImageSelectCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = $Extensions.GetSelect<{
    id?: boolean
    url?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    prompt?: boolean
    user?: boolean | UserDefaultArgs<ExtArgs>
  }, ExtArgs["result"]["generatedImage"]>

  export type GeneratedImageSelectScalar = {
    id?: boolean
    url?: boolean
    createdAt?: boolean
    updatedAt?: boolean
    userId?: boolean
    prompt?: boolean
  }

  export type GeneratedImageInclude<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }
  export type GeneratedImageIncludeCreateManyAndReturn<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    user?: boolean | UserDefaultArgs<ExtArgs>
  }

  export type $GeneratedImagePayload<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    name: "GeneratedImage"
    objects: {
      user: Prisma.$UserPayload<ExtArgs>
    }
    scalars: $Extensions.GetPayloadResult<{
      id: string
      url: string
      createdAt: Date
      updatedAt: Date
      userId: string
      prompt: string
    }, ExtArgs["result"]["generatedImage"]>
    composites: {}
  }

  type GeneratedImageGetPayload<S extends boolean | null | undefined | GeneratedImageDefaultArgs> = $Result.GetResult<Prisma.$GeneratedImagePayload, S>

  type GeneratedImageCountArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = 
    Omit<GeneratedImageFindManyArgs, 'select' | 'include' | 'distinct'> & {
      select?: GeneratedImageCountAggregateInputType | true
    }

  export interface GeneratedImageDelegate<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> {
    [K: symbol]: { types: Prisma.TypeMap<ExtArgs>['model']['GeneratedImage'], meta: { name: 'GeneratedImage' } }
    /**
     * Find zero or one GeneratedImage that matches the filter.
     * @param {GeneratedImageFindUniqueArgs} args - Arguments to find a GeneratedImage
     * @example
     * // Get one GeneratedImage
     * const generatedImage = await prisma.generatedImage.findUnique({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUnique<T extends GeneratedImageFindUniqueArgs>(args: SelectSubset<T, GeneratedImageFindUniqueArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findUnique"> | null, null, ExtArgs>

    /**
     * Find one GeneratedImage that matches the filter or throw an error with `error.code='P2025'` 
     * if no matches were found.
     * @param {GeneratedImageFindUniqueOrThrowArgs} args - Arguments to find a GeneratedImage
     * @example
     * // Get one GeneratedImage
     * const generatedImage = await prisma.generatedImage.findUniqueOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findUniqueOrThrow<T extends GeneratedImageFindUniqueOrThrowArgs>(args: SelectSubset<T, GeneratedImageFindUniqueOrThrowArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findUniqueOrThrow">, never, ExtArgs>

    /**
     * Find the first GeneratedImage that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageFindFirstArgs} args - Arguments to find a GeneratedImage
     * @example
     * // Get one GeneratedImage
     * const generatedImage = await prisma.generatedImage.findFirst({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirst<T extends GeneratedImageFindFirstArgs>(args?: SelectSubset<T, GeneratedImageFindFirstArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findFirst"> | null, null, ExtArgs>

    /**
     * Find the first GeneratedImage that matches the filter or
     * throw `PrismaKnownClientError` with `P2025` code if no matches were found.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageFindFirstOrThrowArgs} args - Arguments to find a GeneratedImage
     * @example
     * // Get one GeneratedImage
     * const generatedImage = await prisma.generatedImage.findFirstOrThrow({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     */
    findFirstOrThrow<T extends GeneratedImageFindFirstOrThrowArgs>(args?: SelectSubset<T, GeneratedImageFindFirstOrThrowArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findFirstOrThrow">, never, ExtArgs>

    /**
     * Find zero or more GeneratedImages that matches the filter.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageFindManyArgs} args - Arguments to filter and select certain fields only.
     * @example
     * // Get all GeneratedImages
     * const generatedImages = await prisma.generatedImage.findMany()
     * 
     * // Get first 10 GeneratedImages
     * const generatedImages = await prisma.generatedImage.findMany({ take: 10 })
     * 
     * // Only select the `id`
     * const generatedImageWithIdOnly = await prisma.generatedImage.findMany({ select: { id: true } })
     * 
     */
    findMany<T extends GeneratedImageFindManyArgs>(args?: SelectSubset<T, GeneratedImageFindManyArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "findMany">>

    /**
     * Create a GeneratedImage.
     * @param {GeneratedImageCreateArgs} args - Arguments to create a GeneratedImage.
     * @example
     * // Create one GeneratedImage
     * const GeneratedImage = await prisma.generatedImage.create({
     *   data: {
     *     // ... data to create a GeneratedImage
     *   }
     * })
     * 
     */
    create<T extends GeneratedImageCreateArgs>(args: SelectSubset<T, GeneratedImageCreateArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "create">, never, ExtArgs>

    /**
     * Create many GeneratedImages.
     * @param {GeneratedImageCreateManyArgs} args - Arguments to create many GeneratedImages.
     * @example
     * // Create many GeneratedImages
     * const generatedImage = await prisma.generatedImage.createMany({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     *     
     */
    createMany<T extends GeneratedImageCreateManyArgs>(args?: SelectSubset<T, GeneratedImageCreateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create many GeneratedImages and returns the data saved in the database.
     * @param {GeneratedImageCreateManyAndReturnArgs} args - Arguments to create many GeneratedImages.
     * @example
     * // Create many GeneratedImages
     * const generatedImage = await prisma.generatedImage.createManyAndReturn({
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * 
     * // Create many GeneratedImages and only return the `id`
     * const generatedImageWithIdOnly = await prisma.generatedImage.createManyAndReturn({ 
     *   select: { id: true },
     *   data: [
     *     // ... provide data here
     *   ]
     * })
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * 
     */
    createManyAndReturn<T extends GeneratedImageCreateManyAndReturnArgs>(args?: SelectSubset<T, GeneratedImageCreateManyAndReturnArgs<ExtArgs>>): Prisma.PrismaPromise<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "createManyAndReturn">>

    /**
     * Delete a GeneratedImage.
     * @param {GeneratedImageDeleteArgs} args - Arguments to delete one GeneratedImage.
     * @example
     * // Delete one GeneratedImage
     * const GeneratedImage = await prisma.generatedImage.delete({
     *   where: {
     *     // ... filter to delete one GeneratedImage
     *   }
     * })
     * 
     */
    delete<T extends GeneratedImageDeleteArgs>(args: SelectSubset<T, GeneratedImageDeleteArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "delete">, never, ExtArgs>

    /**
     * Update one GeneratedImage.
     * @param {GeneratedImageUpdateArgs} args - Arguments to update one GeneratedImage.
     * @example
     * // Update one GeneratedImage
     * const generatedImage = await prisma.generatedImage.update({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    update<T extends GeneratedImageUpdateArgs>(args: SelectSubset<T, GeneratedImageUpdateArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "update">, never, ExtArgs>

    /**
     * Delete zero or more GeneratedImages.
     * @param {GeneratedImageDeleteManyArgs} args - Arguments to filter GeneratedImages to delete.
     * @example
     * // Delete a few GeneratedImages
     * const { count } = await prisma.generatedImage.deleteMany({
     *   where: {
     *     // ... provide filter here
     *   }
     * })
     * 
     */
    deleteMany<T extends GeneratedImageDeleteManyArgs>(args?: SelectSubset<T, GeneratedImageDeleteManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Update zero or more GeneratedImages.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageUpdateManyArgs} args - Arguments to update one or more rows.
     * @example
     * // Update many GeneratedImages
     * const generatedImage = await prisma.generatedImage.updateMany({
     *   where: {
     *     // ... provide filter here
     *   },
     *   data: {
     *     // ... provide data here
     *   }
     * })
     * 
     */
    updateMany<T extends GeneratedImageUpdateManyArgs>(args: SelectSubset<T, GeneratedImageUpdateManyArgs<ExtArgs>>): Prisma.PrismaPromise<BatchPayload>

    /**
     * Create or update one GeneratedImage.
     * @param {GeneratedImageUpsertArgs} args - Arguments to update or create a GeneratedImage.
     * @example
     * // Update or create a GeneratedImage
     * const generatedImage = await prisma.generatedImage.upsert({
     *   create: {
     *     // ... data to create a GeneratedImage
     *   },
     *   update: {
     *     // ... in case it already exists, update
     *   },
     *   where: {
     *     // ... the filter for the GeneratedImage we want to update
     *   }
     * })
     */
    upsert<T extends GeneratedImageUpsertArgs>(args: SelectSubset<T, GeneratedImageUpsertArgs<ExtArgs>>): Prisma__GeneratedImageClient<$Result.GetResult<Prisma.$GeneratedImagePayload<ExtArgs>, T, "upsert">, never, ExtArgs>


    /**
     * Count the number of GeneratedImages.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageCountArgs} args - Arguments to filter GeneratedImages to count.
     * @example
     * // Count the number of GeneratedImages
     * const count = await prisma.generatedImage.count({
     *   where: {
     *     // ... the filter for the GeneratedImages we want to count
     *   }
     * })
    **/
    count<T extends GeneratedImageCountArgs>(
      args?: Subset<T, GeneratedImageCountArgs>,
    ): Prisma.PrismaPromise<
      T extends $Utils.Record<'select', any>
        ? T['select'] extends true
          ? number
          : GetScalarType<T['select'], GeneratedImageCountAggregateOutputType>
        : number
    >

    /**
     * Allows you to perform aggregations operations on a GeneratedImage.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageAggregateArgs} args - Select which aggregations you would like to apply and on what fields.
     * @example
     * // Ordered by age ascending
     * // Where email contains prisma.io
     * // Limited to the 10 users
     * const aggregations = await prisma.user.aggregate({
     *   _avg: {
     *     age: true,
     *   },
     *   where: {
     *     email: {
     *       contains: "prisma.io",
     *     },
     *   },
     *   orderBy: {
     *     age: "asc",
     *   },
     *   take: 10,
     * })
    **/
    aggregate<T extends GeneratedImageAggregateArgs>(args: Subset<T, GeneratedImageAggregateArgs>): Prisma.PrismaPromise<GetGeneratedImageAggregateType<T>>

    /**
     * Group by GeneratedImage.
     * Note, that providing `undefined` is treated as the value not being there.
     * Read more here: https://pris.ly/d/null-undefined
     * @param {GeneratedImageGroupByArgs} args - Group by arguments.
     * @example
     * // Group by city, order by createdAt, get count
     * const result = await prisma.user.groupBy({
     *   by: ['city', 'createdAt'],
     *   orderBy: {
     *     createdAt: true
     *   },
     *   _count: {
     *     _all: true
     *   },
     * })
     * 
    **/
    groupBy<
      T extends GeneratedImageGroupByArgs,
      HasSelectOrTake extends Or<
        Extends<'skip', Keys<T>>,
        Extends<'take', Keys<T>>
      >,
      OrderByArg extends True extends HasSelectOrTake
        ? { orderBy: GeneratedImageGroupByArgs['orderBy'] }
        : { orderBy?: GeneratedImageGroupByArgs['orderBy'] },
      OrderFields extends ExcludeUnderscoreKeys<Keys<MaybeTupleToUnion<T['orderBy']>>>,
      ByFields extends MaybeTupleToUnion<T['by']>,
      ByValid extends Has<ByFields, OrderFields>,
      HavingFields extends GetHavingFields<T['having']>,
      HavingValid extends Has<ByFields, HavingFields>,
      ByEmpty extends T['by'] extends never[] ? True : False,
      InputErrors extends ByEmpty extends True
      ? `Error: "by" must not be empty.`
      : HavingValid extends False
      ? {
          [P in HavingFields]: P extends ByFields
            ? never
            : P extends string
            ? `Error: Field "${P}" used in "having" needs to be provided in "by".`
            : [
                Error,
                'Field ',
                P,
                ` in "having" needs to be provided in "by"`,
              ]
        }[HavingFields]
      : 'take' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "take", you also need to provide "orderBy"'
      : 'skip' extends Keys<T>
      ? 'orderBy' extends Keys<T>
        ? ByValid extends True
          ? {}
          : {
              [P in OrderFields]: P extends ByFields
                ? never
                : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
            }[OrderFields]
        : 'Error: If you provide "skip", you also need to provide "orderBy"'
      : ByValid extends True
      ? {}
      : {
          [P in OrderFields]: P extends ByFields
            ? never
            : `Error: Field "${P}" in "orderBy" needs to be provided in "by"`
        }[OrderFields]
    >(args: SubsetIntersection<T, GeneratedImageGroupByArgs, OrderByArg> & InputErrors): {} extends InputErrors ? GetGeneratedImageGroupByPayload<T> : Prisma.PrismaPromise<InputErrors>
  /**
   * Fields of the GeneratedImage model
   */
  readonly fields: GeneratedImageFieldRefs;
  }

  /**
   * The delegate class that acts as a "Promise-like" for GeneratedImage.
   * Why is this prefixed with `Prisma__`?
   * Because we want to prevent naming conflicts as mentioned in
   * https://github.com/prisma/prisma-client-js/issues/707
   */
  export interface Prisma__GeneratedImageClient<T, Null = never, ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> extends Prisma.PrismaPromise<T> {
    readonly [Symbol.toStringTag]: "PrismaPromise"
    user<T extends UserDefaultArgs<ExtArgs> = {}>(args?: Subset<T, UserDefaultArgs<ExtArgs>>): Prisma__UserClient<$Result.GetResult<Prisma.$UserPayload<ExtArgs>, T, "findUniqueOrThrow"> | Null, Null, ExtArgs>
    /**
     * Attaches callbacks for the resolution and/or rejection of the Promise.
     * @param onfulfilled The callback to execute when the Promise is resolved.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of which ever callback is executed.
     */
    then<TResult1 = T, TResult2 = never>(onfulfilled?: ((value: T) => TResult1 | PromiseLike<TResult1>) | undefined | null, onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null): $Utils.JsPromise<TResult1 | TResult2>
    /**
     * Attaches a callback for only the rejection of the Promise.
     * @param onrejected The callback to execute when the Promise is rejected.
     * @returns A Promise for the completion of the callback.
     */
    catch<TResult = never>(onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null): $Utils.JsPromise<T | TResult>
    /**
     * Attaches a callback that is invoked when the Promise is settled (fulfilled or rejected). The
     * resolved value cannot be modified from the callback.
     * @param onfinally The callback to execute when the Promise is settled (fulfilled or rejected).
     * @returns A Promise for the completion of the callback.
     */
    finally(onfinally?: (() => void) | undefined | null): $Utils.JsPromise<T>
  }




  /**
   * Fields of the GeneratedImage model
   */ 
  interface GeneratedImageFieldRefs {
    readonly id: FieldRef<"GeneratedImage", 'String'>
    readonly url: FieldRef<"GeneratedImage", 'String'>
    readonly createdAt: FieldRef<"GeneratedImage", 'DateTime'>
    readonly updatedAt: FieldRef<"GeneratedImage", 'DateTime'>
    readonly userId: FieldRef<"GeneratedImage", 'String'>
    readonly prompt: FieldRef<"GeneratedImage", 'String'>
  }
    

  // Custom InputTypes
  /**
   * GeneratedImage findUnique
   */
  export type GeneratedImageFindUniqueArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter, which GeneratedImage to fetch.
     */
    where: GeneratedImageWhereUniqueInput
  }

  /**
   * GeneratedImage findUniqueOrThrow
   */
  export type GeneratedImageFindUniqueOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter, which GeneratedImage to fetch.
     */
    where: GeneratedImageWhereUniqueInput
  }

  /**
   * GeneratedImage findFirst
   */
  export type GeneratedImageFindFirstArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter, which GeneratedImage to fetch.
     */
    where?: GeneratedImageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GeneratedImages to fetch.
     */
    orderBy?: GeneratedImageOrderByWithRelationInput | GeneratedImageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GeneratedImages.
     */
    cursor?: GeneratedImageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GeneratedImages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GeneratedImages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GeneratedImages.
     */
    distinct?: GeneratedImageScalarFieldEnum | GeneratedImageScalarFieldEnum[]
  }

  /**
   * GeneratedImage findFirstOrThrow
   */
  export type GeneratedImageFindFirstOrThrowArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter, which GeneratedImage to fetch.
     */
    where?: GeneratedImageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GeneratedImages to fetch.
     */
    orderBy?: GeneratedImageOrderByWithRelationInput | GeneratedImageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for searching for GeneratedImages.
     */
    cursor?: GeneratedImageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GeneratedImages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GeneratedImages.
     */
    skip?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/distinct Distinct Docs}
     * 
     * Filter by unique combinations of GeneratedImages.
     */
    distinct?: GeneratedImageScalarFieldEnum | GeneratedImageScalarFieldEnum[]
  }

  /**
   * GeneratedImage findMany
   */
  export type GeneratedImageFindManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter, which GeneratedImages to fetch.
     */
    where?: GeneratedImageWhereInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/sorting Sorting Docs}
     * 
     * Determine the order of GeneratedImages to fetch.
     */
    orderBy?: GeneratedImageOrderByWithRelationInput | GeneratedImageOrderByWithRelationInput[]
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination#cursor-based-pagination Cursor Docs}
     * 
     * Sets the position for listing GeneratedImages.
     */
    cursor?: GeneratedImageWhereUniqueInput
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Take `±n` GeneratedImages from the position of the cursor.
     */
    take?: number
    /**
     * {@link https://www.prisma.io/docs/concepts/components/prisma-client/pagination Pagination Docs}
     * 
     * Skip the first `n` GeneratedImages.
     */
    skip?: number
    distinct?: GeneratedImageScalarFieldEnum | GeneratedImageScalarFieldEnum[]
  }

  /**
   * GeneratedImage create
   */
  export type GeneratedImageCreateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * The data needed to create a GeneratedImage.
     */
    data: XOR<GeneratedImageCreateInput, GeneratedImageUncheckedCreateInput>
  }

  /**
   * GeneratedImage createMany
   */
  export type GeneratedImageCreateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to create many GeneratedImages.
     */
    data: GeneratedImageCreateManyInput | GeneratedImageCreateManyInput[]
    skipDuplicates?: boolean
  }

  /**
   * GeneratedImage createManyAndReturn
   */
  export type GeneratedImageCreateManyAndReturnArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelectCreateManyAndReturn<ExtArgs> | null
    /**
     * The data used to create many GeneratedImages.
     */
    data: GeneratedImageCreateManyInput | GeneratedImageCreateManyInput[]
    skipDuplicates?: boolean
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageIncludeCreateManyAndReturn<ExtArgs> | null
  }

  /**
   * GeneratedImage update
   */
  export type GeneratedImageUpdateArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * The data needed to update a GeneratedImage.
     */
    data: XOR<GeneratedImageUpdateInput, GeneratedImageUncheckedUpdateInput>
    /**
     * Choose, which GeneratedImage to update.
     */
    where: GeneratedImageWhereUniqueInput
  }

  /**
   * GeneratedImage updateMany
   */
  export type GeneratedImageUpdateManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * The data used to update GeneratedImages.
     */
    data: XOR<GeneratedImageUpdateManyMutationInput, GeneratedImageUncheckedUpdateManyInput>
    /**
     * Filter which GeneratedImages to update
     */
    where?: GeneratedImageWhereInput
  }

  /**
   * GeneratedImage upsert
   */
  export type GeneratedImageUpsertArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * The filter to search for the GeneratedImage to update in case it exists.
     */
    where: GeneratedImageWhereUniqueInput
    /**
     * In case the GeneratedImage found by the `where` argument doesn't exist, create a new GeneratedImage with this data.
     */
    create: XOR<GeneratedImageCreateInput, GeneratedImageUncheckedCreateInput>
    /**
     * In case the GeneratedImage was found with the provided `where` argument, update it with this data.
     */
    update: XOR<GeneratedImageUpdateInput, GeneratedImageUncheckedUpdateInput>
  }

  /**
   * GeneratedImage delete
   */
  export type GeneratedImageDeleteArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
    /**
     * Filter which GeneratedImage to delete.
     */
    where: GeneratedImageWhereUniqueInput
  }

  /**
   * GeneratedImage deleteMany
   */
  export type GeneratedImageDeleteManyArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Filter which GeneratedImages to delete
     */
    where?: GeneratedImageWhereInput
  }

  /**
   * GeneratedImage without action
   */
  export type GeneratedImageDefaultArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = {
    /**
     * Select specific fields to fetch from the GeneratedImage
     */
    select?: GeneratedImageSelect<ExtArgs> | null
    /**
     * Choose, which related nodes to fetch as well
     */
    include?: GeneratedImageInclude<ExtArgs> | null
  }


  /**
   * Enums
   */

  export const TransactionIsolationLevel: {
    ReadUncommitted: 'ReadUncommitted',
    ReadCommitted: 'ReadCommitted',
    RepeatableRead: 'RepeatableRead',
    Serializable: 'Serializable'
  };

  export type TransactionIsolationLevel = (typeof TransactionIsolationLevel)[keyof typeof TransactionIsolationLevel]


  export const UserScalarFieldEnum: {
    id: 'id',
    name: 'name',
    email: 'email',
    password: 'password',
    emailVerified: 'emailVerified',
    image: 'image',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    headline: 'headline',
    bio: 'bio',
    interests: 'interests',
    location: 'location',
    website: 'website',
    role: 'role',
    hasAccess: 'hasAccess'
  };

  export type UserScalarFieldEnum = (typeof UserScalarFieldEnum)[keyof typeof UserScalarFieldEnum]


  export const BaseDocumentScalarFieldEnum: {
    id: 'id',
    title: 'title',
    type: 'type',
    userId: 'userId',
    thumbnailUrl: 'thumbnailUrl',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    isPublic: 'isPublic',
    documentType: 'documentType'
  };

  export type BaseDocumentScalarFieldEnum = (typeof BaseDocumentScalarFieldEnum)[keyof typeof BaseDocumentScalarFieldEnum]


  export const PresentationScalarFieldEnum: {
    id: 'id',
    content: 'content',
    theme: 'theme',
    imageModel: 'imageModel',
    presentationStyle: 'presentationStyle',
    language: 'language',
    outline: 'outline',
    templateId: 'templateId',
    customThemeId: 'customThemeId'
  };

  export type PresentationScalarFieldEnum = (typeof PresentationScalarFieldEnum)[keyof typeof PresentationScalarFieldEnum]


  export const CustomThemeScalarFieldEnum: {
    id: 'id',
    name: 'name',
    description: 'description',
    userId: 'userId',
    logoUrl: 'logoUrl',
    isPublic: 'isPublic',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    themeData: 'themeData'
  };

  export type CustomThemeScalarFieldEnum = (typeof CustomThemeScalarFieldEnum)[keyof typeof CustomThemeScalarFieldEnum]


  export const FavoriteDocumentScalarFieldEnum: {
    id: 'id',
    documentId: 'documentId',
    userId: 'userId'
  };

  export type FavoriteDocumentScalarFieldEnum = (typeof FavoriteDocumentScalarFieldEnum)[keyof typeof FavoriteDocumentScalarFieldEnum]


  export const GeneratedImageScalarFieldEnum: {
    id: 'id',
    url: 'url',
    createdAt: 'createdAt',
    updatedAt: 'updatedAt',
    userId: 'userId',
    prompt: 'prompt'
  };

  export type GeneratedImageScalarFieldEnum = (typeof GeneratedImageScalarFieldEnum)[keyof typeof GeneratedImageScalarFieldEnum]


  export const SortOrder: {
    asc: 'asc',
    desc: 'desc'
  };

  export type SortOrder = (typeof SortOrder)[keyof typeof SortOrder]


  export const JsonNullValueInput: {
    JsonNull: typeof JsonNull
  };

  export type JsonNullValueInput = (typeof JsonNullValueInput)[keyof typeof JsonNullValueInput]


  export const QueryMode: {
    default: 'default',
    insensitive: 'insensitive'
  };

  export type QueryMode = (typeof QueryMode)[keyof typeof QueryMode]


  export const NullsOrder: {
    first: 'first',
    last: 'last'
  };

  export type NullsOrder = (typeof NullsOrder)[keyof typeof NullsOrder]


  export const JsonNullValueFilter: {
    DbNull: typeof DbNull,
    JsonNull: typeof JsonNull,
    AnyNull: typeof AnyNull
  };

  export type JsonNullValueFilter = (typeof JsonNullValueFilter)[keyof typeof JsonNullValueFilter]


  /**
   * Field references 
   */


  /**
   * Reference to a field of type 'String'
   */
  export type StringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String'>
    


  /**
   * Reference to a field of type 'String[]'
   */
  export type ListStringFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'String[]'>
    


  /**
   * Reference to a field of type 'DateTime'
   */
  export type DateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime'>
    


  /**
   * Reference to a field of type 'DateTime[]'
   */
  export type ListDateTimeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DateTime[]'>
    


  /**
   * Reference to a field of type 'UserRole'
   */
  export type EnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole'>
    


  /**
   * Reference to a field of type 'UserRole[]'
   */
  export type ListEnumUserRoleFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'UserRole[]'>
    


  /**
   * Reference to a field of type 'Boolean'
   */
  export type BooleanFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Boolean'>
    


  /**
   * Reference to a field of type 'DocumentType'
   */
  export type EnumDocumentTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DocumentType'>
    


  /**
   * Reference to a field of type 'DocumentType[]'
   */
  export type ListEnumDocumentTypeFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'DocumentType[]'>
    


  /**
   * Reference to a field of type 'Json'
   */
  export type JsonFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Json'>
    


  /**
   * Reference to a field of type 'Int'
   */
  export type IntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int'>
    


  /**
   * Reference to a field of type 'Int[]'
   */
  export type ListIntFieldRefInput<$PrismaModel> = FieldRefInputType<$PrismaModel, 'Int[]'>
    
  /**
   * Deep Input Types
   */


  export type UserWhereInput = {
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    id?: StringFilter<"User"> | string
    name?: StringNullableFilter<"User"> | string | null
    email?: StringNullableFilter<"User"> | string | null
    password?: StringNullableFilter<"User"> | string | null
    emailVerified?: DateTimeNullableFilter<"User"> | Date | string | null
    image?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    headline?: StringNullableFilter<"User"> | string | null
    bio?: StringNullableFilter<"User"> | string | null
    interests?: StringNullableListFilter<"User">
    location?: StringNullableFilter<"User"> | string | null
    website?: StringNullableFilter<"User"> | string | null
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    hasAccess?: BoolFilter<"User"> | boolean
    documents?: BaseDocumentListRelationFilter
    favorites?: FavoriteDocumentListRelationFilter
    CustomTheme?: CustomThemeListRelationFilter
    GeneratedImage?: GeneratedImageListRelationFilter
  }

  export type UserOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrderInput | SortOrder
    email?: SortOrderInput | SortOrder
    password?: SortOrderInput | SortOrder
    emailVerified?: SortOrderInput | SortOrder
    image?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    headline?: SortOrderInput | SortOrder
    bio?: SortOrderInput | SortOrder
    interests?: SortOrder
    location?: SortOrderInput | SortOrder
    website?: SortOrderInput | SortOrder
    role?: SortOrder
    hasAccess?: SortOrder
    documents?: BaseDocumentOrderByRelationAggregateInput
    favorites?: FavoriteDocumentOrderByRelationAggregateInput
    CustomTheme?: CustomThemeOrderByRelationAggregateInput
    GeneratedImage?: GeneratedImageOrderByRelationAggregateInput
  }

  export type UserWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    email?: string
    AND?: UserWhereInput | UserWhereInput[]
    OR?: UserWhereInput[]
    NOT?: UserWhereInput | UserWhereInput[]
    name?: StringNullableFilter<"User"> | string | null
    password?: StringNullableFilter<"User"> | string | null
    emailVerified?: DateTimeNullableFilter<"User"> | Date | string | null
    image?: StringNullableFilter<"User"> | string | null
    createdAt?: DateTimeFilter<"User"> | Date | string
    updatedAt?: DateTimeFilter<"User"> | Date | string
    headline?: StringNullableFilter<"User"> | string | null
    bio?: StringNullableFilter<"User"> | string | null
    interests?: StringNullableListFilter<"User">
    location?: StringNullableFilter<"User"> | string | null
    website?: StringNullableFilter<"User"> | string | null
    role?: EnumUserRoleFilter<"User"> | $Enums.UserRole
    hasAccess?: BoolFilter<"User"> | boolean
    documents?: BaseDocumentListRelationFilter
    favorites?: FavoriteDocumentListRelationFilter
    CustomTheme?: CustomThemeListRelationFilter
    GeneratedImage?: GeneratedImageListRelationFilter
  }, "id" | "email">

  export type UserOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrderInput | SortOrder
    email?: SortOrderInput | SortOrder
    password?: SortOrderInput | SortOrder
    emailVerified?: SortOrderInput | SortOrder
    image?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    headline?: SortOrderInput | SortOrder
    bio?: SortOrderInput | SortOrder
    interests?: SortOrder
    location?: SortOrderInput | SortOrder
    website?: SortOrderInput | SortOrder
    role?: SortOrder
    hasAccess?: SortOrder
    _count?: UserCountOrderByAggregateInput
    _max?: UserMaxOrderByAggregateInput
    _min?: UserMinOrderByAggregateInput
  }

  export type UserScalarWhereWithAggregatesInput = {
    AND?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    OR?: UserScalarWhereWithAggregatesInput[]
    NOT?: UserScalarWhereWithAggregatesInput | UserScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"User"> | string
    name?: StringNullableWithAggregatesFilter<"User"> | string | null
    email?: StringNullableWithAggregatesFilter<"User"> | string | null
    password?: StringNullableWithAggregatesFilter<"User"> | string | null
    emailVerified?: DateTimeNullableWithAggregatesFilter<"User"> | Date | string | null
    image?: StringNullableWithAggregatesFilter<"User"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"User"> | Date | string
    headline?: StringNullableWithAggregatesFilter<"User"> | string | null
    bio?: StringNullableWithAggregatesFilter<"User"> | string | null
    interests?: StringNullableListFilter<"User">
    location?: StringNullableWithAggregatesFilter<"User"> | string | null
    website?: StringNullableWithAggregatesFilter<"User"> | string | null
    role?: EnumUserRoleWithAggregatesFilter<"User"> | $Enums.UserRole
    hasAccess?: BoolWithAggregatesFilter<"User"> | boolean
  }

  export type BaseDocumentWhereInput = {
    AND?: BaseDocumentWhereInput | BaseDocumentWhereInput[]
    OR?: BaseDocumentWhereInput[]
    NOT?: BaseDocumentWhereInput | BaseDocumentWhereInput[]
    id?: StringFilter<"BaseDocument"> | string
    title?: StringFilter<"BaseDocument"> | string
    type?: EnumDocumentTypeFilter<"BaseDocument"> | $Enums.DocumentType
    userId?: StringFilter<"BaseDocument"> | string
    thumbnailUrl?: StringNullableFilter<"BaseDocument"> | string | null
    createdAt?: DateTimeFilter<"BaseDocument"> | Date | string
    updatedAt?: DateTimeFilter<"BaseDocument"> | Date | string
    isPublic?: BoolFilter<"BaseDocument"> | boolean
    documentType?: StringFilter<"BaseDocument"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    presentation?: XOR<PresentationNullableRelationFilter, PresentationWhereInput> | null
    favorites?: FavoriteDocumentListRelationFilter
  }

  export type BaseDocumentOrderByWithRelationInput = {
    id?: SortOrder
    title?: SortOrder
    type?: SortOrder
    userId?: SortOrder
    thumbnailUrl?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    isPublic?: SortOrder
    documentType?: SortOrder
    user?: UserOrderByWithRelationInput
    presentation?: PresentationOrderByWithRelationInput
    favorites?: FavoriteDocumentOrderByRelationAggregateInput
  }

  export type BaseDocumentWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: BaseDocumentWhereInput | BaseDocumentWhereInput[]
    OR?: BaseDocumentWhereInput[]
    NOT?: BaseDocumentWhereInput | BaseDocumentWhereInput[]
    title?: StringFilter<"BaseDocument"> | string
    type?: EnumDocumentTypeFilter<"BaseDocument"> | $Enums.DocumentType
    userId?: StringFilter<"BaseDocument"> | string
    thumbnailUrl?: StringNullableFilter<"BaseDocument"> | string | null
    createdAt?: DateTimeFilter<"BaseDocument"> | Date | string
    updatedAt?: DateTimeFilter<"BaseDocument"> | Date | string
    isPublic?: BoolFilter<"BaseDocument"> | boolean
    documentType?: StringFilter<"BaseDocument"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
    presentation?: XOR<PresentationNullableRelationFilter, PresentationWhereInput> | null
    favorites?: FavoriteDocumentListRelationFilter
  }, "id">

  export type BaseDocumentOrderByWithAggregationInput = {
    id?: SortOrder
    title?: SortOrder
    type?: SortOrder
    userId?: SortOrder
    thumbnailUrl?: SortOrderInput | SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    isPublic?: SortOrder
    documentType?: SortOrder
    _count?: BaseDocumentCountOrderByAggregateInput
    _max?: BaseDocumentMaxOrderByAggregateInput
    _min?: BaseDocumentMinOrderByAggregateInput
  }

  export type BaseDocumentScalarWhereWithAggregatesInput = {
    AND?: BaseDocumentScalarWhereWithAggregatesInput | BaseDocumentScalarWhereWithAggregatesInput[]
    OR?: BaseDocumentScalarWhereWithAggregatesInput[]
    NOT?: BaseDocumentScalarWhereWithAggregatesInput | BaseDocumentScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"BaseDocument"> | string
    title?: StringWithAggregatesFilter<"BaseDocument"> | string
    type?: EnumDocumentTypeWithAggregatesFilter<"BaseDocument"> | $Enums.DocumentType
    userId?: StringWithAggregatesFilter<"BaseDocument"> | string
    thumbnailUrl?: StringNullableWithAggregatesFilter<"BaseDocument"> | string | null
    createdAt?: DateTimeWithAggregatesFilter<"BaseDocument"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"BaseDocument"> | Date | string
    isPublic?: BoolWithAggregatesFilter<"BaseDocument"> | boolean
    documentType?: StringWithAggregatesFilter<"BaseDocument"> | string
  }

  export type PresentationWhereInput = {
    AND?: PresentationWhereInput | PresentationWhereInput[]
    OR?: PresentationWhereInput[]
    NOT?: PresentationWhereInput | PresentationWhereInput[]
    id?: StringFilter<"Presentation"> | string
    content?: JsonFilter<"Presentation">
    theme?: StringFilter<"Presentation"> | string
    imageModel?: StringNullableFilter<"Presentation"> | string | null
    presentationStyle?: StringNullableFilter<"Presentation"> | string | null
    language?: StringNullableFilter<"Presentation"> | string | null
    outline?: StringNullableListFilter<"Presentation">
    templateId?: StringNullableFilter<"Presentation"> | string | null
    customThemeId?: StringNullableFilter<"Presentation"> | string | null
    base?: XOR<BaseDocumentRelationFilter, BaseDocumentWhereInput>
    customTheme?: XOR<CustomThemeNullableRelationFilter, CustomThemeWhereInput> | null
  }

  export type PresentationOrderByWithRelationInput = {
    id?: SortOrder
    content?: SortOrder
    theme?: SortOrder
    imageModel?: SortOrderInput | SortOrder
    presentationStyle?: SortOrderInput | SortOrder
    language?: SortOrderInput | SortOrder
    outline?: SortOrder
    templateId?: SortOrderInput | SortOrder
    customThemeId?: SortOrderInput | SortOrder
    base?: BaseDocumentOrderByWithRelationInput
    customTheme?: CustomThemeOrderByWithRelationInput
  }

  export type PresentationWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: PresentationWhereInput | PresentationWhereInput[]
    OR?: PresentationWhereInput[]
    NOT?: PresentationWhereInput | PresentationWhereInput[]
    content?: JsonFilter<"Presentation">
    theme?: StringFilter<"Presentation"> | string
    imageModel?: StringNullableFilter<"Presentation"> | string | null
    presentationStyle?: StringNullableFilter<"Presentation"> | string | null
    language?: StringNullableFilter<"Presentation"> | string | null
    outline?: StringNullableListFilter<"Presentation">
    templateId?: StringNullableFilter<"Presentation"> | string | null
    customThemeId?: StringNullableFilter<"Presentation"> | string | null
    base?: XOR<BaseDocumentRelationFilter, BaseDocumentWhereInput>
    customTheme?: XOR<CustomThemeNullableRelationFilter, CustomThemeWhereInput> | null
  }, "id">

  export type PresentationOrderByWithAggregationInput = {
    id?: SortOrder
    content?: SortOrder
    theme?: SortOrder
    imageModel?: SortOrderInput | SortOrder
    presentationStyle?: SortOrderInput | SortOrder
    language?: SortOrderInput | SortOrder
    outline?: SortOrder
    templateId?: SortOrderInput | SortOrder
    customThemeId?: SortOrderInput | SortOrder
    _count?: PresentationCountOrderByAggregateInput
    _max?: PresentationMaxOrderByAggregateInput
    _min?: PresentationMinOrderByAggregateInput
  }

  export type PresentationScalarWhereWithAggregatesInput = {
    AND?: PresentationScalarWhereWithAggregatesInput | PresentationScalarWhereWithAggregatesInput[]
    OR?: PresentationScalarWhereWithAggregatesInput[]
    NOT?: PresentationScalarWhereWithAggregatesInput | PresentationScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"Presentation"> | string
    content?: JsonWithAggregatesFilter<"Presentation">
    theme?: StringWithAggregatesFilter<"Presentation"> | string
    imageModel?: StringNullableWithAggregatesFilter<"Presentation"> | string | null
    presentationStyle?: StringNullableWithAggregatesFilter<"Presentation"> | string | null
    language?: StringNullableWithAggregatesFilter<"Presentation"> | string | null
    outline?: StringNullableListFilter<"Presentation">
    templateId?: StringNullableWithAggregatesFilter<"Presentation"> | string | null
    customThemeId?: StringNullableWithAggregatesFilter<"Presentation"> | string | null
  }

  export type CustomThemeWhereInput = {
    AND?: CustomThemeWhereInput | CustomThemeWhereInput[]
    OR?: CustomThemeWhereInput[]
    NOT?: CustomThemeWhereInput | CustomThemeWhereInput[]
    id?: StringFilter<"CustomTheme"> | string
    name?: StringFilter<"CustomTheme"> | string
    description?: StringNullableFilter<"CustomTheme"> | string | null
    userId?: StringFilter<"CustomTheme"> | string
    logoUrl?: StringNullableFilter<"CustomTheme"> | string | null
    isPublic?: BoolFilter<"CustomTheme"> | boolean
    createdAt?: DateTimeFilter<"CustomTheme"> | Date | string
    updatedAt?: DateTimeFilter<"CustomTheme"> | Date | string
    themeData?: JsonFilter<"CustomTheme">
    user?: XOR<UserRelationFilter, UserWhereInput>
    presentations?: PresentationListRelationFilter
  }

  export type CustomThemeOrderByWithRelationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    userId?: SortOrder
    logoUrl?: SortOrderInput | SortOrder
    isPublic?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    themeData?: SortOrder
    user?: UserOrderByWithRelationInput
    presentations?: PresentationOrderByRelationAggregateInput
  }

  export type CustomThemeWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: CustomThemeWhereInput | CustomThemeWhereInput[]
    OR?: CustomThemeWhereInput[]
    NOT?: CustomThemeWhereInput | CustomThemeWhereInput[]
    name?: StringFilter<"CustomTheme"> | string
    description?: StringNullableFilter<"CustomTheme"> | string | null
    userId?: StringFilter<"CustomTheme"> | string
    logoUrl?: StringNullableFilter<"CustomTheme"> | string | null
    isPublic?: BoolFilter<"CustomTheme"> | boolean
    createdAt?: DateTimeFilter<"CustomTheme"> | Date | string
    updatedAt?: DateTimeFilter<"CustomTheme"> | Date | string
    themeData?: JsonFilter<"CustomTheme">
    user?: XOR<UserRelationFilter, UserWhereInput>
    presentations?: PresentationListRelationFilter
  }, "id">

  export type CustomThemeOrderByWithAggregationInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrderInput | SortOrder
    userId?: SortOrder
    logoUrl?: SortOrderInput | SortOrder
    isPublic?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    themeData?: SortOrder
    _count?: CustomThemeCountOrderByAggregateInput
    _max?: CustomThemeMaxOrderByAggregateInput
    _min?: CustomThemeMinOrderByAggregateInput
  }

  export type CustomThemeScalarWhereWithAggregatesInput = {
    AND?: CustomThemeScalarWhereWithAggregatesInput | CustomThemeScalarWhereWithAggregatesInput[]
    OR?: CustomThemeScalarWhereWithAggregatesInput[]
    NOT?: CustomThemeScalarWhereWithAggregatesInput | CustomThemeScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"CustomTheme"> | string
    name?: StringWithAggregatesFilter<"CustomTheme"> | string
    description?: StringNullableWithAggregatesFilter<"CustomTheme"> | string | null
    userId?: StringWithAggregatesFilter<"CustomTheme"> | string
    logoUrl?: StringNullableWithAggregatesFilter<"CustomTheme"> | string | null
    isPublic?: BoolWithAggregatesFilter<"CustomTheme"> | boolean
    createdAt?: DateTimeWithAggregatesFilter<"CustomTheme"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"CustomTheme"> | Date | string
    themeData?: JsonWithAggregatesFilter<"CustomTheme">
  }

  export type FavoriteDocumentWhereInput = {
    AND?: FavoriteDocumentWhereInput | FavoriteDocumentWhereInput[]
    OR?: FavoriteDocumentWhereInput[]
    NOT?: FavoriteDocumentWhereInput | FavoriteDocumentWhereInput[]
    id?: StringFilter<"FavoriteDocument"> | string
    documentId?: StringFilter<"FavoriteDocument"> | string
    userId?: StringFilter<"FavoriteDocument"> | string
    document?: XOR<BaseDocumentRelationFilter, BaseDocumentWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }

  export type FavoriteDocumentOrderByWithRelationInput = {
    id?: SortOrder
    documentId?: SortOrder
    userId?: SortOrder
    document?: BaseDocumentOrderByWithRelationInput
    user?: UserOrderByWithRelationInput
  }

  export type FavoriteDocumentWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: FavoriteDocumentWhereInput | FavoriteDocumentWhereInput[]
    OR?: FavoriteDocumentWhereInput[]
    NOT?: FavoriteDocumentWhereInput | FavoriteDocumentWhereInput[]
    documentId?: StringFilter<"FavoriteDocument"> | string
    userId?: StringFilter<"FavoriteDocument"> | string
    document?: XOR<BaseDocumentRelationFilter, BaseDocumentWhereInput>
    user?: XOR<UserRelationFilter, UserWhereInput>
  }, "id">

  export type FavoriteDocumentOrderByWithAggregationInput = {
    id?: SortOrder
    documentId?: SortOrder
    userId?: SortOrder
    _count?: FavoriteDocumentCountOrderByAggregateInput
    _max?: FavoriteDocumentMaxOrderByAggregateInput
    _min?: FavoriteDocumentMinOrderByAggregateInput
  }

  export type FavoriteDocumentScalarWhereWithAggregatesInput = {
    AND?: FavoriteDocumentScalarWhereWithAggregatesInput | FavoriteDocumentScalarWhereWithAggregatesInput[]
    OR?: FavoriteDocumentScalarWhereWithAggregatesInput[]
    NOT?: FavoriteDocumentScalarWhereWithAggregatesInput | FavoriteDocumentScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"FavoriteDocument"> | string
    documentId?: StringWithAggregatesFilter<"FavoriteDocument"> | string
    userId?: StringWithAggregatesFilter<"FavoriteDocument"> | string
  }

  export type GeneratedImageWhereInput = {
    AND?: GeneratedImageWhereInput | GeneratedImageWhereInput[]
    OR?: GeneratedImageWhereInput[]
    NOT?: GeneratedImageWhereInput | GeneratedImageWhereInput[]
    id?: StringFilter<"GeneratedImage"> | string
    url?: StringFilter<"GeneratedImage"> | string
    createdAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    updatedAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    userId?: StringFilter<"GeneratedImage"> | string
    prompt?: StringFilter<"GeneratedImage"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
  }

  export type GeneratedImageOrderByWithRelationInput = {
    id?: SortOrder
    url?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    prompt?: SortOrder
    user?: UserOrderByWithRelationInput
  }

  export type GeneratedImageWhereUniqueInput = Prisma.AtLeast<{
    id?: string
    AND?: GeneratedImageWhereInput | GeneratedImageWhereInput[]
    OR?: GeneratedImageWhereInput[]
    NOT?: GeneratedImageWhereInput | GeneratedImageWhereInput[]
    url?: StringFilter<"GeneratedImage"> | string
    createdAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    updatedAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    userId?: StringFilter<"GeneratedImage"> | string
    prompt?: StringFilter<"GeneratedImage"> | string
    user?: XOR<UserRelationFilter, UserWhereInput>
  }, "id">

  export type GeneratedImageOrderByWithAggregationInput = {
    id?: SortOrder
    url?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    prompt?: SortOrder
    _count?: GeneratedImageCountOrderByAggregateInput
    _max?: GeneratedImageMaxOrderByAggregateInput
    _min?: GeneratedImageMinOrderByAggregateInput
  }

  export type GeneratedImageScalarWhereWithAggregatesInput = {
    AND?: GeneratedImageScalarWhereWithAggregatesInput | GeneratedImageScalarWhereWithAggregatesInput[]
    OR?: GeneratedImageScalarWhereWithAggregatesInput[]
    NOT?: GeneratedImageScalarWhereWithAggregatesInput | GeneratedImageScalarWhereWithAggregatesInput[]
    id?: StringWithAggregatesFilter<"GeneratedImage"> | string
    url?: StringWithAggregatesFilter<"GeneratedImage"> | string
    createdAt?: DateTimeWithAggregatesFilter<"GeneratedImage"> | Date | string
    updatedAt?: DateTimeWithAggregatesFilter<"GeneratedImage"> | Date | string
    userId?: StringWithAggregatesFilter<"GeneratedImage"> | string
    prompt?: StringWithAggregatesFilter<"GeneratedImage"> | string
  }

  export type UserCreateInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentUncheckedCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeUncheckedCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUncheckedUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateManyInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
  }

  export type UserUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
  }

  export type UserUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
  }

  export type BaseDocumentCreateInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    user: UserCreateNestedOneWithoutDocumentsInput
    presentation?: PresentationCreateNestedOneWithoutBaseInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentUncheckedCreateInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    userId: string
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    presentation?: PresentationUncheckedCreateNestedOneWithoutBaseInput
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    user?: UserUpdateOneRequiredWithoutDocumentsNestedInput
    presentation?: PresentationUpdateOneWithoutBaseNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutDocumentNestedInput
  }

  export type BaseDocumentUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    userId?: StringFieldUpdateOperationsInput | string
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    presentation?: PresentationUncheckedUpdateOneWithoutBaseNestedInput
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutDocumentNestedInput
  }

  export type BaseDocumentCreateManyInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    userId: string
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
  }

  export type BaseDocumentUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
  }

  export type BaseDocumentUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    userId?: StringFieldUpdateOperationsInput | string
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
  }

  export type PresentationCreateInput = {
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    base?: BaseDocumentCreateNestedOneWithoutPresentationInput
    customTheme?: CustomThemeCreateNestedOneWithoutPresentationsInput
  }

  export type PresentationUncheckedCreateInput = {
    id?: string
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    customThemeId?: string | null
  }

  export type PresentationUpdateInput = {
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    base?: BaseDocumentUpdateOneRequiredWithoutPresentationNestedInput
    customTheme?: CustomThemeUpdateOneWithoutPresentationsNestedInput
  }

  export type PresentationUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    customThemeId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type PresentationCreateManyInput = {
    id?: string
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    customThemeId?: string | null
  }

  export type PresentationUpdateManyMutationInput = {
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type PresentationUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    customThemeId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type CustomThemeCreateInput = {
    id?: string
    name: string
    description?: string | null
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
    user: UserCreateNestedOneWithoutCustomThemeInput
    presentations?: PresentationCreateNestedManyWithoutCustomThemeInput
  }

  export type CustomThemeUncheckedCreateInput = {
    id?: string
    name: string
    description?: string | null
    userId: string
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
    presentations?: PresentationUncheckedCreateNestedManyWithoutCustomThemeInput
  }

  export type CustomThemeUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
    user?: UserUpdateOneRequiredWithoutCustomThemeNestedInput
    presentations?: PresentationUpdateManyWithoutCustomThemeNestedInput
  }

  export type CustomThemeUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: StringFieldUpdateOperationsInput | string
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
    presentations?: PresentationUncheckedUpdateManyWithoutCustomThemeNestedInput
  }

  export type CustomThemeCreateManyInput = {
    id?: string
    name: string
    description?: string | null
    userId: string
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
  }

  export type CustomThemeUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
  }

  export type CustomThemeUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: StringFieldUpdateOperationsInput | string
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
  }

  export type FavoriteDocumentCreateInput = {
    id?: string
    document: BaseDocumentCreateNestedOneWithoutFavoritesInput
    user: UserCreateNestedOneWithoutFavoritesInput
  }

  export type FavoriteDocumentUncheckedCreateInput = {
    id?: string
    documentId: string
    userId: string
  }

  export type FavoriteDocumentUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    document?: BaseDocumentUpdateOneRequiredWithoutFavoritesNestedInput
    user?: UserUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type FavoriteDocumentUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    documentId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentCreateManyInput = {
    id?: string
    documentId: string
    userId: string
  }

  export type FavoriteDocumentUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    documentId?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type GeneratedImageCreateInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    prompt: string
    user: UserCreateNestedOneWithoutGeneratedImageInput
  }

  export type GeneratedImageUncheckedCreateInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: string
    prompt: string
  }

  export type GeneratedImageUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    prompt?: StringFieldUpdateOperationsInput | string
    user?: UserUpdateOneRequiredWithoutGeneratedImageNestedInput
  }

  export type GeneratedImageUncheckedUpdateInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type GeneratedImageCreateManyInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    userId: string
    prompt: string
  }

  export type GeneratedImageUpdateManyMutationInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type GeneratedImageUncheckedUpdateManyInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    userId?: StringFieldUpdateOperationsInput | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type StringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type StringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type DateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type DateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type StringNullableListFilter<$PrismaModel = never> = {
    equals?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    has?: string | StringFieldRefInput<$PrismaModel> | null
    hasEvery?: string[] | ListStringFieldRefInput<$PrismaModel>
    hasSome?: string[] | ListStringFieldRefInput<$PrismaModel>
    isEmpty?: boolean
  }

  export type EnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type BoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type BaseDocumentListRelationFilter = {
    every?: BaseDocumentWhereInput
    some?: BaseDocumentWhereInput
    none?: BaseDocumentWhereInput
  }

  export type FavoriteDocumentListRelationFilter = {
    every?: FavoriteDocumentWhereInput
    some?: FavoriteDocumentWhereInput
    none?: FavoriteDocumentWhereInput
  }

  export type CustomThemeListRelationFilter = {
    every?: CustomThemeWhereInput
    some?: CustomThemeWhereInput
    none?: CustomThemeWhereInput
  }

  export type GeneratedImageListRelationFilter = {
    every?: GeneratedImageWhereInput
    some?: GeneratedImageWhereInput
    none?: GeneratedImageWhereInput
  }

  export type SortOrderInput = {
    sort: SortOrder
    nulls?: NullsOrder
  }

  export type BaseDocumentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type FavoriteDocumentOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CustomThemeOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type GeneratedImageOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type UserCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    email?: SortOrder
    password?: SortOrder
    emailVerified?: SortOrder
    image?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    headline?: SortOrder
    bio?: SortOrder
    interests?: SortOrder
    location?: SortOrder
    website?: SortOrder
    role?: SortOrder
    hasAccess?: SortOrder
  }

  export type UserMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    email?: SortOrder
    password?: SortOrder
    emailVerified?: SortOrder
    image?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    headline?: SortOrder
    bio?: SortOrder
    location?: SortOrder
    website?: SortOrder
    role?: SortOrder
    hasAccess?: SortOrder
  }

  export type UserMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    email?: SortOrder
    password?: SortOrder
    emailVerified?: SortOrder
    image?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    headline?: SortOrder
    bio?: SortOrder
    location?: SortOrder
    website?: SortOrder
    role?: SortOrder
    hasAccess?: SortOrder
  }

  export type StringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type StringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    mode?: QueryMode
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type DateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type DateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type EnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type BoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type EnumDocumentTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.DocumentType | EnumDocumentTypeFieldRefInput<$PrismaModel>
    in?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumDocumentTypeFilter<$PrismaModel> | $Enums.DocumentType
  }

  export type UserRelationFilter = {
    is?: UserWhereInput
    isNot?: UserWhereInput
  }

  export type PresentationNullableRelationFilter = {
    is?: PresentationWhereInput | null
    isNot?: PresentationWhereInput | null
  }

  export type BaseDocumentCountOrderByAggregateInput = {
    id?: SortOrder
    title?: SortOrder
    type?: SortOrder
    userId?: SortOrder
    thumbnailUrl?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    isPublic?: SortOrder
    documentType?: SortOrder
  }

  export type BaseDocumentMaxOrderByAggregateInput = {
    id?: SortOrder
    title?: SortOrder
    type?: SortOrder
    userId?: SortOrder
    thumbnailUrl?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    isPublic?: SortOrder
    documentType?: SortOrder
  }

  export type BaseDocumentMinOrderByAggregateInput = {
    id?: SortOrder
    title?: SortOrder
    type?: SortOrder
    userId?: SortOrder
    thumbnailUrl?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    isPublic?: SortOrder
    documentType?: SortOrder
  }

  export type EnumDocumentTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.DocumentType | EnumDocumentTypeFieldRefInput<$PrismaModel>
    in?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumDocumentTypeWithAggregatesFilter<$PrismaModel> | $Enums.DocumentType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumDocumentTypeFilter<$PrismaModel>
    _max?: NestedEnumDocumentTypeFilter<$PrismaModel>
  }
  export type JsonFilter<$PrismaModel = never> = 
    | PatchUndefined<
        Either<Required<JsonFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonFilterBase<$PrismaModel>>, 'path'>>

  export type JsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type BaseDocumentRelationFilter = {
    is?: BaseDocumentWhereInput
    isNot?: BaseDocumentWhereInput
  }

  export type CustomThemeNullableRelationFilter = {
    is?: CustomThemeWhereInput | null
    isNot?: CustomThemeWhereInput | null
  }

  export type PresentationCountOrderByAggregateInput = {
    id?: SortOrder
    content?: SortOrder
    theme?: SortOrder
    imageModel?: SortOrder
    presentationStyle?: SortOrder
    language?: SortOrder
    outline?: SortOrder
    templateId?: SortOrder
    customThemeId?: SortOrder
  }

  export type PresentationMaxOrderByAggregateInput = {
    id?: SortOrder
    theme?: SortOrder
    imageModel?: SortOrder
    presentationStyle?: SortOrder
    language?: SortOrder
    templateId?: SortOrder
    customThemeId?: SortOrder
  }

  export type PresentationMinOrderByAggregateInput = {
    id?: SortOrder
    theme?: SortOrder
    imageModel?: SortOrder
    presentationStyle?: SortOrder
    language?: SortOrder
    templateId?: SortOrder
    customThemeId?: SortOrder
  }
  export type JsonWithAggregatesFilter<$PrismaModel = never> = 
    | PatchUndefined<
        Either<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, Exclude<keyof Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>,
        Required<JsonWithAggregatesFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<JsonWithAggregatesFilterBase<$PrismaModel>>, 'path'>>

  export type JsonWithAggregatesFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedJsonFilter<$PrismaModel>
    _max?: NestedJsonFilter<$PrismaModel>
  }

  export type PresentationListRelationFilter = {
    every?: PresentationWhereInput
    some?: PresentationWhereInput
    none?: PresentationWhereInput
  }

  export type PresentationOrderByRelationAggregateInput = {
    _count?: SortOrder
  }

  export type CustomThemeCountOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    userId?: SortOrder
    logoUrl?: SortOrder
    isPublic?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    themeData?: SortOrder
  }

  export type CustomThemeMaxOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    userId?: SortOrder
    logoUrl?: SortOrder
    isPublic?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type CustomThemeMinOrderByAggregateInput = {
    id?: SortOrder
    name?: SortOrder
    description?: SortOrder
    userId?: SortOrder
    logoUrl?: SortOrder
    isPublic?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
  }

  export type FavoriteDocumentCountOrderByAggregateInput = {
    id?: SortOrder
    documentId?: SortOrder
    userId?: SortOrder
  }

  export type FavoriteDocumentMaxOrderByAggregateInput = {
    id?: SortOrder
    documentId?: SortOrder
    userId?: SortOrder
  }

  export type FavoriteDocumentMinOrderByAggregateInput = {
    id?: SortOrder
    documentId?: SortOrder
    userId?: SortOrder
  }

  export type GeneratedImageCountOrderByAggregateInput = {
    id?: SortOrder
    url?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    prompt?: SortOrder
  }

  export type GeneratedImageMaxOrderByAggregateInput = {
    id?: SortOrder
    url?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    prompt?: SortOrder
  }

  export type GeneratedImageMinOrderByAggregateInput = {
    id?: SortOrder
    url?: SortOrder
    createdAt?: SortOrder
    updatedAt?: SortOrder
    userId?: SortOrder
    prompt?: SortOrder
  }

  export type UserCreateinterestsInput = {
    set: string[]
  }

  export type BaseDocumentCreateNestedManyWithoutUserInput = {
    create?: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput> | BaseDocumentCreateWithoutUserInput[] | BaseDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutUserInput | BaseDocumentCreateOrConnectWithoutUserInput[]
    createMany?: BaseDocumentCreateManyUserInputEnvelope
    connect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
  }

  export type FavoriteDocumentCreateNestedManyWithoutUserInput = {
    create?: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput> | FavoriteDocumentCreateWithoutUserInput[] | FavoriteDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutUserInput | FavoriteDocumentCreateOrConnectWithoutUserInput[]
    createMany?: FavoriteDocumentCreateManyUserInputEnvelope
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
  }

  export type CustomThemeCreateNestedManyWithoutUserInput = {
    create?: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput> | CustomThemeCreateWithoutUserInput[] | CustomThemeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CustomThemeCreateOrConnectWithoutUserInput | CustomThemeCreateOrConnectWithoutUserInput[]
    createMany?: CustomThemeCreateManyUserInputEnvelope
    connect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
  }

  export type GeneratedImageCreateNestedManyWithoutUserInput = {
    create?: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput> | GeneratedImageCreateWithoutUserInput[] | GeneratedImageUncheckedCreateWithoutUserInput[]
    connectOrCreate?: GeneratedImageCreateOrConnectWithoutUserInput | GeneratedImageCreateOrConnectWithoutUserInput[]
    createMany?: GeneratedImageCreateManyUserInputEnvelope
    connect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
  }

  export type BaseDocumentUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput> | BaseDocumentCreateWithoutUserInput[] | BaseDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutUserInput | BaseDocumentCreateOrConnectWithoutUserInput[]
    createMany?: BaseDocumentCreateManyUserInputEnvelope
    connect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
  }

  export type FavoriteDocumentUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput> | FavoriteDocumentCreateWithoutUserInput[] | FavoriteDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutUserInput | FavoriteDocumentCreateOrConnectWithoutUserInput[]
    createMany?: FavoriteDocumentCreateManyUserInputEnvelope
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
  }

  export type CustomThemeUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput> | CustomThemeCreateWithoutUserInput[] | CustomThemeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CustomThemeCreateOrConnectWithoutUserInput | CustomThemeCreateOrConnectWithoutUserInput[]
    createMany?: CustomThemeCreateManyUserInputEnvelope
    connect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
  }

  export type GeneratedImageUncheckedCreateNestedManyWithoutUserInput = {
    create?: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput> | GeneratedImageCreateWithoutUserInput[] | GeneratedImageUncheckedCreateWithoutUserInput[]
    connectOrCreate?: GeneratedImageCreateOrConnectWithoutUserInput | GeneratedImageCreateOrConnectWithoutUserInput[]
    createMany?: GeneratedImageCreateManyUserInputEnvelope
    connect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
  }

  export type StringFieldUpdateOperationsInput = {
    set?: string
  }

  export type NullableStringFieldUpdateOperationsInput = {
    set?: string | null
  }

  export type NullableDateTimeFieldUpdateOperationsInput = {
    set?: Date | string | null
  }

  export type DateTimeFieldUpdateOperationsInput = {
    set?: Date | string
  }

  export type UserUpdateinterestsInput = {
    set?: string[]
    push?: string | string[]
  }

  export type EnumUserRoleFieldUpdateOperationsInput = {
    set?: $Enums.UserRole
  }

  export type BoolFieldUpdateOperationsInput = {
    set?: boolean
  }

  export type BaseDocumentUpdateManyWithoutUserNestedInput = {
    create?: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput> | BaseDocumentCreateWithoutUserInput[] | BaseDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutUserInput | BaseDocumentCreateOrConnectWithoutUserInput[]
    upsert?: BaseDocumentUpsertWithWhereUniqueWithoutUserInput | BaseDocumentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: BaseDocumentCreateManyUserInputEnvelope
    set?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    disconnect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    delete?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    connect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    update?: BaseDocumentUpdateWithWhereUniqueWithoutUserInput | BaseDocumentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: BaseDocumentUpdateManyWithWhereWithoutUserInput | BaseDocumentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: BaseDocumentScalarWhereInput | BaseDocumentScalarWhereInput[]
  }

  export type FavoriteDocumentUpdateManyWithoutUserNestedInput = {
    create?: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput> | FavoriteDocumentCreateWithoutUserInput[] | FavoriteDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutUserInput | FavoriteDocumentCreateOrConnectWithoutUserInput[]
    upsert?: FavoriteDocumentUpsertWithWhereUniqueWithoutUserInput | FavoriteDocumentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: FavoriteDocumentCreateManyUserInputEnvelope
    set?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    disconnect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    delete?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    update?: FavoriteDocumentUpdateWithWhereUniqueWithoutUserInput | FavoriteDocumentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: FavoriteDocumentUpdateManyWithWhereWithoutUserInput | FavoriteDocumentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
  }

  export type CustomThemeUpdateManyWithoutUserNestedInput = {
    create?: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput> | CustomThemeCreateWithoutUserInput[] | CustomThemeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CustomThemeCreateOrConnectWithoutUserInput | CustomThemeCreateOrConnectWithoutUserInput[]
    upsert?: CustomThemeUpsertWithWhereUniqueWithoutUserInput | CustomThemeUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CustomThemeCreateManyUserInputEnvelope
    set?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    disconnect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    delete?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    connect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    update?: CustomThemeUpdateWithWhereUniqueWithoutUserInput | CustomThemeUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CustomThemeUpdateManyWithWhereWithoutUserInput | CustomThemeUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CustomThemeScalarWhereInput | CustomThemeScalarWhereInput[]
  }

  export type GeneratedImageUpdateManyWithoutUserNestedInput = {
    create?: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput> | GeneratedImageCreateWithoutUserInput[] | GeneratedImageUncheckedCreateWithoutUserInput[]
    connectOrCreate?: GeneratedImageCreateOrConnectWithoutUserInput | GeneratedImageCreateOrConnectWithoutUserInput[]
    upsert?: GeneratedImageUpsertWithWhereUniqueWithoutUserInput | GeneratedImageUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: GeneratedImageCreateManyUserInputEnvelope
    set?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    disconnect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    delete?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    connect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    update?: GeneratedImageUpdateWithWhereUniqueWithoutUserInput | GeneratedImageUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: GeneratedImageUpdateManyWithWhereWithoutUserInput | GeneratedImageUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: GeneratedImageScalarWhereInput | GeneratedImageScalarWhereInput[]
  }

  export type BaseDocumentUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput> | BaseDocumentCreateWithoutUserInput[] | BaseDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutUserInput | BaseDocumentCreateOrConnectWithoutUserInput[]
    upsert?: BaseDocumentUpsertWithWhereUniqueWithoutUserInput | BaseDocumentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: BaseDocumentCreateManyUserInputEnvelope
    set?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    disconnect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    delete?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    connect?: BaseDocumentWhereUniqueInput | BaseDocumentWhereUniqueInput[]
    update?: BaseDocumentUpdateWithWhereUniqueWithoutUserInput | BaseDocumentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: BaseDocumentUpdateManyWithWhereWithoutUserInput | BaseDocumentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: BaseDocumentScalarWhereInput | BaseDocumentScalarWhereInput[]
  }

  export type FavoriteDocumentUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput> | FavoriteDocumentCreateWithoutUserInput[] | FavoriteDocumentUncheckedCreateWithoutUserInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutUserInput | FavoriteDocumentCreateOrConnectWithoutUserInput[]
    upsert?: FavoriteDocumentUpsertWithWhereUniqueWithoutUserInput | FavoriteDocumentUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: FavoriteDocumentCreateManyUserInputEnvelope
    set?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    disconnect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    delete?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    update?: FavoriteDocumentUpdateWithWhereUniqueWithoutUserInput | FavoriteDocumentUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: FavoriteDocumentUpdateManyWithWhereWithoutUserInput | FavoriteDocumentUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
  }

  export type CustomThemeUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput> | CustomThemeCreateWithoutUserInput[] | CustomThemeUncheckedCreateWithoutUserInput[]
    connectOrCreate?: CustomThemeCreateOrConnectWithoutUserInput | CustomThemeCreateOrConnectWithoutUserInput[]
    upsert?: CustomThemeUpsertWithWhereUniqueWithoutUserInput | CustomThemeUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: CustomThemeCreateManyUserInputEnvelope
    set?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    disconnect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    delete?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    connect?: CustomThemeWhereUniqueInput | CustomThemeWhereUniqueInput[]
    update?: CustomThemeUpdateWithWhereUniqueWithoutUserInput | CustomThemeUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: CustomThemeUpdateManyWithWhereWithoutUserInput | CustomThemeUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: CustomThemeScalarWhereInput | CustomThemeScalarWhereInput[]
  }

  export type GeneratedImageUncheckedUpdateManyWithoutUserNestedInput = {
    create?: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput> | GeneratedImageCreateWithoutUserInput[] | GeneratedImageUncheckedCreateWithoutUserInput[]
    connectOrCreate?: GeneratedImageCreateOrConnectWithoutUserInput | GeneratedImageCreateOrConnectWithoutUserInput[]
    upsert?: GeneratedImageUpsertWithWhereUniqueWithoutUserInput | GeneratedImageUpsertWithWhereUniqueWithoutUserInput[]
    createMany?: GeneratedImageCreateManyUserInputEnvelope
    set?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    disconnect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    delete?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    connect?: GeneratedImageWhereUniqueInput | GeneratedImageWhereUniqueInput[]
    update?: GeneratedImageUpdateWithWhereUniqueWithoutUserInput | GeneratedImageUpdateWithWhereUniqueWithoutUserInput[]
    updateMany?: GeneratedImageUpdateManyWithWhereWithoutUserInput | GeneratedImageUpdateManyWithWhereWithoutUserInput[]
    deleteMany?: GeneratedImageScalarWhereInput | GeneratedImageScalarWhereInput[]
  }

  export type UserCreateNestedOneWithoutDocumentsInput = {
    create?: XOR<UserCreateWithoutDocumentsInput, UserUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: UserCreateOrConnectWithoutDocumentsInput
    connect?: UserWhereUniqueInput
  }

  export type PresentationCreateNestedOneWithoutBaseInput = {
    create?: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
    connectOrCreate?: PresentationCreateOrConnectWithoutBaseInput
    connect?: PresentationWhereUniqueInput
  }

  export type FavoriteDocumentCreateNestedManyWithoutDocumentInput = {
    create?: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput> | FavoriteDocumentCreateWithoutDocumentInput[] | FavoriteDocumentUncheckedCreateWithoutDocumentInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutDocumentInput | FavoriteDocumentCreateOrConnectWithoutDocumentInput[]
    createMany?: FavoriteDocumentCreateManyDocumentInputEnvelope
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
  }

  export type PresentationUncheckedCreateNestedOneWithoutBaseInput = {
    create?: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
    connectOrCreate?: PresentationCreateOrConnectWithoutBaseInput
    connect?: PresentationWhereUniqueInput
  }

  export type FavoriteDocumentUncheckedCreateNestedManyWithoutDocumentInput = {
    create?: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput> | FavoriteDocumentCreateWithoutDocumentInput[] | FavoriteDocumentUncheckedCreateWithoutDocumentInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutDocumentInput | FavoriteDocumentCreateOrConnectWithoutDocumentInput[]
    createMany?: FavoriteDocumentCreateManyDocumentInputEnvelope
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
  }

  export type EnumDocumentTypeFieldUpdateOperationsInput = {
    set?: $Enums.DocumentType
  }

  export type UserUpdateOneRequiredWithoutDocumentsNestedInput = {
    create?: XOR<UserCreateWithoutDocumentsInput, UserUncheckedCreateWithoutDocumentsInput>
    connectOrCreate?: UserCreateOrConnectWithoutDocumentsInput
    upsert?: UserUpsertWithoutDocumentsInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutDocumentsInput, UserUpdateWithoutDocumentsInput>, UserUncheckedUpdateWithoutDocumentsInput>
  }

  export type PresentationUpdateOneWithoutBaseNestedInput = {
    create?: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
    connectOrCreate?: PresentationCreateOrConnectWithoutBaseInput
    upsert?: PresentationUpsertWithoutBaseInput
    disconnect?: PresentationWhereInput | boolean
    delete?: PresentationWhereInput | boolean
    connect?: PresentationWhereUniqueInput
    update?: XOR<XOR<PresentationUpdateToOneWithWhereWithoutBaseInput, PresentationUpdateWithoutBaseInput>, PresentationUncheckedUpdateWithoutBaseInput>
  }

  export type FavoriteDocumentUpdateManyWithoutDocumentNestedInput = {
    create?: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput> | FavoriteDocumentCreateWithoutDocumentInput[] | FavoriteDocumentUncheckedCreateWithoutDocumentInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutDocumentInput | FavoriteDocumentCreateOrConnectWithoutDocumentInput[]
    upsert?: FavoriteDocumentUpsertWithWhereUniqueWithoutDocumentInput | FavoriteDocumentUpsertWithWhereUniqueWithoutDocumentInput[]
    createMany?: FavoriteDocumentCreateManyDocumentInputEnvelope
    set?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    disconnect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    delete?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    update?: FavoriteDocumentUpdateWithWhereUniqueWithoutDocumentInput | FavoriteDocumentUpdateWithWhereUniqueWithoutDocumentInput[]
    updateMany?: FavoriteDocumentUpdateManyWithWhereWithoutDocumentInput | FavoriteDocumentUpdateManyWithWhereWithoutDocumentInput[]
    deleteMany?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
  }

  export type PresentationUncheckedUpdateOneWithoutBaseNestedInput = {
    create?: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
    connectOrCreate?: PresentationCreateOrConnectWithoutBaseInput
    upsert?: PresentationUpsertWithoutBaseInput
    disconnect?: PresentationWhereInput | boolean
    delete?: PresentationWhereInput | boolean
    connect?: PresentationWhereUniqueInput
    update?: XOR<XOR<PresentationUpdateToOneWithWhereWithoutBaseInput, PresentationUpdateWithoutBaseInput>, PresentationUncheckedUpdateWithoutBaseInput>
  }

  export type FavoriteDocumentUncheckedUpdateManyWithoutDocumentNestedInput = {
    create?: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput> | FavoriteDocumentCreateWithoutDocumentInput[] | FavoriteDocumentUncheckedCreateWithoutDocumentInput[]
    connectOrCreate?: FavoriteDocumentCreateOrConnectWithoutDocumentInput | FavoriteDocumentCreateOrConnectWithoutDocumentInput[]
    upsert?: FavoriteDocumentUpsertWithWhereUniqueWithoutDocumentInput | FavoriteDocumentUpsertWithWhereUniqueWithoutDocumentInput[]
    createMany?: FavoriteDocumentCreateManyDocumentInputEnvelope
    set?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    disconnect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    delete?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    connect?: FavoriteDocumentWhereUniqueInput | FavoriteDocumentWhereUniqueInput[]
    update?: FavoriteDocumentUpdateWithWhereUniqueWithoutDocumentInput | FavoriteDocumentUpdateWithWhereUniqueWithoutDocumentInput[]
    updateMany?: FavoriteDocumentUpdateManyWithWhereWithoutDocumentInput | FavoriteDocumentUpdateManyWithWhereWithoutDocumentInput[]
    deleteMany?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
  }

  export type PresentationCreateoutlineInput = {
    set: string[]
  }

  export type BaseDocumentCreateNestedOneWithoutPresentationInput = {
    create?: XOR<BaseDocumentCreateWithoutPresentationInput, BaseDocumentUncheckedCreateWithoutPresentationInput>
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutPresentationInput
    connect?: BaseDocumentWhereUniqueInput
  }

  export type CustomThemeCreateNestedOneWithoutPresentationsInput = {
    create?: XOR<CustomThemeCreateWithoutPresentationsInput, CustomThemeUncheckedCreateWithoutPresentationsInput>
    connectOrCreate?: CustomThemeCreateOrConnectWithoutPresentationsInput
    connect?: CustomThemeWhereUniqueInput
  }

  export type PresentationUpdateoutlineInput = {
    set?: string[]
    push?: string | string[]
  }

  export type BaseDocumentUpdateOneRequiredWithoutPresentationNestedInput = {
    create?: XOR<BaseDocumentCreateWithoutPresentationInput, BaseDocumentUncheckedCreateWithoutPresentationInput>
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutPresentationInput
    upsert?: BaseDocumentUpsertWithoutPresentationInput
    connect?: BaseDocumentWhereUniqueInput
    update?: XOR<XOR<BaseDocumentUpdateToOneWithWhereWithoutPresentationInput, BaseDocumentUpdateWithoutPresentationInput>, BaseDocumentUncheckedUpdateWithoutPresentationInput>
  }

  export type CustomThemeUpdateOneWithoutPresentationsNestedInput = {
    create?: XOR<CustomThemeCreateWithoutPresentationsInput, CustomThemeUncheckedCreateWithoutPresentationsInput>
    connectOrCreate?: CustomThemeCreateOrConnectWithoutPresentationsInput
    upsert?: CustomThemeUpsertWithoutPresentationsInput
    disconnect?: CustomThemeWhereInput | boolean
    delete?: CustomThemeWhereInput | boolean
    connect?: CustomThemeWhereUniqueInput
    update?: XOR<XOR<CustomThemeUpdateToOneWithWhereWithoutPresentationsInput, CustomThemeUpdateWithoutPresentationsInput>, CustomThemeUncheckedUpdateWithoutPresentationsInput>
  }

  export type UserCreateNestedOneWithoutCustomThemeInput = {
    create?: XOR<UserCreateWithoutCustomThemeInput, UserUncheckedCreateWithoutCustomThemeInput>
    connectOrCreate?: UserCreateOrConnectWithoutCustomThemeInput
    connect?: UserWhereUniqueInput
  }

  export type PresentationCreateNestedManyWithoutCustomThemeInput = {
    create?: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput> | PresentationCreateWithoutCustomThemeInput[] | PresentationUncheckedCreateWithoutCustomThemeInput[]
    connectOrCreate?: PresentationCreateOrConnectWithoutCustomThemeInput | PresentationCreateOrConnectWithoutCustomThemeInput[]
    createMany?: PresentationCreateManyCustomThemeInputEnvelope
    connect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
  }

  export type PresentationUncheckedCreateNestedManyWithoutCustomThemeInput = {
    create?: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput> | PresentationCreateWithoutCustomThemeInput[] | PresentationUncheckedCreateWithoutCustomThemeInput[]
    connectOrCreate?: PresentationCreateOrConnectWithoutCustomThemeInput | PresentationCreateOrConnectWithoutCustomThemeInput[]
    createMany?: PresentationCreateManyCustomThemeInputEnvelope
    connect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
  }

  export type UserUpdateOneRequiredWithoutCustomThemeNestedInput = {
    create?: XOR<UserCreateWithoutCustomThemeInput, UserUncheckedCreateWithoutCustomThemeInput>
    connectOrCreate?: UserCreateOrConnectWithoutCustomThemeInput
    upsert?: UserUpsertWithoutCustomThemeInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutCustomThemeInput, UserUpdateWithoutCustomThemeInput>, UserUncheckedUpdateWithoutCustomThemeInput>
  }

  export type PresentationUpdateManyWithoutCustomThemeNestedInput = {
    create?: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput> | PresentationCreateWithoutCustomThemeInput[] | PresentationUncheckedCreateWithoutCustomThemeInput[]
    connectOrCreate?: PresentationCreateOrConnectWithoutCustomThemeInput | PresentationCreateOrConnectWithoutCustomThemeInput[]
    upsert?: PresentationUpsertWithWhereUniqueWithoutCustomThemeInput | PresentationUpsertWithWhereUniqueWithoutCustomThemeInput[]
    createMany?: PresentationCreateManyCustomThemeInputEnvelope
    set?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    disconnect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    delete?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    connect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    update?: PresentationUpdateWithWhereUniqueWithoutCustomThemeInput | PresentationUpdateWithWhereUniqueWithoutCustomThemeInput[]
    updateMany?: PresentationUpdateManyWithWhereWithoutCustomThemeInput | PresentationUpdateManyWithWhereWithoutCustomThemeInput[]
    deleteMany?: PresentationScalarWhereInput | PresentationScalarWhereInput[]
  }

  export type PresentationUncheckedUpdateManyWithoutCustomThemeNestedInput = {
    create?: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput> | PresentationCreateWithoutCustomThemeInput[] | PresentationUncheckedCreateWithoutCustomThemeInput[]
    connectOrCreate?: PresentationCreateOrConnectWithoutCustomThemeInput | PresentationCreateOrConnectWithoutCustomThemeInput[]
    upsert?: PresentationUpsertWithWhereUniqueWithoutCustomThemeInput | PresentationUpsertWithWhereUniqueWithoutCustomThemeInput[]
    createMany?: PresentationCreateManyCustomThemeInputEnvelope
    set?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    disconnect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    delete?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    connect?: PresentationWhereUniqueInput | PresentationWhereUniqueInput[]
    update?: PresentationUpdateWithWhereUniqueWithoutCustomThemeInput | PresentationUpdateWithWhereUniqueWithoutCustomThemeInput[]
    updateMany?: PresentationUpdateManyWithWhereWithoutCustomThemeInput | PresentationUpdateManyWithWhereWithoutCustomThemeInput[]
    deleteMany?: PresentationScalarWhereInput | PresentationScalarWhereInput[]
  }

  export type BaseDocumentCreateNestedOneWithoutFavoritesInput = {
    create?: XOR<BaseDocumentCreateWithoutFavoritesInput, BaseDocumentUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutFavoritesInput
    connect?: BaseDocumentWhereUniqueInput
  }

  export type UserCreateNestedOneWithoutFavoritesInput = {
    create?: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: UserCreateOrConnectWithoutFavoritesInput
    connect?: UserWhereUniqueInput
  }

  export type BaseDocumentUpdateOneRequiredWithoutFavoritesNestedInput = {
    create?: XOR<BaseDocumentCreateWithoutFavoritesInput, BaseDocumentUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: BaseDocumentCreateOrConnectWithoutFavoritesInput
    upsert?: BaseDocumentUpsertWithoutFavoritesInput
    connect?: BaseDocumentWhereUniqueInput
    update?: XOR<XOR<BaseDocumentUpdateToOneWithWhereWithoutFavoritesInput, BaseDocumentUpdateWithoutFavoritesInput>, BaseDocumentUncheckedUpdateWithoutFavoritesInput>
  }

  export type UserUpdateOneRequiredWithoutFavoritesNestedInput = {
    create?: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    connectOrCreate?: UserCreateOrConnectWithoutFavoritesInput
    upsert?: UserUpsertWithoutFavoritesInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutFavoritesInput, UserUpdateWithoutFavoritesInput>, UserUncheckedUpdateWithoutFavoritesInput>
  }

  export type UserCreateNestedOneWithoutGeneratedImageInput = {
    create?: XOR<UserCreateWithoutGeneratedImageInput, UserUncheckedCreateWithoutGeneratedImageInput>
    connectOrCreate?: UserCreateOrConnectWithoutGeneratedImageInput
    connect?: UserWhereUniqueInput
  }

  export type UserUpdateOneRequiredWithoutGeneratedImageNestedInput = {
    create?: XOR<UserCreateWithoutGeneratedImageInput, UserUncheckedCreateWithoutGeneratedImageInput>
    connectOrCreate?: UserCreateOrConnectWithoutGeneratedImageInput
    upsert?: UserUpsertWithoutGeneratedImageInput
    connect?: UserWhereUniqueInput
    update?: XOR<XOR<UserUpdateToOneWithWhereWithoutGeneratedImageInput, UserUpdateWithoutGeneratedImageInput>, UserUncheckedUpdateWithoutGeneratedImageInput>
  }

  export type NestedStringFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringFilter<$PrismaModel> | string
  }

  export type NestedStringNullableFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableFilter<$PrismaModel> | string | null
  }

  export type NestedDateTimeNullableFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableFilter<$PrismaModel> | Date | string | null
  }

  export type NestedDateTimeFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeFilter<$PrismaModel> | Date | string
  }

  export type NestedEnumUserRoleFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleFilter<$PrismaModel> | $Enums.UserRole
  }

  export type NestedBoolFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolFilter<$PrismaModel> | boolean
  }

  export type NestedStringWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel>
    in?: string[] | ListStringFieldRefInput<$PrismaModel>
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel>
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringWithAggregatesFilter<$PrismaModel> | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedStringFilter<$PrismaModel>
    _max?: NestedStringFilter<$PrismaModel>
  }

  export type NestedIntFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel>
    in?: number[] | ListIntFieldRefInput<$PrismaModel>
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel>
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntFilter<$PrismaModel> | number
  }

  export type NestedStringNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: string | StringFieldRefInput<$PrismaModel> | null
    in?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    notIn?: string[] | ListStringFieldRefInput<$PrismaModel> | null
    lt?: string | StringFieldRefInput<$PrismaModel>
    lte?: string | StringFieldRefInput<$PrismaModel>
    gt?: string | StringFieldRefInput<$PrismaModel>
    gte?: string | StringFieldRefInput<$PrismaModel>
    contains?: string | StringFieldRefInput<$PrismaModel>
    startsWith?: string | StringFieldRefInput<$PrismaModel>
    endsWith?: string | StringFieldRefInput<$PrismaModel>
    not?: NestedStringNullableWithAggregatesFilter<$PrismaModel> | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedStringNullableFilter<$PrismaModel>
    _max?: NestedStringNullableFilter<$PrismaModel>
  }

  export type NestedIntNullableFilter<$PrismaModel = never> = {
    equals?: number | IntFieldRefInput<$PrismaModel> | null
    in?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    notIn?: number[] | ListIntFieldRefInput<$PrismaModel> | null
    lt?: number | IntFieldRefInput<$PrismaModel>
    lte?: number | IntFieldRefInput<$PrismaModel>
    gt?: number | IntFieldRefInput<$PrismaModel>
    gte?: number | IntFieldRefInput<$PrismaModel>
    not?: NestedIntNullableFilter<$PrismaModel> | number | null
  }

  export type NestedDateTimeNullableWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel> | null
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel> | null
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeNullableWithAggregatesFilter<$PrismaModel> | Date | string | null
    _count?: NestedIntNullableFilter<$PrismaModel>
    _min?: NestedDateTimeNullableFilter<$PrismaModel>
    _max?: NestedDateTimeNullableFilter<$PrismaModel>
  }

  export type NestedDateTimeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    in?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    notIn?: Date[] | string[] | ListDateTimeFieldRefInput<$PrismaModel>
    lt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    lte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gt?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    gte?: Date | string | DateTimeFieldRefInput<$PrismaModel>
    not?: NestedDateTimeWithAggregatesFilter<$PrismaModel> | Date | string
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedDateTimeFilter<$PrismaModel>
    _max?: NestedDateTimeFilter<$PrismaModel>
  }

  export type NestedEnumUserRoleWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.UserRole | EnumUserRoleFieldRefInput<$PrismaModel>
    in?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    notIn?: $Enums.UserRole[] | ListEnumUserRoleFieldRefInput<$PrismaModel>
    not?: NestedEnumUserRoleWithAggregatesFilter<$PrismaModel> | $Enums.UserRole
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumUserRoleFilter<$PrismaModel>
    _max?: NestedEnumUserRoleFilter<$PrismaModel>
  }

  export type NestedBoolWithAggregatesFilter<$PrismaModel = never> = {
    equals?: boolean | BooleanFieldRefInput<$PrismaModel>
    not?: NestedBoolWithAggregatesFilter<$PrismaModel> | boolean
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedBoolFilter<$PrismaModel>
    _max?: NestedBoolFilter<$PrismaModel>
  }

  export type NestedEnumDocumentTypeFilter<$PrismaModel = never> = {
    equals?: $Enums.DocumentType | EnumDocumentTypeFieldRefInput<$PrismaModel>
    in?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumDocumentTypeFilter<$PrismaModel> | $Enums.DocumentType
  }

  export type NestedEnumDocumentTypeWithAggregatesFilter<$PrismaModel = never> = {
    equals?: $Enums.DocumentType | EnumDocumentTypeFieldRefInput<$PrismaModel>
    in?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    notIn?: $Enums.DocumentType[] | ListEnumDocumentTypeFieldRefInput<$PrismaModel>
    not?: NestedEnumDocumentTypeWithAggregatesFilter<$PrismaModel> | $Enums.DocumentType
    _count?: NestedIntFilter<$PrismaModel>
    _min?: NestedEnumDocumentTypeFilter<$PrismaModel>
    _max?: NestedEnumDocumentTypeFilter<$PrismaModel>
  }
  export type NestedJsonFilter<$PrismaModel = never> = 
    | PatchUndefined<
        Either<Required<NestedJsonFilterBase<$PrismaModel>>, Exclude<keyof Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>,
        Required<NestedJsonFilterBase<$PrismaModel>>
      >
    | OptionalFlat<Omit<Required<NestedJsonFilterBase<$PrismaModel>>, 'path'>>

  export type NestedJsonFilterBase<$PrismaModel = never> = {
    equals?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
    path?: string[]
    string_contains?: string | StringFieldRefInput<$PrismaModel>
    string_starts_with?: string | StringFieldRefInput<$PrismaModel>
    string_ends_with?: string | StringFieldRefInput<$PrismaModel>
    array_contains?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_starts_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    array_ends_with?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | null
    lt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    lte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gt?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    gte?: InputJsonValue | JsonFieldRefInput<$PrismaModel>
    not?: InputJsonValue | JsonFieldRefInput<$PrismaModel> | JsonNullValueFilter
  }

  export type BaseDocumentCreateWithoutUserInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    presentation?: PresentationCreateNestedOneWithoutBaseInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentUncheckedCreateWithoutUserInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    presentation?: PresentationUncheckedCreateNestedOneWithoutBaseInput
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentCreateOrConnectWithoutUserInput = {
    where: BaseDocumentWhereUniqueInput
    create: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput>
  }

  export type BaseDocumentCreateManyUserInputEnvelope = {
    data: BaseDocumentCreateManyUserInput | BaseDocumentCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type FavoriteDocumentCreateWithoutUserInput = {
    id?: string
    document: BaseDocumentCreateNestedOneWithoutFavoritesInput
  }

  export type FavoriteDocumentUncheckedCreateWithoutUserInput = {
    id?: string
    documentId: string
  }

  export type FavoriteDocumentCreateOrConnectWithoutUserInput = {
    where: FavoriteDocumentWhereUniqueInput
    create: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput>
  }

  export type FavoriteDocumentCreateManyUserInputEnvelope = {
    data: FavoriteDocumentCreateManyUserInput | FavoriteDocumentCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type CustomThemeCreateWithoutUserInput = {
    id?: string
    name: string
    description?: string | null
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
    presentations?: PresentationCreateNestedManyWithoutCustomThemeInput
  }

  export type CustomThemeUncheckedCreateWithoutUserInput = {
    id?: string
    name: string
    description?: string | null
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
    presentations?: PresentationUncheckedCreateNestedManyWithoutCustomThemeInput
  }

  export type CustomThemeCreateOrConnectWithoutUserInput = {
    where: CustomThemeWhereUniqueInput
    create: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput>
  }

  export type CustomThemeCreateManyUserInputEnvelope = {
    data: CustomThemeCreateManyUserInput | CustomThemeCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type GeneratedImageCreateWithoutUserInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    prompt: string
  }

  export type GeneratedImageUncheckedCreateWithoutUserInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    prompt: string
  }

  export type GeneratedImageCreateOrConnectWithoutUserInput = {
    where: GeneratedImageWhereUniqueInput
    create: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput>
  }

  export type GeneratedImageCreateManyUserInputEnvelope = {
    data: GeneratedImageCreateManyUserInput | GeneratedImageCreateManyUserInput[]
    skipDuplicates?: boolean
  }

  export type BaseDocumentUpsertWithWhereUniqueWithoutUserInput = {
    where: BaseDocumentWhereUniqueInput
    update: XOR<BaseDocumentUpdateWithoutUserInput, BaseDocumentUncheckedUpdateWithoutUserInput>
    create: XOR<BaseDocumentCreateWithoutUserInput, BaseDocumentUncheckedCreateWithoutUserInput>
  }

  export type BaseDocumentUpdateWithWhereUniqueWithoutUserInput = {
    where: BaseDocumentWhereUniqueInput
    data: XOR<BaseDocumentUpdateWithoutUserInput, BaseDocumentUncheckedUpdateWithoutUserInput>
  }

  export type BaseDocumentUpdateManyWithWhereWithoutUserInput = {
    where: BaseDocumentScalarWhereInput
    data: XOR<BaseDocumentUpdateManyMutationInput, BaseDocumentUncheckedUpdateManyWithoutUserInput>
  }

  export type BaseDocumentScalarWhereInput = {
    AND?: BaseDocumentScalarWhereInput | BaseDocumentScalarWhereInput[]
    OR?: BaseDocumentScalarWhereInput[]
    NOT?: BaseDocumentScalarWhereInput | BaseDocumentScalarWhereInput[]
    id?: StringFilter<"BaseDocument"> | string
    title?: StringFilter<"BaseDocument"> | string
    type?: EnumDocumentTypeFilter<"BaseDocument"> | $Enums.DocumentType
    userId?: StringFilter<"BaseDocument"> | string
    thumbnailUrl?: StringNullableFilter<"BaseDocument"> | string | null
    createdAt?: DateTimeFilter<"BaseDocument"> | Date | string
    updatedAt?: DateTimeFilter<"BaseDocument"> | Date | string
    isPublic?: BoolFilter<"BaseDocument"> | boolean
    documentType?: StringFilter<"BaseDocument"> | string
  }

  export type FavoriteDocumentUpsertWithWhereUniqueWithoutUserInput = {
    where: FavoriteDocumentWhereUniqueInput
    update: XOR<FavoriteDocumentUpdateWithoutUserInput, FavoriteDocumentUncheckedUpdateWithoutUserInput>
    create: XOR<FavoriteDocumentCreateWithoutUserInput, FavoriteDocumentUncheckedCreateWithoutUserInput>
  }

  export type FavoriteDocumentUpdateWithWhereUniqueWithoutUserInput = {
    where: FavoriteDocumentWhereUniqueInput
    data: XOR<FavoriteDocumentUpdateWithoutUserInput, FavoriteDocumentUncheckedUpdateWithoutUserInput>
  }

  export type FavoriteDocumentUpdateManyWithWhereWithoutUserInput = {
    where: FavoriteDocumentScalarWhereInput
    data: XOR<FavoriteDocumentUpdateManyMutationInput, FavoriteDocumentUncheckedUpdateManyWithoutUserInput>
  }

  export type FavoriteDocumentScalarWhereInput = {
    AND?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
    OR?: FavoriteDocumentScalarWhereInput[]
    NOT?: FavoriteDocumentScalarWhereInput | FavoriteDocumentScalarWhereInput[]
    id?: StringFilter<"FavoriteDocument"> | string
    documentId?: StringFilter<"FavoriteDocument"> | string
    userId?: StringFilter<"FavoriteDocument"> | string
  }

  export type CustomThemeUpsertWithWhereUniqueWithoutUserInput = {
    where: CustomThemeWhereUniqueInput
    update: XOR<CustomThemeUpdateWithoutUserInput, CustomThemeUncheckedUpdateWithoutUserInput>
    create: XOR<CustomThemeCreateWithoutUserInput, CustomThemeUncheckedCreateWithoutUserInput>
  }

  export type CustomThemeUpdateWithWhereUniqueWithoutUserInput = {
    where: CustomThemeWhereUniqueInput
    data: XOR<CustomThemeUpdateWithoutUserInput, CustomThemeUncheckedUpdateWithoutUserInput>
  }

  export type CustomThemeUpdateManyWithWhereWithoutUserInput = {
    where: CustomThemeScalarWhereInput
    data: XOR<CustomThemeUpdateManyMutationInput, CustomThemeUncheckedUpdateManyWithoutUserInput>
  }

  export type CustomThemeScalarWhereInput = {
    AND?: CustomThemeScalarWhereInput | CustomThemeScalarWhereInput[]
    OR?: CustomThemeScalarWhereInput[]
    NOT?: CustomThemeScalarWhereInput | CustomThemeScalarWhereInput[]
    id?: StringFilter<"CustomTheme"> | string
    name?: StringFilter<"CustomTheme"> | string
    description?: StringNullableFilter<"CustomTheme"> | string | null
    userId?: StringFilter<"CustomTheme"> | string
    logoUrl?: StringNullableFilter<"CustomTheme"> | string | null
    isPublic?: BoolFilter<"CustomTheme"> | boolean
    createdAt?: DateTimeFilter<"CustomTheme"> | Date | string
    updatedAt?: DateTimeFilter<"CustomTheme"> | Date | string
    themeData?: JsonFilter<"CustomTheme">
  }

  export type GeneratedImageUpsertWithWhereUniqueWithoutUserInput = {
    where: GeneratedImageWhereUniqueInput
    update: XOR<GeneratedImageUpdateWithoutUserInput, GeneratedImageUncheckedUpdateWithoutUserInput>
    create: XOR<GeneratedImageCreateWithoutUserInput, GeneratedImageUncheckedCreateWithoutUserInput>
  }

  export type GeneratedImageUpdateWithWhereUniqueWithoutUserInput = {
    where: GeneratedImageWhereUniqueInput
    data: XOR<GeneratedImageUpdateWithoutUserInput, GeneratedImageUncheckedUpdateWithoutUserInput>
  }

  export type GeneratedImageUpdateManyWithWhereWithoutUserInput = {
    where: GeneratedImageScalarWhereInput
    data: XOR<GeneratedImageUpdateManyMutationInput, GeneratedImageUncheckedUpdateManyWithoutUserInput>
  }

  export type GeneratedImageScalarWhereInput = {
    AND?: GeneratedImageScalarWhereInput | GeneratedImageScalarWhereInput[]
    OR?: GeneratedImageScalarWhereInput[]
    NOT?: GeneratedImageScalarWhereInput | GeneratedImageScalarWhereInput[]
    id?: StringFilter<"GeneratedImage"> | string
    url?: StringFilter<"GeneratedImage"> | string
    createdAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    updatedAt?: DateTimeFilter<"GeneratedImage"> | Date | string
    userId?: StringFilter<"GeneratedImage"> | string
    prompt?: StringFilter<"GeneratedImage"> | string
  }

  export type UserCreateWithoutDocumentsInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    favorites?: FavoriteDocumentCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutDocumentsInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeUncheckedCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutDocumentsInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutDocumentsInput, UserUncheckedCreateWithoutDocumentsInput>
  }

  export type PresentationCreateWithoutBaseInput = {
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    customTheme?: CustomThemeCreateNestedOneWithoutPresentationsInput
  }

  export type PresentationUncheckedCreateWithoutBaseInput = {
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    customThemeId?: string | null
  }

  export type PresentationCreateOrConnectWithoutBaseInput = {
    where: PresentationWhereUniqueInput
    create: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
  }

  export type FavoriteDocumentCreateWithoutDocumentInput = {
    id?: string
    user: UserCreateNestedOneWithoutFavoritesInput
  }

  export type FavoriteDocumentUncheckedCreateWithoutDocumentInput = {
    id?: string
    userId: string
  }

  export type FavoriteDocumentCreateOrConnectWithoutDocumentInput = {
    where: FavoriteDocumentWhereUniqueInput
    create: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput>
  }

  export type FavoriteDocumentCreateManyDocumentInputEnvelope = {
    data: FavoriteDocumentCreateManyDocumentInput | FavoriteDocumentCreateManyDocumentInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithoutDocumentsInput = {
    update: XOR<UserUpdateWithoutDocumentsInput, UserUncheckedUpdateWithoutDocumentsInput>
    create: XOR<UserCreateWithoutDocumentsInput, UserUncheckedCreateWithoutDocumentsInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutDocumentsInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutDocumentsInput, UserUncheckedUpdateWithoutDocumentsInput>
  }

  export type UserUpdateWithoutDocumentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    favorites?: FavoriteDocumentUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutDocumentsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUncheckedUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUncheckedUpdateManyWithoutUserNestedInput
  }

  export type PresentationUpsertWithoutBaseInput = {
    update: XOR<PresentationUpdateWithoutBaseInput, PresentationUncheckedUpdateWithoutBaseInput>
    create: XOR<PresentationCreateWithoutBaseInput, PresentationUncheckedCreateWithoutBaseInput>
    where?: PresentationWhereInput
  }

  export type PresentationUpdateToOneWithWhereWithoutBaseInput = {
    where?: PresentationWhereInput
    data: XOR<PresentationUpdateWithoutBaseInput, PresentationUncheckedUpdateWithoutBaseInput>
  }

  export type PresentationUpdateWithoutBaseInput = {
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    customTheme?: CustomThemeUpdateOneWithoutPresentationsNestedInput
  }

  export type PresentationUncheckedUpdateWithoutBaseInput = {
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    customThemeId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type FavoriteDocumentUpsertWithWhereUniqueWithoutDocumentInput = {
    where: FavoriteDocumentWhereUniqueInput
    update: XOR<FavoriteDocumentUpdateWithoutDocumentInput, FavoriteDocumentUncheckedUpdateWithoutDocumentInput>
    create: XOR<FavoriteDocumentCreateWithoutDocumentInput, FavoriteDocumentUncheckedCreateWithoutDocumentInput>
  }

  export type FavoriteDocumentUpdateWithWhereUniqueWithoutDocumentInput = {
    where: FavoriteDocumentWhereUniqueInput
    data: XOR<FavoriteDocumentUpdateWithoutDocumentInput, FavoriteDocumentUncheckedUpdateWithoutDocumentInput>
  }

  export type FavoriteDocumentUpdateManyWithWhereWithoutDocumentInput = {
    where: FavoriteDocumentScalarWhereInput
    data: XOR<FavoriteDocumentUpdateManyMutationInput, FavoriteDocumentUncheckedUpdateManyWithoutDocumentInput>
  }

  export type BaseDocumentCreateWithoutPresentationInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    user: UserCreateNestedOneWithoutDocumentsInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentUncheckedCreateWithoutPresentationInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    userId: string
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutDocumentInput
  }

  export type BaseDocumentCreateOrConnectWithoutPresentationInput = {
    where: BaseDocumentWhereUniqueInput
    create: XOR<BaseDocumentCreateWithoutPresentationInput, BaseDocumentUncheckedCreateWithoutPresentationInput>
  }

  export type CustomThemeCreateWithoutPresentationsInput = {
    id?: string
    name: string
    description?: string | null
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
    user: UserCreateNestedOneWithoutCustomThemeInput
  }

  export type CustomThemeUncheckedCreateWithoutPresentationsInput = {
    id?: string
    name: string
    description?: string | null
    userId: string
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
  }

  export type CustomThemeCreateOrConnectWithoutPresentationsInput = {
    where: CustomThemeWhereUniqueInput
    create: XOR<CustomThemeCreateWithoutPresentationsInput, CustomThemeUncheckedCreateWithoutPresentationsInput>
  }

  export type BaseDocumentUpsertWithoutPresentationInput = {
    update: XOR<BaseDocumentUpdateWithoutPresentationInput, BaseDocumentUncheckedUpdateWithoutPresentationInput>
    create: XOR<BaseDocumentCreateWithoutPresentationInput, BaseDocumentUncheckedCreateWithoutPresentationInput>
    where?: BaseDocumentWhereInput
  }

  export type BaseDocumentUpdateToOneWithWhereWithoutPresentationInput = {
    where?: BaseDocumentWhereInput
    data: XOR<BaseDocumentUpdateWithoutPresentationInput, BaseDocumentUncheckedUpdateWithoutPresentationInput>
  }

  export type BaseDocumentUpdateWithoutPresentationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    user?: UserUpdateOneRequiredWithoutDocumentsNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutDocumentNestedInput
  }

  export type BaseDocumentUncheckedUpdateWithoutPresentationInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    userId?: StringFieldUpdateOperationsInput | string
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutDocumentNestedInput
  }

  export type CustomThemeUpsertWithoutPresentationsInput = {
    update: XOR<CustomThemeUpdateWithoutPresentationsInput, CustomThemeUncheckedUpdateWithoutPresentationsInput>
    create: XOR<CustomThemeCreateWithoutPresentationsInput, CustomThemeUncheckedCreateWithoutPresentationsInput>
    where?: CustomThemeWhereInput
  }

  export type CustomThemeUpdateToOneWithWhereWithoutPresentationsInput = {
    where?: CustomThemeWhereInput
    data: XOR<CustomThemeUpdateWithoutPresentationsInput, CustomThemeUncheckedUpdateWithoutPresentationsInput>
  }

  export type CustomThemeUpdateWithoutPresentationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
    user?: UserUpdateOneRequiredWithoutCustomThemeNestedInput
  }

  export type CustomThemeUncheckedUpdateWithoutPresentationsInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    userId?: StringFieldUpdateOperationsInput | string
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
  }

  export type UserCreateWithoutCustomThemeInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutCustomThemeInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentUncheckedCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutCustomThemeInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutCustomThemeInput, UserUncheckedCreateWithoutCustomThemeInput>
  }

  export type PresentationCreateWithoutCustomThemeInput = {
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
    base?: BaseDocumentCreateNestedOneWithoutPresentationInput
  }

  export type PresentationUncheckedCreateWithoutCustomThemeInput = {
    id?: string
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
  }

  export type PresentationCreateOrConnectWithoutCustomThemeInput = {
    where: PresentationWhereUniqueInput
    create: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput>
  }

  export type PresentationCreateManyCustomThemeInputEnvelope = {
    data: PresentationCreateManyCustomThemeInput | PresentationCreateManyCustomThemeInput[]
    skipDuplicates?: boolean
  }

  export type UserUpsertWithoutCustomThemeInput = {
    update: XOR<UserUpdateWithoutCustomThemeInput, UserUncheckedUpdateWithoutCustomThemeInput>
    create: XOR<UserCreateWithoutCustomThemeInput, UserUncheckedCreateWithoutCustomThemeInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutCustomThemeInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutCustomThemeInput, UserUncheckedUpdateWithoutCustomThemeInput>
  }

  export type UserUpdateWithoutCustomThemeInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutCustomThemeInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUncheckedUpdateManyWithoutUserNestedInput
  }

  export type PresentationUpsertWithWhereUniqueWithoutCustomThemeInput = {
    where: PresentationWhereUniqueInput
    update: XOR<PresentationUpdateWithoutCustomThemeInput, PresentationUncheckedUpdateWithoutCustomThemeInput>
    create: XOR<PresentationCreateWithoutCustomThemeInput, PresentationUncheckedCreateWithoutCustomThemeInput>
  }

  export type PresentationUpdateWithWhereUniqueWithoutCustomThemeInput = {
    where: PresentationWhereUniqueInput
    data: XOR<PresentationUpdateWithoutCustomThemeInput, PresentationUncheckedUpdateWithoutCustomThemeInput>
  }

  export type PresentationUpdateManyWithWhereWithoutCustomThemeInput = {
    where: PresentationScalarWhereInput
    data: XOR<PresentationUpdateManyMutationInput, PresentationUncheckedUpdateManyWithoutCustomThemeInput>
  }

  export type PresentationScalarWhereInput = {
    AND?: PresentationScalarWhereInput | PresentationScalarWhereInput[]
    OR?: PresentationScalarWhereInput[]
    NOT?: PresentationScalarWhereInput | PresentationScalarWhereInput[]
    id?: StringFilter<"Presentation"> | string
    content?: JsonFilter<"Presentation">
    theme?: StringFilter<"Presentation"> | string
    imageModel?: StringNullableFilter<"Presentation"> | string | null
    presentationStyle?: StringNullableFilter<"Presentation"> | string | null
    language?: StringNullableFilter<"Presentation"> | string | null
    outline?: StringNullableListFilter<"Presentation">
    templateId?: StringNullableFilter<"Presentation"> | string | null
    customThemeId?: StringNullableFilter<"Presentation"> | string | null
  }

  export type BaseDocumentCreateWithoutFavoritesInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    user: UserCreateNestedOneWithoutDocumentsInput
    presentation?: PresentationCreateNestedOneWithoutBaseInput
  }

  export type BaseDocumentUncheckedCreateWithoutFavoritesInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    userId: string
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
    presentation?: PresentationUncheckedCreateNestedOneWithoutBaseInput
  }

  export type BaseDocumentCreateOrConnectWithoutFavoritesInput = {
    where: BaseDocumentWhereUniqueInput
    create: XOR<BaseDocumentCreateWithoutFavoritesInput, BaseDocumentUncheckedCreateWithoutFavoritesInput>
  }

  export type UserCreateWithoutFavoritesInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutFavoritesInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentUncheckedCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeUncheckedCreateNestedManyWithoutUserInput
    GeneratedImage?: GeneratedImageUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutFavoritesInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
  }

  export type BaseDocumentUpsertWithoutFavoritesInput = {
    update: XOR<BaseDocumentUpdateWithoutFavoritesInput, BaseDocumentUncheckedUpdateWithoutFavoritesInput>
    create: XOR<BaseDocumentCreateWithoutFavoritesInput, BaseDocumentUncheckedCreateWithoutFavoritesInput>
    where?: BaseDocumentWhereInput
  }

  export type BaseDocumentUpdateToOneWithWhereWithoutFavoritesInput = {
    where?: BaseDocumentWhereInput
    data: XOR<BaseDocumentUpdateWithoutFavoritesInput, BaseDocumentUncheckedUpdateWithoutFavoritesInput>
  }

  export type BaseDocumentUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    user?: UserUpdateOneRequiredWithoutDocumentsNestedInput
    presentation?: PresentationUpdateOneWithoutBaseNestedInput
  }

  export type BaseDocumentUncheckedUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    userId?: StringFieldUpdateOperationsInput | string
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    presentation?: PresentationUncheckedUpdateOneWithoutBaseNestedInput
  }

  export type UserUpsertWithoutFavoritesInput = {
    update: XOR<UserUpdateWithoutFavoritesInput, UserUncheckedUpdateWithoutFavoritesInput>
    create: XOR<UserCreateWithoutFavoritesInput, UserUncheckedCreateWithoutFavoritesInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutFavoritesInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutFavoritesInput, UserUncheckedUpdateWithoutFavoritesInput>
  }

  export type UserUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutFavoritesInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUncheckedUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUncheckedUpdateManyWithoutUserNestedInput
    GeneratedImage?: GeneratedImageUncheckedUpdateManyWithoutUserNestedInput
  }

  export type UserCreateWithoutGeneratedImageInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeCreateNestedManyWithoutUserInput
  }

  export type UserUncheckedCreateWithoutGeneratedImageInput = {
    id?: string
    name?: string | null
    email?: string | null
    password?: string | null
    emailVerified?: Date | string | null
    image?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    headline?: string | null
    bio?: string | null
    interests?: UserCreateinterestsInput | string[]
    location?: string | null
    website?: string | null
    role?: $Enums.UserRole
    hasAccess?: boolean
    documents?: BaseDocumentUncheckedCreateNestedManyWithoutUserInput
    favorites?: FavoriteDocumentUncheckedCreateNestedManyWithoutUserInput
    CustomTheme?: CustomThemeUncheckedCreateNestedManyWithoutUserInput
  }

  export type UserCreateOrConnectWithoutGeneratedImageInput = {
    where: UserWhereUniqueInput
    create: XOR<UserCreateWithoutGeneratedImageInput, UserUncheckedCreateWithoutGeneratedImageInput>
  }

  export type UserUpsertWithoutGeneratedImageInput = {
    update: XOR<UserUpdateWithoutGeneratedImageInput, UserUncheckedUpdateWithoutGeneratedImageInput>
    create: XOR<UserCreateWithoutGeneratedImageInput, UserUncheckedCreateWithoutGeneratedImageInput>
    where?: UserWhereInput
  }

  export type UserUpdateToOneWithWhereWithoutGeneratedImageInput = {
    where?: UserWhereInput
    data: XOR<UserUpdateWithoutGeneratedImageInput, UserUncheckedUpdateWithoutGeneratedImageInput>
  }

  export type UserUpdateWithoutGeneratedImageInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUpdateManyWithoutUserNestedInput
  }

  export type UserUncheckedUpdateWithoutGeneratedImageInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: NullableStringFieldUpdateOperationsInput | string | null
    email?: NullableStringFieldUpdateOperationsInput | string | null
    password?: NullableStringFieldUpdateOperationsInput | string | null
    emailVerified?: NullableDateTimeFieldUpdateOperationsInput | Date | string | null
    image?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    headline?: NullableStringFieldUpdateOperationsInput | string | null
    bio?: NullableStringFieldUpdateOperationsInput | string | null
    interests?: UserUpdateinterestsInput | string[]
    location?: NullableStringFieldUpdateOperationsInput | string | null
    website?: NullableStringFieldUpdateOperationsInput | string | null
    role?: EnumUserRoleFieldUpdateOperationsInput | $Enums.UserRole
    hasAccess?: BoolFieldUpdateOperationsInput | boolean
    documents?: BaseDocumentUncheckedUpdateManyWithoutUserNestedInput
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutUserNestedInput
    CustomTheme?: CustomThemeUncheckedUpdateManyWithoutUserNestedInput
  }

  export type BaseDocumentCreateManyUserInput = {
    id?: string
    title: string
    type: $Enums.DocumentType
    thumbnailUrl?: string | null
    createdAt?: Date | string
    updatedAt?: Date | string
    isPublic?: boolean
    documentType: string
  }

  export type FavoriteDocumentCreateManyUserInput = {
    id?: string
    documentId: string
  }

  export type CustomThemeCreateManyUserInput = {
    id?: string
    name: string
    description?: string | null
    logoUrl?: string | null
    isPublic?: boolean
    createdAt?: Date | string
    updatedAt?: Date | string
    themeData: JsonNullValueInput | InputJsonValue
  }

  export type GeneratedImageCreateManyUserInput = {
    id?: string
    url: string
    createdAt?: Date | string
    updatedAt?: Date | string
    prompt: string
  }

  export type BaseDocumentUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    presentation?: PresentationUpdateOneWithoutBaseNestedInput
    favorites?: FavoriteDocumentUpdateManyWithoutDocumentNestedInput
  }

  export type BaseDocumentUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
    presentation?: PresentationUncheckedUpdateOneWithoutBaseNestedInput
    favorites?: FavoriteDocumentUncheckedUpdateManyWithoutDocumentNestedInput
  }

  export type BaseDocumentUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    title?: StringFieldUpdateOperationsInput | string
    type?: EnumDocumentTypeFieldUpdateOperationsInput | $Enums.DocumentType
    thumbnailUrl?: NullableStringFieldUpdateOperationsInput | string | null
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    documentType?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    document?: BaseDocumentUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type FavoriteDocumentUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    documentId?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    documentId?: StringFieldUpdateOperationsInput | string
  }

  export type CustomThemeUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
    presentations?: PresentationUpdateManyWithoutCustomThemeNestedInput
  }

  export type CustomThemeUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
    presentations?: PresentationUncheckedUpdateManyWithoutCustomThemeNestedInput
  }

  export type CustomThemeUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    name?: StringFieldUpdateOperationsInput | string
    description?: NullableStringFieldUpdateOperationsInput | string | null
    logoUrl?: NullableStringFieldUpdateOperationsInput | string | null
    isPublic?: BoolFieldUpdateOperationsInput | boolean
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    themeData?: JsonNullValueInput | InputJsonValue
  }

  export type GeneratedImageUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type GeneratedImageUncheckedUpdateWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type GeneratedImageUncheckedUpdateManyWithoutUserInput = {
    id?: StringFieldUpdateOperationsInput | string
    url?: StringFieldUpdateOperationsInput | string
    createdAt?: DateTimeFieldUpdateOperationsInput | Date | string
    updatedAt?: DateTimeFieldUpdateOperationsInput | Date | string
    prompt?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentCreateManyDocumentInput = {
    id?: string
    userId: string
  }

  export type FavoriteDocumentUpdateWithoutDocumentInput = {
    id?: StringFieldUpdateOperationsInput | string
    user?: UserUpdateOneRequiredWithoutFavoritesNestedInput
  }

  export type FavoriteDocumentUncheckedUpdateWithoutDocumentInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type FavoriteDocumentUncheckedUpdateManyWithoutDocumentInput = {
    id?: StringFieldUpdateOperationsInput | string
    userId?: StringFieldUpdateOperationsInput | string
  }

  export type PresentationCreateManyCustomThemeInput = {
    id?: string
    content: JsonNullValueInput | InputJsonValue
    theme?: string
    imageModel?: string | null
    presentationStyle?: string | null
    language?: string | null
    outline?: PresentationCreateoutlineInput | string[]
    templateId?: string | null
  }

  export type PresentationUpdateWithoutCustomThemeInput = {
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
    base?: BaseDocumentUpdateOneRequiredWithoutPresentationNestedInput
  }

  export type PresentationUncheckedUpdateWithoutCustomThemeInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
  }

  export type PresentationUncheckedUpdateManyWithoutCustomThemeInput = {
    id?: StringFieldUpdateOperationsInput | string
    content?: JsonNullValueInput | InputJsonValue
    theme?: StringFieldUpdateOperationsInput | string
    imageModel?: NullableStringFieldUpdateOperationsInput | string | null
    presentationStyle?: NullableStringFieldUpdateOperationsInput | string | null
    language?: NullableStringFieldUpdateOperationsInput | string | null
    outline?: PresentationUpdateoutlineInput | string[]
    templateId?: NullableStringFieldUpdateOperationsInput | string | null
  }



  /**
   * Aliases for legacy arg types
   */
    /**
     * @deprecated Use UserCountOutputTypeDefaultArgs instead
     */
    export type UserCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use BaseDocumentCountOutputTypeDefaultArgs instead
     */
    export type BaseDocumentCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = BaseDocumentCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CustomThemeCountOutputTypeDefaultArgs instead
     */
    export type CustomThemeCountOutputTypeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CustomThemeCountOutputTypeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use UserDefaultArgs instead
     */
    export type UserArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = UserDefaultArgs<ExtArgs>
    /**
     * @deprecated Use BaseDocumentDefaultArgs instead
     */
    export type BaseDocumentArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = BaseDocumentDefaultArgs<ExtArgs>
    /**
     * @deprecated Use PresentationDefaultArgs instead
     */
    export type PresentationArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = PresentationDefaultArgs<ExtArgs>
    /**
     * @deprecated Use CustomThemeDefaultArgs instead
     */
    export type CustomThemeArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = CustomThemeDefaultArgs<ExtArgs>
    /**
     * @deprecated Use FavoriteDocumentDefaultArgs instead
     */
    export type FavoriteDocumentArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = FavoriteDocumentDefaultArgs<ExtArgs>
    /**
     * @deprecated Use GeneratedImageDefaultArgs instead
     */
    export type GeneratedImageArgs<ExtArgs extends $Extensions.InternalArgs = $Extensions.DefaultArgs> = GeneratedImageDefaultArgs<ExtArgs>

  /**
   * Batch Payload for updateMany & deleteMany & createMany
   */

  export type BatchPayload = {
    count: number
  }

  /**
   * DMMF
   */
  export const dmmf: runtime.BaseDMMF
}