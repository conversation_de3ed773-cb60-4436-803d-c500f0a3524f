"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/negotiator@0.6.3";
exports.ids = ["vendor-chunks/negotiator@0.6.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/index.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * negotiator\n * Copyright(c) 2012 Federico Romero\n * Copyright(c) 2012-2014 Isaac Z. Schlueter\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\nvar preferredCharsets = __webpack_require__(/*! ./lib/charset */ \"(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/charset.js\")\nvar preferredEncodings = __webpack_require__(/*! ./lib/encoding */ \"(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/encoding.js\")\nvar preferredLanguages = __webpack_require__(/*! ./lib/language */ \"(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/language.js\")\nvar preferredMediaTypes = __webpack_require__(/*! ./lib/mediaType */ \"(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/mediaType.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Negotiator;\nmodule.exports.Negotiator = Negotiator;\n\n/**\n * Create a Negotiator instance from a request.\n * @param {object} request\n * @public\n */\n\nfunction Negotiator(request) {\n  if (!(this instanceof Negotiator)) {\n    return new Negotiator(request);\n  }\n\n  this.request = request;\n}\n\nNegotiator.prototype.charset = function charset(available) {\n  var set = this.charsets(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.charsets = function charsets(available) {\n  return preferredCharsets(this.request.headers['accept-charset'], available);\n};\n\nNegotiator.prototype.encoding = function encoding(available) {\n  var set = this.encodings(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.encodings = function encodings(available) {\n  return preferredEncodings(this.request.headers['accept-encoding'], available);\n};\n\nNegotiator.prototype.language = function language(available) {\n  var set = this.languages(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.languages = function languages(available) {\n  return preferredLanguages(this.request.headers['accept-language'], available);\n};\n\nNegotiator.prototype.mediaType = function mediaType(available) {\n  var set = this.mediaTypes(available);\n  return set && set[0];\n};\n\nNegotiator.prototype.mediaTypes = function mediaTypes(available) {\n  return preferredMediaTypes(this.request.headers.accept, available);\n};\n\n// Backwards compatibility\nNegotiator.prototype.preferredCharset = Negotiator.prototype.charset;\nNegotiator.prototype.preferredCharsets = Negotiator.prototype.charsets;\nNegotiator.prototype.preferredEncoding = Negotiator.prototype.encoding;\nNegotiator.prototype.preferredEncodings = Negotiator.prototype.encodings;\nNegotiator.prototype.preferredLanguage = Negotiator.prototype.language;\nNegotiator.prototype.preferredLanguages = Negotiator.prototype.languages;\nNegotiator.prototype.preferredMediaType = Negotiator.prototype.mediaType;\nNegotiator.prototype.preferredMediaTypes = Negotiator.prototype.mediaTypes;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vbmVnb3RpYXRvckAwLjYuMy9ub2RlX21vZHVsZXMvbmVnb3RpYXRvci9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYix3QkFBd0IsbUJBQU8sQ0FBQyx5R0FBZTtBQUMvQyx5QkFBeUIsbUJBQU8sQ0FBQywyR0FBZ0I7QUFDakQseUJBQXlCLG1CQUFPLENBQUMsMkdBQWdCO0FBQ2pELDBCQUEwQixtQkFBTyxDQUFDLDZHQUFpQjs7QUFFbkQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSx5QkFBeUI7O0FBRXpCO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vbmVnb3RpYXRvckAwLjYuMy9ub2RlX21vZHVsZXMvbmVnb3RpYXRvci9pbmRleC5qcz8xNTQzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogbmVnb3RpYXRvclxuICogQ29weXJpZ2h0KGMpIDIwMTIgRmVkZXJpY28gUm9tZXJvXG4gKiBDb3B5cmlnaHQoYykgMjAxMi0yMDE0IElzYWFjIFouIFNjaGx1ZXRlclxuICogQ29weXJpZ2h0KGMpIDIwMTUgRG91Z2xhcyBDaHJpc3RvcGhlciBXaWxzb25cbiAqIE1JVCBMaWNlbnNlZFxuICovXG5cbid1c2Ugc3RyaWN0JztcblxudmFyIHByZWZlcnJlZENoYXJzZXRzID0gcmVxdWlyZSgnLi9saWIvY2hhcnNldCcpXG52YXIgcHJlZmVycmVkRW5jb2RpbmdzID0gcmVxdWlyZSgnLi9saWIvZW5jb2RpbmcnKVxudmFyIHByZWZlcnJlZExhbmd1YWdlcyA9IHJlcXVpcmUoJy4vbGliL2xhbmd1YWdlJylcbnZhciBwcmVmZXJyZWRNZWRpYVR5cGVzID0gcmVxdWlyZSgnLi9saWIvbWVkaWFUeXBlJylcblxuLyoqXG4gKiBNb2R1bGUgZXhwb3J0cy5cbiAqIEBwdWJsaWNcbiAqL1xuXG5tb2R1bGUuZXhwb3J0cyA9IE5lZ290aWF0b3I7XG5tb2R1bGUuZXhwb3J0cy5OZWdvdGlhdG9yID0gTmVnb3RpYXRvcjtcblxuLyoqXG4gKiBDcmVhdGUgYSBOZWdvdGlhdG9yIGluc3RhbmNlIGZyb20gYSByZXF1ZXN0LlxuICogQHBhcmFtIHtvYmplY3R9IHJlcXVlc3RcbiAqIEBwdWJsaWNcbiAqL1xuXG5mdW5jdGlvbiBOZWdvdGlhdG9yKHJlcXVlc3QpIHtcbiAgaWYgKCEodGhpcyBpbnN0YW5jZW9mIE5lZ290aWF0b3IpKSB7XG4gICAgcmV0dXJuIG5ldyBOZWdvdGlhdG9yKHJlcXVlc3QpO1xuICB9XG5cbiAgdGhpcy5yZXF1ZXN0ID0gcmVxdWVzdDtcbn1cblxuTmVnb3RpYXRvci5wcm90b3R5cGUuY2hhcnNldCA9IGZ1bmN0aW9uIGNoYXJzZXQoYXZhaWxhYmxlKSB7XG4gIHZhciBzZXQgPSB0aGlzLmNoYXJzZXRzKGF2YWlsYWJsZSk7XG4gIHJldHVybiBzZXQgJiYgc2V0WzBdO1xufTtcblxuTmVnb3RpYXRvci5wcm90b3R5cGUuY2hhcnNldHMgPSBmdW5jdGlvbiBjaGFyc2V0cyhhdmFpbGFibGUpIHtcbiAgcmV0dXJuIHByZWZlcnJlZENoYXJzZXRzKHRoaXMucmVxdWVzdC5oZWFkZXJzWydhY2NlcHQtY2hhcnNldCddLCBhdmFpbGFibGUpO1xufTtcblxuTmVnb3RpYXRvci5wcm90b3R5cGUuZW5jb2RpbmcgPSBmdW5jdGlvbiBlbmNvZGluZyhhdmFpbGFibGUpIHtcbiAgdmFyIHNldCA9IHRoaXMuZW5jb2RpbmdzKGF2YWlsYWJsZSk7XG4gIHJldHVybiBzZXQgJiYgc2V0WzBdO1xufTtcblxuTmVnb3RpYXRvci5wcm90b3R5cGUuZW5jb2RpbmdzID0gZnVuY3Rpb24gZW5jb2RpbmdzKGF2YWlsYWJsZSkge1xuICByZXR1cm4gcHJlZmVycmVkRW5jb2RpbmdzKHRoaXMucmVxdWVzdC5oZWFkZXJzWydhY2NlcHQtZW5jb2RpbmcnXSwgYXZhaWxhYmxlKTtcbn07XG5cbk5lZ290aWF0b3IucHJvdG90eXBlLmxhbmd1YWdlID0gZnVuY3Rpb24gbGFuZ3VhZ2UoYXZhaWxhYmxlKSB7XG4gIHZhciBzZXQgPSB0aGlzLmxhbmd1YWdlcyhhdmFpbGFibGUpO1xuICByZXR1cm4gc2V0ICYmIHNldFswXTtcbn07XG5cbk5lZ290aWF0b3IucHJvdG90eXBlLmxhbmd1YWdlcyA9IGZ1bmN0aW9uIGxhbmd1YWdlcyhhdmFpbGFibGUpIHtcbiAgcmV0dXJuIHByZWZlcnJlZExhbmd1YWdlcyh0aGlzLnJlcXVlc3QuaGVhZGVyc1snYWNjZXB0LWxhbmd1YWdlJ10sIGF2YWlsYWJsZSk7XG59O1xuXG5OZWdvdGlhdG9yLnByb3RvdHlwZS5tZWRpYVR5cGUgPSBmdW5jdGlvbiBtZWRpYVR5cGUoYXZhaWxhYmxlKSB7XG4gIHZhciBzZXQgPSB0aGlzLm1lZGlhVHlwZXMoYXZhaWxhYmxlKTtcbiAgcmV0dXJuIHNldCAmJiBzZXRbMF07XG59O1xuXG5OZWdvdGlhdG9yLnByb3RvdHlwZS5tZWRpYVR5cGVzID0gZnVuY3Rpb24gbWVkaWFUeXBlcyhhdmFpbGFibGUpIHtcbiAgcmV0dXJuIHByZWZlcnJlZE1lZGlhVHlwZXModGhpcy5yZXF1ZXN0LmhlYWRlcnMuYWNjZXB0LCBhdmFpbGFibGUpO1xufTtcblxuLy8gQmFja3dhcmRzIGNvbXBhdGliaWxpdHlcbk5lZ290aWF0b3IucHJvdG90eXBlLnByZWZlcnJlZENoYXJzZXQgPSBOZWdvdGlhdG9yLnByb3RvdHlwZS5jaGFyc2V0O1xuTmVnb3RpYXRvci5wcm90b3R5cGUucHJlZmVycmVkQ2hhcnNldHMgPSBOZWdvdGlhdG9yLnByb3RvdHlwZS5jaGFyc2V0cztcbk5lZ290aWF0b3IucHJvdG90eXBlLnByZWZlcnJlZEVuY29kaW5nID0gTmVnb3RpYXRvci5wcm90b3R5cGUuZW5jb2Rpbmc7XG5OZWdvdGlhdG9yLnByb3RvdHlwZS5wcmVmZXJyZWRFbmNvZGluZ3MgPSBOZWdvdGlhdG9yLnByb3RvdHlwZS5lbmNvZGluZ3M7XG5OZWdvdGlhdG9yLnByb3RvdHlwZS5wcmVmZXJyZWRMYW5ndWFnZSA9IE5lZ290aWF0b3IucHJvdG90eXBlLmxhbmd1YWdlO1xuTmVnb3RpYXRvci5wcm90b3R5cGUucHJlZmVycmVkTGFuZ3VhZ2VzID0gTmVnb3RpYXRvci5wcm90b3R5cGUubGFuZ3VhZ2VzO1xuTmVnb3RpYXRvci5wcm90b3R5cGUucHJlZmVycmVkTWVkaWFUeXBlID0gTmVnb3RpYXRvci5wcm90b3R5cGUubWVkaWFUeXBlO1xuTmVnb3RpYXRvci5wcm90b3R5cGUucHJlZmVycmVkTWVkaWFUeXBlcyA9IE5lZ290aWF0b3IucHJvdG90eXBlLm1lZGlhVHlwZXM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/charset.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/charset.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredCharsets;\nmodule.exports.preferredCharsets = preferredCharsets;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleCharsetRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Charset header.\n * @private\n */\n\nfunction parseAcceptCharset(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var charset = parseCharset(accepts[i].trim(), i);\n\n    if (charset) {\n      accepts[j++] = charset;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a charset from the Accept-Charset header.\n * @private\n */\n\nfunction parseCharset(str, i) {\n  var match = simpleCharsetRegExp.exec(str);\n  if (!match) return null;\n\n  var charset = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    charset: charset,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a charset.\n * @private\n */\n\nfunction getCharsetPriority(charset, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(charset, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the charset.\n * @private\n */\n\nfunction specify(charset, spec, index) {\n  var s = 0;\n  if(spec.charset.toLowerCase() === charset.toLowerCase()){\n    s |= 1;\n  } else if (spec.charset !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n}\n\n/**\n * Get the preferred charsets from an Accept-Charset header.\n * @public\n */\n\nfunction preferredCharsets(accept, provided) {\n  // RFC 2616 sec 14.2: no header = *\n  var accepts = parseAcceptCharset(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all charsets\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullCharset);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getCharsetPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted charsets\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getCharset(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full charset string.\n * @private\n */\n\nfunction getFullCharset(spec) {\n  return spec.charset;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/charset.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/encoding.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/encoding.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredEncodings;\nmodule.exports.preferredEncodings = preferredEncodings;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleEncodingRegExp = /^\\s*([^\\s;]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Encoding header.\n * @private\n */\n\nfunction parseAcceptEncoding(accept) {\n  var accepts = accept.split(',');\n  var hasIdentity = false;\n  var minQuality = 1;\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var encoding = parseEncoding(accepts[i].trim(), i);\n\n    if (encoding) {\n      accepts[j++] = encoding;\n      hasIdentity = hasIdentity || specify('identity', encoding);\n      minQuality = Math.min(minQuality, encoding.q || 1);\n    }\n  }\n\n  if (!hasIdentity) {\n    /*\n     * If identity doesn't explicitly appear in the accept-encoding header,\n     * it's added to the list of acceptable encoding with the lowest q\n     */\n    accepts[j++] = {\n      encoding: 'identity',\n      q: minQuality,\n      i: i\n    };\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse an encoding from the Accept-Encoding header.\n * @private\n */\n\nfunction parseEncoding(str, i) {\n  var match = simpleEncodingRegExp.exec(str);\n  if (!match) return null;\n\n  var encoding = match[1];\n  var q = 1;\n  if (match[2]) {\n    var params = match[2].split(';');\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].trim().split('=');\n      if (p[0] === 'q') {\n        q = parseFloat(p[1]);\n        break;\n      }\n    }\n  }\n\n  return {\n    encoding: encoding,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of an encoding.\n * @private\n */\n\nfunction getEncodingPriority(encoding, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(encoding, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the encoding.\n * @private\n */\n\nfunction specify(encoding, spec, index) {\n  var s = 0;\n  if(spec.encoding.toLowerCase() === encoding.toLowerCase()){\n    s |= 1;\n  } else if (spec.encoding !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred encodings from an Accept-Encoding header.\n * @public\n */\n\nfunction preferredEncodings(accept, provided) {\n  var accepts = parseAcceptEncoding(accept || '');\n\n  if (!provided) {\n    // sorted list of all encodings\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullEncoding);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getEncodingPriority(type, accepts, index);\n  });\n\n  // sorted list of accepted encodings\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getEncoding(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full encoding string.\n * @private\n */\n\nfunction getFullEncoding(spec) {\n  return spec.encoding;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/encoding.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/language.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/language.js ***!
  \*************************************************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredLanguages;\nmodule.exports.preferredLanguages = preferredLanguages;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleLanguageRegExp = /^\\s*([^\\s\\-;]+)(?:-([^\\s;]+))?\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept-Language header.\n * @private\n */\n\nfunction parseAcceptLanguage(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var language = parseLanguage(accepts[i].trim(), i);\n\n    if (language) {\n      accepts[j++] = language;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a language from the Accept-Language header.\n * @private\n */\n\nfunction parseLanguage(str, i) {\n  var match = simpleLanguageRegExp.exec(str);\n  if (!match) return null;\n\n  var prefix = match[1]\n  var suffix = match[2]\n  var full = prefix\n\n  if (suffix) full += \"-\" + suffix;\n\n  var q = 1;\n  if (match[3]) {\n    var params = match[3].split(';')\n    for (var j = 0; j < params.length; j++) {\n      var p = params[j].split('=');\n      if (p[0] === 'q') q = parseFloat(p[1]);\n    }\n  }\n\n  return {\n    prefix: prefix,\n    suffix: suffix,\n    q: q,\n    i: i,\n    full: full\n  };\n}\n\n/**\n * Get the priority of a language.\n * @private\n */\n\nfunction getLanguagePriority(language, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(language, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the language.\n * @private\n */\n\nfunction specify(language, spec, index) {\n  var p = parseLanguage(language)\n  if (!p) return null;\n  var s = 0;\n  if(spec.full.toLowerCase() === p.full.toLowerCase()){\n    s |= 4;\n  } else if (spec.prefix.toLowerCase() === p.full.toLowerCase()) {\n    s |= 2;\n  } else if (spec.full.toLowerCase() === p.prefix.toLowerCase()) {\n    s |= 1;\n  } else if (spec.full !== '*' ) {\n    return null\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s\n  }\n};\n\n/**\n * Get the preferred languages from an Accept-Language header.\n * @public\n */\n\nfunction preferredLanguages(accept, provided) {\n  // RFC 2616 sec 14.4: no header = *\n  var accepts = parseAcceptLanguage(accept === undefined ? '*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all languages\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullLanguage);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getLanguagePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted languages\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getLanguage(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full language string.\n * @private\n */\n\nfunction getFullLanguage(spec) {\n  return spec.full;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/language.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/mediaType.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/mediaType.js ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("/**\n * negotiator\n * Copyright(c) 2012 Isaac Z. Schlueter\n * Copyright(c) 2014 Federico Romero\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = preferredMediaTypes;\nmodule.exports.preferredMediaTypes = preferredMediaTypes;\n\n/**\n * Module variables.\n * @private\n */\n\nvar simpleMediaTypeRegExp = /^\\s*([^\\s\\/;]+)\\/([^;\\s]+)\\s*(?:;(.*))?$/;\n\n/**\n * Parse the Accept header.\n * @private\n */\n\nfunction parseAccept(accept) {\n  var accepts = splitMediaTypes(accept);\n\n  for (var i = 0, j = 0; i < accepts.length; i++) {\n    var mediaType = parseMediaType(accepts[i].trim(), i);\n\n    if (mediaType) {\n      accepts[j++] = mediaType;\n    }\n  }\n\n  // trim accepts\n  accepts.length = j;\n\n  return accepts;\n}\n\n/**\n * Parse a media type from the Accept header.\n * @private\n */\n\nfunction parseMediaType(str, i) {\n  var match = simpleMediaTypeRegExp.exec(str);\n  if (!match) return null;\n\n  var params = Object.create(null);\n  var q = 1;\n  var subtype = match[2];\n  var type = match[1];\n\n  if (match[3]) {\n    var kvps = splitParameters(match[3]).map(splitKeyValuePair);\n\n    for (var j = 0; j < kvps.length; j++) {\n      var pair = kvps[j];\n      var key = pair[0].toLowerCase();\n      var val = pair[1];\n\n      // get the value, unwrapping quotes\n      var value = val && val[0] === '\"' && val[val.length - 1] === '\"'\n        ? val.substr(1, val.length - 2)\n        : val;\n\n      if (key === 'q') {\n        q = parseFloat(value);\n        break;\n      }\n\n      // store parameter\n      params[key] = value;\n    }\n  }\n\n  return {\n    type: type,\n    subtype: subtype,\n    params: params,\n    q: q,\n    i: i\n  };\n}\n\n/**\n * Get the priority of a media type.\n * @private\n */\n\nfunction getMediaTypePriority(type, accepted, index) {\n  var priority = {o: -1, q: 0, s: 0};\n\n  for (var i = 0; i < accepted.length; i++) {\n    var spec = specify(type, accepted[i], index);\n\n    if (spec && (priority.s - spec.s || priority.q - spec.q || priority.o - spec.o) < 0) {\n      priority = spec;\n    }\n  }\n\n  return priority;\n}\n\n/**\n * Get the specificity of the media type.\n * @private\n */\n\nfunction specify(type, spec, index) {\n  var p = parseMediaType(type);\n  var s = 0;\n\n  if (!p) {\n    return null;\n  }\n\n  if(spec.type.toLowerCase() == p.type.toLowerCase()) {\n    s |= 4\n  } else if(spec.type != '*') {\n    return null;\n  }\n\n  if(spec.subtype.toLowerCase() == p.subtype.toLowerCase()) {\n    s |= 2\n  } else if(spec.subtype != '*') {\n    return null;\n  }\n\n  var keys = Object.keys(spec.params);\n  if (keys.length > 0) {\n    if (keys.every(function (k) {\n      return spec.params[k] == '*' || (spec.params[k] || '').toLowerCase() == (p.params[k] || '').toLowerCase();\n    })) {\n      s |= 1\n    } else {\n      return null\n    }\n  }\n\n  return {\n    i: index,\n    o: spec.i,\n    q: spec.q,\n    s: s,\n  }\n}\n\n/**\n * Get the preferred media types from an Accept header.\n * @public\n */\n\nfunction preferredMediaTypes(accept, provided) {\n  // RFC 2616 sec 14.2: no header = */*\n  var accepts = parseAccept(accept === undefined ? '*/*' : accept || '');\n\n  if (!provided) {\n    // sorted list of all types\n    return accepts\n      .filter(isQuality)\n      .sort(compareSpecs)\n      .map(getFullType);\n  }\n\n  var priorities = provided.map(function getPriority(type, index) {\n    return getMediaTypePriority(type, accepts, index);\n  });\n\n  // sorted list of accepted types\n  return priorities.filter(isQuality).sort(compareSpecs).map(function getType(priority) {\n    return provided[priorities.indexOf(priority)];\n  });\n}\n\n/**\n * Compare two specs.\n * @private\n */\n\nfunction compareSpecs(a, b) {\n  return (b.q - a.q) || (b.s - a.s) || (a.o - b.o) || (a.i - b.i) || 0;\n}\n\n/**\n * Get full type string.\n * @private\n */\n\nfunction getFullType(spec) {\n  return spec.type + '/' + spec.subtype;\n}\n\n/**\n * Check if a spec has any quality.\n * @private\n */\n\nfunction isQuality(spec) {\n  return spec.q > 0;\n}\n\n/**\n * Count the number of quotes in a string.\n * @private\n */\n\nfunction quoteCount(string) {\n  var count = 0;\n  var index = 0;\n\n  while ((index = string.indexOf('\"', index)) !== -1) {\n    count++;\n    index++;\n  }\n\n  return count;\n}\n\n/**\n * Split a key value pair.\n * @private\n */\n\nfunction splitKeyValuePair(str) {\n  var index = str.indexOf('=');\n  var key;\n  var val;\n\n  if (index === -1) {\n    key = str;\n  } else {\n    key = str.substr(0, index);\n    val = str.substr(index + 1);\n  }\n\n  return [key, val];\n}\n\n/**\n * Split an Accept header into media types.\n * @private\n */\n\nfunction splitMediaTypes(accept) {\n  var accepts = accept.split(',');\n\n  for (var i = 1, j = 0; i < accepts.length; i++) {\n    if (quoteCount(accepts[j]) % 2 == 0) {\n      accepts[++j] = accepts[i];\n    } else {\n      accepts[j] += ',' + accepts[i];\n    }\n  }\n\n  // trim accepts\n  accepts.length = j + 1;\n\n  return accepts;\n}\n\n/**\n * Split a string of parameters.\n * @private\n */\n\nfunction splitParameters(str) {\n  var parameters = str.split(';');\n\n  for (var i = 1, j = 0; i < parameters.length; i++) {\n    if (quoteCount(parameters[j]) % 2 == 0) {\n      parameters[++j] = parameters[i];\n    } else {\n      parameters[j] += ';' + parameters[i];\n    }\n  }\n\n  // trim parameters\n  parameters.length = j + 1;\n\n  for (var i = 0; i < parameters.length; i++) {\n    parameters[i] = parameters[i].trim();\n  }\n\n  return parameters;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/lib/mediaType.js\n");

/***/ })

};
;