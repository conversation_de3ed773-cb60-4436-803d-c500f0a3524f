"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai@2.8.4_@types+react@18.3.20_react@18.2.0";
exports.ids = ["vendor-chunks/jotai@2.8.4_@types+react@18.3.20_react@18.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react.mjs":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useAtom: () => (/* binding */ useAtom),\n/* harmony export */   useAtomValue: () => (/* binding */ useAtomValue),\n/* harmony export */   useSetAtom: () => (/* binding */ useSetAtom),\n/* harmony export */   useStore: () => (/* binding */ useStore)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\");\n/* __next_internal_client_entry_do_not_use__ Provider,useAtom,useAtomValue,useSetAtom,useStore auto */ \n\nconst StoreContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nconst useStore = (options)=>{\n    const store = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(StoreContext);\n    return (options == null ? void 0 : options.store) || store || (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.getDefaultStore)();\n};\nconst Provider = ({ children, store })=>{\n    const storeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    if (!store && !storeRef.current) {\n        storeRef.current = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.createStore)();\n    }\n    return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(StoreContext.Provider, {\n        value: store || storeRef.current\n    }, children);\n};\nconst isPromiseLike = (x)=>typeof (x == null ? void 0 : x.then) === \"function\";\nconst use = react__WEBPACK_IMPORTED_MODULE_0__.use || ((promise)=>{\n    if (promise.status === \"pending\") {\n        throw promise;\n    } else if (promise.status === \"fulfilled\") {\n        return promise.value;\n    } else if (promise.status === \"rejected\") {\n        throw promise.reason;\n    } else {\n        promise.status = \"pending\";\n        promise.then((v)=>{\n            promise.status = \"fulfilled\";\n            promise.value = v;\n        }, (e)=>{\n            promise.status = \"rejected\";\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nfunction useAtomValue(atom, options) {\n    const store = useStore(options);\n    const [[valueFromReducer, storeFromReducer, atomFromReducer], rerender] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((prev)=>{\n        const nextValue = store.get(atom);\n        if (Object.is(prev[0], nextValue) && prev[1] === store && prev[2] === atom) {\n            return prev;\n        }\n        return [\n            nextValue,\n            store,\n            atom\n        ];\n    }, void 0, ()=>[\n            store.get(atom),\n            store,\n            atom\n        ]);\n    let value = valueFromReducer;\n    if (storeFromReducer !== store || atomFromReducer !== atom) {\n        rerender();\n        value = store.get(atom);\n    }\n    const delay = options == null ? void 0 : options.delay;\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const unsub = store.sub(atom, ()=>{\n            if (typeof delay === \"number\") {\n                setTimeout(rerender, delay);\n                return;\n            }\n            rerender();\n        });\n        rerender();\n        return unsub;\n    }, [\n        store,\n        atom,\n        delay\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(value);\n    return isPromiseLike(value) ? use(value) : value;\n}\nfunction useSetAtom(atom, options) {\n    const store = useStore(options);\n    const setAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args)=>{\n        if (( false ? 0 : void 0) !== \"production\" && !(\"write\" in atom)) {\n            throw new Error(\"not writable atom\");\n        }\n        return store.set(atom, ...args);\n    }, [\n        store,\n        atom\n    ]);\n    return setAtom;\n}\nfunction useAtom(atom, options) {\n    return [\n        useAtomValue(atom, options),\n        // We do wrong type assertion here, which results in throwing an error.\n        useSetAtom(atom, options)\n    ];\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react/utils.mjs":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react/utils.mjs ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useAtomCallback: () => (/* binding */ useAtomCallback),\n/* harmony export */   useHydrateAtoms: () => (/* binding */ useHydrateAtoms),\n/* harmony export */   useReducerAtom: () => (/* binding */ useReducerAtom),\n/* harmony export */   useResetAtom: () => (/* binding */ useResetAtom)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/react */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var jotai_vanilla_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jotai/vanilla/utils */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla/utils.mjs\");\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\");\n/* __next_internal_client_entry_do_not_use__ useAtomCallback,useHydrateAtoms,useReducerAtom,useResetAtom auto */ \n\n\n\nfunction useResetAtom(anAtom, options) {\n    const setAtom = (0,jotai_react__WEBPACK_IMPORTED_MODULE_1__.useSetAtom)(anAtom, options);\n    const resetAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>setAtom(jotai_vanilla_utils__WEBPACK_IMPORTED_MODULE_2__.RESET), [\n        setAtom\n    ]);\n    return resetAtom;\n}\nfunction useReducerAtom(anAtom, reducer, options) {\n    if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[DEPRECATED] useReducerAtom is deprecated and will be removed in the future. Please create your own version using the recipe. https://github.com/pmndrs/jotai/pull/2467\");\n    }\n    const [state, setState] = (0,jotai_react__WEBPACK_IMPORTED_MODULE_1__.useAtom)(anAtom, options);\n    const dispatch = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((action)=>{\n        setState((prev)=>reducer(prev, action));\n    }, [\n        setState,\n        reducer\n    ]);\n    return [\n        state,\n        dispatch\n    ];\n}\nfunction useAtomCallback(callback, options) {\n    const anAtom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_3__.atom)(null, (get, set, ...args)=>callback(get, set, ...args)), [\n        callback\n    ]);\n    return (0,jotai_react__WEBPACK_IMPORTED_MODULE_1__.useSetAtom)(anAtom, options);\n}\nconst hydratedMap = /* @__PURE__ */ new WeakMap();\nfunction useHydrateAtoms(values, options) {\n    const store = (0,jotai_react__WEBPACK_IMPORTED_MODULE_1__.useStore)(options);\n    const hydratedSet = getHydratedSet(store);\n    for (const [atom, value] of values){\n        if (!hydratedSet.has(atom) || (options == null ? void 0 : options.dangerouslyForceHydrate)) {\n            hydratedSet.add(atom);\n            store.set(atom, value);\n        }\n    }\n}\nconst getHydratedSet = (store)=>{\n    let hydratedSet = hydratedMap.get(store);\n    if (!hydratedSet) {\n        hydratedSet = /* @__PURE__ */ new WeakSet();\n        hydratedMap.set(store, hydratedSet);\n    }\n    return hydratedSet;\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vam90YWlAMi44LjRfQHR5cGVzK3JlYWN0QDE4LjMuMjBfcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9qb3RhaS9lc20vcmVhY3QvdXRpbHMubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O2lIQUM2QztBQUNlO0FBQ2hCO0FBQ1A7QUFFckMsU0FBU08sYUFBYUMsTUFBTSxFQUFFQyxPQUFPO0lBQ25DLE1BQU1DLFVBQVVSLHVEQUFVQSxDQUFDTSxRQUFRQztJQUNuQyxNQUFNRSxZQUFZWCxrREFBV0EsQ0FBQyxJQUFNVSxRQUFRTCxzREFBS0EsR0FBRztRQUFDSztLQUFRO0lBQzdELE9BQU9DO0FBQ1Q7QUFFQSxTQUFTQyxlQUFlSixNQUFNLEVBQUVLLE9BQU8sRUFBRUosT0FBTztJQUM5QyxJQUFJLENBQUMsTUFBZSxHQUFHLENBQW9CLEdBQUcsS0FBSyxPQUFPLGNBQWM7UUFDdEVPLFFBQVFDLElBQUksQ0FDVjtJQUVKO0lBQ0EsTUFBTSxDQUFDQyxPQUFPQyxTQUFTLEdBQUdoQixvREFBT0EsQ0FBQ0ssUUFBUUM7SUFDMUMsTUFBTVcsV0FBV3BCLGtEQUFXQSxDQUMxQixDQUFDcUI7UUFDQ0YsU0FBUyxDQUFDRyxPQUFTVCxRQUFRUyxNQUFNRDtJQUNuQyxHQUNBO1FBQUNGO1FBQVVOO0tBQVE7SUFFckIsT0FBTztRQUFDSztRQUFPRTtLQUFTO0FBQzFCO0FBRUEsU0FBU0csZ0JBQWdCQyxRQUFRLEVBQUVmLE9BQU87SUFDeEMsTUFBTUQsU0FBU1AsOENBQU9BLENBQ3BCLElBQU1LLG1EQUFJQSxDQUFDLE1BQU0sQ0FBQ21CLEtBQUtDLEtBQUssR0FBR0MsT0FBU0gsU0FBU0MsS0FBS0MsUUFBUUMsUUFDOUQ7UUFBQ0g7S0FBUztJQUVaLE9BQU90Qix1REFBVUEsQ0FBQ00sUUFBUUM7QUFDNUI7QUFFQSxNQUFNbUIsY0FBYyxhQUFhLEdBQUcsSUFBSUM7QUFDeEMsU0FBU0MsZ0JBQWdCQyxNQUFNLEVBQUV0QixPQUFPO0lBQ3RDLE1BQU11QixRQUFRNUIscURBQVFBLENBQUNLO0lBQ3ZCLE1BQU13QixjQUFjQyxlQUFlRjtJQUNuQyxLQUFLLE1BQU0sQ0FBQzFCLE1BQU02QixNQUFNLElBQUlKLE9BQVE7UUFDbEMsSUFBSSxDQUFDRSxZQUFZRyxHQUFHLENBQUM5QixTQUFVRyxDQUFBQSxXQUFXLE9BQU8sS0FBSyxJQUFJQSxRQUFRNEIsdUJBQXVCLEdBQUc7WUFDMUZKLFlBQVlLLEdBQUcsQ0FBQ2hDO1lBQ2hCMEIsTUFBTU4sR0FBRyxDQUFDcEIsTUFBTTZCO1FBQ2xCO0lBQ0Y7QUFDRjtBQUNBLE1BQU1ELGlCQUFpQixDQUFDRjtJQUN0QixJQUFJQyxjQUFjTCxZQUFZSCxHQUFHLENBQUNPO0lBQ2xDLElBQUksQ0FBQ0MsYUFBYTtRQUNoQkEsY0FBYyxhQUFhLEdBQUcsSUFBSU07UUFDbENYLFlBQVlGLEdBQUcsQ0FBQ00sT0FBT0M7SUFDekI7SUFDQSxPQUFPQTtBQUNUO0FBRTBFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2pvdGFpQDIuOC40X0B0eXBlcytyZWFjdEAxOC4zLjIwX3JlYWN0QDE4LjIuMC9ub2RlX21vZHVsZXMvam90YWkvZXNtL3JlYWN0L3V0aWxzLm1qcz8xYTY3Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50JztcbmltcG9ydCB7IHVzZUNhbGxiYWNrLCB1c2VNZW1vIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2V0QXRvbSwgdXNlQXRvbSwgdXNlU3RvcmUgfSBmcm9tICdqb3RhaS9yZWFjdCc7XG5pbXBvcnQgeyBSRVNFVCB9IGZyb20gJ2pvdGFpL3ZhbmlsbGEvdXRpbHMnO1xuaW1wb3J0IHsgYXRvbSB9IGZyb20gJ2pvdGFpL3ZhbmlsbGEnO1xuXG5mdW5jdGlvbiB1c2VSZXNldEF0b20oYW5BdG9tLCBvcHRpb25zKSB7XG4gIGNvbnN0IHNldEF0b20gPSB1c2VTZXRBdG9tKGFuQXRvbSwgb3B0aW9ucyk7XG4gIGNvbnN0IHJlc2V0QXRvbSA9IHVzZUNhbGxiYWNrKCgpID0+IHNldEF0b20oUkVTRVQpLCBbc2V0QXRvbV0pO1xuICByZXR1cm4gcmVzZXRBdG9tO1xufVxuXG5mdW5jdGlvbiB1c2VSZWR1Y2VyQXRvbShhbkF0b20sIHJlZHVjZXIsIG9wdGlvbnMpIHtcbiAgaWYgKChpbXBvcnQubWV0YS5lbnYgPyBpbXBvcnQubWV0YS5lbnYuTU9ERSA6IHZvaWQgMCkgIT09IFwicHJvZHVjdGlvblwiKSB7XG4gICAgY29uc29sZS53YXJuKFxuICAgICAgXCJbREVQUkVDQVRFRF0gdXNlUmVkdWNlckF0b20gaXMgZGVwcmVjYXRlZCBhbmQgd2lsbCBiZSByZW1vdmVkIGluIHRoZSBmdXR1cmUuIFBsZWFzZSBjcmVhdGUgeW91ciBvd24gdmVyc2lvbiB1c2luZyB0aGUgcmVjaXBlLiBodHRwczovL2dpdGh1Yi5jb20vcG1uZHJzL2pvdGFpL3B1bGwvMjQ2N1wiXG4gICAgKTtcbiAgfVxuICBjb25zdCBbc3RhdGUsIHNldFN0YXRlXSA9IHVzZUF0b20oYW5BdG9tLCBvcHRpb25zKTtcbiAgY29uc3QgZGlzcGF0Y2ggPSB1c2VDYWxsYmFjayhcbiAgICAoYWN0aW9uKSA9PiB7XG4gICAgICBzZXRTdGF0ZSgocHJldikgPT4gcmVkdWNlcihwcmV2LCBhY3Rpb24pKTtcbiAgICB9LFxuICAgIFtzZXRTdGF0ZSwgcmVkdWNlcl1cbiAgKTtcbiAgcmV0dXJuIFtzdGF0ZSwgZGlzcGF0Y2hdO1xufVxuXG5mdW5jdGlvbiB1c2VBdG9tQ2FsbGJhY2soY2FsbGJhY2ssIG9wdGlvbnMpIHtcbiAgY29uc3QgYW5BdG9tID0gdXNlTWVtbyhcbiAgICAoKSA9PiBhdG9tKG51bGwsIChnZXQsIHNldCwgLi4uYXJncykgPT4gY2FsbGJhY2soZ2V0LCBzZXQsIC4uLmFyZ3MpKSxcbiAgICBbY2FsbGJhY2tdXG4gICk7XG4gIHJldHVybiB1c2VTZXRBdG9tKGFuQXRvbSwgb3B0aW9ucyk7XG59XG5cbmNvbnN0IGh5ZHJhdGVkTWFwID0gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrTWFwKCk7XG5mdW5jdGlvbiB1c2VIeWRyYXRlQXRvbXModmFsdWVzLCBvcHRpb25zKSB7XG4gIGNvbnN0IHN0b3JlID0gdXNlU3RvcmUob3B0aW9ucyk7XG4gIGNvbnN0IGh5ZHJhdGVkU2V0ID0gZ2V0SHlkcmF0ZWRTZXQoc3RvcmUpO1xuICBmb3IgKGNvbnN0IFthdG9tLCB2YWx1ZV0gb2YgdmFsdWVzKSB7XG4gICAgaWYgKCFoeWRyYXRlZFNldC5oYXMoYXRvbSkgfHwgKG9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuZGFuZ2Vyb3VzbHlGb3JjZUh5ZHJhdGUpKSB7XG4gICAgICBoeWRyYXRlZFNldC5hZGQoYXRvbSk7XG4gICAgICBzdG9yZS5zZXQoYXRvbSwgdmFsdWUpO1xuICAgIH1cbiAgfVxufVxuY29uc3QgZ2V0SHlkcmF0ZWRTZXQgPSAoc3RvcmUpID0+IHtcbiAgbGV0IGh5ZHJhdGVkU2V0ID0gaHlkcmF0ZWRNYXAuZ2V0KHN0b3JlKTtcbiAgaWYgKCFoeWRyYXRlZFNldCkge1xuICAgIGh5ZHJhdGVkU2V0ID0gLyogQF9fUFVSRV9fICovIG5ldyBXZWFrU2V0KCk7XG4gICAgaHlkcmF0ZWRNYXAuc2V0KHN0b3JlLCBoeWRyYXRlZFNldCk7XG4gIH1cbiAgcmV0dXJuIGh5ZHJhdGVkU2V0O1xufTtcblxuZXhwb3J0IHsgdXNlQXRvbUNhbGxiYWNrLCB1c2VIeWRyYXRlQXRvbXMsIHVzZVJlZHVjZXJBdG9tLCB1c2VSZXNldEF0b20gfTtcbiJdLCJuYW1lcyI6WyJ1c2VDYWxsYmFjayIsInVzZU1lbW8iLCJ1c2VTZXRBdG9tIiwidXNlQXRvbSIsInVzZVN0b3JlIiwiUkVTRVQiLCJhdG9tIiwidXNlUmVzZXRBdG9tIiwiYW5BdG9tIiwib3B0aW9ucyIsInNldEF0b20iLCJyZXNldEF0b20iLCJ1c2VSZWR1Y2VyQXRvbSIsInJlZHVjZXIiLCJlbnYiLCJNT0RFIiwiY29uc29sZSIsIndhcm4iLCJzdGF0ZSIsInNldFN0YXRlIiwiZGlzcGF0Y2giLCJhY3Rpb24iLCJwcmV2IiwidXNlQXRvbUNhbGxiYWNrIiwiY2FsbGJhY2siLCJnZXQiLCJzZXQiLCJhcmdzIiwiaHlkcmF0ZWRNYXAiLCJXZWFrTWFwIiwidXNlSHlkcmF0ZUF0b21zIiwidmFsdWVzIiwic3RvcmUiLCJoeWRyYXRlZFNldCIsImdldEh5ZHJhdGVkU2V0IiwidmFsdWUiLCJoYXMiLCJkYW5nZXJvdXNseUZvcmNlSHlkcmF0ZSIsImFkZCIsIldlYWtTZXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react/utils.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs":
/*!*************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   atom: () => (/* binding */ atom),\n/* harmony export */   createStore: () => (/* binding */ createStore),\n/* harmony export */   getDefaultStore: () => (/* binding */ getDefaultStore)\n/* harmony export */ });\nlet keyCount = 0;\nfunction atom(read, write) {\n  const key = `atom${++keyCount}`;\n  const config = {\n    toString: () => key\n  };\n  if (typeof read === \"function\") {\n    config.read = read;\n  } else {\n    config.init = read;\n    config.read = defaultRead;\n    config.write = defaultWrite;\n  }\n  if (write) {\n    config.write = write;\n  }\n  return config;\n}\nfunction defaultRead(get) {\n  return get(this);\n}\nfunction defaultWrite(get, set, arg) {\n  return set(\n    this,\n    typeof arg === \"function\" ? arg(get(this)) : arg\n  );\n}\n\nconst isSelfAtom = (atom, a) => atom.unstable_is ? atom.unstable_is(a) : a === atom;\nconst hasInitialValue = (atom) => \"init\" in atom;\nconst isActuallyWritableAtom = (atom) => !!atom.write;\nconst cancelPromiseMap = /* @__PURE__ */ new WeakMap();\nconst registerCancelPromise = (promise, cancel) => {\n  cancelPromiseMap.set(promise, cancel);\n  promise.catch(() => {\n  }).finally(() => cancelPromiseMap.delete(promise));\n};\nconst cancelPromise = (promise, next) => {\n  const cancel = cancelPromiseMap.get(promise);\n  if (cancel) {\n    cancelPromiseMap.delete(promise);\n    cancel(next);\n  }\n};\nconst resolvePromise = (promise, value) => {\n  promise.status = \"fulfilled\";\n  promise.value = value;\n};\nconst rejectPromise = (promise, e) => {\n  promise.status = \"rejected\";\n  promise.reason = e;\n};\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nconst isEqualAtomValue = (a, b) => !!a && \"v\" in a && \"v\" in b && Object.is(a.v, b.v);\nconst isEqualAtomError = (a, b) => !!a && \"e\" in a && \"e\" in b && Object.is(a.e, b.e);\nconst hasPromiseAtomValue = (a) => !!a && \"v\" in a && a.v instanceof Promise;\nconst isEqualPromiseAtomValue = (a, b) => \"v\" in a && \"v\" in b && a.v.orig && a.v.orig === b.v.orig;\nconst returnAtomValue = (atomState) => {\n  if (\"e\" in atomState) {\n    throw atomState.e;\n  }\n  return atomState.v;\n};\nconst createStore$1 = () => {\n  const atomStateMap = /* @__PURE__ */ new WeakMap();\n  const mountedMap = /* @__PURE__ */ new WeakMap();\n  const pendingStack = [];\n  const pendingMap = /* @__PURE__ */ new WeakMap();\n  let devListenersRev2;\n  let mountedAtoms;\n  if (( false ? 0 : void 0) !== \"production\") {\n    devListenersRev2 = /* @__PURE__ */ new Set();\n    mountedAtoms = /* @__PURE__ */ new Set();\n  }\n  const getAtomState = (atom) => atomStateMap.get(atom);\n  const addPendingDependent = (atom, atomState) => {\n    atomState.d.forEach((_, a) => {\n      if (!pendingMap.has(a)) {\n        const aState = getAtomState(a);\n        pendingMap.set(a, [aState, /* @__PURE__ */ new Set()]);\n        if (aState) {\n          addPendingDependent(a, aState);\n        }\n      }\n      pendingMap.get(a)[1].add(atom);\n    });\n  };\n  const setAtomState = (atom, atomState) => {\n    var _a;\n    if (( false ? 0 : void 0) !== \"production\") {\n      Object.freeze(atomState);\n    }\n    const prevAtomState = getAtomState(atom);\n    atomStateMap.set(atom, atomState);\n    (_a = pendingStack[pendingStack.length - 1]) == null ? void 0 : _a.add(atom);\n    if (!pendingMap.has(atom)) {\n      pendingMap.set(atom, [prevAtomState, /* @__PURE__ */ new Set()]);\n      addPendingDependent(atom, atomState);\n    }\n    if (hasPromiseAtomValue(prevAtomState)) {\n      const next = \"v\" in atomState ? atomState.v instanceof Promise ? atomState.v : Promise.resolve(atomState.v) : Promise.reject(atomState.e);\n      if (prevAtomState.v !== next) {\n        cancelPromise(prevAtomState.v, next);\n      }\n    }\n  };\n  const updateDependencies = (atom, nextAtomState, nextDependencies, keepPreviousDependencies) => {\n    const dependencies = new Map(\n      keepPreviousDependencies ? nextAtomState.d : null\n    );\n    let changed = false;\n    nextDependencies.forEach((aState, a) => {\n      if (!aState && isSelfAtom(atom, a)) {\n        aState = nextAtomState;\n      }\n      if (aState) {\n        dependencies.set(a, aState);\n        if (nextAtomState.d.get(a) !== aState) {\n          changed = true;\n        }\n      } else if (( false ? 0 : void 0) !== \"production\") {\n        console.warn(\"[Bug] atom state not found\");\n      }\n    });\n    if (changed || nextAtomState.d.size !== dependencies.size) {\n      nextAtomState.d = dependencies;\n    }\n  };\n  const setAtomValue = (atom, value, nextDependencies, keepPreviousDependencies) => {\n    const prevAtomState = getAtomState(atom);\n    const nextAtomState = {\n      d: (prevAtomState == null ? void 0 : prevAtomState.d) || /* @__PURE__ */ new Map(),\n      v: value\n    };\n    if (nextDependencies) {\n      updateDependencies(\n        atom,\n        nextAtomState,\n        nextDependencies,\n        keepPreviousDependencies\n      );\n    }\n    if (isEqualAtomValue(prevAtomState, nextAtomState) && prevAtomState.d === nextAtomState.d) {\n      return prevAtomState;\n    }\n    if (hasPromiseAtomValue(prevAtomState) && hasPromiseAtomValue(nextAtomState) && isEqualPromiseAtomValue(prevAtomState, nextAtomState)) {\n      if (prevAtomState.d === nextAtomState.d) {\n        return prevAtomState;\n      } else {\n        nextAtomState.v = prevAtomState.v;\n      }\n    }\n    setAtomState(atom, nextAtomState);\n    return nextAtomState;\n  };\n  const setAtomValueOrPromise = (atom, valueOrPromise, nextDependencies, abortPromise) => {\n    if (isPromiseLike(valueOrPromise)) {\n      let continuePromise;\n      const updatePromiseDependencies = () => {\n        const prevAtomState = getAtomState(atom);\n        if (!hasPromiseAtomValue(prevAtomState) || prevAtomState.v !== promise) {\n          return;\n        }\n        const nextAtomState = setAtomValue(\n          atom,\n          promise,\n          nextDependencies\n        );\n        if (mountedMap.has(atom) && prevAtomState.d !== nextAtomState.d) {\n          mountDependencies(atom, nextAtomState, prevAtomState.d);\n        }\n      };\n      const promise = new Promise((resolve, reject) => {\n        let settled = false;\n        valueOrPromise.then(\n          (v) => {\n            if (!settled) {\n              settled = true;\n              resolvePromise(promise, v);\n              resolve(v);\n              updatePromiseDependencies();\n            }\n          },\n          (e) => {\n            if (!settled) {\n              settled = true;\n              rejectPromise(promise, e);\n              reject(e);\n              updatePromiseDependencies();\n            }\n          }\n        );\n        continuePromise = (next) => {\n          if (!settled) {\n            settled = true;\n            next.then(\n              (v) => resolvePromise(promise, v),\n              (e) => rejectPromise(promise, e)\n            );\n            resolve(next);\n          }\n        };\n      });\n      promise.orig = valueOrPromise;\n      promise.status = \"pending\";\n      registerCancelPromise(promise, (next) => {\n        if (next) {\n          continuePromise(next);\n          abortPromise == null ? void 0 : abortPromise();\n        }\n      });\n      return setAtomValue(atom, promise, nextDependencies, true);\n    }\n    return setAtomValue(atom, valueOrPromise, nextDependencies);\n  };\n  const setAtomError = (atom, error, nextDependencies) => {\n    const prevAtomState = getAtomState(atom);\n    const nextAtomState = {\n      d: (prevAtomState == null ? void 0 : prevAtomState.d) || /* @__PURE__ */ new Map(),\n      e: error\n    };\n    if (nextDependencies) {\n      updateDependencies(atom, nextAtomState, nextDependencies);\n    }\n    if (isEqualAtomError(prevAtomState, nextAtomState) && prevAtomState.d === nextAtomState.d) {\n      return prevAtomState;\n    }\n    setAtomState(atom, nextAtomState);\n    return nextAtomState;\n  };\n  const readAtomState = (atom, force) => {\n    const atomState = getAtomState(atom);\n    if (!(force == null ? void 0 : force(atom)) && atomState) {\n      if (mountedMap.has(atom)) {\n        return atomState;\n      }\n      if (Array.from(atomState.d).every(([a, s]) => {\n        if (a === atom) {\n          return true;\n        }\n        const aState = readAtomState(a, force);\n        return aState === s || isEqualAtomValue(aState, s);\n      })) {\n        return atomState;\n      }\n    }\n    const nextDependencies = /* @__PURE__ */ new Map();\n    let isSync = true;\n    const getter = (a) => {\n      if (isSelfAtom(atom, a)) {\n        const aState2 = getAtomState(a);\n        if (aState2) {\n          nextDependencies.set(a, aState2);\n          return returnAtomValue(aState2);\n        }\n        if (hasInitialValue(a)) {\n          nextDependencies.set(a, void 0);\n          return a.init;\n        }\n        throw new Error(\"no atom init\");\n      }\n      const aState = readAtomState(a, force);\n      nextDependencies.set(a, aState);\n      return returnAtomValue(aState);\n    };\n    let controller;\n    let setSelf;\n    const options = {\n      get signal() {\n        if (!controller) {\n          controller = new AbortController();\n        }\n        return controller.signal;\n      },\n      get setSelf() {\n        if (( false ? 0 : void 0) !== \"production\" && !isActuallyWritableAtom(atom)) {\n          console.warn(\"setSelf function cannot be used with read-only atom\");\n        }\n        if (!setSelf && isActuallyWritableAtom(atom)) {\n          setSelf = (...args) => {\n            if (( false ? 0 : void 0) !== \"production\" && isSync) {\n              console.warn(\"setSelf function cannot be called in sync\");\n            }\n            if (!isSync) {\n              return writeAtom(atom, ...args);\n            }\n          };\n        }\n        return setSelf;\n      }\n    };\n    try {\n      const valueOrPromise = atom.read(getter, options);\n      return setAtomValueOrPromise(\n        atom,\n        valueOrPromise,\n        nextDependencies,\n        () => controller == null ? void 0 : controller.abort()\n      );\n    } catch (error) {\n      return setAtomError(atom, error, nextDependencies);\n    } finally {\n      isSync = false;\n    }\n  };\n  const readAtom = (atom) => returnAtomValue(readAtomState(atom));\n  const recomputeDependents = (atom) => {\n    const getDependents = (a) => {\n      var _a, _b;\n      const dependents = new Set((_a = mountedMap.get(a)) == null ? void 0 : _a.t);\n      (_b = pendingMap.get(a)) == null ? void 0 : _b[1].forEach((dependent) => {\n        dependents.add(dependent);\n      });\n      return dependents;\n    };\n    const topsortedAtoms = new Array();\n    const markedAtoms = /* @__PURE__ */ new Set();\n    const visit = (n) => {\n      if (markedAtoms.has(n)) {\n        return;\n      }\n      markedAtoms.add(n);\n      for (const m of getDependents(n)) {\n        if (n !== m) {\n          visit(m);\n        }\n      }\n      topsortedAtoms.push(n);\n    };\n    visit(atom);\n    const changedAtoms = /* @__PURE__ */ new Set([atom]);\n    const isMarked = (a) => markedAtoms.has(a);\n    for (let i = topsortedAtoms.length - 1; i >= 0; --i) {\n      const a = topsortedAtoms[i];\n      const prevAtomState = getAtomState(a);\n      if (!prevAtomState) {\n        continue;\n      }\n      let hasChangedDeps = false;\n      for (const dep of prevAtomState.d.keys()) {\n        if (dep !== a && changedAtoms.has(dep)) {\n          hasChangedDeps = true;\n          break;\n        }\n      }\n      if (hasChangedDeps) {\n        const nextAtomState = readAtomState(a, isMarked);\n        addPendingDependent(a, nextAtomState);\n        if (!isEqualAtomValue(prevAtomState, nextAtomState)) {\n          changedAtoms.add(a);\n        }\n      }\n      markedAtoms.delete(a);\n    }\n  };\n  const writeAtomState = (atom, ...args) => {\n    const getter = (a) => returnAtomValue(readAtomState(a));\n    const setter = (a, ...args2) => {\n      const isSync = pendingStack.length > 0;\n      if (!isSync) {\n        pendingStack.push(/* @__PURE__ */ new Set([a]));\n      }\n      let r;\n      if (isSelfAtom(atom, a)) {\n        if (!hasInitialValue(a)) {\n          throw new Error(\"atom not writable\");\n        }\n        const prevAtomState = getAtomState(a);\n        const nextAtomState = setAtomValueOrPromise(a, args2[0]);\n        if (!isEqualAtomValue(prevAtomState, nextAtomState)) {\n          recomputeDependents(a);\n        }\n      } else {\n        r = writeAtomState(a, ...args2);\n      }\n      if (!isSync) {\n        const flushed = flushPending(pendingStack.pop());\n        if (( false ? 0 : void 0) !== \"production\") {\n          devListenersRev2.forEach(\n            (l) => l({ type: \"async-write\", flushed })\n          );\n        }\n      }\n      return r;\n    };\n    const result = atom.write(getter, setter, ...args);\n    return result;\n  };\n  const writeAtom = (atom, ...args) => {\n    pendingStack.push(/* @__PURE__ */ new Set([atom]));\n    const result = writeAtomState(atom, ...args);\n    const flushed = flushPending(pendingStack.pop());\n    if (( false ? 0 : void 0) !== \"production\") {\n      devListenersRev2.forEach((l) => l({ type: \"write\", flushed }));\n    }\n    return result;\n  };\n  const mountAtom = (atom, initialDependent, onMountQueue) => {\n    var _a;\n    const existingMount = mountedMap.get(atom);\n    if (existingMount) {\n      if (initialDependent) {\n        existingMount.t.add(initialDependent);\n      }\n      return existingMount;\n    }\n    const queue = onMountQueue || [];\n    (_a = getAtomState(atom)) == null ? void 0 : _a.d.forEach((_, a) => {\n      if (a !== atom) {\n        mountAtom(a, atom, queue);\n      }\n    });\n    readAtomState(atom);\n    const mounted = {\n      t: new Set(initialDependent && [initialDependent]),\n      l: /* @__PURE__ */ new Set()\n    };\n    mountedMap.set(atom, mounted);\n    if (( false ? 0 : void 0) !== \"production\") {\n      mountedAtoms.add(atom);\n    }\n    if (isActuallyWritableAtom(atom) && atom.onMount) {\n      const { onMount } = atom;\n      queue.push(() => {\n        const onUnmount = onMount((...args) => writeAtom(atom, ...args));\n        if (onUnmount) {\n          mounted.u = onUnmount;\n        }\n      });\n    }\n    if (!onMountQueue) {\n      queue.forEach((f) => f());\n    }\n    return mounted;\n  };\n  const canUnmountAtom = (atom, mounted) => !mounted.l.size && (!mounted.t.size || mounted.t.size === 1 && mounted.t.has(atom));\n  const tryUnmountAtom = (atom, mounted) => {\n    if (!canUnmountAtom(atom, mounted)) {\n      return;\n    }\n    const onUnmount = mounted.u;\n    if (onUnmount) {\n      onUnmount();\n    }\n    mountedMap.delete(atom);\n    if (( false ? 0 : void 0) !== \"production\") {\n      mountedAtoms.delete(atom);\n    }\n    const atomState = getAtomState(atom);\n    if (atomState) {\n      if (hasPromiseAtomValue(atomState)) {\n        cancelPromise(atomState.v);\n      }\n      atomState.d.forEach((_, a) => {\n        if (a !== atom) {\n          const mountedDep = mountedMap.get(a);\n          if (mountedDep) {\n            mountedDep.t.delete(atom);\n            tryUnmountAtom(a, mountedDep);\n          }\n        }\n      });\n    } else if (( false ? 0 : void 0) !== \"production\") {\n      console.warn(\"[Bug] could not find atom state to unmount\", atom);\n    }\n  };\n  const mountDependencies = (atom, atomState, prevDependencies) => {\n    const depSet = new Set(atomState.d.keys());\n    const maybeUnmountAtomSet = /* @__PURE__ */ new Set();\n    prevDependencies == null ? void 0 : prevDependencies.forEach((_, a) => {\n      if (depSet.has(a)) {\n        depSet.delete(a);\n        return;\n      }\n      maybeUnmountAtomSet.add(a);\n      const mounted = mountedMap.get(a);\n      if (mounted) {\n        mounted.t.delete(atom);\n      }\n    });\n    depSet.forEach((a) => {\n      mountAtom(a, atom);\n    });\n    maybeUnmountAtomSet.forEach((a) => {\n      const mounted = mountedMap.get(a);\n      if (mounted) {\n        tryUnmountAtom(a, mounted);\n      }\n    });\n  };\n  const flushPending = (pendingAtoms) => {\n    let flushed;\n    if (( false ? 0 : void 0) !== \"production\") {\n      flushed = /* @__PURE__ */ new Set();\n    }\n    const pending = [];\n    const collectPending = (pendingAtom) => {\n      var _a;\n      if (!pendingMap.has(pendingAtom)) {\n        return;\n      }\n      const [prevAtomState, dependents] = pendingMap.get(pendingAtom);\n      pendingMap.delete(pendingAtom);\n      pending.push([pendingAtom, prevAtomState]);\n      dependents.forEach(collectPending);\n      (_a = getAtomState(pendingAtom)) == null ? void 0 : _a.d.forEach((_, a) => collectPending(a));\n    };\n    pendingAtoms.forEach(collectPending);\n    pending.forEach(([atom, prevAtomState]) => {\n      const atomState = getAtomState(atom);\n      if (!atomState) {\n        if (( false ? 0 : void 0) !== \"production\") {\n          console.warn(\"[Bug] no atom state to flush\");\n        }\n        return;\n      }\n      if (atomState !== prevAtomState) {\n        const mounted = mountedMap.get(atom);\n        if (mounted && atomState.d !== (prevAtomState == null ? void 0 : prevAtomState.d)) {\n          mountDependencies(atom, atomState, prevAtomState == null ? void 0 : prevAtomState.d);\n        }\n        if (mounted && !// TODO This seems pretty hacky. Hope to fix it.\n        // Maybe we could `mountDependencies` in `setAtomState`?\n        (!hasPromiseAtomValue(prevAtomState) && (isEqualAtomValue(prevAtomState, atomState) || isEqualAtomError(prevAtomState, atomState)))) {\n          mounted.l.forEach((listener) => listener());\n          if (( false ? 0 : void 0) !== \"production\") {\n            flushed.add(atom);\n          }\n        }\n      }\n    });\n    if (( false ? 0 : void 0) !== \"production\") {\n      return flushed;\n    }\n  };\n  const subscribeAtom = (atom, listener) => {\n    const mounted = mountAtom(atom);\n    const flushed = flushPending([atom]);\n    const listeners = mounted.l;\n    listeners.add(listener);\n    if (( false ? 0 : void 0) !== \"production\") {\n      devListenersRev2.forEach(\n        (l) => l({ type: \"sub\", flushed })\n      );\n    }\n    return () => {\n      listeners.delete(listener);\n      tryUnmountAtom(atom, mounted);\n      if (( false ? 0 : void 0) !== \"production\") {\n        devListenersRev2.forEach((l) => l({ type: \"unsub\" }));\n      }\n    };\n  };\n  if (( false ? 0 : void 0) !== \"production\") {\n    return {\n      get: readAtom,\n      set: writeAtom,\n      sub: subscribeAtom,\n      // store dev methods (these are tentative and subject to change without notice)\n      dev_subscribe_store: (l) => {\n        devListenersRev2.add(l);\n        return () => {\n          devListenersRev2.delete(l);\n        };\n      },\n      dev_get_mounted_atoms: () => mountedAtoms.values(),\n      dev_get_atom_state: (a) => atomStateMap.get(a),\n      dev_get_mounted: (a) => mountedMap.get(a),\n      dev_restore_atoms: (values) => {\n        pendingStack.push(/* @__PURE__ */ new Set());\n        for (const [atom, valueOrPromise] of values) {\n          if (hasInitialValue(atom)) {\n            setAtomValueOrPromise(atom, valueOrPromise);\n            recomputeDependents(atom);\n          }\n        }\n        const flushed = flushPending(pendingStack.pop());\n        devListenersRev2.forEach(\n          (l) => l({ type: \"restore\", flushed })\n        );\n      }\n    };\n  }\n  return {\n    get: readAtom,\n    set: writeAtom,\n    sub: subscribeAtom\n  };\n};\nlet defaultStore;\nconst getDefaultStore$1 = () => {\n  if (!defaultStore) {\n    defaultStore = createStore$1();\n    if (( false ? 0 : void 0) !== \"production\") {\n      globalThis.__JOTAI_DEFAULT_STORE__ || (globalThis.__JOTAI_DEFAULT_STORE__ = defaultStore);\n      if (globalThis.__JOTAI_DEFAULT_STORE__ !== defaultStore) {\n        console.warn(\n          \"Detected multiple Jotai instances. It may cause unexpected behavior with the default store. https://github.com/pmndrs/jotai/discussions/2044\"\n        );\n      }\n    }\n  }\n  return defaultStore;\n};\n\nSymbol(\n  ( false ? 0 : void 0) !== \"production\" ? \"CONTINUE_PROMISE\" : \"\"\n);\n\nconst createStore = createStore$1;\nconst getDefaultStore = getDefaultStore$1;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla/utils.mjs":
/*!*******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla/utils.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RESET: () => (/* binding */ RESET),\n/* harmony export */   atomFamily: () => (/* binding */ atomFamily),\n/* harmony export */   atomWithDefault: () => (/* binding */ atomWithDefault),\n/* harmony export */   atomWithLazy: () => (/* binding */ atomWithLazy),\n/* harmony export */   atomWithObservable: () => (/* binding */ atomWithObservable),\n/* harmony export */   atomWithReducer: () => (/* binding */ atomWithReducer),\n/* harmony export */   atomWithRefresh: () => (/* binding */ atomWithRefresh),\n/* harmony export */   atomWithReset: () => (/* binding */ atomWithReset),\n/* harmony export */   atomWithStorage: () => (/* binding */ atomWithStorage),\n/* harmony export */   createJSONStorage: () => (/* binding */ createJSONStorage),\n/* harmony export */   freezeAtom: () => (/* binding */ freezeAtom),\n/* harmony export */   freezeAtomCreator: () => (/* binding */ freezeAtomCreator),\n/* harmony export */   loadable: () => (/* binding */ loadable),\n/* harmony export */   selectAtom: () => (/* binding */ selectAtom),\n/* harmony export */   splitAtom: () => (/* binding */ splitAtom),\n/* harmony export */   unstable_withStorageValidator: () => (/* binding */ withStorageValidator),\n/* harmony export */   unwrap: () => (/* binding */ unwrap)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\");\n\n\nconst RESET = Symbol(\n  ( false ? 0 : void 0) !== \"production\" ? \"RESET\" : \"\"\n);\n\nfunction atomWithReset(initialValue) {\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    initialValue,\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(anAtom)) : update;\n      set(anAtom, nextValue === RESET ? initialValue : nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithReducer(initialValue, reducer) {\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(initialValue, function(get, set, action) {\n    set(this, reducer(get(this), action));\n  });\n}\n\nfunction atomFamily(initializeAtom, areEqual) {\n  let shouldRemove = null;\n  const atoms = /* @__PURE__ */ new Map();\n  const createAtom = (param) => {\n    let item;\n    if (areEqual === void 0) {\n      item = atoms.get(param);\n    } else {\n      for (const [key, value] of atoms) {\n        if (areEqual(key, param)) {\n          item = value;\n          break;\n        }\n      }\n    }\n    if (item !== void 0) {\n      if (shouldRemove == null ? void 0 : shouldRemove(item[1], param)) {\n        createAtom.remove(param);\n      } else {\n        return item[0];\n      }\n    }\n    const newAtom = initializeAtom(param);\n    atoms.set(param, [newAtom, Date.now()]);\n    return newAtom;\n  };\n  createAtom.remove = (param) => {\n    if (areEqual === void 0) {\n      atoms.delete(param);\n    } else {\n      for (const [key] of atoms) {\n        if (areEqual(key, param)) {\n          atoms.delete(key);\n          break;\n        }\n      }\n    }\n  };\n  createAtom.setShouldRemove = (fn) => {\n    shouldRemove = fn;\n    if (!shouldRemove) return;\n    for (const [key, value] of atoms) {\n      if (shouldRemove(value[1], key)) {\n        atoms.delete(key);\n      }\n    }\n  };\n  return createAtom;\n}\n\nconst getCached$2 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$3 = /* @__PURE__ */ new WeakMap();\nconst memo3 = (create, dep1, dep2, dep3) => {\n  const cache2 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache1$3, dep1);\n  const cache3 = getCached$2(() => /* @__PURE__ */ new WeakMap(), cache2, dep2);\n  return getCached$2(create, cache3, dep3);\n};\nfunction selectAtom(anAtom, selector, equalityFn = Object.is) {\n  return memo3(\n    () => {\n      const EMPTY = Symbol();\n      const selectValue = ([value, prevSlice]) => {\n        if (prevSlice === EMPTY) {\n          return selector(value);\n        }\n        const slice = selector(value, prevSlice);\n        return equalityFn(prevSlice, slice) ? prevSlice : slice;\n      };\n      const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(derivedAtom);\n        const value = get(anAtom);\n        return selectValue([value, prev]);\n      });\n      derivedAtom.init = EMPTY;\n      return derivedAtom;\n    },\n    anAtom,\n    selector,\n    equalityFn\n  );\n}\n\nconst frozenAtoms = /* @__PURE__ */ new WeakSet();\nconst deepFreeze = (obj) => {\n  if (typeof obj !== \"object\" || obj === null) return;\n  Object.freeze(obj);\n  const propNames = Object.getOwnPropertyNames(obj);\n  for (const name of propNames) {\n    const value = obj[name];\n    deepFreeze(value);\n  }\n  return obj;\n};\nfunction freezeAtom(anAtom) {\n  if (frozenAtoms.has(anAtom)) {\n    return anAtom;\n  }\n  frozenAtoms.add(anAtom);\n  const origRead = anAtom.read;\n  anAtom.read = function(get, options) {\n    return deepFreeze(origRead.call(this, get, options));\n  };\n  if (\"write\" in anAtom) {\n    const origWrite = anAtom.write;\n    anAtom.write = function(get, set, ...args) {\n      return origWrite.call(\n        this,\n        get,\n        (...setArgs) => {\n          if (setArgs[0] === anAtom) {\n            setArgs[1] = deepFreeze(setArgs[1]);\n          }\n          return set(...setArgs);\n        },\n        ...args\n      );\n    };\n  }\n  return anAtom;\n}\nfunction freezeAtomCreator(createAtom) {\n  if (( false ? 0 : void 0) !== \"production\") {\n    console.warn(\n      \"[DEPRECATED] freezeAtomCreator is deprecated, define it on users end\"\n    );\n  }\n  return (...args) => freezeAtom(createAtom(...args));\n}\n\nconst getCached$1 = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1$2 = /* @__PURE__ */ new WeakMap();\nconst memo2$1 = (create, dep1, dep2) => {\n  const cache2 = getCached$1(() => /* @__PURE__ */ new WeakMap(), cache1$2, dep1);\n  return getCached$1(create, cache2, dep2);\n};\nconst cacheKeyForEmptyKeyExtractor = {};\nconst isWritable = (atom2) => !!atom2.write;\nconst isFunction = (x) => typeof x === \"function\";\nfunction splitAtom(arrAtom, keyExtractor) {\n  return memo2$1(\n    () => {\n      const mappingCache = /* @__PURE__ */ new WeakMap();\n      const getMapping = (arr, prev) => {\n        let mapping = mappingCache.get(arr);\n        if (mapping) {\n          return mapping;\n        }\n        const prevMapping = prev && mappingCache.get(prev);\n        const atomList = [];\n        const keyList = [];\n        arr.forEach((item, index) => {\n          const key = keyExtractor ? keyExtractor(item) : index;\n          keyList[index] = key;\n          const cachedAtom = prevMapping && prevMapping.atomList[prevMapping.keyList.indexOf(key)];\n          if (cachedAtom) {\n            atomList[index] = cachedAtom;\n            return;\n          }\n          const read = (get) => {\n            const prev2 = get(mappingAtom);\n            const currArr = get(arrAtom);\n            const mapping2 = getMapping(currArr, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= currArr.length) {\n              const prevItem = arr[getMapping(arr).keyList.indexOf(key)];\n              if (prevItem) {\n                return prevItem;\n              }\n              throw new Error(\"splitAtom: index out of bounds for read\");\n            }\n            return currArr[index2];\n          };\n          const write = (get, set, update) => {\n            const prev2 = get(mappingAtom);\n            const arr2 = get(arrAtom);\n            const mapping2 = getMapping(arr2, prev2 == null ? void 0 : prev2.arr);\n            const index2 = mapping2.keyList.indexOf(key);\n            if (index2 < 0 || index2 >= arr2.length) {\n              throw new Error(\"splitAtom: index out of bounds for write\");\n            }\n            const nextItem = isFunction(update) ? update(arr2[index2]) : update;\n            if (!Object.is(arr2[index2], nextItem)) {\n              set(arrAtom, [\n                ...arr2.slice(0, index2),\n                nextItem,\n                ...arr2.slice(index2 + 1)\n              ]);\n            }\n          };\n          atomList[index] = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read, write) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(read);\n        });\n        if (prevMapping && prevMapping.keyList.length === keyList.length && prevMapping.keyList.every((x, i) => x === keyList[i])) {\n          mapping = prevMapping;\n        } else {\n          mapping = { arr, atomList, keyList };\n        }\n        mappingCache.set(arr, mapping);\n        return mapping;\n      };\n      const mappingAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n        const prev = get(mappingAtom);\n        const arr = get(arrAtom);\n        const mapping = getMapping(arr, prev == null ? void 0 : prev.arr);\n        return mapping;\n      });\n      if (( false ? 0 : void 0) !== \"production\") {\n        mappingAtom.debugPrivate = true;\n      }\n      mappingAtom.init = void 0;\n      const splittedAtom = isWritable(arrAtom) ? (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => get(mappingAtom).atomList,\n        (get, set, action) => {\n          switch (action.type) {\n            case \"remove\": {\n              const index = get(splittedAtom).indexOf(action.atom);\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  ...arr.slice(index + 1)\n                ]);\n              }\n              break;\n            }\n            case \"insert\": {\n              const index = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index >= 0) {\n                const arr = get(arrAtom);\n                set(arrAtom, [\n                  ...arr.slice(0, index),\n                  action.value,\n                  ...arr.slice(index)\n                ]);\n              }\n              break;\n            }\n            case \"move\": {\n              const index1 = get(splittedAtom).indexOf(action.atom);\n              const index2 = action.before ? get(splittedAtom).indexOf(action.before) : get(splittedAtom).length;\n              if (index1 >= 0 && index2 >= 0) {\n                const arr = get(arrAtom);\n                if (index1 < index2) {\n                  set(arrAtom, [\n                    ...arr.slice(0, index1),\n                    ...arr.slice(index1 + 1, index2),\n                    arr[index1],\n                    ...arr.slice(index2)\n                  ]);\n                } else {\n                  set(arrAtom, [\n                    ...arr.slice(0, index2),\n                    arr[index1],\n                    ...arr.slice(index2, index1),\n                    ...arr.slice(index1 + 1)\n                  ]);\n                }\n              }\n              break;\n            }\n          }\n        }\n      ) : (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(mappingAtom).atomList);\n      return splittedAtom;\n    },\n    arrAtom,\n    keyExtractor || cacheKeyForEmptyKeyExtractor\n  );\n}\n\nfunction atomWithDefault(getDefault) {\n  const EMPTY = Symbol();\n  const overwrittenAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(EMPTY);\n  if (( false ? 0 : void 0) !== \"production\") {\n    overwrittenAtom.debugPrivate = true;\n  }\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      const overwritten = get(overwrittenAtom);\n      if (overwritten !== EMPTY) {\n        return overwritten;\n      }\n      return getDefault(get, options);\n    },\n    (get, set, update) => {\n      if (update === RESET) {\n        set(overwrittenAtom, EMPTY);\n      } else if (typeof update === \"function\") {\n        const prevValue = get(anAtom);\n        set(overwrittenAtom, update(prevValue));\n      } else {\n        set(overwrittenAtom, update);\n      }\n    }\n  );\n  return anAtom;\n}\n\nconst isPromiseLike = (x) => typeof (x == null ? void 0 : x.then) === \"function\";\nfunction withStorageValidator(validator) {\n  return (unknownStorage) => {\n    const storage = {\n      ...unknownStorage,\n      getItem: (key, initialValue) => {\n        const validate = (value2) => {\n          if (!validator(value2)) {\n            return initialValue;\n          }\n          return value2;\n        };\n        const value = unknownStorage.getItem(key, initialValue);\n        if (isPromiseLike(value)) {\n          return value.then(validate);\n        }\n        return validate(value);\n      }\n    };\n    return storage;\n  };\n}\nfunction createJSONStorage(getStringStorage = () => {\n  try {\n    return window.localStorage;\n  } catch (e) {\n    if (( false ? 0 : void 0) !== \"production\") {\n      if (typeof window !== \"undefined\") {\n        console.warn(e);\n      }\n    }\n    return void 0;\n  }\n}, options) {\n  var _a;\n  let lastStr;\n  let lastValue;\n  const storage = {\n    getItem: (key, initialValue) => {\n      var _a2, _b;\n      const parse = (str2) => {\n        str2 = str2 || \"\";\n        if (lastStr !== str2) {\n          try {\n            lastValue = JSON.parse(str2, options == null ? void 0 : options.reviver);\n          } catch (e) {\n            return initialValue;\n          }\n          lastStr = str2;\n        }\n        return lastValue;\n      };\n      const str = (_b = (_a2 = getStringStorage()) == null ? void 0 : _a2.getItem(key)) != null ? _b : null;\n      if (isPromiseLike(str)) {\n        return str.then(parse);\n      }\n      return parse(str);\n    },\n    setItem: (key, newValue) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.setItem(\n        key,\n        JSON.stringify(newValue, options == null ? void 0 : options.replacer)\n      );\n    },\n    removeItem: (key) => {\n      var _a2;\n      return (_a2 = getStringStorage()) == null ? void 0 : _a2.removeItem(key);\n    }\n  };\n  const createHandleSubscribe = (subscriber2) => (key, callback, initialValue) => subscriber2(key, (v) => {\n    let newValue;\n    try {\n      newValue = JSON.parse(v || \"\");\n    } catch (e) {\n      newValue = initialValue;\n    }\n    callback(newValue);\n  });\n  let subscriber;\n  try {\n    subscriber = (_a = getStringStorage()) == null ? void 0 : _a.subscribe;\n  } catch (e) {\n  }\n  if (!subscriber && typeof window !== \"undefined\" && typeof window.addEventListener === \"function\" && window.Storage) {\n    subscriber = (key, callback) => {\n      if (!(getStringStorage() instanceof window.Storage)) {\n        return () => {\n        };\n      }\n      const storageEventCallback = (e) => {\n        if (e.storageArea === getStringStorage() && e.key === key) {\n          callback(e.newValue);\n        }\n      };\n      window.addEventListener(\"storage\", storageEventCallback);\n      return () => {\n        window.removeEventListener(\"storage\", storageEventCallback);\n      };\n    };\n  }\n  if (subscriber) {\n    storage.subscribe = createHandleSubscribe(subscriber);\n  }\n  return storage;\n}\nconst defaultStorage = createJSONStorage();\nfunction atomWithStorage(key, initialValue, storage = defaultStorage, options) {\n  const getOnInit = options == null ? void 0 : options.getOnInit;\n  const baseAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    getOnInit ? storage.getItem(key, initialValue) : initialValue\n  );\n  if (( false ? 0 : void 0) !== \"production\") {\n    baseAtom.debugPrivate = true;\n  }\n  baseAtom.onMount = (setAtom) => {\n    setAtom(storage.getItem(key, initialValue));\n    let unsub;\n    if (storage.subscribe) {\n      unsub = storage.subscribe(key, setAtom, initialValue);\n    }\n    return unsub;\n  };\n  const anAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => get(baseAtom),\n    (get, set, update) => {\n      const nextValue = typeof update === \"function\" ? update(get(baseAtom)) : update;\n      if (nextValue === RESET) {\n        set(baseAtom, initialValue);\n        return storage.removeItem(key);\n      }\n      if (nextValue instanceof Promise) {\n        return nextValue.then((resolvedValue) => {\n          set(baseAtom, resolvedValue);\n          return storage.setItem(key, resolvedValue);\n        });\n      }\n      set(baseAtom, nextValue);\n      return storage.setItem(key, nextValue);\n    }\n  );\n  return anAtom;\n}\n\nfunction atomWithObservable(getObservable, options) {\n  const returnResultData = (result) => {\n    if (\"e\" in result) {\n      throw result.e;\n    }\n    return result.d;\n  };\n  const observableResultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => {\n    var _a;\n    let observable = getObservable(get);\n    const itself = (_a = observable[Symbol.observable]) == null ? void 0 : _a.call(observable);\n    if (itself) {\n      observable = itself;\n    }\n    let resolve;\n    const makePending = () => new Promise((r) => {\n      resolve = r;\n    });\n    const initialResult = options && \"initialValue\" in options ? {\n      d: typeof options.initialValue === \"function\" ? options.initialValue() : options.initialValue\n    } : makePending();\n    let setResult;\n    let lastResult;\n    const listener = (result) => {\n      lastResult = result;\n      resolve == null ? void 0 : resolve(result);\n      setResult == null ? void 0 : setResult(result);\n    };\n    let subscription;\n    let timer;\n    const isNotMounted = () => !setResult;\n    const start = () => {\n      if (subscription) {\n        clearTimeout(timer);\n        subscription.unsubscribe();\n      }\n      subscription = observable.subscribe({\n        next: (d) => listener({ d }),\n        error: (e) => listener({ e }),\n        complete: () => {\n        }\n      });\n      if (isNotMounted() && (options == null ? void 0 : options.unstable_timeout)) {\n        timer = setTimeout(() => {\n          if (subscription) {\n            subscription.unsubscribe();\n            subscription = void 0;\n          }\n        }, options.unstable_timeout);\n      }\n    };\n    start();\n    const resultAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(lastResult || initialResult);\n    if (( false ? 0 : void 0) !== \"production\") {\n      resultAtom.debugPrivate = true;\n    }\n    resultAtom.onMount = (update) => {\n      setResult = update;\n      if (lastResult) {\n        update(lastResult);\n      }\n      if (subscription) {\n        clearTimeout(timer);\n      } else {\n        start();\n      }\n      return () => {\n        setResult = void 0;\n        if (subscription) {\n          subscription.unsubscribe();\n          subscription = void 0;\n        }\n      };\n    };\n    return [resultAtom, observable, makePending, start, isNotMounted];\n  });\n  if (( false ? 0 : void 0) !== \"production\") {\n    observableResultAtom.debugPrivate = true;\n  }\n  const observableAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => {\n      const [resultAtom] = get(observableResultAtom);\n      const result = get(resultAtom);\n      if (result instanceof Promise) {\n        return result.then(returnResultData);\n      }\n      return returnResultData(result);\n    },\n    (get, set, data) => {\n      const [resultAtom, observable, makePending, start, isNotMounted] = get(observableResultAtom);\n      if (\"next\" in observable) {\n        if (isNotMounted()) {\n          set(resultAtom, makePending());\n          start();\n        }\n        observable.next(data);\n      } else {\n        throw new Error(\"observable is not subject\");\n      }\n    }\n  );\n  return observableAtom;\n}\n\nconst cache1$1 = /* @__PURE__ */ new WeakMap();\nconst memo1 = (create, dep1) => (cache1$1.has(dep1) ? cache1$1 : cache1$1.set(dep1, create())).get(dep1);\nconst isPromise$1 = (x) => x instanceof Promise;\nconst LOADING = { state: \"loading\" };\nfunction loadable(anAtom) {\n  return memo1(() => {\n    const loadableCache = /* @__PURE__ */ new WeakMap();\n    const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n    if (( false ? 0 : void 0) !== \"production\") {\n      refreshAtom.debugPrivate = true;\n    }\n    const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n      (get, { setSelf }) => {\n        get(refreshAtom);\n        let value;\n        try {\n          value = get(anAtom);\n        } catch (error) {\n          return { state: \"hasError\", error };\n        }\n        if (!isPromise$1(value)) {\n          return { state: \"hasData\", data: value };\n        }\n        const promise = value;\n        const cached1 = loadableCache.get(promise);\n        if (cached1) {\n          return cached1;\n        }\n        if (promise.status === \"fulfilled\") {\n          loadableCache.set(promise, { state: \"hasData\", data: promise.value });\n        } else if (promise.status === \"rejected\") {\n          loadableCache.set(promise, {\n            state: \"hasError\",\n            error: promise.reason\n          });\n        } else {\n          promise.then(\n            (data) => {\n              loadableCache.set(promise, { state: \"hasData\", data });\n            },\n            (error) => {\n              loadableCache.set(promise, { state: \"hasError\", error });\n            }\n          ).finally(setSelf);\n        }\n        const cached2 = loadableCache.get(promise);\n        if (cached2) {\n          return cached2;\n        }\n        loadableCache.set(promise, LOADING);\n        return LOADING;\n      },\n      (_get, set) => {\n        set(refreshAtom, (c) => c + 1);\n      }\n    );\n    if (( false ? 0 : void 0) !== \"production\") {\n      derivedAtom.debugPrivate = true;\n    }\n    return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)((get) => get(derivedAtom));\n  }, anAtom);\n}\n\nconst getCached = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1 = /* @__PURE__ */ new WeakMap();\nconst memo2 = (create, dep1, dep2) => {\n  const cache2 = getCached(() => /* @__PURE__ */ new WeakMap(), cache1, dep1);\n  return getCached(create, cache2, dep2);\n};\nconst isPromise = (x) => x instanceof Promise;\nconst defaultFallback = () => void 0;\nfunction unwrap(anAtom, fallback = defaultFallback) {\n  return memo2(\n    () => {\n      const promiseErrorCache = /* @__PURE__ */ new WeakMap();\n      const promiseResultCache = /* @__PURE__ */ new WeakMap();\n      const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n      if (( false ? 0 : void 0) !== \"production\") {\n        refreshAtom.debugPrivate = true;\n      }\n      const promiseAndValueAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get, { setSelf }) => {\n          get(refreshAtom);\n          const prev = get(promiseAndValueAtom);\n          const promise = get(anAtom);\n          if (!isPromise(promise)) {\n            return { v: promise };\n          }\n          if (promise !== (prev == null ? void 0 : prev.p)) {\n            if (promise.status === \"fulfilled\") {\n              promiseResultCache.set(promise, promise.value);\n            } else if (promise.status === \"rejected\") {\n              promiseErrorCache.set(promise, promise.reason);\n            } else {\n              promise.then(\n                (v) => promiseResultCache.set(promise, v),\n                (e) => promiseErrorCache.set(promise, e)\n              ).finally(setSelf);\n            }\n          }\n          if (promiseErrorCache.has(promise)) {\n            throw promiseErrorCache.get(promise);\n          }\n          if (promiseResultCache.has(promise)) {\n            return {\n              p: promise,\n              v: promiseResultCache.get(promise)\n            };\n          }\n          if (prev && \"v\" in prev) {\n            return { p: promise, f: fallback(prev.v), v: prev.v };\n          }\n          return { p: promise, f: fallback() };\n        },\n        (_get, set) => {\n          set(refreshAtom, (c) => c + 1);\n        }\n      );\n      promiseAndValueAtom.init = void 0;\n      if (( false ? 0 : void 0) !== \"production\") {\n        promiseAndValueAtom.debugPrivate = true;\n      }\n      return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n        (get) => {\n          const state = get(promiseAndValueAtom);\n          if (\"f\" in state) {\n            return state.f;\n          }\n          return state.v;\n        },\n        (_get, set, ...args) => set(anAtom, ...args)\n      );\n    },\n    anAtom,\n    fallback\n  );\n}\n\nfunction atomWithRefresh(read, write) {\n  const refreshAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(0);\n  if (( false ? 0 : void 0) !== \"production\") {\n    refreshAtom.debugPrivate = true;\n  }\n  return (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get, options) => {\n      get(refreshAtom);\n      return read(get, options);\n    },\n    (get, set, ...args) => {\n      if (args.length === 0) {\n        set(refreshAtom, (c) => c + 1);\n      } else if (write) {\n        return write(get, set, ...args);\n      }\n    }\n  );\n}\n\nfunction atomWithLazy(makeInitial) {\n  const a = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_0__.atom)(void 0);\n  delete a.init;\n  Object.defineProperty(a, \"init\", {\n    get() {\n      return makeInitial();\n    }\n  });\n  return a;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla/utils.mjs\n");

/***/ })

};
;