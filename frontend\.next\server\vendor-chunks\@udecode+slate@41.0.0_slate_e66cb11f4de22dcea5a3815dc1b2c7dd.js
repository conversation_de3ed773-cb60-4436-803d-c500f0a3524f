"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd";
exports.ids = ["vendor-chunks/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs":
/*!************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs ***!
  \************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HISTORY: () => (/* binding */ HISTORY),\n/* harmony export */   History: () => (/* binding */ History),\n/* harmony export */   HistoryEditor: () => (/* binding */ HistoryEditor),\n/* harmony export */   MERGING: () => (/* binding */ MERGING),\n/* harmony export */   SAVING: () => (/* binding */ SAVING),\n/* harmony export */   SPLITTING_ONCE: () => (/* binding */ SPLITTING_ONCE),\n/* harmony export */   addMark: () => (/* binding */ addMark),\n/* harmony export */   addRangeMarks: () => (/* binding */ addRangeMarks),\n/* harmony export */   collapseSelection: () => (/* binding */ collapseSelection),\n/* harmony export */   createPathRef: () => (/* binding */ createPathRef),\n/* harmony export */   createPointRef: () => (/* binding */ createPointRef),\n/* harmony export */   createRangeRef: () => (/* binding */ createRangeRef),\n/* harmony export */   createTEditor: () => (/* binding */ createTEditor),\n/* harmony export */   deleteBackward: () => (/* binding */ deleteBackward),\n/* harmony export */   deleteForward: () => (/* binding */ deleteForward),\n/* harmony export */   deleteFragment: () => (/* binding */ deleteFragment),\n/* harmony export */   deleteMerge: () => (/* binding */ deleteMerge),\n/* harmony export */   deleteText: () => (/* binding */ deleteText),\n/* harmony export */   deselect: () => (/* binding */ deselect),\n/* harmony export */   elementMatches: () => (/* binding */ elementMatches),\n/* harmony export */   findNode: () => (/* binding */ findNode),\n/* harmony export */   findNodePath: () => (/* binding */ findNodePath),\n/* harmony export */   getAboveNode: () => (/* binding */ getAboveNode),\n/* harmony export */   getCommonNode: () => (/* binding */ getCommonNode),\n/* harmony export */   getEdgePoints: () => (/* binding */ getEdgePoints),\n/* harmony export */   getEditorString: () => (/* binding */ getEditorString),\n/* harmony export */   getEndPoint: () => (/* binding */ getEndPoint),\n/* harmony export */   getFirstNode: () => (/* binding */ getFirstNode),\n/* harmony export */   getFragment: () => (/* binding */ getFragment),\n/* harmony export */   getLastNode: () => (/* binding */ getLastNode),\n/* harmony export */   getLeafNode: () => (/* binding */ getLeafNode),\n/* harmony export */   getLevels: () => (/* binding */ getLevels),\n/* harmony export */   getMarks: () => (/* binding */ getMarks),\n/* harmony export */   getNextNode: () => (/* binding */ getNextNode),\n/* harmony export */   getNode: () => (/* binding */ getNode),\n/* harmony export */   getNodeAncestor: () => (/* binding */ getNodeAncestor),\n/* harmony export */   getNodeAncestors: () => (/* binding */ getNodeAncestors),\n/* harmony export */   getNodeChild: () => (/* binding */ getNodeChild),\n/* harmony export */   getNodeChildren: () => (/* binding */ getNodeChildren),\n/* harmony export */   getNodeDescendant: () => (/* binding */ getNodeDescendant),\n/* harmony export */   getNodeDescendants: () => (/* binding */ getNodeDescendants),\n/* harmony export */   getNodeElements: () => (/* binding */ getNodeElements),\n/* harmony export */   getNodeEntries: () => (/* binding */ getNodeEntries),\n/* harmony export */   getNodeEntry: () => (/* binding */ getNodeEntry),\n/* harmony export */   getNodeFirstNode: () => (/* binding */ getNodeFirstNode),\n/* harmony export */   getNodeFragment: () => (/* binding */ getNodeFragment),\n/* harmony export */   getNodeLastNode: () => (/* binding */ getNodeLastNode),\n/* harmony export */   getNodeLeaf: () => (/* binding */ getNodeLeaf),\n/* harmony export */   getNodeLevels: () => (/* binding */ getNodeLevels),\n/* harmony export */   getNodeParent: () => (/* binding */ getNodeParent),\n/* harmony export */   getNodeProps: () => (/* binding */ getNodeProps),\n/* harmony export */   getNodeString: () => (/* binding */ getNodeString),\n/* harmony export */   getNodeTexts: () => (/* binding */ getNodeTexts),\n/* harmony export */   getNodes: () => (/* binding */ getNodes),\n/* harmony export */   getParentNode: () => (/* binding */ getParentNode),\n/* harmony export */   getPath: () => (/* binding */ getPath),\n/* harmony export */   getPathRefs: () => (/* binding */ getPathRefs),\n/* harmony export */   getPoint: () => (/* binding */ getPoint),\n/* harmony export */   getPointAfter: () => (/* binding */ getPointAfter),\n/* harmony export */   getPointBefore: () => (/* binding */ getPointBefore),\n/* harmony export */   getPointRefs: () => (/* binding */ getPointRefs),\n/* harmony export */   getPositions: () => (/* binding */ getPositions),\n/* harmony export */   getPreviousNode: () => (/* binding */ getPreviousNode),\n/* harmony export */   getQueryOptions: () => (/* binding */ getQueryOptions),\n/* harmony export */   getRange: () => (/* binding */ getRange),\n/* harmony export */   getRangeRefs: () => (/* binding */ getRangeRefs),\n/* harmony export */   getStartPoint: () => (/* binding */ getStartPoint),\n/* harmony export */   getVoidNode: () => (/* binding */ getVoidNode),\n/* harmony export */   hasBlocks: () => (/* binding */ hasBlocks),\n/* harmony export */   hasInlines: () => (/* binding */ hasInlines),\n/* harmony export */   hasNode: () => (/* binding */ hasNode),\n/* harmony export */   hasSingleChild: () => (/* binding */ hasSingleChild),\n/* harmony export */   hasTexts: () => (/* binding */ hasTexts),\n/* harmony export */   insertBreak: () => (/* binding */ insertBreak),\n/* harmony export */   insertFragment: () => (/* binding */ insertFragment),\n/* harmony export */   insertNode: () => (/* binding */ insertNode),\n/* harmony export */   insertNodes: () => (/* binding */ insertNodes),\n/* harmony export */   insertText: () => (/* binding */ insertText),\n/* harmony export */   isAncestor: () => (/* binding */ isAncestor),\n/* harmony export */   isBlock: () => (/* binding */ isBlock),\n/* harmony export */   isCollapsed: () => (/* binding */ isCollapsed),\n/* harmony export */   isDescendant: () => (/* binding */ isDescendant),\n/* harmony export */   isEdgePoint: () => (/* binding */ isEdgePoint),\n/* harmony export */   isEditor: () => (/* binding */ isEditor),\n/* harmony export */   isEditorNormalizing: () => (/* binding */ isEditorNormalizing),\n/* harmony export */   isElement: () => (/* binding */ isElement),\n/* harmony export */   isElementEmpty: () => (/* binding */ isElementEmpty),\n/* harmony export */   isElementList: () => (/* binding */ isElementList),\n/* harmony export */   isEndPoint: () => (/* binding */ isEndPoint),\n/* harmony export */   isExpanded: () => (/* binding */ isExpanded),\n/* harmony export */   isHistoryEditor: () => (/* binding */ isHistoryEditor),\n/* harmony export */   isHistoryMerging: () => (/* binding */ isHistoryMerging),\n/* harmony export */   isHistorySaving: () => (/* binding */ isHistorySaving),\n/* harmony export */   isInline: () => (/* binding */ isInline),\n/* harmony export */   isMarkableVoid: () => (/* binding */ isMarkableVoid),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isNodeList: () => (/* binding */ isNodeList),\n/* harmony export */   isStartPoint: () => (/* binding */ isStartPoint),\n/* harmony export */   isText: () => (/* binding */ isText),\n/* harmony export */   isTextList: () => (/* binding */ isTextList),\n/* harmony export */   isVoid: () => (/* binding */ isVoid),\n/* harmony export */   liftNodes: () => (/* binding */ liftNodes),\n/* harmony export */   match: () => (/* binding */ match),\n/* harmony export */   mergeNodes: () => (/* binding */ mergeNodes),\n/* harmony export */   moveNodes: () => (/* binding */ moveNodes),\n/* harmony export */   moveSelection: () => (/* binding */ moveSelection),\n/* harmony export */   nodeMatches: () => (/* binding */ nodeMatches),\n/* harmony export */   normalizeEditor: () => (/* binding */ normalizeEditor),\n/* harmony export */   queryNode: () => (/* binding */ queryNode),\n/* harmony export */   removeEditorMark: () => (/* binding */ removeEditorMark),\n/* harmony export */   removeNodes: () => (/* binding */ removeNodes),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   setElements: () => (/* binding */ setElements),\n/* harmony export */   setNode: () => (/* binding */ setNode),\n/* harmony export */   setNodes: () => (/* binding */ setNodes),\n/* harmony export */   setPoint: () => (/* binding */ setPoint),\n/* harmony export */   setSelection: () => (/* binding */ setSelection),\n/* harmony export */   someNode: () => (/* binding */ someNode),\n/* harmony export */   splitNodes: () => (/* binding */ splitNodes),\n/* harmony export */   textEquals: () => (/* binding */ textEquals),\n/* harmony export */   textMatches: () => (/* binding */ textMatches),\n/* harmony export */   unhangCharacterRange: () => (/* binding */ unhangCharacterRange),\n/* harmony export */   unhangRange: () => (/* binding */ unhangRange),\n/* harmony export */   unsetNodes: () => (/* binding */ unsetNodes),\n/* harmony export */   unwrapNodes: () => (/* binding */ unwrapNodes),\n/* harmony export */   withHistory: () => (/* binding */ withHistory),\n/* harmony export */   withMerging: () => (/* binding */ withMerging),\n/* harmony export */   withNewBatch: () => (/* binding */ withNewBatch),\n/* harmony export */   withoutMergingHistory: () => (/* binding */ withoutMergingHistory),\n/* harmony export */   withoutNormalizing: () => (/* binding */ withoutNormalizing),\n/* harmony export */   withoutSavingHistory: () => (/* binding */ withoutSavingHistory),\n/* harmony export */   wrapNodes: () => (/* binding */ wrapNodes)\n/* harmony export */ });\n/* harmony import */ var slate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! slate */ \"(ssr)/./node_modules/.pnpm/slate@0.103.0/node_modules/slate/dist/index.es.js\");\n/* harmony import */ var is_plain_object__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-plain-object */ \"(ssr)/./node_modules/.pnpm/is-plain-object@5.0.0/node_modules/is-plain-object/dist/is-plain-object.mjs\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/createTEditor.ts\n\nvar noop = (name, returnValue) => () => {\n  console.warn(\n    `[OVERRIDE_MISSING] The method editor.${name}() has not been implemented or overridden. This may cause unexpected behavior. Please ensure that all required editor methods are properly defined.`\n  );\n  return returnValue;\n};\nvar createTEditor = () => {\n  const editor = (0,slate__WEBPACK_IMPORTED_MODULE_0__.createEditor)();\n  editor.hasEditableTarget = noop(\"hasEditableTarget\", false);\n  editor.hasRange = noop(\"hasRange\", false);\n  editor.hasSelectableTarget = noop(\"hasSelectableTarget\", false);\n  editor.hasTarget = noop(\"hasTarget\", false);\n  editor.insertData = noop(\"insertData\");\n  editor.insertFragmentData = noop(\"insertFragmentData\");\n  editor.insertTextData = noop(\"insertTextData\", false);\n  editor.isTargetInsideNonReadonlyVoid = noop(\n    \"isTargetInsideNonReadonlyVoid\",\n    false\n  );\n  editor.setFragmentData = noop(\"setFragmentData\");\n  editor.history = { redos: [], undos: [] };\n  editor.undo = noop(\"undo\");\n  editor.redo = noop(\"redo\");\n  editor.writeHistory = noop(\"writeHistory\");\n  return editor;\n};\n\n// src/interfaces/editor/addMark.ts\n\nvar addMark = (editor, key, value) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.addMark(editor, key, value);\n\n// src/interfaces/editor/createPathRef.ts\n\nvar createPathRef = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.pathRef(editor, at, options);\n\n// src/interfaces/editor/createPointRef.ts\n\nvar createPointRef = (editor, point, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.pointRef(editor, point, options);\n\n// src/interfaces/editor/createRangeRef.ts\n\nvar createRangeRef = (editor, range, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.rangeRef(editor, range, options);\n\n// src/interfaces/editor/deleteBackward.ts\n\nvar deleteBackward = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.deleteBackward(editor, options);\n\n// src/interfaces/editor/deleteForward.ts\n\nvar deleteForward = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.deleteForward(editor, options);\n\n// src/interfaces/editor/deleteFragment.ts\n\nvar deleteFragment = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.deleteFragment(editor, options);\n\n// src/interfaces/editor/deleteMerge.ts\n\n\n// src/interfaces/transforms/mergeNodes.ts\n\n\n// src/interfaces/editor/getAboveNode.ts\n\n\n// src/interfaces/editor/isBlock.ts\n\n\n// src/interfaces/element/elementMatches.ts\n\nvar elementMatches = (element, props) => slate__WEBPACK_IMPORTED_MODULE_0__.Element.matches(element, props);\n\n// src/interfaces/element/isElement.ts\n\nvar isElement = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Element.isElement(value);\n\n// src/interfaces/element/isElementList.ts\n\nvar isElementList = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Element.isElementList(value);\n\n// src/interfaces/editor/isBlock.ts\nvar isBlock = (editor, value) => isElement(value) && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isBlock(editor, value);\n\n// src/utils/match.ts\nfunction castArray(value) {\n  return Array.isArray(value) ? value : [value];\n}\nvar match = (obj, path, predicate) => {\n  if (!predicate) return true;\n  if (typeof predicate === \"object\") {\n    return Object.entries(predicate).every(([key, value]) => {\n      const values = castArray(value);\n      return values.includes(obj[key]);\n    });\n  }\n  return predicate(obj, path);\n};\nvar getQueryOptions = (editor, options = {}) => {\n  const { block, match: _match } = options;\n  return __spreadProps(__spreadValues({}, options), {\n    match: _match || block ? (n, path) => match(n, path, _match) && (!block || isBlock(editor, n)) : void 0\n  });\n};\n\n// src/interfaces/editor/getAboveNode.ts\nvar getAboveNode = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.above(editor, getQueryOptions(editor, options));\n\n// src/interfaces/editor/getNodeEntries.ts\n\n\n// src/interfaces/editor/unhangRange.ts\n\nvar unhangRange = (editor, range, options = {}) => {\n  const { unhang = true, voids } = options;\n  if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(range) && unhang) {\n    return slate__WEBPACK_IMPORTED_MODULE_0__.Editor.unhangRange(editor, range, { voids });\n  }\n  return range;\n};\n\n// src/interfaces/editor/getNodeEntries.ts\nvar getNodeEntries = (editor, options) => {\n  unhangRange(editor, options == null ? void 0 : options.at, options);\n  return slate__WEBPACK_IMPORTED_MODULE_0__.Editor.nodes(editor, getQueryOptions(editor, options));\n};\n\n// src/interfaces/editor/getParentNode.ts\n\nvar getParentNode = (editor, at, options) => {\n  try {\n    return slate__WEBPACK_IMPORTED_MODULE_0__.Editor.parent(editor, at, options);\n  } catch (error) {\n  }\n};\n\n// src/interfaces/editor/getPreviousNode.ts\n\nvar getPreviousNode = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.previous(editor, options);\n\n// src/interfaces/editor/isElementEmpty.ts\n\nvar isElementEmpty = (editor, element) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isEmpty(editor, element);\n\n// src/interfaces/editor/withoutNormalizing.ts\n\nvar withoutNormalizing = (editor, fn) => {\n  let normalized = false;\n  slate__WEBPACK_IMPORTED_MODULE_0__.Editor.withoutNormalizing(editor, () => {\n    normalized = !!fn();\n  });\n  return normalized;\n};\n\n// src/interfaces/text/isText.ts\n\nvar isText = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Text.isText(value);\n\n// src/interfaces/node/hasSingleChild.ts\nvar hasSingleChild = (node) => {\n  if (isText(node)) {\n    return true;\n  }\n  return node.children.length === 1 && hasSingleChild(node.children[0]);\n};\n\n// src/interfaces/transforms/deleteText.ts\n\nvar deleteText = (editor, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.delete(editor, options);\n};\n\n// src/interfaces/transforms/moveNodes.ts\n\nvar moveNodes = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.moveNodes(editor, options);\n\n// src/interfaces/transforms/removeNodes.ts\n\nvar removeNodes = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.removeNodes(editor, options);\n\n// src/interfaces/transforms/select.ts\n\nvar select = (editor, target) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.select(editor, target);\n};\n\n// src/interfaces/transforms/mergeNodes.ts\nvar mergeNodes = (editor, options = {}) => {\n  withoutNormalizing(editor, () => {\n    let { at = editor.selection, match: match2 } = options;\n    const {\n      hanging = false,\n      mergeNode,\n      mode = \"lowest\",\n      removeEmptyAncestor,\n      voids = false\n    } = options;\n    if (!at) {\n      return;\n    }\n    if (match2 == null) {\n      if (slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at)) {\n        const [parent] = getParentNode(editor, at);\n        match2 = (n) => parent.children.includes(n);\n      } else {\n        match2 = (n) => isBlock(editor, n);\n      }\n    }\n    if (!hanging && slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(at)) {\n      at = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.unhangRange(editor, at);\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(at)) {\n      if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isCollapsed(at)) {\n        at = at.anchor;\n      } else {\n        const [, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n        const pointRef = createPointRef(editor, end);\n        deleteText(editor, { at });\n        at = pointRef.unref();\n        if (options.at == null) {\n          select(editor, at);\n        }\n      }\n    }\n    const _nodes = getNodeEntries(editor, { at, match: match2, mode, voids });\n    const [current] = Array.from(_nodes);\n    const prev = getPreviousNode(editor, { at, match: match2, mode, voids });\n    if (!current || !prev) {\n      return;\n    }\n    const [node, path] = current;\n    const [prevNode, prevPath] = prev;\n    if (path.length === 0 || prevPath.length === 0) {\n      return;\n    }\n    const newPath = slate__WEBPACK_IMPORTED_MODULE_0__.Path.next(prevPath);\n    const commonPath = slate__WEBPACK_IMPORTED_MODULE_0__.Path.common(path, prevPath);\n    const isPreviousSibling = slate__WEBPACK_IMPORTED_MODULE_0__.Path.isSibling(path, prevPath);\n    const _levels = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.levels(editor, { at: path });\n    const levels = new Set(\n      Array.from(_levels, ([n]) => n).slice(commonPath.length).slice(0, -1)\n    );\n    const emptyAncestor = getAboveNode(editor, {\n      at: path,\n      match: (n) => levels.has(n) && isElement(n) && hasSingleChild(n),\n      mode: \"highest\"\n    });\n    const emptyRef = emptyAncestor && createPathRef(editor, emptyAncestor[1]);\n    let properties;\n    let position;\n    if (isText(node) && isText(prevNode)) {\n      const _a = node, { text } = _a, rest = __objRest(_a, [\"text\"]);\n      position = prevNode.text.length;\n      properties = rest;\n    } else if (isElement(node) && isElement(prevNode)) {\n      const _b = node, { children } = _b, rest = __objRest(_b, [\"children\"]);\n      position = prevNode.children.length;\n      properties = rest;\n    } else {\n      throw new Error(\n        `Cannot merge the node at path [${path}] with the previous sibling because it is not the same kind: ${JSON.stringify(\n          node\n        )} ${JSON.stringify(prevNode)}`\n      );\n    }\n    if (!isPreviousSibling && // DIFF\n    !mergeNode) {\n      moveNodes(editor, { at: path, to: newPath, voids });\n    }\n    if (emptyRef) {\n      if (removeEmptyAncestor) {\n        const emptyPath = emptyRef.current;\n        emptyPath && removeEmptyAncestor(editor, { at: emptyPath });\n      } else {\n        removeNodes(editor, { at: emptyRef.current, voids });\n      }\n    }\n    if (mergeNode) {\n      mergeNode(editor, { at: path, to: newPath });\n    } else if (isElement(prevNode) && isElementEmpty(editor, prevNode) || isText(prevNode) && prevNode.text === \"\") {\n      removeNodes(editor, { at: prevPath, voids });\n    } else {\n      editor.apply({\n        path: newPath,\n        position,\n        properties,\n        type: \"merge_node\"\n      });\n    }\n    if (emptyRef) {\n      emptyRef.unref();\n    }\n  });\n};\n\n// src/interfaces/editor/getEndPoint.ts\n\nvar getEndPoint = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.end(editor, at);\n\n// src/interfaces/editor/getLeafNode.ts\n\nvar getLeafNode = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.leaf(editor, at, options);\n\n// src/interfaces/editor/getPointAfter.ts\n\nvar getPointAfter = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.after(editor, at, options);\n\n// src/interfaces/editor/getPointBefore.ts\n\nvar getPointBefore = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.before(editor, at, options);\n\n// src/interfaces/editor/getStartPoint.ts\n\nvar getStartPoint = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.start(editor, at);\n\n// src/interfaces/editor/getVoidNode.ts\n\nvar getVoidNode = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.void(editor, options);\n\n// src/interfaces/editor/isVoid.ts\n\nvar isVoid = (editor, value) => {\n  return isElement(value) && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isVoid(editor, value);\n};\n\n// src/interfaces/editor/deleteMerge.ts\nvar deleteMerge = (editor, options = {}) => {\n  withoutNormalizing(editor, () => {\n    const {\n      distance = 1,\n      reverse = false,\n      unit = \"character\",\n      voids = false\n    } = options;\n    let { at = editor.selection, hanging = false } = options;\n    if (!at) {\n      return;\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(at) && slate__WEBPACK_IMPORTED_MODULE_0__.Range.isCollapsed(at)) {\n      at = at.anchor;\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Point.isPoint(at)) {\n      const furthestVoid = getVoidNode(editor, { at, mode: \"highest\" });\n      if (!voids && furthestVoid) {\n        const [, voidPath] = furthestVoid;\n        at = voidPath;\n      } else {\n        const opts = { distance, unit };\n        const target = reverse ? getPointBefore(editor, at, opts) || getStartPoint(editor, []) : getPointAfter(editor, at, opts) || getEndPoint(editor, []);\n        at = { anchor: at, focus: target };\n        hanging = true;\n      }\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at)) {\n      removeNodes(editor, { at, voids });\n      return;\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isCollapsed(at)) {\n      return;\n    }\n    if (!hanging) {\n      at = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.unhangRange(editor, at, { voids });\n    }\n    let [start, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n    const startBlock = getAboveNode(editor, {\n      at: start,\n      match: (n) => isBlock(editor, n),\n      voids\n    });\n    const endBlock = getAboveNode(editor, {\n      at: end,\n      match: (n) => isBlock(editor, n),\n      voids\n    });\n    const isAcrossBlocks = startBlock && endBlock && !slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(startBlock[1], endBlock[1]);\n    const isSingleText = slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(start.path, end.path);\n    const startVoid = voids ? null : getVoidNode(editor, { at: start, mode: \"highest\" });\n    const endVoid = voids ? null : getVoidNode(editor, { at: end, mode: \"highest\" });\n    if (startVoid) {\n      const before = getPointBefore(editor, start);\n      if (before && startBlock && slate__WEBPACK_IMPORTED_MODULE_0__.Path.isAncestor(startBlock[1], before.path)) {\n        start = before;\n      }\n    }\n    if (endVoid) {\n      const after = getPointAfter(editor, end);\n      if (after && endBlock && slate__WEBPACK_IMPORTED_MODULE_0__.Path.isAncestor(endBlock[1], after.path)) {\n        end = after;\n      }\n    }\n    const matches = [];\n    let lastPath;\n    const _nodes = getNodeEntries(editor, { at, voids });\n    for (const entry of _nodes) {\n      const [node, path] = entry;\n      if (lastPath && slate__WEBPACK_IMPORTED_MODULE_0__.Path.compare(path, lastPath) === 0) {\n        continue;\n      }\n      if (!voids && isVoid(editor, node) || !slate__WEBPACK_IMPORTED_MODULE_0__.Path.isCommon(path, start.path) && !slate__WEBPACK_IMPORTED_MODULE_0__.Path.isCommon(path, end.path)) {\n        matches.push(entry);\n        lastPath = path;\n      }\n    }\n    const pathRefs = Array.from(\n      matches,\n      ([, p]) => createPathRef(editor, p)\n    );\n    const startRef = createPointRef(editor, start);\n    const endRef = createPointRef(editor, end);\n    if (!isSingleText && !startVoid) {\n      const point2 = startRef.current;\n      const [node] = getLeafNode(editor, point2);\n      const { path } = point2;\n      const { offset } = start;\n      const text = node.text.slice(offset);\n      editor.apply({ offset, path, text, type: \"remove_text\" });\n    }\n    for (const pathRef of pathRefs) {\n      const path = pathRef.unref();\n      removeNodes(editor, { at: path, voids });\n    }\n    if (!endVoid) {\n      const point2 = endRef.current;\n      const [node] = getLeafNode(editor, point2);\n      const { path } = point2;\n      const offset = isSingleText ? start.offset : 0;\n      const text = node.text.slice(offset, end.offset);\n      editor.apply({ offset, path, text, type: \"remove_text\" });\n    }\n    if (!isSingleText && isAcrossBlocks && endRef.current && startRef.current) {\n      mergeNodes(editor, {\n        at: endRef.current,\n        hanging: true,\n        voids\n      });\n    }\n    const point = endRef.unref() || startRef.unref();\n    if (options.at == null && point) {\n      select(editor, point);\n    }\n  });\n};\n\n// src/interfaces/editor/getEdgePoints.ts\n\nvar getEdgePoints = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.edges(editor, at);\n\n// src/interfaces/editor/getEditorString.ts\n\nvar getEditorString = (editor, at, options) => {\n  if (!at) return \"\";\n  try {\n    return slate__WEBPACK_IMPORTED_MODULE_0__.Editor.string(editor, at, options);\n  } catch (e) {\n    return \"\";\n  }\n};\n\n// src/interfaces/editor/getFirstNode.ts\n\nvar getFirstNode = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.first(editor, at);\n\n// src/interfaces/editor/getFragment.ts\n\nvar getFragment = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.fragment(editor, at);\n\n// src/interfaces/editor/getLastNode.ts\n\nvar getLastNode = (editor, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.last(editor, at);\n\n// src/interfaces/editor/getLevels.ts\n\nvar getLevels = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.levels(editor, options);\n\n// src/interfaces/editor/getMarks.ts\n\nvar getMarks = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.marks(editor);\n\n// src/interfaces/editor/getNextNode.ts\n\nvar getNextNode = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.next(editor, options);\n\n// src/interfaces/editor/getNodeEntry.ts\n\nvar getNodeEntry = (editor, at, options) => {\n  try {\n    return slate__WEBPACK_IMPORTED_MODULE_0__.Editor.node(editor, at, options);\n  } catch (error) {\n  }\n};\n\n// src/interfaces/editor/getPath.ts\n\nvar getPath = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.path(editor, at, options);\n\n// src/interfaces/editor/getPathRefs.ts\n\nvar getPathRefs = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.pathRefs(editor);\n\n// src/interfaces/editor/getPoint.ts\n\nvar getPoint = (editor, at, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.point(editor, at, options);\n\n// src/interfaces/editor/getPointRefs.ts\n\nvar getPointRefs = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.pointRefs(editor);\n\n// src/interfaces/editor/getPositions.ts\n\nvar getPositions = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.positions(editor, options);\n\n// src/interfaces/editor/getRange.ts\n\nvar getRange = (editor, at, to) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.range(editor, at, to);\n\n// src/interfaces/editor/getRangeRefs.ts\n\nvar getRangeRefs = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.rangeRefs(editor);\n\n// src/interfaces/editor/hasBlocks.ts\n\nvar hasBlocks = (editor, element) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.hasBlocks(editor, element);\n\n// src/interfaces/editor/hasInlines.ts\n\nvar hasInlines = (editor, element) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.hasInlines(editor, element);\n\n// src/interfaces/editor/hasTexts.ts\n\nvar hasTexts = (editor, element) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.hasTexts(editor, element);\n\n// src/interfaces/editor/insertBreak.ts\n\nvar insertBreak = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.insertBreak(editor);\n\n// src/interfaces/editor/insertNode.ts\n\nvar insertNode = (editor, node) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.insertNode(editor, node);\n\n// src/interfaces/editor/isEdgePoint.ts\n\nvar isEdgePoint = (editor, point, at) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isEdge(editor, point, at);\n\n// src/interfaces/editor/isEditor.ts\n\nvar isEditor = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isEditor(value);\n\n// src/interfaces/editor/isEditorNormalizing.ts\n\nvar isEditorNormalizing = (editor) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isNormalizing(editor);\n\n// src/interfaces/editor/isEndPoint.ts\n\nvar isEndPoint = (editor, point, at) => !!point && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isEnd(editor, point, at);\n\n// src/interfaces/editor/isInline.ts\n\nvar isInline = (editor, value) => isElement(value) && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isInline(editor, value);\n\n// src/interfaces/editor/isMarkableVoid.ts\nvar isMarkableVoid = (editor, value) => {\n  return isElement(value) && editor.markableVoid(value);\n};\n\n// src/interfaces/editor/isStartPoint.ts\n\nvar isStartPoint = (editor, point, at) => !!point && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isStart(editor, point, at);\n\n// src/interfaces/editor/normalizeEditor.ts\n\nvar normalizeEditor = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.normalize(editor, options);\n\n// src/interfaces/editor/removeEditorMark.ts\n\nvar removeEditorMark = (editor, key) => slate__WEBPACK_IMPORTED_MODULE_0__.Editor.removeMark(editor, key);\n\n// src/slate-history/history-editor.ts\n\n\n// src/slate-history/history.ts\n\n\nvar History = {\n  /** Check if a value is a `History` object. */\n  isHistory(value) {\n    return (0,is_plain_object__WEBPACK_IMPORTED_MODULE_1__.isPlainObject)(value) && Array.isArray(value.redos) && Array.isArray(value.undos) && (value.redos.length === 0 || slate__WEBPACK_IMPORTED_MODULE_0__.Operation.isOperationList(value.redos[0].operations)) && (value.undos.length === 0 || slate__WEBPACK_IMPORTED_MODULE_0__.Operation.isOperationList(value.undos[0].operations));\n  }\n};\n\n// src/slate-history/history-editor.ts\nvar HISTORY = /* @__PURE__ */ new WeakMap();\nvar SAVING = /* @__PURE__ */ new WeakMap();\nvar MERGING = /* @__PURE__ */ new WeakMap();\nvar SPLITTING_ONCE = /* @__PURE__ */ new WeakMap();\nvar HistoryEditor = {\n  /** Check if a value is a `HistoryEditor` object. */\n  isHistoryEditor(value) {\n    return History.isHistory(value.history) && slate__WEBPACK_IMPORTED_MODULE_0__.Editor.isEditor(value);\n  },\n  /** Get the merge flag's current value. */\n  isMerging(editor) {\n    return MERGING.get(editor);\n  },\n  /** Get the splitting once flag's current value. */\n  isSaving(editor) {\n    return SAVING.get(editor);\n  },\n  isSplittingOnce(editor) {\n    return SPLITTING_ONCE.get(editor);\n  },\n  /** Get the saving flag's current value. */\n  redo(editor) {\n    editor.redo();\n  },\n  /** Redo to the previous saved state. */\n  setSplittingOnce(editor, value) {\n    SPLITTING_ONCE.set(editor, value);\n  },\n  /** Undo to the previous saved state. */\n  undo(editor) {\n    editor.undo();\n  },\n  /**\n   * Apply a series of changes inside a synchronous `fn`, These operations will\n   * be merged into the previous history.\n   */\n  withMerging(editor, fn) {\n    const prev = HistoryEditor.isMerging(editor);\n    MERGING.set(editor, true);\n    fn();\n    MERGING.set(editor, prev);\n  },\n  /**\n   * Apply a series of changes inside a synchronous `fn`, ensuring that the\n   * first operation starts a new batch in the history. Subsequent operations\n   * will be merged as usual.\n   */\n  withNewBatch(editor, fn) {\n    const prev = HistoryEditor.isMerging(editor);\n    MERGING.set(editor, true);\n    SPLITTING_ONCE.set(editor, true);\n    fn();\n    MERGING.set(editor, prev);\n    SPLITTING_ONCE.delete(editor);\n  },\n  /**\n   * Apply a series of changes inside a synchronous `fn`, without merging any of\n   * the new operations into previous save point in the history.\n   */\n  withoutMerging(editor, fn) {\n    const prev = HistoryEditor.isMerging(editor);\n    MERGING.set(editor, false);\n    fn();\n    MERGING.set(editor, prev);\n  },\n  /**\n   * Apply a series of changes inside a synchronous `fn`, without saving any of\n   * their operations into the history.\n   */\n  withoutSaving(editor, fn) {\n    const prev = HistoryEditor.isSaving(editor);\n    SAVING.set(editor, false);\n    fn();\n    SAVING.set(editor, prev);\n  }\n};\n\n// src/slate-history/with-history.ts\n\nvar withHistory = (editor) => {\n  const e = editor;\n  const { apply } = e;\n  e.history = { redos: [], undos: [] };\n  e.redo = () => {\n    const { history } = e;\n    const { redos } = history;\n    if (redos.length > 0) {\n      const batch = redos.at(-1);\n      if (batch.selectionBefore) {\n        slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setSelection(e, batch.selectionBefore);\n      }\n      HistoryEditor.withoutSaving(e, () => {\n        slate__WEBPACK_IMPORTED_MODULE_0__.Editor.withoutNormalizing(e, () => {\n          for (const op of batch.operations) {\n            e.apply(op);\n          }\n        });\n      });\n      history.redos.pop();\n      e.writeHistory(\"undos\", batch);\n    }\n  };\n  e.undo = () => {\n    const { history } = e;\n    const { undos } = history;\n    if (undos.length > 0) {\n      const batch = undos.at(-1);\n      HistoryEditor.withoutSaving(e, () => {\n        slate__WEBPACK_IMPORTED_MODULE_0__.Editor.withoutNormalizing(e, () => {\n          const inverseOps = batch.operations.map(slate__WEBPACK_IMPORTED_MODULE_0__.Operation.inverse).reverse();\n          for (const op of inverseOps) {\n            e.apply(op);\n          }\n          if (batch.selectionBefore) {\n            slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setSelection(e, batch.selectionBefore);\n          }\n        });\n      });\n      e.writeHistory(\"redos\", batch);\n      history.undos.pop();\n    }\n  };\n  e.apply = (op) => {\n    const { history, operations } = e;\n    const { undos } = history;\n    const lastBatch = undos.at(-1);\n    const lastOp = lastBatch == null ? void 0 : lastBatch.operations.at(-1);\n    let save = HistoryEditor.isSaving(e);\n    let merge = HistoryEditor.isMerging(e);\n    if (save == null) {\n      save = shouldSave(op, lastOp);\n    }\n    if (save) {\n      if (merge == null) {\n        if (lastBatch == null) {\n          merge = false;\n        } else if (operations.length > 0) {\n          merge = true;\n        } else {\n          merge = shouldMerge(op, lastOp);\n        }\n      }\n      if (HistoryEditor.isSplittingOnce(e)) {\n        merge = false;\n        HistoryEditor.setSplittingOnce(e, void 0);\n      }\n      if (lastBatch && merge) {\n        lastBatch.operations.push(op);\n      } else {\n        const batch = {\n          operations: [op],\n          selectionBefore: e.selection\n        };\n        e.writeHistory(\"undos\", batch);\n      }\n      while (undos.length > 100) {\n        undos.shift();\n      }\n      history.redos = [];\n    }\n    apply(op);\n  };\n  e.writeHistory = (stack, batch) => {\n    e.history[stack].push(batch);\n  };\n  return e;\n};\nvar shouldMerge = (op, prev) => {\n  if (prev && op.type === \"insert_text\" && prev.type === \"insert_text\" && op.offset === prev.offset + prev.text.length && slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(op.path, prev.path)) {\n    return true;\n  }\n  if (prev && op.type === \"remove_text\" && prev.type === \"remove_text\" && op.offset + op.text.length === prev.offset && slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(op.path, prev.path)) {\n    return true;\n  }\n  return false;\n};\nvar shouldSave = (op, _) => {\n  if (op.type === \"set_selection\") {\n    return false;\n  }\n  return true;\n};\n\n// src/interfaces/history-editor/isHistoryEditor.ts\nvar isHistoryEditor = (value) => HistoryEditor.isHistoryEditor(value);\n\n// src/interfaces/history-editor/isHistoryMerging.ts\nvar isHistoryMerging = (editor) => HistoryEditor.isMerging(editor);\n\n// src/interfaces/history-editor/isHistorySaving.ts\nvar isHistorySaving = (editor) => HistoryEditor.isSaving(editor);\n\n// src/interfaces/history-editor/withMerging.ts\nvar withMerging = (editor, fn) => HistoryEditor.withMerging(editor, fn);\n\n// src/interfaces/history-editor/withNewBatch.ts\nvar withNewBatch = (editor, fn) => HistoryEditor.withNewBatch(editor, fn);\n\n// src/interfaces/history-editor/withoutMergingHistory.ts\nvar withoutMergingHistory = (editor, fn) => HistoryEditor.withoutMerging(editor, fn);\n\n// src/interfaces/history-editor/withoutSavingHistory.ts\nvar withoutSavingHistory = (editor, fn) => HistoryEditor.withoutSaving(editor, fn);\n\n// src/interfaces/node/TDescendant.ts\nvar isDescendant = (node) => isElement(node) || isText(node);\n\n// src/interfaces/node/getCommonNode.ts\n\nvar getCommonNode = (root, path, another) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.common(root, path, another);\n\n// src/interfaces/text/isTextList.ts\n\nvar isTextList = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Text.isTextList(value);\n\n// src/interfaces/text/textEquals.ts\n\nvar textEquals = (text, another) => slate__WEBPACK_IMPORTED_MODULE_0__.Text.equals(text, another);\n\n// src/interfaces/text/textMatches.ts\n\nvar textMatches = (text, props) => slate__WEBPACK_IMPORTED_MODULE_0__.Text.matches(text, props);\n\n// src/interfaces/node/getNode.ts\nvar getNode = (root, path) => {\n  try {\n    for (let i = 0; i < path.length; i++) {\n      const p = path[i];\n      if (isText(root) || !root.children[p]) {\n        return null;\n      }\n      root = root.children[p];\n    }\n    return root;\n  } catch (error) {\n    return null;\n  }\n};\n\n// src/interfaces/node/getNodeAncestor.ts\n\nvar getNodeAncestor = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.ancestor(root, path);\n\n// src/interfaces/node/getNodeAncestors.ts\n\nvar getNodeAncestors = (root, path, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.ancestors(root, path, options);\n\n// src/interfaces/node/getNodeChild.ts\n\nvar getNodeChild = (root, index) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.child(root, index);\n\n// src/interfaces/node/getNodeChildren.ts\n\nvar getNodeChildren = (root, path, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.children(root, path, options);\n\n// src/interfaces/node/getNodeDescendant.ts\n\nvar getNodeDescendant = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.descendant(root, path);\n\n// src/interfaces/node/getNodeDescendants.ts\n\nvar getNodeDescendants = (root, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.descendants(root, options);\n\n// src/interfaces/node/getNodeElements.ts\n\nvar getNodeElements = (root, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.elements(root, options);\n\n// src/interfaces/node/getNodeFirstNode.ts\n\nvar getNodeFirstNode = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.first(root, path);\n\n// src/interfaces/node/getNodeFragment.ts\n\nvar getNodeFragment = (root, range) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.fragment(root, range);\n\n// src/interfaces/node/getNodeLastNode.ts\n\nvar getNodeLastNode = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.last(root, path);\n\n// src/interfaces/node/getNodeLeaf.ts\n\nvar getNodeLeaf = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.leaf(root, path);\n\n// src/interfaces/node/getNodeLevels.ts\n\nvar getNodeLevels = (root, path, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.levels(root, path, options);\n\n// src/interfaces/node/getNodeParent.ts\n\nvar getNodeParent = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.parent(root, path);\n\n// src/interfaces/node/getNodeProps.ts\n\nvar getNodeProps = (node) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.extractProps(node);\n\n// src/interfaces/node/getNodeString.ts\n\nvar getNodeString = (node) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.string(node);\n\n// src/interfaces/node/getNodeTexts.ts\n\nvar getNodeTexts = (root, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.texts(root, options);\n\n// src/interfaces/node/getNodes.ts\n\nvar getNodes = (root, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.nodes(root, options);\n\n// src/interfaces/node/hasNode.ts\n\nvar hasNode = (root, path) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.has(root, path);\n\n// src/interfaces/node/isAncestor.ts\n\nvar isAncestor = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Element.isAncestor(value);\n\n// src/interfaces/node/isNode.ts\n\nvar isNode = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.isNode(value);\n\n// src/interfaces/node/isNodeList.ts\n\nvar isNodeList = (value) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.isNodeList(value);\n\n// src/interfaces/node/nodeMatches.ts\n\nvar nodeMatches = (node, props) => slate__WEBPACK_IMPORTED_MODULE_0__.Node.matches(node, props);\n\n// src/interfaces/range/isCollapsed.ts\n\nvar isCollapsed = (range) => !!range && slate__WEBPACK_IMPORTED_MODULE_0__.Range.isCollapsed(range);\n\n// src/interfaces/range/isExpanded.ts\n\nvar isExpanded = (range) => !!range && slate__WEBPACK_IMPORTED_MODULE_0__.Range.isExpanded(range);\n\n// src/interfaces/transforms/collapseSelection.ts\n\nvar collapseSelection = (editor, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.collapse(editor, options);\n};\n\n// src/interfaces/transforms/deselect.ts\n\nvar deselect = (editor) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.deselect(editor);\n};\n\n// src/interfaces/transforms/insertFragment.ts\n\nvar insertFragment = (editor, fragment, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.insertFragment(editor, fragment, options);\n};\n\n// src/interfaces/transforms/insertNodes.ts\n\n\n// src/utils/queryNode.ts\nfunction castArray2(value) {\n  return Array.isArray(value) ? value : [value];\n}\nvar queryNode = (entry, { allow, exclude, filter, level, maxLevel } = {}) => {\n  if (!entry) return false;\n  const [node, path] = entry;\n  if (level) {\n    const levels = castArray2(level);\n    if (!levels.includes(path.length)) {\n      return false;\n    }\n  }\n  if (maxLevel && path.length > maxLevel) {\n    return false;\n  }\n  if (filter && !filter(entry)) {\n    return false;\n  }\n  if (allow) {\n    const allows = castArray2(allow);\n    if (allows.length > 0 && !allows.includes(node.type)) {\n      return false;\n    }\n  }\n  if (exclude) {\n    const excludes = castArray2(exclude);\n    if (excludes.length > 0 && excludes.includes(node.type)) {\n      return false;\n    }\n  }\n  return true;\n};\n\n// src/interfaces/transforms/insertNodes.ts\nvar insertNodes = (editor, nodes, _a = {}) => {\n  var _b = _a, { nextBlock, removeEmpty } = _b, options = __objRest(_b, [\"nextBlock\", \"removeEmpty\"]);\n  (0,slate__WEBPACK_IMPORTED_MODULE_0__.withoutNormalizing)(editor, () => {\n    if (removeEmpty) {\n      const blockEntry = getAboveNode(editor, { at: options.at });\n      if (blockEntry) {\n        const queryNodeOptions = removeEmpty === true ? {\n          allow: [\"p\"]\n        } : removeEmpty;\n        const { filter } = queryNodeOptions;\n        queryNodeOptions.filter = ([node, path]) => {\n          if (getNodeString(node)) return false;\n          const children = node.children;\n          if (children.some((n) => isInline(editor, n))) return false;\n          return !filter || filter([node, path]);\n        };\n        if (queryNode(blockEntry, queryNodeOptions)) {\n          (0,slate__WEBPACK_IMPORTED_MODULE_0__.removeNodes)(editor, { at: blockEntry[1] });\n          nextBlock = false;\n        }\n      }\n    }\n    if (nextBlock) {\n      const { at = editor.selection } = options;\n      if (at) {\n        const endPoint = getEndPoint(editor, at);\n        const blockEntry = getAboveNode(editor, {\n          at: endPoint,\n          block: true\n        });\n        if (blockEntry) {\n          options.at = slate__WEBPACK_IMPORTED_MODULE_0__.Path.next(blockEntry[1]);\n        }\n      }\n    }\n    slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.insertNodes(editor, nodes, options);\n  });\n};\n\n// src/interfaces/transforms/insertText.ts\n\nvar insertText = (editor, text, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.insertText(editor, text, options);\n};\n\n// src/interfaces/transforms/liftNodes.ts\n\nvar liftNodes = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.liftNodes(editor, options);\n\n// src/interfaces/transforms/moveSelection.ts\n\nvar moveSelection = (editor, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.move(editor, options);\n};\n\n// src/interfaces/transforms/setNodes.ts\n\nvar setNodes = (editor, props, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setNodes(editor, props, options);\n\n// src/interfaces/transforms/setPoint.ts\n\nvar setPoint = (editor, props, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setPoint(editor, props, options);\n};\n\n// src/interfaces/transforms/setSelection.ts\n\nvar setSelection = (editor, props) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setSelection(editor, props);\n};\n\n// src/interfaces/transforms/splitNodes.ts\n\nvar splitNodes = (editor, options) => slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.splitNodes(editor, options);\n\n// src/interfaces/transforms/unsetNodes.ts\n\nvar unsetNodes = (editor, props, options) => {\n  return slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.unsetNodes(editor, props, options);\n};\n\n// src/interfaces/transforms/unwrapNodes.ts\n\nvar unwrapNodes = (editor, options) => {\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.unwrapNodes(editor, getQueryOptions(editor, options));\n};\n\n// src/interfaces/transforms/wrapNodes.ts\n\nvar wrapNodes = (editor, element, options) => {\n  unhangRange(editor, options == null ? void 0 : options.at, options);\n  slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.wrapNodes(editor, element, options);\n};\n\n// src/queries/findNode.ts\nvar findNode = (editor, options = {}) => {\n  try {\n    const nodeEntries = getNodeEntries(editor, __spreadValues({\n      at: editor.selection || []\n    }, getQueryOptions(editor, options)));\n    for (const [node, path] of nodeEntries) {\n      return [node, path];\n    }\n  } catch (error) {\n    return void 0;\n  }\n};\n\n// src/queries/findNodePath.ts\nvar findNodePath = (editor, node, options = {}) => {\n  const { match: match2 } = getQueryOptions(editor, options);\n  const nodeEntry = findNode(editor, __spreadValues({\n    at: [],\n    match: (n) => n === node && (!match2 || match2(n))\n  }, options));\n  return nodeEntry == null ? void 0 : nodeEntry[1];\n};\n\n// src/queries/someNode.ts\nvar someNode = (editor, options) => {\n  return !!findNode(editor, options);\n};\n\n// src/transforms/addRangeMarks.ts\n\nvar addRangeMarks = (editor, props, {\n  at = editor.selection\n} = {}) => {\n  if (at) {\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at)) {\n      at = getRange(editor, at);\n    }\n    const match2 = (node, path) => {\n      if (!slate__WEBPACK_IMPORTED_MODULE_0__.Text.isText(node)) {\n        return false;\n      }\n      const parentEntry = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.parent(editor, path);\n      if (!parentEntry) return false;\n      const [parentNode] = parentEntry;\n      return !editor.isVoid(parentNode) || editor.markableVoid(parentNode);\n    };\n    const isExpandedRange = slate__WEBPACK_IMPORTED_MODULE_0__.Range.isExpanded(at);\n    let markAcceptingVoidSelected = false;\n    if (!isExpandedRange) {\n      const selectedEntry = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.node(editor, at);\n      if (!selectedEntry) return;\n      const [selectedNode, selectedPath] = selectedEntry;\n      if (selectedNode && match2(selectedNode, selectedPath)) {\n        const parentEntry = slate__WEBPACK_IMPORTED_MODULE_0__.Editor.parent(editor, selectedPath);\n        if (!parentEntry) return;\n        const [parentNode] = parentEntry;\n        markAcceptingVoidSelected = parentNode && editor.markableVoid(parentNode);\n      }\n    }\n    if (isExpandedRange || markAcceptingVoidSelected) {\n      slate__WEBPACK_IMPORTED_MODULE_0__.Transforms.setNodes(editor, props, {\n        at,\n        match: match2,\n        split: true,\n        voids: true\n      });\n    }\n  }\n};\n\n// src/transforms/setElements.ts\nvar setElements = (editor, props, options) => setNodes(editor, props, options);\n\n// src/transforms/setNode.ts\nvar setNode = (editor, node, props, options) => {\n  const path = findNodePath(editor, node);\n  if (!path) return;\n  editor.setNodes(props, __spreadProps(__spreadValues({}, options), {\n    at: path\n  }));\n};\n\n// src/transforms/unhangCharacterRange.ts\n\nvar unhangCharacterRange = (editor, at) => {\n  let [start, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n  if (!slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(start.path, end.path)) {\n    if (end.offset === 0) {\n      const pointAfter = getPointAfter(editor, start);\n      if (pointAfter) {\n        end = pointAfter;\n      }\n    } else {\n      const pointBefore = getPointBefore(editor, end);\n      if (pointBefore) {\n        start = pointBefore;\n      }\n    }\n  }\n  return { anchor: start, focus: end };\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs\n");

/***/ })

};
;