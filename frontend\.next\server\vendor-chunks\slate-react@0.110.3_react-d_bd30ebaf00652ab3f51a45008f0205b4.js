"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4";
exports.ids = ["vendor-chunks/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4/node_modules/slate-react/dist/index.es.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4/node_modules/slate-react/dist/index.es.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultElement: () => (/* binding */ DefaultElement),\n/* harmony export */   DefaultLeaf: () => (/* binding */ DefaultLeaf),\n/* harmony export */   DefaultPlaceholder: () => (/* binding */ DefaultPlaceholder),\n/* harmony export */   Editable: () => (/* binding */ Editable),\n/* harmony export */   NODE_TO_INDEX: () => (/* binding */ NODE_TO_INDEX),\n/* harmony export */   NODE_TO_PARENT: () => (/* binding */ NODE_TO_PARENT),\n/* harmony export */   ReactEditor: () => (/* binding */ ReactEditor),\n/* harmony export */   Slate: () => (/* binding */ Slate),\n/* harmony export */   useComposing: () => (/* binding */ useComposing),\n/* harmony export */   useEditor: () => (/* binding */ useEditor),\n/* harmony export */   useFocused: () => (/* binding */ useFocused),\n/* harmony export */   useReadOnly: () => (/* binding */ useReadOnly),\n/* harmony export */   useSelected: () => (/* binding */ useSelected),\n/* harmony export */   useSlate: () => (/* binding */ useSlate),\n/* harmony export */   useSlateSelection: () => (/* binding */ useSlateSelection),\n/* harmony export */   useSlateSelector: () => (/* binding */ useSlateSelector),\n/* harmony export */   useSlateStatic: () => (/* binding */ useSlateStatic),\n/* harmony export */   useSlateWithV: () => (/* binding */ useSlateWithV),\n/* harmony export */   withReact: () => (/* binding */ withReact)\n/* harmony export */ });\n/* harmony import */ var direction__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! direction */ \"(ssr)/./node_modules/.pnpm/direction@1.0.4/node_modules/direction/index.js\");\n/* harmony import */ var direction__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(direction__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! lodash/debounce */ \"(ssr)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/debounce.js\");\n/* harmony import */ var lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(lodash_debounce__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/throttle */ \"(ssr)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/throttle.js\");\n/* harmony import */ var lodash_throttle__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(lodash_throttle__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! scroll-into-view-if-needed */ \"(ssr)/./node_modules/.pnpm/scroll-into-view-if-needed@3.1.0/node_modules/scroll-into-view-if-needed/dist/index.js\");\n/* harmony import */ var slate__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! slate */ \"(ssr)/./node_modules/.pnpm/slate@0.103.0/node_modules/slate/dist/index.es.js\");\n/* harmony import */ var _juggle_resize_observer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @juggle/resize-observer */ \"(ssr)/./node_modules/.pnpm/@juggle+resize-observer@3.4.0/node_modules/@juggle/resize-observer/lib/exports/resize-observer.js\");\n/* harmony import */ var is_hotkey__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! is-hotkey */ \"(ssr)/./node_modules/.pnpm/is-hotkey@0.2.0/node_modules/is-hotkey/lib/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(react_dom__WEBPACK_IMPORTED_MODULE_6__);\n\n\n\n\n\n\n\n\n\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\n\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = _objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\n\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n/**\n * A React context for sharing the editor object.\n */\nvar EditorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(null);\n/**\n * Get the current editor object from the React context.\n */\nvar useSlateStatic = () => {\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(EditorContext);\n  if (!editor) {\n    throw new Error(\"The `useSlateStatic` hook must be used inside the <Slate> component's context.\");\n  }\n  return editor;\n};\n\nvar _navigator$userAgent$, _navigator$userAgent$2;\nvar REACT_MAJOR_VERSION = parseInt(react__WEBPACK_IMPORTED_MODULE_3___default().version.split('.')[0], 10);\nvar IS_IOS = typeof navigator !== 'undefined' && typeof window !== 'undefined' && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\nvar IS_APPLE = typeof navigator !== 'undefined' && /Mac OS X/.test(navigator.userAgent);\nvar IS_ANDROID = typeof navigator !== 'undefined' && /Android/.test(navigator.userAgent);\nvar IS_FIREFOX = typeof navigator !== 'undefined' && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nvar IS_WEBKIT = typeof navigator !== 'undefined' && /AppleWebKit(?!.*Chrome)/i.test(navigator.userAgent);\n// \"modern\" Edge was released at 79.x\nvar IS_EDGE_LEGACY = typeof navigator !== 'undefined' && /Edge?\\/(?:[0-6][0-9]|[0-7][0-8])(?:\\.)/i.test(navigator.userAgent);\nvar IS_CHROME = typeof navigator !== 'undefined' && /Chrome/i.test(navigator.userAgent);\n// Native `beforeInput` events don't work well with react on Chrome 75\n// and older, Chrome 76+ can use `beforeInput` though.\nvar IS_CHROME_LEGACY = typeof navigator !== 'undefined' && /Chrome?\\/(?:[0-7][0-5]|[0-6][0-9])(?:\\.)/i.test(navigator.userAgent);\nvar IS_ANDROID_CHROME_LEGACY = IS_ANDROID && typeof navigator !== 'undefined' && /Chrome?\\/(?:[0-5]?\\d)(?:\\.)/i.test(navigator.userAgent);\n// Firefox did not support `beforeInput` until `v87`.\nvar IS_FIREFOX_LEGACY = typeof navigator !== 'undefined' && /^(?!.*Seamonkey)(?=.*Firefox\\/(?:[0-7][0-9]|[0-8][0-6])(?:\\.)).*/i.test(navigator.userAgent);\n// UC mobile browser\nvar IS_UC_MOBILE = typeof navigator !== 'undefined' && /.*UCBrowser/.test(navigator.userAgent);\n// Wechat browser (not including mac wechat)\nvar IS_WECHATBROWSER = typeof navigator !== 'undefined' && /.*Wechat/.test(navigator.userAgent) && !/.*MacWechat/.test(navigator.userAgent); // avoid lookbehind (buggy in safari < 16.4)\n// Check if DOM is available as React does internally.\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nvar CAN_USE_DOM = !!(typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined');\n// Check if the browser is Safari and older than 17\ntypeof navigator !== 'undefined' && /Safari/.test(navigator.userAgent) && /Version\\/(\\d+)/.test(navigator.userAgent) && ((_navigator$userAgent$ = navigator.userAgent.match(/Version\\/(\\d+)/)) !== null && _navigator$userAgent$ !== void 0 && _navigator$userAgent$[1] ? parseInt((_navigator$userAgent$2 = navigator.userAgent.match(/Version\\/(\\d+)/)) === null || _navigator$userAgent$2 === void 0 ? void 0 : _navigator$userAgent$2[1], 10) < 17 : false);\n// COMPAT: Firefox/Edge Legacy don't support the `beforeinput` event\n// Chrome Legacy doesn't support `beforeinput` correctly\nvar HAS_BEFORE_INPUT_SUPPORT = (!IS_CHROME_LEGACY || !IS_ANDROID_CHROME_LEGACY) && !IS_EDGE_LEGACY &&\n// globalThis is undefined in older browsers\ntypeof globalThis !== 'undefined' && globalThis.InputEvent &&\n// @ts-ignore The `getTargetRanges` property isn't recognized.\ntypeof globalThis.InputEvent.prototype.getTargetRanges === 'function';\n\n/**\n * Two weak maps that allow us rebuild a path given a node. They are populated\n * at render time such that after a render occurs we can always backtrack.\n */\nvar IS_NODE_MAP_DIRTY = new WeakMap();\nvar NODE_TO_INDEX = new WeakMap();\nvar NODE_TO_PARENT = new WeakMap();\n/**\n * Weak maps that allow us to go between Slate nodes and DOM nodes. These\n * are used to resolve DOM event-related logic into Slate actions.\n */\nvar EDITOR_TO_WINDOW = new WeakMap();\nvar EDITOR_TO_ELEMENT = new WeakMap();\nvar EDITOR_TO_PLACEHOLDER_ELEMENT = new WeakMap();\nvar ELEMENT_TO_NODE = new WeakMap();\nvar NODE_TO_ELEMENT = new WeakMap();\nvar NODE_TO_KEY = new WeakMap();\nvar EDITOR_TO_KEY_TO_ELEMENT = new WeakMap();\n/**\n * Weak maps for storing editor-related state.\n */\nvar IS_READ_ONLY = new WeakMap();\nvar IS_FOCUSED = new WeakMap();\nvar IS_COMPOSING = new WeakMap();\nvar EDITOR_TO_USER_SELECTION = new WeakMap();\n/**\n * Weak map for associating the context `onChange` context with the plugin.\n */\nvar EDITOR_TO_ON_CHANGE = new WeakMap();\n/**\n * Weak maps for saving pending state on composition stage.\n */\nvar EDITOR_TO_SCHEDULE_FLUSH = new WeakMap();\nvar EDITOR_TO_PENDING_INSERTION_MARKS = new WeakMap();\nvar EDITOR_TO_USER_MARKS = new WeakMap();\n/**\n * Android input handling specific weak-maps\n */\nvar EDITOR_TO_PENDING_DIFFS = new WeakMap();\nvar EDITOR_TO_PENDING_ACTION = new WeakMap();\nvar EDITOR_TO_PENDING_SELECTION = new WeakMap();\nvar EDITOR_TO_FORCE_RENDER = new WeakMap();\n/**\n * Symbols.\n */\nvar PLACEHOLDER_SYMBOL = Symbol('placeholder');\nvar MARK_PLACEHOLDER_SYMBOL = Symbol('mark-placeholder');\n\n/**\n * Types.\n */\n// COMPAT: This is required to prevent TypeScript aliases from doing some very\n// weird things for Slate's types with the same name as globals. (2019/11/27)\n// https://github.com/microsoft/TypeScript/issues/35002\nvar DOMNode = globalThis.Node;\nvar DOMText = globalThis.Text;\n/**\n * Returns the host window of a DOM node\n */\nvar getDefaultView = value => {\n  return value && value.ownerDocument && value.ownerDocument.defaultView || null;\n};\n/**\n * Check if a DOM node is a comment node.\n */\nvar isDOMComment = value => {\n  return isDOMNode(value) && value.nodeType === 8;\n};\n/**\n * Check if a DOM node is an element node.\n */\nvar isDOMElement = value => {\n  return isDOMNode(value) && value.nodeType === 1;\n};\n/**\n * Check if a value is a DOM node.\n */\nvar isDOMNode = value => {\n  var window = getDefaultView(value);\n  return !!window && value instanceof window.Node;\n};\n/**\n * Check if a value is a DOM selection.\n */\nvar isDOMSelection = value => {\n  var window = value && value.anchorNode && getDefaultView(value.anchorNode);\n  return !!window && value instanceof window.Selection;\n};\n/**\n * Check if a DOM node is an element node.\n */\nvar isDOMText = value => {\n  return isDOMNode(value) && value.nodeType === 3;\n};\n/**\n * Checks whether a paste event is a plaintext-only event.\n */\nvar isPlainTextOnlyPaste = event => {\n  return event.clipboardData && event.clipboardData.getData('text/plain') !== '' && event.clipboardData.types.length === 1;\n};\n/**\n * Normalize a DOM point so that it always refers to a text node.\n */\nvar normalizeDOMPoint = domPoint => {\n  var [node, offset] = domPoint;\n  // If it's an element node, its offset refers to the index of its children\n  // including comment nodes, so try to find the right text child node.\n  if (isDOMElement(node) && node.childNodes.length) {\n    var isLast = offset === node.childNodes.length;\n    var index = isLast ? offset - 1 : offset;\n    [node, index] = getEditableChildAndIndex(node, index, isLast ? 'backward' : 'forward');\n    // If the editable child found is in front of input offset, we instead seek to its end\n    isLast = index < offset;\n    // If the node has children, traverse until we have a leaf node. Leaf nodes\n    // can be either text nodes, or other void DOM nodes.\n    while (isDOMElement(node) && node.childNodes.length) {\n      var i = isLast ? node.childNodes.length - 1 : 0;\n      node = getEditableChild(node, i, isLast ? 'backward' : 'forward');\n    }\n    // Determine the new offset inside the text node.\n    offset = isLast && node.textContent != null ? node.textContent.length : 0;\n  }\n  // Return the node and offset.\n  return [node, offset];\n};\n/**\n * Determines whether the active element is nested within a shadowRoot\n */\nvar hasShadowRoot = node => {\n  var parent = node && node.parentNode;\n  while (parent) {\n    if (parent.toString() === '[object ShadowRoot]') {\n      return true;\n    }\n    parent = parent.parentNode;\n  }\n  return false;\n};\n/**\n * Get the nearest editable child and index at `index` in a `parent`, preferring\n * `direction`.\n */\nvar getEditableChildAndIndex = (parent, index, direction) => {\n  var {\n    childNodes\n  } = parent;\n  var child = childNodes[index];\n  var i = index;\n  var triedForward = false;\n  var triedBackward = false;\n  // While the child is a comment node, or an element node with no children,\n  // keep iterating to find a sibling non-void, non-comment node.\n  while (isDOMComment(child) || isDOMElement(child) && child.childNodes.length === 0 || isDOMElement(child) && child.getAttribute('contenteditable') === 'false') {\n    if (triedForward && triedBackward) {\n      break;\n    }\n    if (i >= childNodes.length) {\n      triedForward = true;\n      i = index - 1;\n      direction = 'backward';\n      continue;\n    }\n    if (i < 0) {\n      triedBackward = true;\n      i = index + 1;\n      direction = 'forward';\n      continue;\n    }\n    child = childNodes[i];\n    index = i;\n    i += direction === 'forward' ? 1 : -1;\n  }\n  return [child, index];\n};\n/**\n * Get the nearest editable child at `index` in a `parent`, preferring\n * `direction`.\n */\nvar getEditableChild = (parent, index, direction) => {\n  var [child] = getEditableChildAndIndex(parent, index, direction);\n  return child;\n};\n/**\n * Get a plaintext representation of the content of a node, accounting for block\n * elements which get a newline appended.\n *\n * The domNode must be attached to the DOM.\n */\nvar getPlainText = domNode => {\n  var text = '';\n  if (isDOMText(domNode) && domNode.nodeValue) {\n    return domNode.nodeValue;\n  }\n  if (isDOMElement(domNode)) {\n    for (var childNode of Array.from(domNode.childNodes)) {\n      text += getPlainText(childNode);\n    }\n    var display = getComputedStyle(domNode).getPropertyValue('display');\n    if (display === 'block' || display === 'list' || domNode.tagName === 'BR') {\n      text += '\\n';\n    }\n  }\n  return text;\n};\n/**\n * Get x-slate-fragment attribute from data-slate-fragment\n */\nvar catchSlateFragment = /data-slate-fragment=\"(.+?)\"/m;\nvar getSlateFragmentAttribute = dataTransfer => {\n  var htmlData = dataTransfer.getData('text/html');\n  var [, fragment] = htmlData.match(catchSlateFragment) || [];\n  return fragment;\n};\n/**\n * Get the dom selection from Shadow Root if possible, otherwise from the document\n */\nvar getSelection = root => {\n  if (root.getSelection != null) {\n    return root.getSelection();\n  }\n  return document.getSelection();\n};\n/**\n * Check whether a mutation originates from a editable element inside the editor.\n */\nvar isTrackedMutation = (editor, mutation, batch) => {\n  var {\n    target\n  } = mutation;\n  if (isDOMElement(target) && target.matches('[contentEditable=\"false\"]')) {\n    return false;\n  }\n  var {\n    document\n  } = ReactEditor.getWindow(editor);\n  if (document.contains(target)) {\n    return ReactEditor.hasDOMNode(editor, target, {\n      editable: true\n    });\n  }\n  var parentMutation = batch.find(_ref => {\n    var {\n      addedNodes,\n      removedNodes\n    } = _ref;\n    for (var node of addedNodes) {\n      if (node === target || node.contains(target)) {\n        return true;\n      }\n    }\n    for (var _node of removedNodes) {\n      if (_node === target || _node.contains(target)) {\n        return true;\n      }\n    }\n  });\n  if (!parentMutation || parentMutation === mutation) {\n    return false;\n  }\n  // Target add/remove is tracked. Track the mutation if we track the parent mutation.\n  return isTrackedMutation(editor, parentMutation, batch);\n};\n/**\n * Retrieves the deepest active element in the DOM, considering nested shadow DOMs.\n */\nvar getActiveElement = () => {\n  var activeElement = document.activeElement;\n  while ((_activeElement = activeElement) !== null && _activeElement !== void 0 && _activeElement.shadowRoot && (_activeElement$shadow = activeElement.shadowRoot) !== null && _activeElement$shadow !== void 0 && _activeElement$shadow.activeElement) {\n    var _activeElement, _activeElement$shadow, _activeElement2;\n    activeElement = (_activeElement2 = activeElement) === null || _activeElement2 === void 0 || (_activeElement2 = _activeElement2.shadowRoot) === null || _activeElement2 === void 0 ? void 0 : _activeElement2.activeElement;\n  }\n  return activeElement;\n};\n/**\n * @returns `true` if `otherNode` is before `node` in the document; otherwise, `false`.\n */\nvar isBefore = (node, otherNode) => Boolean(node.compareDocumentPosition(otherNode) & DOMNode.DOCUMENT_POSITION_PRECEDING);\n/**\n * @returns `true` if `otherNode` is after `node` in the document; otherwise, `false`.\n */\nvar isAfter = (node, otherNode) => Boolean(node.compareDocumentPosition(otherNode) & DOMNode.DOCUMENT_POSITION_FOLLOWING);\n\n/**\n * An auto-incrementing identifier for keys.\n */\nvar n = 0;\n/**\n * A class that keeps track of a key string. We use a full class here because we\n * want to be able to use them as keys in `WeakMap` objects.\n */\nclass Key {\n  constructor() {\n    _defineProperty(this, \"id\", void 0);\n    this.id = \"\".concat(n++);\n  }\n}\n\n// eslint-disable-next-line no-redeclare\nvar ReactEditor = {\n  androidPendingDiffs: editor => EDITOR_TO_PENDING_DIFFS.get(editor),\n  androidScheduleFlush: editor => {\n    var _EDITOR_TO_SCHEDULE_F;\n    (_EDITOR_TO_SCHEDULE_F = EDITOR_TO_SCHEDULE_FLUSH.get(editor)) === null || _EDITOR_TO_SCHEDULE_F === void 0 || _EDITOR_TO_SCHEDULE_F();\n  },\n  blur: editor => {\n    var el = ReactEditor.toDOMNode(editor, editor);\n    var root = ReactEditor.findDocumentOrShadowRoot(editor);\n    IS_FOCUSED.set(editor, false);\n    if (root.activeElement === el) {\n      el.blur();\n    }\n  },\n  deselect: editor => {\n    var {\n      selection\n    } = editor;\n    var root = ReactEditor.findDocumentOrShadowRoot(editor);\n    var domSelection = getSelection(root);\n    if (domSelection && domSelection.rangeCount > 0) {\n      domSelection.removeAllRanges();\n    }\n    if (selection) {\n      slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.deselect(editor);\n    }\n  },\n  findDocumentOrShadowRoot: editor => {\n    var el = ReactEditor.toDOMNode(editor, editor);\n    var root = el.getRootNode();\n    if (root instanceof Document || root instanceof ShadowRoot) {\n      return root;\n    }\n    return el.ownerDocument;\n  },\n  findEventRange: (editor, event) => {\n    if ('nativeEvent' in event) {\n      event = event.nativeEvent;\n    }\n    var {\n      clientX: x,\n      clientY: y,\n      target\n    } = event;\n    if (x == null || y == null) {\n      throw new Error(\"Cannot resolve a Slate range from a DOM event: \".concat(event));\n    }\n    var node = ReactEditor.toSlateNode(editor, event.target);\n    var path = ReactEditor.findPath(editor, node);\n    // If the drop target is inside a void node, move it into either the\n    // next or previous node, depending on which side the `x` and `y`\n    // coordinates are closest to.\n    if (slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, node)) {\n      var rect = target.getBoundingClientRect();\n      var isPrev = editor.isInline(node) ? x - rect.left < rect.left + rect.width - x : y - rect.top < rect.top + rect.height - y;\n      var edge = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.point(editor, path, {\n        edge: isPrev ? 'start' : 'end'\n      });\n      var point = isPrev ? slate__WEBPACK_IMPORTED_MODULE_7__.Editor.before(editor, edge) : slate__WEBPACK_IMPORTED_MODULE_7__.Editor.after(editor, edge);\n      if (point) {\n        var _range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, point);\n        return _range;\n      }\n    }\n    // Else resolve a range from the caret position where the drop occured.\n    var domRange;\n    var {\n      document\n    } = ReactEditor.getWindow(editor);\n    // COMPAT: In Firefox, `caretRangeFromPoint` doesn't exist. (2016/07/25)\n    if (document.caretRangeFromPoint) {\n      domRange = document.caretRangeFromPoint(x, y);\n    } else {\n      var position = document.caretPositionFromPoint(x, y);\n      if (position) {\n        domRange = document.createRange();\n        domRange.setStart(position.offsetNode, position.offset);\n        domRange.setEnd(position.offsetNode, position.offset);\n      }\n    }\n    if (!domRange) {\n      throw new Error(\"Cannot resolve a Slate range from a DOM event: \".concat(event));\n    }\n    // Resolve a Slate range from the DOM range.\n    var range = ReactEditor.toSlateRange(editor, domRange, {\n      exactMatch: false,\n      suppressThrow: false\n    });\n    return range;\n  },\n  findKey: (editor, node) => {\n    var key = NODE_TO_KEY.get(node);\n    if (!key) {\n      key = new Key();\n      NODE_TO_KEY.set(node, key);\n    }\n    return key;\n  },\n  findPath: (editor, node) => {\n    var path = [];\n    var child = node;\n    while (true) {\n      var parent = NODE_TO_PARENT.get(child);\n      if (parent == null) {\n        if (slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isEditor(child)) {\n          return path;\n        } else {\n          break;\n        }\n      }\n      var i = NODE_TO_INDEX.get(child);\n      if (i == null) {\n        break;\n      }\n      path.unshift(i);\n      child = parent;\n    }\n    throw new Error(\"Unable to find the path for Slate node: \".concat(slate__WEBPACK_IMPORTED_MODULE_7__.Scrubber.stringify(node)));\n  },\n  focus: function focus(editor) {\n    var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {\n      retries: 5\n    };\n    // Return if already focused\n    if (IS_FOCUSED.get(editor)) {\n      return;\n    }\n    // Retry setting focus if the editor has pending operations.\n    // The DOM (selection) is unstable while changes are applied.\n    // Retry until retries are exhausted or editor is focused.\n    if (options.retries <= 0) {\n      throw new Error('Could not set focus, editor seems stuck with pending operations');\n    }\n    if (editor.operations.length > 0) {\n      setTimeout(() => {\n        ReactEditor.focus(editor, {\n          retries: options.retries - 1\n        });\n      }, 10);\n      return;\n    }\n    var el = ReactEditor.toDOMNode(editor, editor);\n    var root = ReactEditor.findDocumentOrShadowRoot(editor);\n    if (root.activeElement !== el) {\n      // Ensure that the DOM selection state is set to the editor's selection\n      if (editor.selection && root instanceof Document) {\n        var domSelection = getSelection(root);\n        var domRange = ReactEditor.toDOMRange(editor, editor.selection);\n        domSelection === null || domSelection === void 0 || domSelection.removeAllRanges();\n        domSelection === null || domSelection === void 0 || domSelection.addRange(domRange);\n      }\n      // Create a new selection in the top of the document if missing\n      if (!editor.selection) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, slate__WEBPACK_IMPORTED_MODULE_7__.Editor.start(editor, []));\n      }\n      // IS_FOCUSED should be set before calling el.focus() to ensure that\n      // FocusedContext is updated to the correct value\n      IS_FOCUSED.set(editor, true);\n      el.focus({\n        preventScroll: true\n      });\n    }\n  },\n  getWindow: editor => {\n    var window = EDITOR_TO_WINDOW.get(editor);\n    if (!window) {\n      throw new Error('Unable to find a host window element for this editor');\n    }\n    return window;\n  },\n  hasDOMNode: function hasDOMNode(editor, target) {\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    var {\n      editable = false\n    } = options;\n    var editorEl = ReactEditor.toDOMNode(editor, editor);\n    var targetEl;\n    // COMPAT: In Firefox, reading `target.nodeType` will throw an error if\n    // target is originating from an internal \"restricted\" element (e.g. a\n    // stepper arrow on a number input). (2018/05/04)\n    // https://github.com/ianstormtaylor/slate/issues/1819\n    try {\n      targetEl = isDOMElement(target) ? target : target.parentElement;\n    } catch (err) {\n      if (err instanceof Error && !err.message.includes('Permission denied to access property \"nodeType\"')) {\n        throw err;\n      }\n    }\n    if (!targetEl) {\n      return false;\n    }\n    return targetEl.closest(\"[data-slate-editor]\") === editorEl && (!editable || targetEl.isContentEditable ? true : typeof targetEl.isContentEditable === 'boolean' &&\n    // isContentEditable exists only on HTMLElement, and on other nodes it will be undefined\n    // this is the core logic that lets you know you got the right editor.selection instead of null when editor is contenteditable=\"false\"(readOnly)\n    targetEl.closest('[contenteditable=\"false\"]') === editorEl || !!targetEl.getAttribute('data-slate-zero-width'));\n  },\n  hasEditableTarget: (editor, target) => isDOMNode(target) && ReactEditor.hasDOMNode(editor, target, {\n    editable: true\n  }),\n  hasRange: (editor, range) => {\n    var {\n      anchor,\n      focus\n    } = range;\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, anchor.path) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, focus.path);\n  },\n  hasSelectableTarget: (editor, target) => ReactEditor.hasEditableTarget(editor, target) || ReactEditor.isTargetInsideNonReadonlyVoid(editor, target),\n  hasTarget: (editor, target) => isDOMNode(target) && ReactEditor.hasDOMNode(editor, target),\n  insertData: (editor, data) => {\n    editor.insertData(data);\n  },\n  insertFragmentData: (editor, data) => editor.insertFragmentData(data),\n  insertTextData: (editor, data) => editor.insertTextData(data),\n  isComposing: editor => {\n    return !!IS_COMPOSING.get(editor);\n  },\n  isFocused: editor => !!IS_FOCUSED.get(editor),\n  isReadOnly: editor => !!IS_READ_ONLY.get(editor),\n  isTargetInsideNonReadonlyVoid: (editor, target) => {\n    if (IS_READ_ONLY.get(editor)) return false;\n    var slateNode = ReactEditor.hasTarget(editor, target) && ReactEditor.toSlateNode(editor, target);\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(slateNode) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, slateNode);\n  },\n  setFragmentData: (editor, data, originEvent) => editor.setFragmentData(data, originEvent),\n  toDOMNode: (editor, node) => {\n    var KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor);\n    var domNode = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isEditor(node) ? EDITOR_TO_ELEMENT.get(editor) : KEY_TO_ELEMENT === null || KEY_TO_ELEMENT === void 0 ? void 0 : KEY_TO_ELEMENT.get(ReactEditor.findKey(editor, node));\n    if (!domNode) {\n      throw new Error(\"Cannot resolve a DOM node from Slate node: \".concat(slate__WEBPACK_IMPORTED_MODULE_7__.Scrubber.stringify(node)));\n    }\n    return domNode;\n  },\n  toDOMPoint: (editor, point) => {\n    var [node] = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.node(editor, point.path);\n    var el = ReactEditor.toDOMNode(editor, node);\n    var domPoint;\n    // If we're inside a void node, force the offset to 0, otherwise the zero\n    // width spacing character will result in an incorrect offset of 1\n    if (slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n      at: point\n    })) {\n      point = {\n        path: point.path,\n        offset: 0\n      };\n    }\n    // For each leaf, we need to isolate its content, which means filtering\n    // to its direct text and zero-width spans. (We have to filter out any\n    // other siblings that may have been rendered alongside them.)\n    var selector = \"[data-slate-string], [data-slate-zero-width]\";\n    var texts = Array.from(el.querySelectorAll(selector));\n    var start = 0;\n    for (var i = 0; i < texts.length; i++) {\n      var text = texts[i];\n      var domNode = text.childNodes[0];\n      if (domNode == null || domNode.textContent == null) {\n        continue;\n      }\n      var {\n        length\n      } = domNode.textContent;\n      var attr = text.getAttribute('data-slate-length');\n      var trueLength = attr == null ? length : parseInt(attr, 10);\n      var end = start + trueLength;\n      // Prefer putting the selection inside the mark placeholder to ensure\n      // composed text is displayed with the correct marks.\n      var nextText = texts[i + 1];\n      if (point.offset === end && nextText !== null && nextText !== void 0 && nextText.hasAttribute('data-slate-mark-placeholder')) {\n        var _nextText$textContent;\n        var domText = nextText.childNodes[0];\n        domPoint = [\n        // COMPAT: If we don't explicity set the dom point to be on the actual\n        // dom text element, chrome will put the selection behind the actual dom\n        // text element, causing domRange.getBoundingClientRect() calls on a collapsed\n        // selection to return incorrect zero values (https://bugs.chromium.org/p/chromium/issues/detail?id=435438)\n        // which will cause issues when scrolling to it.\n        domText instanceof DOMText ? domText : nextText, (_nextText$textContent = nextText.textContent) !== null && _nextText$textContent !== void 0 && _nextText$textContent.startsWith('\\uFEFF') ? 1 : 0];\n        break;\n      }\n      if (point.offset <= end) {\n        var offset = Math.min(length, Math.max(0, point.offset - start));\n        domPoint = [domNode, offset];\n        break;\n      }\n      start = end;\n    }\n    if (!domPoint) {\n      throw new Error(\"Cannot resolve a DOM point from Slate point: \".concat(slate__WEBPACK_IMPORTED_MODULE_7__.Scrubber.stringify(point)));\n    }\n    return domPoint;\n  },\n  toDOMRange: (editor, range) => {\n    var {\n      anchor,\n      focus\n    } = range;\n    var isBackward = slate__WEBPACK_IMPORTED_MODULE_7__.Range.isBackward(range);\n    var domAnchor = ReactEditor.toDOMPoint(editor, anchor);\n    var domFocus = slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(range) ? domAnchor : ReactEditor.toDOMPoint(editor, focus);\n    var window = ReactEditor.getWindow(editor);\n    var domRange = window.document.createRange();\n    var [startNode, startOffset] = isBackward ? domFocus : domAnchor;\n    var [endNode, endOffset] = isBackward ? domAnchor : domFocus;\n    // A slate Point at zero-width Leaf always has an offset of 0 but a native DOM selection at\n    // zero-width node has an offset of 1 so we have to check if we are in a zero-width node and\n    // adjust the offset accordingly.\n    var startEl = isDOMElement(startNode) ? startNode : startNode.parentElement;\n    var isStartAtZeroWidth = !!startEl.getAttribute('data-slate-zero-width');\n    var endEl = isDOMElement(endNode) ? endNode : endNode.parentElement;\n    var isEndAtZeroWidth = !!endEl.getAttribute('data-slate-zero-width');\n    domRange.setStart(startNode, isStartAtZeroWidth ? 1 : startOffset);\n    domRange.setEnd(endNode, isEndAtZeroWidth ? 1 : endOffset);\n    return domRange;\n  },\n  toSlateNode: (editor, domNode) => {\n    var domEl = isDOMElement(domNode) ? domNode : domNode.parentElement;\n    if (domEl && !domEl.hasAttribute('data-slate-node')) {\n      domEl = domEl.closest(\"[data-slate-node]\");\n    }\n    var node = domEl ? ELEMENT_TO_NODE.get(domEl) : null;\n    if (!node) {\n      throw new Error(\"Cannot resolve a Slate node from DOM node: \".concat(domEl));\n    }\n    return node;\n  },\n  toSlatePoint: (editor, domPoint, options) => {\n    var {\n      exactMatch,\n      suppressThrow,\n      searchDirection = 'backward'\n    } = options;\n    var [nearestNode, nearestOffset] = exactMatch ? domPoint : normalizeDOMPoint(domPoint);\n    var parentNode = nearestNode.parentNode;\n    var textNode = null;\n    var offset = 0;\n    if (parentNode) {\n      var _domNode$textContent, _domNode$textContent2;\n      var editorEl = ReactEditor.toDOMNode(editor, editor);\n      var potentialVoidNode = parentNode.closest('[data-slate-void=\"true\"]');\n      // Need to ensure that the closest void node is actually a void node\n      // within this editor, and not a void node within some parent editor. This can happen\n      // if this editor is within a void node of another editor (\"nested editors\", like in\n      // the \"Editable Voids\" example on the docs site).\n      var voidNode = potentialVoidNode && editorEl.contains(potentialVoidNode) ? potentialVoidNode : null;\n      var potentialNonEditableNode = parentNode.closest('[contenteditable=\"false\"]');\n      var nonEditableNode = potentialNonEditableNode && editorEl.contains(potentialNonEditableNode) ? potentialNonEditableNode : null;\n      var leafNode = parentNode.closest('[data-slate-leaf]');\n      var domNode = null;\n      // Calculate how far into the text node the `nearestNode` is, so that we\n      // can determine what the offset relative to the text node is.\n      if (leafNode) {\n        textNode = leafNode.closest('[data-slate-node=\"text\"]');\n        if (textNode) {\n          var window = ReactEditor.getWindow(editor);\n          var range = window.document.createRange();\n          range.setStart(textNode, 0);\n          range.setEnd(nearestNode, nearestOffset);\n          var contents = range.cloneContents();\n          var removals = [...Array.prototype.slice.call(contents.querySelectorAll('[data-slate-zero-width]')), ...Array.prototype.slice.call(contents.querySelectorAll('[contenteditable=false]'))];\n          removals.forEach(el => {\n            // COMPAT: While composing at the start of a text node, some keyboards put\n            // the text content inside the zero width space.\n            if (IS_ANDROID && !exactMatch && el.hasAttribute('data-slate-zero-width') && el.textContent.length > 0 && el.textContext !== '\\uFEFF') {\n              if (el.textContent.startsWith('\\uFEFF')) {\n                el.textContent = el.textContent.slice(1);\n              }\n              return;\n            }\n            el.parentNode.removeChild(el);\n          });\n          // COMPAT: Edge has a bug where Range.prototype.toString() will\n          // convert \\n into \\r\\n. The bug causes a loop when slate-react\n          // attempts to reposition its cursor to match the native position. Use\n          // textContent.length instead.\n          // https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/10291116/\n          offset = contents.textContent.length;\n          domNode = textNode;\n        }\n      } else if (voidNode) {\n        // For void nodes, the element with the offset key will be a cousin, not an\n        // ancestor, so find it by going down from the nearest void parent and taking the\n        // first one that isn't inside a nested editor.\n        var leafNodes = voidNode.querySelectorAll('[data-slate-leaf]');\n        for (var index = 0; index < leafNodes.length; index++) {\n          var current = leafNodes[index];\n          if (ReactEditor.hasDOMNode(editor, current)) {\n            leafNode = current;\n            break;\n          }\n        }\n        // COMPAT: In read-only editors the leaf is not rendered.\n        if (!leafNode) {\n          offset = 1;\n        } else {\n          textNode = leafNode.closest('[data-slate-node=\"text\"]');\n          domNode = leafNode;\n          offset = domNode.textContent.length;\n          domNode.querySelectorAll('[data-slate-zero-width]').forEach(el => {\n            offset -= el.textContent.length;\n          });\n        }\n      } else if (nonEditableNode) {\n        // Find the edge of the nearest leaf in `searchDirection`\n        var getLeafNodes = node => node ? node.querySelectorAll(\n        // Exclude leaf nodes in nested editors\n        '[data-slate-leaf]:not(:scope [data-slate-editor] [data-slate-leaf])') : [];\n        var elementNode = nonEditableNode.closest('[data-slate-node=\"element\"]');\n        if (searchDirection === 'forward') {\n          var _leafNodes$find;\n          var _leafNodes = [...getLeafNodes(elementNode), ...getLeafNodes(elementNode === null || elementNode === void 0 ? void 0 : elementNode.nextElementSibling)];\n          leafNode = (_leafNodes$find = _leafNodes.find(leaf => isAfter(nonEditableNode, leaf))) !== null && _leafNodes$find !== void 0 ? _leafNodes$find : null;\n        } else {\n          var _leafNodes2$findLast;\n          var _leafNodes2 = [...getLeafNodes(elementNode === null || elementNode === void 0 ? void 0 : elementNode.previousElementSibling), ...getLeafNodes(elementNode)];\n          leafNode = (_leafNodes2$findLast = _leafNodes2.findLast(leaf => isBefore(nonEditableNode, leaf))) !== null && _leafNodes2$findLast !== void 0 ? _leafNodes2$findLast : null;\n        }\n        if (leafNode) {\n          textNode = leafNode.closest('[data-slate-node=\"text\"]');\n          domNode = leafNode;\n          if (searchDirection === 'forward') {\n            offset = 0;\n          } else {\n            offset = domNode.textContent.length;\n            domNode.querySelectorAll('[data-slate-zero-width]').forEach(el => {\n              offset -= el.textContent.length;\n            });\n          }\n        }\n      }\n      if (domNode && offset === domNode.textContent.length &&\n      // COMPAT: Android IMEs might remove the zero width space while composing,\n      // and we don't add it for line-breaks.\n      IS_ANDROID && domNode.getAttribute('data-slate-zero-width') === 'z' && (_domNode$textContent = domNode.textContent) !== null && _domNode$textContent !== void 0 && _domNode$textContent.startsWith('\\uFEFF') && (\n      // COMPAT: If the parent node is a Slate zero-width space, editor is\n      // because the text node should have no characters. However, during IME\n      // composition the ASCII characters will be prepended to the zero-width\n      // space, so subtract 1 from the offset to account for the zero-width\n      // space character.\n      parentNode.hasAttribute('data-slate-zero-width') ||\n      // COMPAT: In Firefox, `range.cloneContents()` returns an extra trailing '\\n'\n      // when the document ends with a new-line character. This results in the offset\n      // length being off by one, so we need to subtract one to account for this.\n      IS_FIREFOX && (_domNode$textContent2 = domNode.textContent) !== null && _domNode$textContent2 !== void 0 && _domNode$textContent2.endsWith('\\n\\n'))) {\n        offset--;\n      }\n    }\n    if (IS_ANDROID && !textNode && !exactMatch) {\n      var node = parentNode.hasAttribute('data-slate-node') ? parentNode : parentNode.closest('[data-slate-node]');\n      if (node && ReactEditor.hasDOMNode(editor, node, {\n        editable: true\n      })) {\n        var _slateNode = ReactEditor.toSlateNode(editor, node);\n        var {\n          path: _path,\n          offset: _offset\n        } = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.start(editor, ReactEditor.findPath(editor, _slateNode));\n        if (!node.querySelector('[data-slate-leaf]')) {\n          _offset = nearestOffset;\n        }\n        return {\n          path: _path,\n          offset: _offset\n        };\n      }\n    }\n    if (!textNode) {\n      if (suppressThrow) {\n        return null;\n      }\n      throw new Error(\"Cannot resolve a Slate point from DOM point: \".concat(domPoint));\n    }\n    // COMPAT: If someone is clicking from one Slate editor into another,\n    // the select event fires twice, once for the old editor's `element`\n    // first, and then afterwards for the correct `element`. (2017/03/03)\n    var slateNode = ReactEditor.toSlateNode(editor, textNode);\n    var path = ReactEditor.findPath(editor, slateNode);\n    return {\n      path,\n      offset\n    };\n  },\n  toSlateRange: (editor, domRange, options) => {\n    var _focusNode$textConten;\n    var {\n      exactMatch,\n      suppressThrow\n    } = options;\n    var el = isDOMSelection(domRange) ? domRange.anchorNode : domRange.startContainer;\n    var anchorNode;\n    var anchorOffset;\n    var focusNode;\n    var focusOffset;\n    var isCollapsed;\n    if (el) {\n      if (isDOMSelection(domRange)) {\n        // COMPAT: In firefox the normal seletion way does not work\n        // (https://github.com/ianstormtaylor/slate/pull/5486#issue-1820720223)\n        if (IS_FIREFOX && domRange.rangeCount > 1) {\n          focusNode = domRange.focusNode; // Focus node works fine\n          var firstRange = domRange.getRangeAt(0);\n          var lastRange = domRange.getRangeAt(domRange.rangeCount - 1);\n          // Here we are in the contenteditable mode of a table in firefox\n          if (focusNode instanceof HTMLTableRowElement && firstRange.startContainer instanceof HTMLTableRowElement && lastRange.startContainer instanceof HTMLTableRowElement) {\n            // HTMLElement, becouse Element is a slate element\n            function getLastChildren(element) {\n              if (element.childElementCount > 0) {\n                return getLastChildren(element.children[0]);\n              } else {\n                return element;\n              }\n            }\n            var firstNodeRow = firstRange.startContainer;\n            var lastNodeRow = lastRange.startContainer;\n            // This should never fail as \"The HTMLElement interface represents any HTML element.\"\n            var firstNode = getLastChildren(firstNodeRow.children[firstRange.startOffset]);\n            var lastNode = getLastChildren(lastNodeRow.children[lastRange.startOffset]);\n            // Zero, as we allways take the right one as the anchor point\n            focusOffset = 0;\n            if (lastNode.childNodes.length > 0) {\n              anchorNode = lastNode.childNodes[0];\n            } else {\n              anchorNode = lastNode;\n            }\n            if (firstNode.childNodes.length > 0) {\n              focusNode = firstNode.childNodes[0];\n            } else {\n              focusNode = firstNode;\n            }\n            if (lastNode instanceof HTMLElement) {\n              anchorOffset = lastNode.innerHTML.length;\n            } else {\n              // Fallback option\n              anchorOffset = 0;\n            }\n          } else {\n            // This is the read only mode of a firefox table\n            // Right to left\n            if (firstRange.startContainer === focusNode) {\n              anchorNode = lastRange.endContainer;\n              anchorOffset = lastRange.endOffset;\n              focusOffset = firstRange.startOffset;\n            } else {\n              // Left to right\n              anchorNode = firstRange.startContainer;\n              anchorOffset = firstRange.endOffset;\n              focusOffset = lastRange.startOffset;\n            }\n          }\n        } else {\n          anchorNode = domRange.anchorNode;\n          anchorOffset = domRange.anchorOffset;\n          focusNode = domRange.focusNode;\n          focusOffset = domRange.focusOffset;\n        }\n        // COMPAT: There's a bug in chrome that always returns `true` for\n        // `isCollapsed` for a Selection that comes from a ShadowRoot.\n        // (2020/08/08)\n        // https://bugs.chromium.org/p/chromium/issues/detail?id=447523\n        // IsCollapsed might not work in firefox, but this will\n        if (IS_CHROME && hasShadowRoot(anchorNode) || IS_FIREFOX) {\n          isCollapsed = domRange.anchorNode === domRange.focusNode && domRange.anchorOffset === domRange.focusOffset;\n        } else {\n          isCollapsed = domRange.isCollapsed;\n        }\n      } else {\n        anchorNode = domRange.startContainer;\n        anchorOffset = domRange.startOffset;\n        focusNode = domRange.endContainer;\n        focusOffset = domRange.endOffset;\n        isCollapsed = domRange.collapsed;\n      }\n    }\n    if (anchorNode == null || focusNode == null || anchorOffset == null || focusOffset == null) {\n      throw new Error(\"Cannot resolve a Slate range from DOM range: \".concat(domRange));\n    }\n    // COMPAT: Firefox sometimes includes an extra \\n (rendered by TextString\n    // when isTrailing is true) in the focusOffset, resulting in an invalid\n    // Slate point. (2023/11/01)\n    if (IS_FIREFOX && (_focusNode$textConten = focusNode.textContent) !== null && _focusNode$textConten !== void 0 && _focusNode$textConten.endsWith('\\n\\n') && focusOffset === focusNode.textContent.length) {\n      focusOffset--;\n    }\n    var anchor = ReactEditor.toSlatePoint(editor, [anchorNode, anchorOffset], {\n      exactMatch,\n      suppressThrow\n    });\n    if (!anchor) {\n      return null;\n    }\n    var focusBeforeAnchor = isBefore(anchorNode, focusNode) || anchorNode === focusNode && focusOffset < anchorOffset;\n    var focus = isCollapsed ? anchor : ReactEditor.toSlatePoint(editor, [focusNode, focusOffset], {\n      exactMatch,\n      suppressThrow,\n      searchDirection: focusBeforeAnchor ? 'forward' : 'backward'\n    });\n    if (!focus) {\n      return null;\n    }\n    var range = {\n      anchor: anchor,\n      focus: focus\n    };\n    // if the selection is a hanging range that ends in a void\n    // and the DOM focus is an Element\n    // (meaning that the selection ends before the element)\n    // unhang the range to avoid mistakenly including the void\n    if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(range) && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isForward(range) && isDOMElement(focusNode) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n      at: range.focus,\n      mode: 'highest'\n    })) {\n      range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.unhangRange(editor, range, {\n        voids: true\n      });\n    }\n    return range;\n  }\n};\n\n/**\n * Check whether a text diff was applied in a way we can perform the pending action on /\n * recover the pending selection.\n */\nfunction verifyDiffState(editor, textDiff) {\n  var {\n    path,\n    diff\n  } = textDiff;\n  if (!slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, path)) {\n    return false;\n  }\n  var node = slate__WEBPACK_IMPORTED_MODULE_7__.Node.get(editor, path);\n  if (!slate__WEBPACK_IMPORTED_MODULE_7__.Text.isText(node)) {\n    return false;\n  }\n  if (diff.start !== node.text.length || diff.text.length === 0) {\n    return node.text.slice(diff.start, diff.start + diff.text.length) === diff.text;\n  }\n  var nextPath = slate__WEBPACK_IMPORTED_MODULE_7__.Path.next(path);\n  if (!slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, nextPath)) {\n    return false;\n  }\n  var nextNode = slate__WEBPACK_IMPORTED_MODULE_7__.Node.get(editor, nextPath);\n  return slate__WEBPACK_IMPORTED_MODULE_7__.Text.isText(nextNode) && nextNode.text.startsWith(diff.text);\n}\nfunction applyStringDiff(text) {\n  for (var _len = arguments.length, diffs = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    diffs[_key - 1] = arguments[_key];\n  }\n  return diffs.reduce((text, diff) => text.slice(0, diff.start) + diff.text + text.slice(diff.end), text);\n}\nfunction longestCommonPrefixLength(str, another) {\n  var length = Math.min(str.length, another.length);\n  for (var i = 0; i < length; i++) {\n    if (str.charAt(i) !== another.charAt(i)) {\n      return i;\n    }\n  }\n  return length;\n}\nfunction longestCommonSuffixLength(str, another, max) {\n  var length = Math.min(str.length, another.length, max);\n  for (var i = 0; i < length; i++) {\n    if (str.charAt(str.length - i - 1) !== another.charAt(another.length - i - 1)) {\n      return i;\n    }\n  }\n  return length;\n}\n/**\n * Remove redundant changes from the diff so that it spans the minimal possible range\n */\nfunction normalizeStringDiff(targetText, diff) {\n  var {\n    start,\n    end,\n    text\n  } = diff;\n  var removedText = targetText.slice(start, end);\n  var prefixLength = longestCommonPrefixLength(removedText, text);\n  var max = Math.min(removedText.length - prefixLength, text.length - prefixLength);\n  var suffixLength = longestCommonSuffixLength(removedText, text, max);\n  var normalized = {\n    start: start + prefixLength,\n    end: end - suffixLength,\n    text: text.slice(prefixLength, text.length - suffixLength)\n  };\n  if (normalized.start === normalized.end && normalized.text.length === 0) {\n    return null;\n  }\n  return normalized;\n}\n/**\n * Return a string diff that is equivalent to applying b after a spanning the range of\n * both changes\n */\nfunction mergeStringDiffs(targetText, a, b) {\n  var start = Math.min(a.start, b.start);\n  var overlap = Math.max(0, Math.min(a.start + a.text.length, b.end) - b.start);\n  var applied = applyStringDiff(targetText, a, b);\n  var sliceEnd = Math.max(b.start + b.text.length, a.start + a.text.length + (a.start + a.text.length > b.start ? b.text.length : 0) - overlap);\n  var text = applied.slice(start, sliceEnd);\n  var end = Math.max(a.end, b.end - a.text.length + (a.end - a.start));\n  return normalizeStringDiff(targetText, {\n    start,\n    end,\n    text\n  });\n}\n/**\n * Get the slate range the text diff spans.\n */\nfunction targetRange(textDiff) {\n  var {\n    path,\n    diff\n  } = textDiff;\n  return {\n    anchor: {\n      path,\n      offset: diff.start\n    },\n    focus: {\n      path,\n      offset: diff.end\n    }\n  };\n}\n/**\n * Normalize a 'pending point' a.k.a a point based on the dom state before applying\n * the pending diffs. Since the pending diffs might have been inserted with different\n * marks we have to 'walk' the offset from the starting position to ensure we still\n * have a valid point inside the document\n */\nfunction normalizePoint(editor, point) {\n  var {\n    path,\n    offset\n  } = point;\n  if (!slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, path)) {\n    return null;\n  }\n  var leaf = slate__WEBPACK_IMPORTED_MODULE_7__.Node.get(editor, path);\n  if (!slate__WEBPACK_IMPORTED_MODULE_7__.Text.isText(leaf)) {\n    return null;\n  }\n  var parentBlock = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.above(editor, {\n    match: n => slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(n) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(editor, n),\n    at: path\n  });\n  if (!parentBlock) {\n    return null;\n  }\n  while (offset > leaf.text.length) {\n    var entry = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.next(editor, {\n      at: path,\n      match: slate__WEBPACK_IMPORTED_MODULE_7__.Text.isText\n    });\n    if (!entry || !slate__WEBPACK_IMPORTED_MODULE_7__.Path.isDescendant(entry[1], parentBlock[1])) {\n      return null;\n    }\n    offset -= leaf.text.length;\n    leaf = entry[0];\n    path = entry[1];\n  }\n  return {\n    path,\n    offset\n  };\n}\n/**\n * Normalize a 'pending selection' to ensure it's valid in the current document state.\n */\nfunction normalizeRange(editor, range) {\n  var anchor = normalizePoint(editor, range.anchor);\n  if (!anchor) {\n    return null;\n  }\n  if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(range)) {\n    return {\n      anchor,\n      focus: anchor\n    };\n  }\n  var focus = normalizePoint(editor, range.focus);\n  if (!focus) {\n    return null;\n  }\n  return {\n    anchor,\n    focus\n  };\n}\nfunction transformPendingPoint(editor, point, op) {\n  var pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(editor);\n  var textDiff = pendingDiffs === null || pendingDiffs === void 0 ? void 0 : pendingDiffs.find(_ref => {\n    var {\n      path\n    } = _ref;\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(path, point.path);\n  });\n  if (!textDiff || point.offset <= textDiff.diff.start) {\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Point.transform(point, op, {\n      affinity: 'backward'\n    });\n  }\n  var {\n    diff\n  } = textDiff;\n  // Point references location inside the diff => transform the point based on the location\n  // the diff will be applied to and add the offset inside the diff.\n  if (point.offset <= diff.start + diff.text.length) {\n    var _anchor = {\n      path: point.path,\n      offset: diff.start\n    };\n    var _transformed = slate__WEBPACK_IMPORTED_MODULE_7__.Point.transform(_anchor, op, {\n      affinity: 'backward'\n    });\n    if (!_transformed) {\n      return null;\n    }\n    return {\n      path: _transformed.path,\n      offset: _transformed.offset + point.offset - diff.start\n    };\n  }\n  // Point references location after the diff\n  var anchor = {\n    path: point.path,\n    offset: point.offset - diff.text.length + diff.end - diff.start\n  };\n  var transformed = slate__WEBPACK_IMPORTED_MODULE_7__.Point.transform(anchor, op, {\n    affinity: 'backward'\n  });\n  if (!transformed) {\n    return null;\n  }\n  if (op.type === 'split_node' && slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(op.path, point.path) && anchor.offset < op.position && diff.start < op.position) {\n    return transformed;\n  }\n  return {\n    path: transformed.path,\n    offset: transformed.offset + diff.text.length - diff.end + diff.start\n  };\n}\nfunction transformPendingRange(editor, range, op) {\n  var anchor = transformPendingPoint(editor, range.anchor, op);\n  if (!anchor) {\n    return null;\n  }\n  if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(range)) {\n    return {\n      anchor,\n      focus: anchor\n    };\n  }\n  var focus = transformPendingPoint(editor, range.focus, op);\n  if (!focus) {\n    return null;\n  }\n  return {\n    anchor,\n    focus\n  };\n}\nfunction transformTextDiff(textDiff, op) {\n  var {\n    path,\n    diff,\n    id\n  } = textDiff;\n  switch (op.type) {\n    case 'insert_text':\n      {\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(op.path, path) || op.offset >= diff.end) {\n          return textDiff;\n        }\n        if (op.offset <= diff.start) {\n          return {\n            diff: {\n              start: op.text.length + diff.start,\n              end: op.text.length + diff.end,\n              text: diff.text\n            },\n            id,\n            path\n          };\n        }\n        return {\n          diff: {\n            start: diff.start,\n            end: diff.end + op.text.length,\n            text: diff.text\n          },\n          id,\n          path\n        };\n      }\n    case 'remove_text':\n      {\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(op.path, path) || op.offset >= diff.end) {\n          return textDiff;\n        }\n        if (op.offset + op.text.length <= diff.start) {\n          return {\n            diff: {\n              start: diff.start - op.text.length,\n              end: diff.end - op.text.length,\n              text: diff.text\n            },\n            id,\n            path\n          };\n        }\n        return {\n          diff: {\n            start: diff.start,\n            end: diff.end - op.text.length,\n            text: diff.text\n          },\n          id,\n          path\n        };\n      }\n    case 'split_node':\n      {\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(op.path, path) || op.position >= diff.end) {\n          return {\n            diff,\n            id,\n            path: slate__WEBPACK_IMPORTED_MODULE_7__.Path.transform(path, op, {\n              affinity: 'backward'\n            })\n          };\n        }\n        if (op.position > diff.start) {\n          return {\n            diff: {\n              start: diff.start,\n              end: Math.min(op.position, diff.end),\n              text: diff.text\n            },\n            id,\n            path\n          };\n        }\n        return {\n          diff: {\n            start: diff.start - op.position,\n            end: diff.end - op.position,\n            text: diff.text\n          },\n          id,\n          path: slate__WEBPACK_IMPORTED_MODULE_7__.Path.transform(path, op, {\n            affinity: 'forward'\n          })\n        };\n      }\n    case 'merge_node':\n      {\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(op.path, path)) {\n          return {\n            diff,\n            id,\n            path: slate__WEBPACK_IMPORTED_MODULE_7__.Path.transform(path, op)\n          };\n        }\n        return {\n          diff: {\n            start: diff.start + op.position,\n            end: diff.end + op.position,\n            text: diff.text\n          },\n          id,\n          path: slate__WEBPACK_IMPORTED_MODULE_7__.Path.transform(path, op)\n        };\n      }\n  }\n  var newPath = slate__WEBPACK_IMPORTED_MODULE_7__.Path.transform(path, op);\n  if (!newPath) {\n    return null;\n  }\n  return {\n    diff,\n    path: newPath,\n    id\n  };\n}\n\nfunction ownKeys$6(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$6(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$6(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$6(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n// https://github.com/facebook/draft-js/blob/main/src/component/handlers/composition/DraftEditorCompositionHandler.js#L41\n// When using keyboard English association function, conpositionEnd triggered too fast, resulting in after `insertText` still maintain association state.\nvar RESOLVE_DELAY = 25;\n// Time with no user interaction before the current user action is considered as done.\nvar FLUSH_DELAY = 200;\n// Replace with `const debug = console.log` to debug\nvar debug = function debug() {};\n// Type guard to check if a value is a DataTransfer\nvar isDataTransfer = value => (value === null || value === void 0 ? void 0 : value.constructor.name) === 'DataTransfer';\nfunction createAndroidInputManager(_ref) {\n  var {\n    editor,\n    scheduleOnDOMSelectionChange,\n    onDOMSelectionChange\n  } = _ref;\n  var flushing = false;\n  var compositionEndTimeoutId = null;\n  var flushTimeoutId = null;\n  var actionTimeoutId = null;\n  var idCounter = 0;\n  var insertPositionHint = false;\n  var applyPendingSelection = () => {\n    var pendingSelection = EDITOR_TO_PENDING_SELECTION.get(editor);\n    EDITOR_TO_PENDING_SELECTION.delete(editor);\n    if (pendingSelection) {\n      var {\n        selection\n      } = editor;\n      var normalized = normalizeRange(editor, pendingSelection);\n      if (normalized && (!selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(normalized, selection))) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, normalized);\n      }\n    }\n  };\n  var performAction = () => {\n    var action = EDITOR_TO_PENDING_ACTION.get(editor);\n    EDITOR_TO_PENDING_ACTION.delete(editor);\n    if (!action) {\n      return;\n    }\n    if (action.at) {\n      var target = slate__WEBPACK_IMPORTED_MODULE_7__.Point.isPoint(action.at) ? normalizePoint(editor, action.at) : normalizeRange(editor, action.at);\n      if (!target) {\n        return;\n      }\n      var _targetRange = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, target);\n      if (!editor.selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(editor.selection, _targetRange)) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, target);\n      }\n    }\n    action.run();\n  };\n  var flush = () => {\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId);\n      flushTimeoutId = null;\n    }\n    if (actionTimeoutId) {\n      clearTimeout(actionTimeoutId);\n      actionTimeoutId = null;\n    }\n    if (!hasPendingDiffs() && !hasPendingAction()) {\n      applyPendingSelection();\n      return;\n    }\n    if (!flushing) {\n      flushing = true;\n      setTimeout(() => flushing = false);\n    }\n    if (hasPendingAction()) {\n      flushing = 'action';\n    }\n    var selectionRef = editor.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.rangeRef(editor, editor.selection, {\n      affinity: 'forward'\n    });\n    EDITOR_TO_USER_MARKS.set(editor, editor.marks);\n    debug('flush', EDITOR_TO_PENDING_ACTION.get(editor), EDITOR_TO_PENDING_DIFFS.get(editor));\n    var scheduleSelectionChange = hasPendingDiffs();\n    var diff;\n    while (diff = (_EDITOR_TO_PENDING_DI = EDITOR_TO_PENDING_DIFFS.get(editor)) === null || _EDITOR_TO_PENDING_DI === void 0 ? void 0 : _EDITOR_TO_PENDING_DI[0]) {\n      var _EDITOR_TO_PENDING_DI, _EDITOR_TO_PENDING_DI2;\n      var pendingMarks = EDITOR_TO_PENDING_INSERTION_MARKS.get(editor);\n      if (pendingMarks !== undefined) {\n        EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor);\n        editor.marks = pendingMarks;\n      }\n      if (pendingMarks && insertPositionHint === false) {\n        insertPositionHint = null;\n      }\n      var range = targetRange(diff);\n      if (!editor.selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(editor.selection, range)) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, range);\n      }\n      if (diff.diff.text) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, diff.diff.text);\n      } else {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor);\n      }\n      // Remove diff only after we have applied it to account for it when transforming\n      // pending ranges.\n      EDITOR_TO_PENDING_DIFFS.set(editor, (_EDITOR_TO_PENDING_DI2 = EDITOR_TO_PENDING_DIFFS.get(editor)) === null || _EDITOR_TO_PENDING_DI2 === void 0 ? void 0 : _EDITOR_TO_PENDING_DI2.filter(_ref2 => {\n        var {\n          id\n        } = _ref2;\n        return id !== diff.id;\n      }));\n      if (!verifyDiffState(editor, diff)) {\n        scheduleSelectionChange = false;\n        EDITOR_TO_PENDING_ACTION.delete(editor);\n        EDITOR_TO_USER_MARKS.delete(editor);\n        flushing = 'action';\n        // Ensure we don't restore the pending user (dom) selection\n        // since the document and dom state do not match.\n        EDITOR_TO_PENDING_SELECTION.delete(editor);\n        scheduleOnDOMSelectionChange.cancel();\n        onDOMSelectionChange.cancel();\n        selectionRef === null || selectionRef === void 0 || selectionRef.unref();\n      }\n    }\n    var selection = selectionRef === null || selectionRef === void 0 ? void 0 : selectionRef.unref();\n    if (selection && !EDITOR_TO_PENDING_SELECTION.get(editor) && (!editor.selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(selection, editor.selection))) {\n      slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, selection);\n    }\n    if (hasPendingAction()) {\n      performAction();\n      return;\n    }\n    // COMPAT: The selectionChange event is fired after the action is performed,\n    // so we have to manually schedule it to ensure we don't 'throw away' the selection\n    // while rendering if we have pending changes.\n    if (scheduleSelectionChange) {\n      scheduleOnDOMSelectionChange();\n    }\n    scheduleOnDOMSelectionChange.flush();\n    onDOMSelectionChange.flush();\n    applyPendingSelection();\n    var userMarks = EDITOR_TO_USER_MARKS.get(editor);\n    EDITOR_TO_USER_MARKS.delete(editor);\n    if (userMarks !== undefined) {\n      editor.marks = userMarks;\n      editor.onChange();\n    }\n  };\n  var handleCompositionEnd = _event => {\n    if (compositionEndTimeoutId) {\n      clearTimeout(compositionEndTimeoutId);\n    }\n    compositionEndTimeoutId = setTimeout(() => {\n      IS_COMPOSING.set(editor, false);\n      flush();\n    }, RESOLVE_DELAY);\n  };\n  var handleCompositionStart = _event => {\n    IS_COMPOSING.set(editor, true);\n    if (compositionEndTimeoutId) {\n      clearTimeout(compositionEndTimeoutId);\n      compositionEndTimeoutId = null;\n    }\n  };\n  var updatePlaceholderVisibility = function updatePlaceholderVisibility() {\n    var forceHide = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : false;\n    var placeholderElement = EDITOR_TO_PLACEHOLDER_ELEMENT.get(editor);\n    if (!placeholderElement) {\n      return;\n    }\n    if (hasPendingDiffs() || forceHide) {\n      placeholderElement.style.display = 'none';\n      return;\n    }\n    placeholderElement.style.removeProperty('display');\n  };\n  var storeDiff = (path, diff) => {\n    var _EDITOR_TO_PENDING_DI3;\n    var pendingDiffs = (_EDITOR_TO_PENDING_DI3 = EDITOR_TO_PENDING_DIFFS.get(editor)) !== null && _EDITOR_TO_PENDING_DI3 !== void 0 ? _EDITOR_TO_PENDING_DI3 : [];\n    EDITOR_TO_PENDING_DIFFS.set(editor, pendingDiffs);\n    var target = slate__WEBPACK_IMPORTED_MODULE_7__.Node.leaf(editor, path);\n    var idx = pendingDiffs.findIndex(change => slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(change.path, path));\n    if (idx < 0) {\n      var normalized = normalizeStringDiff(target.text, diff);\n      if (normalized) {\n        pendingDiffs.push({\n          path,\n          diff,\n          id: idCounter++\n        });\n      }\n      updatePlaceholderVisibility();\n      return;\n    }\n    var merged = mergeStringDiffs(target.text, pendingDiffs[idx].diff, diff);\n    if (!merged) {\n      pendingDiffs.splice(idx, 1);\n      updatePlaceholderVisibility();\n      return;\n    }\n    pendingDiffs[idx] = _objectSpread$6(_objectSpread$6({}, pendingDiffs[idx]), {}, {\n      diff: merged\n    });\n  };\n  var scheduleAction = function scheduleAction(run) {\n    var {\n      at\n    } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    insertPositionHint = false;\n    EDITOR_TO_PENDING_SELECTION.delete(editor);\n    scheduleOnDOMSelectionChange.cancel();\n    onDOMSelectionChange.cancel();\n    if (hasPendingAction()) {\n      flush();\n    }\n    EDITOR_TO_PENDING_ACTION.set(editor, {\n      at,\n      run\n    });\n    // COMPAT: When deleting before a non-contenteditable element chrome only fires a beforeinput,\n    // (no input) and doesn't perform any dom mutations. Without a flush timeout we would never flush\n    // in this case and thus never actually perform the action.\n    actionTimeoutId = setTimeout(flush);\n  };\n  var handleDOMBeforeInput = event => {\n    var _targetRange2;\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId);\n      flushTimeoutId = null;\n    }\n    if (IS_NODE_MAP_DIRTY.get(editor)) {\n      return;\n    }\n    var {\n      inputType: type\n    } = event;\n    var targetRange = null;\n    var data = event.dataTransfer || event.data || undefined;\n    if (insertPositionHint !== false && type !== 'insertText' && type !== 'insertCompositionText') {\n      insertPositionHint = false;\n    }\n    var [nativeTargetRange] = event.getTargetRanges();\n    if (nativeTargetRange) {\n      targetRange = ReactEditor.toSlateRange(editor, nativeTargetRange, {\n        exactMatch: false,\n        suppressThrow: true\n      });\n    }\n    // COMPAT: SelectionChange event is fired after the action is performed, so we\n    // have to manually get the selection here to ensure it's up-to-date.\n    var window = ReactEditor.getWindow(editor);\n    var domSelection = window.getSelection();\n    if (!targetRange && domSelection) {\n      nativeTargetRange = domSelection;\n      targetRange = ReactEditor.toSlateRange(editor, domSelection, {\n        exactMatch: false,\n        suppressThrow: true\n      });\n    }\n    targetRange = (_targetRange2 = targetRange) !== null && _targetRange2 !== void 0 ? _targetRange2 : editor.selection;\n    if (!targetRange) {\n      return;\n    }\n    // By default, the input manager tries to store text diffs so that we can\n    // defer flushing them at a later point in time. We don't want to flush\n    // for every input event as this can be expensive. However, there are some\n    // scenarios where we cannot safely store the text diff and must instead\n    // schedule an action to let Slate normalize the editor state.\n    var canStoreDiff = true;\n    if (type.startsWith('delete')) {\n      if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(targetRange)) {\n        var [_start, _end] = slate__WEBPACK_IMPORTED_MODULE_7__.Range.edges(targetRange);\n        var _leaf = slate__WEBPACK_IMPORTED_MODULE_7__.Node.leaf(editor, _start.path);\n        if (_leaf.text.length === _start.offset && _end.offset === 0) {\n          var next = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.next(editor, {\n            at: _start.path,\n            match: slate__WEBPACK_IMPORTED_MODULE_7__.Text.isText\n          });\n          if (next && slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(next[1], _end.path)) {\n            targetRange = {\n              anchor: _end,\n              focus: _end\n            };\n          }\n        }\n      }\n      var direction = type.endsWith('Backward') ? 'backward' : 'forward';\n      var [start, end] = slate__WEBPACK_IMPORTED_MODULE_7__.Range.edges(targetRange);\n      var [leaf, path] = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.leaf(editor, start.path);\n      var diff = {\n        text: '',\n        start: start.offset,\n        end: end.offset\n      };\n      var pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(editor);\n      var relevantPendingDiffs = pendingDiffs === null || pendingDiffs === void 0 ? void 0 : pendingDiffs.find(change => slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(change.path, path));\n      var diffs = relevantPendingDiffs ? [relevantPendingDiffs.diff, diff] : [diff];\n      var text = applyStringDiff(leaf.text, ...diffs);\n      if (text.length === 0) {\n        // Text leaf will be removed, so we need to schedule an\n        // action to remove it so that Slate can normalize instead\n        // of storing as a diff\n        canStoreDiff = false;\n      }\n      if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(targetRange)) {\n        if (canStoreDiff && slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(targetRange.anchor.path, targetRange.focus.path)) {\n          var point = {\n            path: targetRange.anchor.path,\n            offset: start.offset\n          };\n          var range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, point, point);\n          handleUserSelect(range);\n          return storeDiff(targetRange.anchor.path, {\n            text: '',\n            end: end.offset,\n            start: start.offset\n          });\n        }\n        return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n          direction\n        }), {\n          at: targetRange\n        });\n      }\n    }\n    switch (type) {\n      case 'deleteByComposition':\n      case 'deleteByCut':\n      case 'deleteByDrag':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor), {\n            at: targetRange\n          });\n        }\n      case 'deleteContent':\n      case 'deleteContentForward':\n        {\n          var {\n            anchor\n          } = targetRange;\n          if (canStoreDiff && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(targetRange)) {\n            var targetNode = slate__WEBPACK_IMPORTED_MODULE_7__.Node.leaf(editor, anchor.path);\n            if (anchor.offset < targetNode.text.length) {\n              return storeDiff(anchor.path, {\n                text: '',\n                start: anchor.offset,\n                end: anchor.offset + 1\n              });\n            }\n          }\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor), {\n            at: targetRange\n          });\n        }\n      case 'deleteContentBackward':\n        {\n          var _nativeTargetRange;\n          var {\n            anchor: _anchor\n          } = targetRange;\n          // If we have a mismatch between the native and slate selection being collapsed\n          // we are most likely deleting a zero-width placeholder and thus should perform it\n          // as an action to ensure correct behavior (mostly happens with mark placeholders)\n          var nativeCollapsed = isDOMSelection(nativeTargetRange) ? nativeTargetRange.isCollapsed : !!((_nativeTargetRange = nativeTargetRange) !== null && _nativeTargetRange !== void 0 && _nativeTargetRange.collapsed);\n          if (canStoreDiff && nativeCollapsed && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(targetRange) && _anchor.offset > 0) {\n            return storeDiff(_anchor.path, {\n              text: '',\n              start: _anchor.offset - 1,\n              end: _anchor.offset\n            });\n          }\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor), {\n            at: targetRange\n          });\n        }\n      case 'deleteEntireSoftLine':\n        {\n          return scheduleAction(() => {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n              unit: 'line'\n            });\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n              unit: 'line'\n            });\n          }, {\n            at: targetRange\n          });\n        }\n      case 'deleteHardLineBackward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n            unit: 'block'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'deleteSoftLineBackward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n            unit: 'line'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'deleteHardLineForward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n            unit: 'block'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'deleteSoftLineForward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n            unit: 'line'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'deleteWordBackward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n            unit: 'word'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'deleteWordForward':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n            unit: 'word'\n          }), {\n            at: targetRange\n          });\n        }\n      case 'insertLineBreak':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertSoftBreak(editor), {\n            at: targetRange\n          });\n        }\n      case 'insertParagraph':\n        {\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertBreak(editor), {\n            at: targetRange\n          });\n        }\n      case 'insertCompositionText':\n      case 'deleteCompositionText':\n      case 'insertFromComposition':\n      case 'insertFromDrop':\n      case 'insertFromPaste':\n      case 'insertFromYank':\n      case 'insertReplacementText':\n      case 'insertText':\n        {\n          if (isDataTransfer(data)) {\n            return scheduleAction(() => ReactEditor.insertData(editor, data), {\n              at: targetRange\n            });\n          }\n          var _text = data !== null && data !== void 0 ? data : '';\n          // COMPAT: If we are writing inside a placeholder, the ime inserts the text inside\n          // the placeholder itself and thus includes the zero-width space inside edit events.\n          if (EDITOR_TO_PENDING_INSERTION_MARKS.get(editor)) {\n            _text = _text.replace('\\uFEFF', '');\n          }\n          // Pastes from the Android clipboard will generate `insertText` events.\n          // If the copied text contains any newlines, Android will append an\n          // extra newline to the end of the copied text.\n          if (type === 'insertText' && /.*\\n.*\\n$/.test(_text)) {\n            _text = _text.slice(0, -1);\n          }\n          // If the text includes a newline, split it at newlines and paste each component\n          // string, with soft breaks in between each.\n          if (_text.includes('\\n')) {\n            return scheduleAction(() => {\n              var parts = _text.split('\\n');\n              parts.forEach((line, i) => {\n                if (line) {\n                  slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, line);\n                }\n                if (i !== parts.length - 1) {\n                  slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertSoftBreak(editor);\n                }\n              });\n            }, {\n              at: targetRange\n            });\n          }\n          if (slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(targetRange.anchor.path, targetRange.focus.path)) {\n            var [_start2, _end2] = slate__WEBPACK_IMPORTED_MODULE_7__.Range.edges(targetRange);\n            var _diff = {\n              start: _start2.offset,\n              end: _end2.offset,\n              text: _text\n            };\n            // COMPAT: Swiftkey has a weird bug where the target range of the 2nd word\n            // inserted after a mark placeholder is inserted with an anchor offset off by 1.\n            // So writing 'some text' will result in 'some ttext'. Luckily all 'normal' insert\n            // text events are fired with the correct target ranges, only the final 'insertComposition'\n            // isn't, so we can adjust the target range start offset if we are confident this is the\n            // swiftkey insert causing the issue.\n            if (_text && insertPositionHint && type === 'insertCompositionText') {\n              var hintPosition = insertPositionHint.start + insertPositionHint.text.search(/\\S|$/);\n              var diffPosition = _diff.start + _diff.text.search(/\\S|$/);\n              if (diffPosition === hintPosition + 1 && _diff.end === insertPositionHint.start + insertPositionHint.text.length) {\n                _diff.start -= 1;\n                insertPositionHint = null;\n                scheduleFlush();\n              } else {\n                insertPositionHint = false;\n              }\n            } else if (type === 'insertText') {\n              if (insertPositionHint === null) {\n                insertPositionHint = _diff;\n              } else if (insertPositionHint && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(targetRange) && insertPositionHint.end + insertPositionHint.text.length === _start2.offset) {\n                insertPositionHint = _objectSpread$6(_objectSpread$6({}, insertPositionHint), {}, {\n                  text: insertPositionHint.text + _text\n                });\n              } else {\n                insertPositionHint = false;\n              }\n            } else {\n              insertPositionHint = false;\n            }\n            if (canStoreDiff) {\n              storeDiff(_start2.path, _diff);\n              return;\n            }\n          }\n          return scheduleAction(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, _text), {\n            at: targetRange\n          });\n        }\n    }\n  };\n  var hasPendingAction = () => {\n    return !!EDITOR_TO_PENDING_ACTION.get(editor);\n  };\n  var hasPendingDiffs = () => {\n    var _EDITOR_TO_PENDING_DI4;\n    return !!((_EDITOR_TO_PENDING_DI4 = EDITOR_TO_PENDING_DIFFS.get(editor)) !== null && _EDITOR_TO_PENDING_DI4 !== void 0 && _EDITOR_TO_PENDING_DI4.length);\n  };\n  var hasPendingChanges = () => {\n    return hasPendingAction() || hasPendingDiffs();\n  };\n  var isFlushing = () => {\n    return flushing;\n  };\n  var handleUserSelect = range => {\n    EDITOR_TO_PENDING_SELECTION.set(editor, range);\n    if (flushTimeoutId) {\n      clearTimeout(flushTimeoutId);\n      flushTimeoutId = null;\n    }\n    var {\n      selection\n    } = editor;\n    if (!range) {\n      return;\n    }\n    var pathChanged = !selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(selection.anchor.path, range.anchor.path);\n    var parentPathChanged = !selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(selection.anchor.path.slice(0, -1), range.anchor.path.slice(0, -1));\n    if (pathChanged && insertPositionHint || parentPathChanged) {\n      insertPositionHint = false;\n    }\n    if (pathChanged || hasPendingDiffs()) {\n      flushTimeoutId = setTimeout(flush, FLUSH_DELAY);\n    }\n  };\n  var handleInput = () => {\n    if (hasPendingAction() || !hasPendingDiffs()) {\n      flush();\n    }\n  };\n  var handleKeyDown = _ => {\n    // COMPAT: Swiftkey closes the keyboard when typing inside a empty node\n    // directly next to a non-contenteditable element (= the placeholder).\n    // The only event fired soon enough for us to allow hiding the placeholder\n    // without swiftkey picking it up is the keydown event, so we have to hide it\n    // here. See https://github.com/ianstormtaylor/slate/pull/4988#issuecomment-1201050535\n    if (!hasPendingDiffs()) {\n      updatePlaceholderVisibility(true);\n      setTimeout(updatePlaceholderVisibility);\n    }\n  };\n  var scheduleFlush = () => {\n    if (!hasPendingAction()) {\n      actionTimeoutId = setTimeout(flush);\n    }\n  };\n  var handleDomMutations = mutations => {\n    if (hasPendingDiffs() || hasPendingAction()) {\n      return;\n    }\n    if (mutations.some(mutation => isTrackedMutation(editor, mutation, mutations))) {\n      var _EDITOR_TO_FORCE_REND;\n      // Cause a re-render to restore the dom state if we encounter tracked mutations without\n      // a corresponding pending action.\n      (_EDITOR_TO_FORCE_REND = EDITOR_TO_FORCE_RENDER.get(editor)) === null || _EDITOR_TO_FORCE_REND === void 0 || _EDITOR_TO_FORCE_REND();\n    }\n  };\n  return {\n    flush,\n    scheduleFlush,\n    hasPendingDiffs,\n    hasPendingAction,\n    hasPendingChanges,\n    isFlushing,\n    handleUserSelect,\n    handleCompositionEnd,\n    handleCompositionStart,\n    handleDOMBeforeInput,\n    handleKeyDown,\n    handleDomMutations,\n    handleInput\n  };\n}\n\nfunction useIsMounted() {\n  var isMountedRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(false);\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  return isMountedRef.current;\n}\n\n/**\n * Prevent warning on SSR by falling back to useEffect when DOM isn't available\n */\nvar useIsomorphicLayoutEffect = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_3__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_3__.useEffect;\n\nfunction useMutationObserver(node, callback, options) {\n  var [mutationObserver] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(() => new MutationObserver(callback));\n  useIsomorphicLayoutEffect(() => {\n    // Discard mutations caused during render phase. This works due to react calling\n    // useLayoutEffect synchronously after the render phase before the next tick.\n    mutationObserver.takeRecords();\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    if (!node.current) {\n      throw new Error('Failed to attach MutationObserver, `node` is undefined');\n    }\n    mutationObserver.observe(node.current, options);\n    return () => mutationObserver.disconnect();\n  }, [mutationObserver, node, options]);\n}\n\nvar _excluded$3 = [\"node\"];\nfunction ownKeys$5(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$5(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$5(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$5(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar MUTATION_OBSERVER_CONFIG$1 = {\n  subtree: true,\n  childList: true,\n  characterData: true\n};\nvar useAndroidInputManager = !IS_ANDROID ? () => null : _ref => {\n  var {\n      node\n    } = _ref,\n    options = _objectWithoutProperties(_ref, _excluded$3);\n  if (!IS_ANDROID) {\n    return null;\n  }\n  var editor = useSlateStatic();\n  var isMounted = useIsMounted();\n  var [inputManager] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(() => createAndroidInputManager(_objectSpread$5({\n    editor\n  }, options)));\n  useMutationObserver(node, inputManager.handleDomMutations, MUTATION_OBSERVER_CONFIG$1);\n  EDITOR_TO_SCHEDULE_FLUSH.set(editor, inputManager.scheduleFlush);\n  if (isMounted) {\n    inputManager.flush();\n  }\n  return inputManager;\n};\n\nvar _excluded$2 = [\"anchor\", \"focus\"],\n  _excluded2$1 = [\"anchor\", \"focus\"];\nvar shallowCompare = (obj1, obj2) => Object.keys(obj1).length === Object.keys(obj2).length && Object.keys(obj1).every(key => obj2.hasOwnProperty(key) && obj1[key] === obj2[key]);\nvar isDecorationFlagsEqual = (range, other) => {\n  var rangeOwnProps = _objectWithoutProperties(range, _excluded$2);\n  var otherOwnProps = _objectWithoutProperties(other, _excluded2$1);\n  return range[PLACEHOLDER_SYMBOL] === other[PLACEHOLDER_SYMBOL] && shallowCompare(rangeOwnProps, otherOwnProps);\n};\n/**\n * Check if a list of decorator ranges are equal to another.\n *\n * PERF: this requires the two lists to also have the ranges inside them in the\n * same order, but this is an okay constraint for us since decorations are\n * kept in order, and the odd case where they aren't is okay to re-render for.\n */\nvar isElementDecorationsEqual = (list, another) => {\n  if (list.length !== another.length) {\n    return false;\n  }\n  for (var i = 0; i < list.length; i++) {\n    var range = list[i];\n    var other = another[i];\n    if (!slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(range, other) || !isDecorationFlagsEqual(range, other)) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * Check if a list of decorator ranges are equal to another.\n *\n * PERF: this requires the two lists to also have the ranges inside them in the\n * same order, but this is an okay constraint for us since decorations are\n * kept in order, and the odd case where they aren't is okay to re-render for.\n */\nvar isTextDecorationsEqual = (list, another) => {\n  if (list.length !== another.length) {\n    return false;\n  }\n  for (var i = 0; i < list.length; i++) {\n    var range = list[i];\n    var other = another[i];\n    // compare only offsets because paths doesn't matter for text\n    if (range.anchor.offset !== other.anchor.offset || range.focus.offset !== other.focus.offset || !isDecorationFlagsEqual(range, other)) {\n      return false;\n    }\n  }\n  return true;\n};\n\nfunction ownKeys$4(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$4(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$4(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$4(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n * Leaf content strings.\n */\nvar String$1 = props => {\n  var {\n    isLast,\n    leaf,\n    parent,\n    text\n  } = props;\n  var editor = useSlateStatic();\n  var path = ReactEditor.findPath(editor, text);\n  var parentPath = slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(path);\n  var isMarkPlaceholder = Boolean(leaf[MARK_PLACEHOLDER_SYMBOL]);\n  // COMPAT: Render text inside void nodes with a zero-width space.\n  // So the node can contain selection but the text is not visible.\n  if (editor.isVoid(parent)) {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(ZeroWidthString, {\n      length: slate__WEBPACK_IMPORTED_MODULE_7__.Node.string(parent).length\n    });\n  }\n  // COMPAT: If this is the last text node in an empty block, render a zero-\n  // width space that will convert into a line break when copying and pasting\n  // to support expected plain text.\n  if (leaf.text === '' && parent.children[parent.children.length - 1] === text && !editor.isInline(parent) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.string(editor, parentPath) === '') {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(ZeroWidthString, {\n      isLineBreak: true,\n      isMarkPlaceholder: isMarkPlaceholder\n    });\n  }\n  // COMPAT: If the text is empty, it's because it's on the edge of an inline\n  // node, so we render a zero-width space so that the selection can be\n  // inserted next to it still.\n  if (leaf.text === '') {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(ZeroWidthString, {\n      isMarkPlaceholder: isMarkPlaceholder\n    });\n  }\n  // COMPAT: Browsers will collapse trailing new lines at the end of blocks,\n  // so we need to add an extra trailing new lines to prevent that.\n  if (isLast && leaf.text.slice(-1) === '\\n') {\n    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(TextString, {\n      isTrailing: true,\n      text: leaf.text\n    });\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(TextString, {\n    text: leaf.text\n  });\n};\n/**\n * Leaf strings with text in them.\n */\nvar TextString = props => {\n  var {\n    text,\n    isTrailing = false\n  } = props;\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var getTextContent = () => {\n    return \"\".concat(text !== null && text !== void 0 ? text : '').concat(isTrailing ? '\\n' : '');\n  };\n  var [initialText] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(getTextContent);\n  // This is the actual text rendering boundary where we interface with the DOM\n  // The text is not rendered as part of the virtual DOM, as since we handle basic character insertions natively,\n  // updating the DOM is not a one way dataflow anymore. What we need here is not reconciliation and diffing\n  // with previous version of the virtual DOM, but rather diffing with the actual DOM element, and replace the DOM <span> content\n  // exactly if and only if its current content does not match our current virtual DOM.\n  // Otherwise the DOM TextNode would always be replaced by React as the user types, which interferes with native text features,\n  // eg makes native spellcheck opt out from checking the text node.\n  // useLayoutEffect: updating our span before browser paint\n  useIsomorphicLayoutEffect(() => {\n    // null coalescing text to make sure we're not outputing \"null\" as a string in the extreme case it is nullish at runtime\n    var textWithTrailing = getTextContent();\n    if (ref.current && ref.current.textContent !== textWithTrailing) {\n      ref.current.textContent = textWithTrailing;\n    }\n    // intentionally not specifying dependencies, so that this effect runs on every render\n    // as this effectively replaces \"specifying the text in the virtual DOM under the <span> below\" on each render\n  });\n  // We intentionally render a memoized <span> that only receives the initial text content when the component is mounted.\n  // We defer to the layout effect above to update the `textContent` of the span element when needed.\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(MemoizedText$1, {\n    ref: ref\n  }, initialText);\n};\nvar MemoizedText$1 = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.memo)( /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.forwardRef)((props, ref) => {\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"span\", {\n    \"data-slate-string\": true,\n    ref: ref\n  }, props.children);\n}));\n/**\n * Leaf strings without text, render as zero-width strings.\n */\nvar ZeroWidthString = props => {\n  var {\n    length = 0,\n    isLineBreak = false,\n    isMarkPlaceholder = false\n  } = props;\n  var attributes = {\n    'data-slate-zero-width': isLineBreak ? 'n' : 'z',\n    'data-slate-length': length\n  };\n  if (isMarkPlaceholder) {\n    attributes['data-slate-mark-placeholder'] = true;\n  }\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"span\", _objectSpread$4({}, attributes), !(IS_ANDROID || IS_IOS) || !isLineBreak ? '\\uFEFF' : null, isLineBreak ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"br\", null) : null);\n};\n\nfunction ownKeys$3(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$3(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$3(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$3(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n// Delay the placeholder on Android to prevent the keyboard from closing.\n// (https://github.com/ianstormtaylor/slate/pull/5368)\nvar PLACEHOLDER_DELAY = IS_ANDROID ? 300 : 0;\nfunction disconnectPlaceholderResizeObserver(placeholderResizeObserver, releaseObserver) {\n  if (placeholderResizeObserver.current) {\n    placeholderResizeObserver.current.disconnect();\n    if (releaseObserver) {\n      placeholderResizeObserver.current = null;\n    }\n  }\n}\nfunction clearTimeoutRef(timeoutRef) {\n  if (timeoutRef.current) {\n    clearTimeout(timeoutRef.current);\n    timeoutRef.current = null;\n  }\n}\n/**\n * Individual leaves in a text node with unique formatting.\n */\nvar Leaf = props => {\n  var {\n    leaf,\n    isLast,\n    text,\n    parent,\n    renderPlaceholder,\n    renderLeaf = props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(DefaultLeaf, _objectSpread$3({}, props))\n  } = props;\n  var editor = useSlateStatic();\n  var placeholderResizeObserver = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var placeholderRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var [showPlaceholder, setShowPlaceholder] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n  var showPlaceholderTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var callbackPlaceholderRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(placeholderEl => {\n    disconnectPlaceholderResizeObserver(placeholderResizeObserver, placeholderEl == null);\n    if (placeholderEl == null) {\n      var _leaf$onPlaceholderRe;\n      EDITOR_TO_PLACEHOLDER_ELEMENT.delete(editor);\n      (_leaf$onPlaceholderRe = leaf.onPlaceholderResize) === null || _leaf$onPlaceholderRe === void 0 || _leaf$onPlaceholderRe.call(leaf, null);\n    } else {\n      EDITOR_TO_PLACEHOLDER_ELEMENT.set(editor, placeholderEl);\n      if (!placeholderResizeObserver.current) {\n        // Create a new observer and observe the placeholder element.\n        var ResizeObserver$1 = window.ResizeObserver || _juggle_resize_observer__WEBPACK_IMPORTED_MODULE_4__.ResizeObserver;\n        placeholderResizeObserver.current = new ResizeObserver$1(() => {\n          var _leaf$onPlaceholderRe2;\n          (_leaf$onPlaceholderRe2 = leaf.onPlaceholderResize) === null || _leaf$onPlaceholderRe2 === void 0 || _leaf$onPlaceholderRe2.call(leaf, placeholderEl);\n        });\n      }\n      placeholderResizeObserver.current.observe(placeholderEl);\n      placeholderRef.current = placeholderEl;\n    }\n  }, [placeholderRef, leaf, editor]);\n  var children = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(String$1, {\n    isLast: isLast,\n    leaf: leaf,\n    parent: parent,\n    text: text\n  });\n  var leafIsPlaceholder = Boolean(leaf[PLACEHOLDER_SYMBOL]);\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    if (leafIsPlaceholder) {\n      if (!showPlaceholderTimeoutRef.current) {\n        // Delay the placeholder, so it will not render in a selection\n        showPlaceholderTimeoutRef.current = setTimeout(() => {\n          setShowPlaceholder(true);\n          showPlaceholderTimeoutRef.current = null;\n        }, PLACEHOLDER_DELAY);\n      }\n    } else {\n      clearTimeoutRef(showPlaceholderTimeoutRef);\n      setShowPlaceholder(false);\n    }\n    return () => clearTimeoutRef(showPlaceholderTimeoutRef);\n  }, [leafIsPlaceholder, setShowPlaceholder]);\n  if (leafIsPlaceholder && showPlaceholder) {\n    var placeholderProps = {\n      children: leaf.placeholder,\n      attributes: {\n        'data-slate-placeholder': true,\n        style: {\n          position: 'absolute',\n          top: 0,\n          pointerEvents: 'none',\n          width: '100%',\n          maxWidth: '100%',\n          display: 'block',\n          opacity: '0.333',\n          userSelect: 'none',\n          textDecoration: 'none',\n          // Fixes https://github.com/udecode/plate/issues/2315\n          WebkitUserModify: IS_WEBKIT ? 'inherit' : undefined\n        },\n        contentEditable: false,\n        ref: callbackPlaceholderRef\n      }\n    };\n    children = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement((react__WEBPACK_IMPORTED_MODULE_3___default().Fragment), null, renderPlaceholder(placeholderProps), children);\n  }\n  // COMPAT: Having the `data-` attributes on these leaf elements ensures that\n  // in certain misbehaving browsers they aren't weirdly cloned/destroyed by\n  // contenteditable behaviors. (2019/05/08)\n  var attributes = {\n    'data-slate-leaf': true\n  };\n  return renderLeaf({\n    attributes,\n    children,\n    leaf,\n    text\n  });\n};\nvar MemoizedLeaf = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().memo(Leaf, (prev, next) => {\n  return next.parent === prev.parent && next.isLast === prev.isLast && next.renderLeaf === prev.renderLeaf && next.renderPlaceholder === prev.renderPlaceholder && next.text === prev.text && slate__WEBPACK_IMPORTED_MODULE_7__.Text.equals(next.leaf, prev.leaf) && next.leaf[PLACEHOLDER_SYMBOL] === prev.leaf[PLACEHOLDER_SYMBOL];\n});\nvar DefaultLeaf = props => {\n  var {\n    attributes,\n    children\n  } = props;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"span\", _objectSpread$3({}, attributes), children);\n};\n\n/**\n * Text.\n */\nvar Text = props => {\n  var {\n    decorations,\n    isLast,\n    parent,\n    renderPlaceholder,\n    renderLeaf,\n    text\n  } = props;\n  var editor = useSlateStatic();\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var leaves = slate__WEBPACK_IMPORTED_MODULE_7__.Text.decorations(text, decorations);\n  var key = ReactEditor.findKey(editor, text);\n  var children = [];\n  for (var i = 0; i < leaves.length; i++) {\n    var leaf = leaves[i];\n    children.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(MemoizedLeaf, {\n      isLast: isLast && i === leaves.length - 1,\n      key: \"\".concat(key.id, \"-\").concat(i),\n      renderPlaceholder: renderPlaceholder,\n      leaf: leaf,\n      text: text,\n      parent: parent,\n      renderLeaf: renderLeaf\n    }));\n  }\n  // Update element-related weak maps with the DOM element ref.\n  var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(span => {\n    var KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor);\n    if (span) {\n      KEY_TO_ELEMENT === null || KEY_TO_ELEMENT === void 0 || KEY_TO_ELEMENT.set(key, span);\n      NODE_TO_ELEMENT.set(text, span);\n      ELEMENT_TO_NODE.set(span, text);\n    } else {\n      KEY_TO_ELEMENT === null || KEY_TO_ELEMENT === void 0 || KEY_TO_ELEMENT.delete(key);\n      NODE_TO_ELEMENT.delete(text);\n      if (ref.current) {\n        ELEMENT_TO_NODE.delete(ref.current);\n      }\n    }\n    ref.current = span;\n  }, [ref, editor, key, text]);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"span\", {\n    \"data-slate-node\": \"text\",\n    ref: callbackRef\n  }, children);\n};\nvar MemoizedText = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().memo(Text, (prev, next) => {\n  return next.parent === prev.parent && next.isLast === prev.isLast && next.renderLeaf === prev.renderLeaf && next.renderPlaceholder === prev.renderPlaceholder && next.text === prev.text && isTextDecorationsEqual(next.decorations, prev.decorations);\n});\n\nfunction ownKeys$2(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$2(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$2(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$2(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n * Element.\n */\nvar Element = props => {\n  var {\n    decorations,\n    element,\n    renderElement = p => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(DefaultElement, _objectSpread$2({}, p)),\n    renderPlaceholder,\n    renderLeaf,\n    selection\n  } = props;\n  var editor = useSlateStatic();\n  var readOnly = useReadOnly();\n  var isInline = editor.isInline(element);\n  var key = ReactEditor.findKey(editor, element);\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(ref => {\n    // Update element-related weak maps with the DOM element ref.\n    var KEY_TO_ELEMENT = EDITOR_TO_KEY_TO_ELEMENT.get(editor);\n    if (ref) {\n      KEY_TO_ELEMENT === null || KEY_TO_ELEMENT === void 0 || KEY_TO_ELEMENT.set(key, ref);\n      NODE_TO_ELEMENT.set(element, ref);\n      ELEMENT_TO_NODE.set(ref, element);\n    } else {\n      KEY_TO_ELEMENT === null || KEY_TO_ELEMENT === void 0 || KEY_TO_ELEMENT.delete(key);\n      NODE_TO_ELEMENT.delete(element);\n    }\n  }, [editor, key, element]);\n  var children = useChildren({\n    decorations,\n    node: element,\n    renderElement,\n    renderPlaceholder,\n    renderLeaf,\n    selection\n  });\n  // Attributes that the developer must mix into the element in their\n  // custom node renderer component.\n  var attributes = {\n    'data-slate-node': 'element',\n    ref\n  };\n  if (isInline) {\n    attributes['data-slate-inline'] = true;\n  }\n  // If it's a block node with inline children, add the proper `dir` attribute\n  // for text direction.\n  if (!isInline && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasInlines(editor, element)) {\n    var text = slate__WEBPACK_IMPORTED_MODULE_7__.Node.string(element);\n    var dir = direction__WEBPACK_IMPORTED_MODULE_0___default()(text);\n    if (dir === 'rtl') {\n      attributes.dir = dir;\n    }\n  }\n  // If it's a void node, wrap the children in extra void-specific elements.\n  if (slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, element)) {\n    attributes['data-slate-void'] = true;\n    if (!readOnly && isInline) {\n      attributes.contentEditable = false;\n    }\n    var Tag = isInline ? 'span' : 'div';\n    var [[_text]] = slate__WEBPACK_IMPORTED_MODULE_7__.Node.texts(element);\n    children = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(Tag, {\n      \"data-slate-spacer\": true,\n      style: {\n        height: '0',\n        color: 'transparent',\n        outline: 'none',\n        position: 'absolute'\n      }\n    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(MemoizedText, {\n      renderPlaceholder: renderPlaceholder,\n      decorations: [],\n      isLast: false,\n      parent: element,\n      text: _text\n    }));\n    NODE_TO_INDEX.set(_text, 0);\n    NODE_TO_PARENT.set(_text, element);\n  }\n  return renderElement({\n    attributes,\n    children,\n    element\n  });\n};\nvar MemoizedElement = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().memo(Element, (prev, next) => {\n  return prev.element === next.element && prev.renderElement === next.renderElement && prev.renderLeaf === next.renderLeaf && prev.renderPlaceholder === next.renderPlaceholder && isElementDecorationsEqual(prev.decorations, next.decorations) && (prev.selection === next.selection || !!prev.selection && !!next.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(prev.selection, next.selection));\n});\n/**\n * The default element renderer.\n */\nvar DefaultElement = props => {\n  var {\n    attributes,\n    children,\n    element\n  } = props;\n  var editor = useSlateStatic();\n  var Tag = editor.isInline(element) ? 'span' : 'div';\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(Tag, _objectSpread$2(_objectSpread$2({}, attributes), {}, {\n    style: {\n      position: 'relative'\n    }\n  }), children);\n};\n\n/**\n * A React context for sharing the `decorate` prop of the editable.\n */\nvar DecorateContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(() => []);\n/**\n * Get the current `decorate` prop of the editable.\n */\nvar useDecorate = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(DecorateContext);\n};\n\n/**\n * A React context for sharing the `selected` state of an element.\n */\nvar SelectedContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(false);\n/**\n * Get the current `selected` state of an element.\n */\nvar useSelected = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(SelectedContext);\n};\n\n/**\n * Children.\n */\nvar useChildren = props => {\n  var {\n    decorations,\n    node,\n    renderElement,\n    renderPlaceholder,\n    renderLeaf,\n    selection\n  } = props;\n  var decorate = useDecorate();\n  var editor = useSlateStatic();\n  IS_NODE_MAP_DIRTY.set(editor, false);\n  var path = ReactEditor.findPath(editor, node);\n  var children = [];\n  var isLeafBlock = slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && !editor.isInline(node) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasInlines(editor, node);\n  for (var i = 0; i < node.children.length; i++) {\n    var p = path.concat(i);\n    var n = node.children[i];\n    var key = ReactEditor.findKey(editor, n);\n    var range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, p);\n    var sel = selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.intersection(range, selection);\n    var ds = decorate([n, p]);\n    for (var dec of decorations) {\n      var d = slate__WEBPACK_IMPORTED_MODULE_7__.Range.intersection(dec, range);\n      if (d) {\n        ds.push(d);\n      }\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(n)) {\n      children.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(SelectedContext.Provider, {\n        key: \"provider-\".concat(key.id),\n        value: !!sel\n      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(MemoizedElement, {\n        decorations: ds,\n        element: n,\n        key: key.id,\n        renderElement: renderElement,\n        renderPlaceholder: renderPlaceholder,\n        renderLeaf: renderLeaf,\n        selection: sel\n      })));\n    } else {\n      children.push( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(MemoizedText, {\n        decorations: ds,\n        key: key.id,\n        isLast: isLeafBlock && i === node.children.length - 1,\n        parent: node,\n        renderPlaceholder: renderPlaceholder,\n        renderLeaf: renderLeaf,\n        text: n\n      }));\n    }\n    NODE_TO_INDEX.set(n, i);\n    NODE_TO_PARENT.set(n, node);\n  }\n  return children;\n};\n\n/**\n * A React context for sharing the `readOnly` state of the editor.\n */\nvar ReadOnlyContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(false);\n/**\n * Get the current `readOnly` state of the editor.\n */\nvar useReadOnly = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(ReadOnlyContext);\n};\n\nvar SlateContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(null);\n/**\n * Get the current editor object from the React context.\n */\nvar useSlate = () => {\n  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(SlateContext);\n  if (!context) {\n    throw new Error(\"The `useSlate` hook must be used inside the <Slate> component's context.\");\n  }\n  var {\n    editor\n  } = context;\n  return editor;\n};\nvar useSlateWithV = () => {\n  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(SlateContext);\n  if (!context) {\n    throw new Error(\"The `useSlate` hook must be used inside the <Slate> component's context.\");\n  }\n  return context;\n};\n\nfunction useTrackUserInput() {\n  var editor = useSlateStatic();\n  var receivedUserInput = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(false);\n  var animationFrameIdRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(0);\n  var onUserInput = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(() => {\n    if (receivedUserInput.current) {\n      return;\n    }\n    receivedUserInput.current = true;\n    var window = ReactEditor.getWindow(editor);\n    window.cancelAnimationFrame(animationFrameIdRef.current);\n    animationFrameIdRef.current = window.requestAnimationFrame(() => {\n      receivedUserInput.current = false;\n    });\n  }, [editor]);\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => () => cancelAnimationFrame(animationFrameIdRef.current), []);\n  return {\n    receivedUserInput,\n    onUserInput\n  };\n}\n\nvar TRIPLE_CLICK = 3;\n\n/**\n * Hotkey mappings for each platform.\n */\nvar HOTKEYS = {\n  bold: 'mod+b',\n  compose: ['down', 'left', 'right', 'up', 'backspace', 'enter'],\n  moveBackward: 'left',\n  moveForward: 'right',\n  moveWordBackward: 'ctrl+left',\n  moveWordForward: 'ctrl+right',\n  deleteBackward: 'shift?+backspace',\n  deleteForward: 'shift?+delete',\n  extendBackward: 'shift+left',\n  extendForward: 'shift+right',\n  italic: 'mod+i',\n  insertSoftBreak: 'shift+enter',\n  splitBlock: 'enter',\n  undo: 'mod+z'\n};\nvar APPLE_HOTKEYS = {\n  moveLineBackward: 'opt+up',\n  moveLineForward: 'opt+down',\n  moveWordBackward: 'opt+left',\n  moveWordForward: 'opt+right',\n  deleteBackward: ['ctrl+backspace', 'ctrl+h'],\n  deleteForward: ['ctrl+delete', 'ctrl+d'],\n  deleteLineBackward: 'cmd+shift?+backspace',\n  deleteLineForward: ['cmd+shift?+delete', 'ctrl+k'],\n  deleteWordBackward: 'opt+shift?+backspace',\n  deleteWordForward: 'opt+shift?+delete',\n  extendLineBackward: 'opt+shift+up',\n  extendLineForward: 'opt+shift+down',\n  redo: 'cmd+shift+z',\n  transposeCharacter: 'ctrl+t'\n};\nvar WINDOWS_HOTKEYS = {\n  deleteWordBackward: 'ctrl+shift?+backspace',\n  deleteWordForward: 'ctrl+shift?+delete',\n  redo: ['ctrl+y', 'ctrl+shift+z']\n};\n/**\n * Create a platform-aware hotkey checker.\n */\nvar create = key => {\n  var generic = HOTKEYS[key];\n  var apple = APPLE_HOTKEYS[key];\n  var windows = WINDOWS_HOTKEYS[key];\n  var isGeneric = generic && (0,is_hotkey__WEBPACK_IMPORTED_MODULE_5__.isHotkey)(generic);\n  var isApple = apple && (0,is_hotkey__WEBPACK_IMPORTED_MODULE_5__.isHotkey)(apple);\n  var isWindows = windows && (0,is_hotkey__WEBPACK_IMPORTED_MODULE_5__.isHotkey)(windows);\n  return event => {\n    if (isGeneric && isGeneric(event)) return true;\n    if (IS_APPLE && isApple && isApple(event)) return true;\n    if (!IS_APPLE && isWindows && isWindows(event)) return true;\n    return false;\n  };\n};\n/**\n * Hotkeys.\n */\nvar Hotkeys = {\n  isBold: create('bold'),\n  isCompose: create('compose'),\n  isMoveBackward: create('moveBackward'),\n  isMoveForward: create('moveForward'),\n  isDeleteBackward: create('deleteBackward'),\n  isDeleteForward: create('deleteForward'),\n  isDeleteLineBackward: create('deleteLineBackward'),\n  isDeleteLineForward: create('deleteLineForward'),\n  isDeleteWordBackward: create('deleteWordBackward'),\n  isDeleteWordForward: create('deleteWordForward'),\n  isExtendBackward: create('extendBackward'),\n  isExtendForward: create('extendForward'),\n  isExtendLineBackward: create('extendLineBackward'),\n  isExtendLineForward: create('extendLineForward'),\n  isItalic: create('italic'),\n  isMoveLineBackward: create('moveLineBackward'),\n  isMoveLineForward: create('moveLineForward'),\n  isMoveWordBackward: create('moveWordBackward'),\n  isMoveWordForward: create('moveWordForward'),\n  isRedo: create('redo'),\n  isSoftBreak: create('insertSoftBreak'),\n  isSplitBlock: create('splitBlock'),\n  isTransposeCharacter: create('transposeCharacter'),\n  isUndo: create('undo')\n};\n\nvar createRestoreDomManager = (editor, receivedUserInput) => {\n  var bufferedMutations = [];\n  var clear = () => {\n    bufferedMutations = [];\n  };\n  var registerMutations = mutations => {\n    if (!receivedUserInput.current) {\n      return;\n    }\n    var trackedMutations = mutations.filter(mutation => isTrackedMutation(editor, mutation, mutations));\n    bufferedMutations.push(...trackedMutations);\n  };\n  function restoreDOM() {\n    if (bufferedMutations.length > 0) {\n      bufferedMutations.reverse().forEach(mutation => {\n        if (mutation.type === 'characterData') {\n          // We don't want to restore the DOM for characterData mutations\n          // because this interrupts the composition.\n          return;\n        }\n        mutation.removedNodes.forEach(node => {\n          mutation.target.insertBefore(node, mutation.nextSibling);\n        });\n        mutation.addedNodes.forEach(node => {\n          mutation.target.removeChild(node);\n        });\n      });\n      // Clear buffered mutations to ensure we don't undo them twice\n      clear();\n    }\n  }\n  return {\n    registerMutations,\n    restoreDOM,\n    clear\n  };\n};\n\nvar MUTATION_OBSERVER_CONFIG = {\n  subtree: true,\n  childList: true,\n  characterData: true,\n  characterDataOldValue: true\n};\n// We have to use a class component here since we rely on `getSnapshotBeforeUpdate` which has no FC equivalent\n// to run code synchronously immediately before react commits the component update to the DOM.\nclass RestoreDOMComponent extends react__WEBPACK_IMPORTED_MODULE_3__.Component {\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"context\", null);\n    _defineProperty(this, \"manager\", null);\n    _defineProperty(this, \"mutationObserver\", null);\n  }\n  observe() {\n    var _this$mutationObserve;\n    var {\n      node\n    } = this.props;\n    if (!node.current) {\n      throw new Error('Failed to attach MutationObserver, `node` is undefined');\n    }\n    (_this$mutationObserve = this.mutationObserver) === null || _this$mutationObserve === void 0 || _this$mutationObserve.observe(node.current, MUTATION_OBSERVER_CONFIG);\n  }\n  componentDidMount() {\n    var {\n      receivedUserInput\n    } = this.props;\n    var editor = this.context;\n    this.manager = createRestoreDomManager(editor, receivedUserInput);\n    this.mutationObserver = new MutationObserver(this.manager.registerMutations);\n    this.observe();\n  }\n  getSnapshotBeforeUpdate() {\n    var _this$mutationObserve2, _this$mutationObserve3, _this$manager2;\n    var pendingMutations = (_this$mutationObserve2 = this.mutationObserver) === null || _this$mutationObserve2 === void 0 ? void 0 : _this$mutationObserve2.takeRecords();\n    if (pendingMutations !== null && pendingMutations !== void 0 && pendingMutations.length) {\n      var _this$manager;\n      (_this$manager = this.manager) === null || _this$manager === void 0 || _this$manager.registerMutations(pendingMutations);\n    }\n    (_this$mutationObserve3 = this.mutationObserver) === null || _this$mutationObserve3 === void 0 || _this$mutationObserve3.disconnect();\n    (_this$manager2 = this.manager) === null || _this$manager2 === void 0 || _this$manager2.restoreDOM();\n    return null;\n  }\n  componentDidUpdate() {\n    var _this$manager3;\n    (_this$manager3 = this.manager) === null || _this$manager3 === void 0 || _this$manager3.clear();\n    this.observe();\n  }\n  componentWillUnmount() {\n    var _this$mutationObserve4;\n    (_this$mutationObserve4 = this.mutationObserver) === null || _this$mutationObserve4 === void 0 || _this$mutationObserve4.disconnect();\n  }\n  render() {\n    return this.props.children;\n  }\n}\n_defineProperty(RestoreDOMComponent, \"contextType\", EditorContext);\nvar RestoreDOM = IS_ANDROID ? RestoreDOMComponent : _ref => {\n  var {\n    children\n  } = _ref;\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement((react__WEBPACK_IMPORTED_MODULE_3___default().Fragment), null, children);\n};\n\n/**\n * A React context for sharing the `composing` state of the editor.\n */\nvar ComposingContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(false);\n/**\n * Get the current `composing` state of the editor.\n */\nvar useComposing = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(ComposingContext);\n};\n\nvar _excluded$1 = [\"autoFocus\", \"decorate\", \"onDOMBeforeInput\", \"placeholder\", \"readOnly\", \"renderElement\", \"renderLeaf\", \"renderPlaceholder\", \"scrollSelectionIntoView\", \"style\", \"as\", \"disableDefaultStyles\"],\n  _excluded2 = [\"text\"];\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nvar Children = props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement((react__WEBPACK_IMPORTED_MODULE_3___default().Fragment), null, useChildren(props));\n/**\n * Editable.\n */\nvar Editable = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.forwardRef)((props, forwardedRef) => {\n  var defaultRenderPlaceholder = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(props => /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(DefaultPlaceholder, _objectSpread$1({}, props)), []);\n  var {\n      autoFocus,\n      decorate = defaultDecorate,\n      onDOMBeforeInput: propsOnDOMBeforeInput,\n      placeholder,\n      readOnly = false,\n      renderElement,\n      renderLeaf,\n      renderPlaceholder = defaultRenderPlaceholder,\n      scrollSelectionIntoView = defaultScrollSelectionIntoView,\n      style: userStyle = {},\n      as: Component = 'div',\n      disableDefaultStyles = false\n    } = props,\n    attributes = _objectWithoutProperties(props, _excluded$1);\n  var editor = useSlate();\n  // Rerender editor when composition status changed\n  var [isComposing, setIsComposing] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(false);\n  var ref = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var deferredOperations = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)([]);\n  var [placeholderHeight, setPlaceholderHeight] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)();\n  var processing = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(false);\n  var {\n    onUserInput,\n    receivedUserInput\n  } = useTrackUserInput();\n  var [, forceRender] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useReducer)(s => s + 1, 0);\n  EDITOR_TO_FORCE_RENDER.set(editor, forceRender);\n  // Update internal state on each render.\n  IS_READ_ONLY.set(editor, readOnly);\n  // Keep track of some state for the event handler logic.\n  var state = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(() => ({\n    isDraggingInternally: false,\n    isUpdatingSelection: false,\n    latestElement: null,\n    hasMarkPlaceholder: false\n  }), []);\n  // The autoFocus TextareaHTMLAttribute doesn't do anything on a div, so it\n  // needs to be manually focused.\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    if (ref.current && autoFocus) {\n      ref.current.focus();\n    }\n  }, [autoFocus]);\n  /**\n   * The AndroidInputManager object has a cyclical dependency on onDOMSelectionChange\n   *\n   * It is defined as a reference to simplify hook dependencies and clarify that\n   * it needs to be initialized.\n   */\n  var androidInputManagerRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)();\n  // Listen on the native `selectionchange` event to be able to update any time\n  // the selection changes. This is required because React's `onSelect` is leaky\n  // and non-standard so it doesn't fire until after a selection has been\n  // released. This causes issues in situations where another change happens\n  // while a selection is being dragged.\n  var onDOMSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(() => lodash_throttle__WEBPACK_IMPORTED_MODULE_2___default()(() => {\n    if (IS_NODE_MAP_DIRTY.get(editor)) {\n      onDOMSelectionChange();\n      return;\n    }\n    var el = ReactEditor.toDOMNode(editor, editor);\n    var root = el.getRootNode();\n    if (!processing.current && IS_WEBKIT && root instanceof ShadowRoot) {\n      processing.current = true;\n      var active = getActiveElement();\n      if (active) {\n        document.execCommand('indent');\n      } else {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.deselect(editor);\n      }\n      processing.current = false;\n      return;\n    }\n    var androidInputManager = androidInputManagerRef.current;\n    if ((IS_ANDROID || !ReactEditor.isComposing(editor)) && (!state.isUpdatingSelection || androidInputManager !== null && androidInputManager !== void 0 && androidInputManager.isFlushing()) && !state.isDraggingInternally) {\n      var _root = ReactEditor.findDocumentOrShadowRoot(editor);\n      var {\n        activeElement\n      } = _root;\n      var _el = ReactEditor.toDOMNode(editor, editor);\n      var domSelection = getSelection(_root);\n      if (activeElement === _el) {\n        state.latestElement = activeElement;\n        IS_FOCUSED.set(editor, true);\n      } else {\n        IS_FOCUSED.delete(editor);\n      }\n      if (!domSelection) {\n        return slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.deselect(editor);\n      }\n      var {\n        anchorNode,\n        focusNode\n      } = domSelection;\n      var anchorNodeSelectable = ReactEditor.hasEditableTarget(editor, anchorNode) || ReactEditor.isTargetInsideNonReadonlyVoid(editor, anchorNode);\n      var focusNodeInEditor = ReactEditor.hasTarget(editor, focusNode);\n      if (anchorNodeSelectable && focusNodeInEditor) {\n        var range = ReactEditor.toSlateRange(editor, domSelection, {\n          exactMatch: false,\n          suppressThrow: true\n        });\n        if (range) {\n          if (!ReactEditor.isComposing(editor) && !(androidInputManager !== null && androidInputManager !== void 0 && androidInputManager.hasPendingChanges()) && !(androidInputManager !== null && androidInputManager !== void 0 && androidInputManager.isFlushing())) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, range);\n          } else {\n            androidInputManager === null || androidInputManager === void 0 || androidInputManager.handleUserSelect(range);\n          }\n        }\n      }\n      // Deselect the editor if the dom selection is not selectable in readonly mode\n      if (readOnly && (!anchorNodeSelectable || !focusNodeInEditor)) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.deselect(editor);\n      }\n    }\n  }, 100), [editor, readOnly, state]);\n  var scheduleOnDOMSelectionChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(() => lodash_debounce__WEBPACK_IMPORTED_MODULE_1___default()(onDOMSelectionChange, 0), [onDOMSelectionChange]);\n  androidInputManagerRef.current = useAndroidInputManager({\n    node: ref,\n    onDOMSelectionChange,\n    scheduleOnDOMSelectionChange\n  });\n  useIsomorphicLayoutEffect(() => {\n    var _androidInputManagerR, _androidInputManagerR2;\n    // Update element-related weak maps with the DOM element ref.\n    var window;\n    if (ref.current && (window = getDefaultView(ref.current))) {\n      EDITOR_TO_WINDOW.set(editor, window);\n      EDITOR_TO_ELEMENT.set(editor, ref.current);\n      NODE_TO_ELEMENT.set(editor, ref.current);\n      ELEMENT_TO_NODE.set(ref.current, editor);\n    } else {\n      NODE_TO_ELEMENT.delete(editor);\n    }\n    // Make sure the DOM selection state is in sync.\n    var {\n      selection\n    } = editor;\n    var root = ReactEditor.findDocumentOrShadowRoot(editor);\n    var domSelection = getSelection(root);\n    if (!domSelection || !ReactEditor.isFocused(editor) || (_androidInputManagerR = androidInputManagerRef.current) !== null && _androidInputManagerR !== void 0 && _androidInputManagerR.hasPendingAction()) {\n      return;\n    }\n    var setDomSelection = forceChange => {\n      var hasDomSelection = domSelection.type !== 'None';\n      // If the DOM selection is properly unset, we're done.\n      if (!selection && !hasDomSelection) {\n        return;\n      }\n      // Get anchorNode and focusNode\n      var focusNode = domSelection.focusNode;\n      var anchorNode;\n      // COMPAT: In firefox the normal seletion way does not work\n      // (https://github.com/ianstormtaylor/slate/pull/5486#issue-1820720223)\n      if (IS_FIREFOX && domSelection.rangeCount > 1) {\n        var firstRange = domSelection.getRangeAt(0);\n        var lastRange = domSelection.getRangeAt(domSelection.rangeCount - 1);\n        // Right to left\n        if (firstRange.startContainer === focusNode) {\n          anchorNode = lastRange.endContainer;\n        } else {\n          // Left to right\n          anchorNode = firstRange.startContainer;\n        }\n      } else {\n        anchorNode = domSelection.anchorNode;\n      }\n      // verify that the dom selection is in the editor\n      var editorElement = EDITOR_TO_ELEMENT.get(editor);\n      var hasDomSelectionInEditor = false;\n      if (editorElement.contains(anchorNode) && editorElement.contains(focusNode)) {\n        hasDomSelectionInEditor = true;\n      }\n      // If the DOM selection is in the editor and the editor selection is already correct, we're done.\n      if (hasDomSelection && hasDomSelectionInEditor && selection && !forceChange) {\n        var slateRange = ReactEditor.toSlateRange(editor, domSelection, {\n          exactMatch: true,\n          // domSelection is not necessarily a valid Slate range\n          // (e.g. when clicking on contentEditable:false element)\n          suppressThrow: true\n        });\n        if (slateRange && slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(slateRange, selection)) {\n          var _anchorNode;\n          if (!state.hasMarkPlaceholder) {\n            return;\n          }\n          // Ensure selection is inside the mark placeholder\n          if ((_anchorNode = anchorNode) !== null && _anchorNode !== void 0 && (_anchorNode = _anchorNode.parentElement) !== null && _anchorNode !== void 0 && _anchorNode.hasAttribute('data-slate-mark-placeholder')) {\n            return;\n          }\n        }\n      }\n      // when <Editable/> is being controlled through external value\n      // then its children might just change - DOM responds to it on its own\n      // but Slate's value is not being updated through any operation\n      // and thus it doesn't transform selection on its own\n      if (selection && !ReactEditor.hasRange(editor, selection)) {\n        editor.selection = ReactEditor.toSlateRange(editor, domSelection, {\n          exactMatch: false,\n          suppressThrow: true\n        });\n        return;\n      }\n      // Otherwise the DOM selection is out of sync, so update it.\n      state.isUpdatingSelection = true;\n      var newDomRange = selection && ReactEditor.toDOMRange(editor, selection);\n      if (newDomRange) {\n        if (ReactEditor.isComposing(editor) && !IS_ANDROID) {\n          domSelection.collapseToEnd();\n        } else if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isBackward(selection)) {\n          domSelection.setBaseAndExtent(newDomRange.endContainer, newDomRange.endOffset, newDomRange.startContainer, newDomRange.startOffset);\n        } else {\n          domSelection.setBaseAndExtent(newDomRange.startContainer, newDomRange.startOffset, newDomRange.endContainer, newDomRange.endOffset);\n        }\n        scrollSelectionIntoView(editor, newDomRange);\n      } else {\n        domSelection.removeAllRanges();\n      }\n      return newDomRange;\n    };\n    // In firefox if there is more then 1 range and we call setDomSelection we remove the ability to select more cells in a table\n    if (domSelection.rangeCount <= 1) {\n      setDomSelection();\n    }\n    var ensureSelection = ((_androidInputManagerR2 = androidInputManagerRef.current) === null || _androidInputManagerR2 === void 0 ? void 0 : _androidInputManagerR2.isFlushing()) === 'action';\n    if (!IS_ANDROID || !ensureSelection) {\n      setTimeout(() => {\n        state.isUpdatingSelection = false;\n      });\n      return;\n    }\n    var timeoutId = null;\n    var animationFrameId = requestAnimationFrame(() => {\n      if (ensureSelection) {\n        var ensureDomSelection = forceChange => {\n          try {\n            var el = ReactEditor.toDOMNode(editor, editor);\n            el.focus();\n            setDomSelection(forceChange);\n          } catch (e) {\n            // Ignore, dom and state might be out of sync\n          }\n        };\n        // Compat: Android IMEs try to force their selection by manually re-applying it even after we set it.\n        // This essentially would make setting the slate selection during an update meaningless, so we force it\n        // again here. We can't only do it in the setTimeout after the animation frame since that would cause a\n        // visible flicker.\n        ensureDomSelection();\n        timeoutId = setTimeout(() => {\n          // COMPAT: While setting the selection in an animation frame visually correctly sets the selection,\n          // it doesn't update GBoards spellchecker state. We have to manually trigger a selection change after\n          // the animation frame to ensure it displays the correct state.\n          ensureDomSelection(true);\n          state.isUpdatingSelection = false;\n        });\n      }\n    });\n    return () => {\n      cancelAnimationFrame(animationFrameId);\n      if (timeoutId) {\n        clearTimeout(timeoutId);\n      }\n    };\n  });\n  // Listen on the native `beforeinput` event to get real \"Level 2\" events. This\n  // is required because React's `beforeinput` is fake and never really attaches\n  // to the real event sadly. (2019/11/01)\n  // https://github.com/facebook/react/issues/11211\n  var onDOMBeforeInput = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n    var el = ReactEditor.toDOMNode(editor, editor);\n    var root = el.getRootNode();\n    if (processing !== null && processing !== void 0 && processing.current && IS_WEBKIT && root instanceof ShadowRoot) {\n      var ranges = event.getTargetRanges();\n      var range = ranges[0];\n      var newRange = new window.Range();\n      newRange.setStart(range.startContainer, range.startOffset);\n      newRange.setEnd(range.endContainer, range.endOffset);\n      // Translate the DOM Range into a Slate Range\n      var slateRange = ReactEditor.toSlateRange(editor, newRange, {\n        exactMatch: false,\n        suppressThrow: false\n      });\n      slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, slateRange);\n      event.preventDefault();\n      event.stopImmediatePropagation();\n      return;\n    }\n    onUserInput();\n    if (!readOnly && ReactEditor.hasEditableTarget(editor, event.target) && !isDOMEventHandled(event, propsOnDOMBeforeInput)) {\n      var _EDITOR_TO_USER_SELEC;\n      // COMPAT: BeforeInput events aren't cancelable on android, so we have to handle them differently using the android input manager.\n      if (androidInputManagerRef.current) {\n        return androidInputManagerRef.current.handleDOMBeforeInput(event);\n      }\n      // Some IMEs/Chrome extensions like e.g. Grammarly set the selection immediately before\n      // triggering a `beforeinput` expecting the change to be applied to the immediately before\n      // set selection.\n      scheduleOnDOMSelectionChange.flush();\n      onDOMSelectionChange.flush();\n      var {\n        selection\n      } = editor;\n      var {\n        inputType: type\n      } = event;\n      var data = event.dataTransfer || event.data || undefined;\n      var isCompositionChange = type === 'insertCompositionText' || type === 'deleteCompositionText';\n      // COMPAT: use composition change events as a hint to where we should insert\n      // composition text if we aren't composing to work around https://github.com/ianstormtaylor/slate/issues/5038\n      if (isCompositionChange && ReactEditor.isComposing(editor)) {\n        return;\n      }\n      var native = false;\n      if (type === 'insertText' && selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(selection) &&\n      // Only use native character insertion for single characters a-z or space for now.\n      // Long-press events (hold a + press 4 = ä) to choose a special character otherwise\n      // causes duplicate inserts.\n      event.data && event.data.length === 1 && /[a-z ]/i.test(event.data) &&\n      // Chrome has issues correctly editing the start of nodes: https://bugs.chromium.org/p/chromium/issues/detail?id=1249405\n      // When there is an inline element, e.g. a link, and you select\n      // right after it (the start of the next node).\n      selection.anchor.offset !== 0) {\n        native = true;\n        // Skip native if there are marks, as\n        // `insertText` will insert a node, not just text.\n        if (editor.marks) {\n          native = false;\n        }\n        // If the NODE_MAP is dirty, we can't trust the selection anchor (eg ReactEditor.toDOMPoint)\n        if (!IS_NODE_MAP_DIRTY.get(editor)) {\n          var _node$parentElement, _window$getComputedSt;\n          // Chrome also has issues correctly editing the end of anchor elements: https://bugs.chromium.org/p/chromium/issues/detail?id=1259100\n          // Therefore we don't allow native events to insert text at the end of anchor nodes.\n          var {\n            anchor\n          } = selection;\n          var [node, offset] = ReactEditor.toDOMPoint(editor, anchor);\n          var anchorNode = (_node$parentElement = node.parentElement) === null || _node$parentElement === void 0 ? void 0 : _node$parentElement.closest('a');\n          var _window = ReactEditor.getWindow(editor);\n          if (native && anchorNode && ReactEditor.hasDOMNode(editor, anchorNode)) {\n            var _lastText$textContent;\n            // Find the last text node inside the anchor.\n            var lastText = _window === null || _window === void 0 ? void 0 : _window.document.createTreeWalker(anchorNode, NodeFilter.SHOW_TEXT).lastChild();\n            if (lastText === node && ((_lastText$textContent = lastText.textContent) === null || _lastText$textContent === void 0 ? void 0 : _lastText$textContent.length) === offset) {\n              native = false;\n            }\n          }\n          // Chrome has issues with the presence of tab characters inside elements with whiteSpace = 'pre'\n          // causing abnormal insert behavior: https://bugs.chromium.org/p/chromium/issues/detail?id=1219139\n          if (native && node.parentElement && (_window === null || _window === void 0 || (_window$getComputedSt = _window.getComputedStyle(node.parentElement)) === null || _window$getComputedSt === void 0 ? void 0 : _window$getComputedSt.whiteSpace) === 'pre') {\n            var block = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.above(editor, {\n              at: anchor.path,\n              match: n => slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(n) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(editor, n)\n            });\n            if (block && slate__WEBPACK_IMPORTED_MODULE_7__.Node.string(block[0]).includes('\\t')) {\n              native = false;\n            }\n          }\n        }\n      }\n      // COMPAT: For the deleting forward/backward input types we don't want\n      // to change the selection because it is the range that will be deleted,\n      // and those commands determine that for themselves.\n      // If the NODE_MAP is dirty, we can't trust the selection anchor (eg ReactEditor.toDOMPoint via ReactEditor.toSlateRange)\n      if ((!type.startsWith('delete') || type.startsWith('deleteBy')) && !IS_NODE_MAP_DIRTY.get(editor)) {\n        var [targetRange] = event.getTargetRanges();\n        if (targetRange) {\n          var _range = ReactEditor.toSlateRange(editor, targetRange, {\n            exactMatch: false,\n            suppressThrow: false\n          });\n          if (!selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(selection, _range)) {\n            native = false;\n            var selectionRef = !isCompositionChange && editor.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.rangeRef(editor, editor.selection);\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, _range);\n            if (selectionRef) {\n              EDITOR_TO_USER_SELECTION.set(editor, selectionRef);\n            }\n          }\n        }\n      }\n      // Composition change types occur while a user is composing text and can't be\n      // cancelled. Let them through and wait for the composition to end.\n      if (isCompositionChange) {\n        return;\n      }\n      if (!native) {\n        event.preventDefault();\n      }\n      // COMPAT: If the selection is expanded, even if the command seems like\n      // a delete forward/backward command it should delete the selection.\n      if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection) && type.startsWith('delete')) {\n        var direction = type.endsWith('Backward') ? 'backward' : 'forward';\n        slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n          direction\n        });\n        return;\n      }\n      switch (type) {\n        case 'deleteByComposition':\n        case 'deleteByCut':\n        case 'deleteByDrag':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor);\n            break;\n          }\n        case 'deleteContent':\n        case 'deleteContentForward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor);\n            break;\n          }\n        case 'deleteContentBackward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor);\n            break;\n          }\n        case 'deleteEntireSoftLine':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n              unit: 'line'\n            });\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n              unit: 'line'\n            });\n            break;\n          }\n        case 'deleteHardLineBackward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n              unit: 'block'\n            });\n            break;\n          }\n        case 'deleteSoftLineBackward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n              unit: 'line'\n            });\n            break;\n          }\n        case 'deleteHardLineForward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n              unit: 'block'\n            });\n            break;\n          }\n        case 'deleteSoftLineForward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n              unit: 'line'\n            });\n            break;\n          }\n        case 'deleteWordBackward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n              unit: 'word'\n            });\n            break;\n          }\n        case 'deleteWordForward':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n              unit: 'word'\n            });\n            break;\n          }\n        case 'insertLineBreak':\n          slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertSoftBreak(editor);\n          break;\n        case 'insertParagraph':\n          {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertBreak(editor);\n            break;\n          }\n        case 'insertFromComposition':\n        case 'insertFromDrop':\n        case 'insertFromPaste':\n        case 'insertFromYank':\n        case 'insertReplacementText':\n        case 'insertText':\n          {\n            if (type === 'insertFromComposition') {\n              // COMPAT: in Safari, `compositionend` is dispatched after the\n              // `beforeinput` for \"insertFromComposition\". But if we wait for it\n              // then we will abort because we're still composing and the selection\n              // won't be updated properly.\n              // https://www.w3.org/TR/input-events-2/\n              if (ReactEditor.isComposing(editor)) {\n                setIsComposing(false);\n                IS_COMPOSING.set(editor, false);\n              }\n            }\n            // use a weak comparison instead of 'instanceof' to allow\n            // programmatic access of paste events coming from external windows\n            // like cypress where cy.window does not work realibly\n            if ((data === null || data === void 0 ? void 0 : data.constructor.name) === 'DataTransfer') {\n              ReactEditor.insertData(editor, data);\n            } else if (typeof data === 'string') {\n              // Only insertText operations use the native functionality, for now.\n              // Potentially expand to single character deletes, as well.\n              if (native) {\n                deferredOperations.current.push(() => slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, data));\n              } else {\n                slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, data);\n              }\n            }\n            break;\n          }\n      }\n      // Restore the actual user section if nothing manually set it.\n      var toRestore = (_EDITOR_TO_USER_SELEC = EDITOR_TO_USER_SELECTION.get(editor)) === null || _EDITOR_TO_USER_SELEC === void 0 ? void 0 : _EDITOR_TO_USER_SELEC.unref();\n      EDITOR_TO_USER_SELECTION.delete(editor);\n      if (toRestore && (!editor.selection || !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(editor.selection, toRestore))) {\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, toRestore);\n      }\n    }\n  }, [editor, onDOMSelectionChange, onUserInput, propsOnDOMBeforeInput, readOnly, scheduleOnDOMSelectionChange]);\n  var callbackRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(node => {\n    if (node == null) {\n      onDOMSelectionChange.cancel();\n      scheduleOnDOMSelectionChange.cancel();\n      EDITOR_TO_ELEMENT.delete(editor);\n      NODE_TO_ELEMENT.delete(editor);\n      if (ref.current && HAS_BEFORE_INPUT_SUPPORT) {\n        // @ts-ignore The `beforeinput` event isn't recognized.\n        ref.current.removeEventListener('beforeinput', onDOMBeforeInput);\n      }\n    } else {\n      // Attach a native DOM event handler for `beforeinput` events, because React's\n      // built-in `onBeforeInput` is actually a leaky polyfill that doesn't expose\n      // real `beforeinput` events sadly... (2019/11/04)\n      // https://github.com/facebook/react/issues/11211\n      if (HAS_BEFORE_INPUT_SUPPORT) {\n        // @ts-ignore The `beforeinput` event isn't recognized.\n        node.addEventListener('beforeinput', onDOMBeforeInput);\n      }\n    }\n    ref.current = node;\n    if (typeof forwardedRef === 'function') {\n      forwardedRef(node);\n    } else if (forwardedRef) {\n      forwardedRef.current = node;\n    }\n  }, [onDOMSelectionChange, scheduleOnDOMSelectionChange, editor, onDOMBeforeInput, forwardedRef]);\n  useIsomorphicLayoutEffect(() => {\n    var window = ReactEditor.getWindow(editor);\n    // Attach a native DOM event handler for `selectionchange`, because React's\n    // built-in `onSelect` handler doesn't fire for all selection changes. It's\n    // a leaky polyfill that only fires on keypresses or clicks. Instead, we\n    // want to fire for any change to the selection inside the editor.\n    // (2019/11/04) https://github.com/facebook/react/issues/5785\n    window.document.addEventListener('selectionchange', scheduleOnDOMSelectionChange);\n    // Listen for dragend and drop globally. In Firefox, if a drop handler\n    // initiates an operation that causes the originally dragged element to\n    // unmount, that element will not emit a dragend event. (2024/06/21)\n    var stoppedDragging = () => {\n      state.isDraggingInternally = false;\n    };\n    window.document.addEventListener('dragend', stoppedDragging);\n    window.document.addEventListener('drop', stoppedDragging);\n    return () => {\n      window.document.removeEventListener('selectionchange', scheduleOnDOMSelectionChange);\n      window.document.removeEventListener('dragend', stoppedDragging);\n      window.document.removeEventListener('drop', stoppedDragging);\n    };\n  }, [scheduleOnDOMSelectionChange, state]);\n  var decorations = decorate([editor, []]);\n  var showPlaceholder = placeholder && editor.children.length === 1 && Array.from(slate__WEBPACK_IMPORTED_MODULE_7__.Node.texts(editor)).length === 1 && slate__WEBPACK_IMPORTED_MODULE_7__.Node.string(editor) === '' && !isComposing;\n  var placeHolderResizeHandler = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(placeholderEl => {\n    if (placeholderEl && showPlaceholder) {\n      var _placeholderEl$getBou;\n      setPlaceholderHeight((_placeholderEl$getBou = placeholderEl.getBoundingClientRect()) === null || _placeholderEl$getBou === void 0 ? void 0 : _placeholderEl$getBou.height);\n    } else {\n      setPlaceholderHeight(undefined);\n    }\n  }, [showPlaceholder]);\n  if (showPlaceholder) {\n    var start = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.start(editor, []);\n    decorations.push({\n      [PLACEHOLDER_SYMBOL]: true,\n      placeholder,\n      onPlaceholderResize: placeHolderResizeHandler,\n      anchor: start,\n      focus: start\n    });\n  }\n  var {\n    marks\n  } = editor;\n  state.hasMarkPlaceholder = false;\n  if (editor.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(editor.selection) && marks) {\n    var {\n      anchor\n    } = editor.selection;\n    var leaf = slate__WEBPACK_IMPORTED_MODULE_7__.Node.leaf(editor, anchor.path);\n    var rest = _objectWithoutProperties(leaf, _excluded2);\n    // While marks isn't a 'complete' text, we can still use loose Text.equals\n    // here which only compares marks anyway.\n    if (!slate__WEBPACK_IMPORTED_MODULE_7__.Text.equals(leaf, marks, {\n      loose: true\n    })) {\n      state.hasMarkPlaceholder = true;\n      var unset = Object.fromEntries(Object.keys(rest).map(mark => [mark, null]));\n      decorations.push(_objectSpread$1(_objectSpread$1(_objectSpread$1({\n        [MARK_PLACEHOLDER_SYMBOL]: true\n      }, unset), marks), {}, {\n        anchor,\n        focus: anchor\n      }));\n    }\n  }\n  // Update EDITOR_TO_MARK_PLACEHOLDER_MARKS in setTimeout useEffect to ensure we don't set it\n  // before we receive the composition end event.\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    setTimeout(() => {\n      var {\n        selection\n      } = editor;\n      if (selection) {\n        var {\n          anchor: _anchor\n        } = selection;\n        var _text = slate__WEBPACK_IMPORTED_MODULE_7__.Node.leaf(editor, _anchor.path);\n        // While marks isn't a 'complete' text, we can still use loose Text.equals\n        // here which only compares marks anyway.\n        if (marks && !slate__WEBPACK_IMPORTED_MODULE_7__.Text.equals(_text, marks, {\n          loose: true\n        })) {\n          EDITOR_TO_PENDING_INSERTION_MARKS.set(editor, marks);\n          return;\n        }\n      }\n      EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor);\n    });\n  });\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(ReadOnlyContext.Provider, {\n    value: readOnly\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(ComposingContext.Provider, {\n    value: isComposing\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(DecorateContext.Provider, {\n    value: decorate\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(RestoreDOM, {\n    node: ref,\n    receivedUserInput: receivedUserInput\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(Component, _objectSpread$1(_objectSpread$1({\n    role: readOnly ? undefined : 'textbox',\n    \"aria-multiline\": readOnly ? undefined : true\n  }, attributes), {}, {\n    // COMPAT: Certain browsers don't support the `beforeinput` event, so we'd\n    // have to use hacks to make these replacement-based features work.\n    // For SSR situations HAS_BEFORE_INPUT_SUPPORT is false and results in prop\n    // mismatch warning app moves to browser. Pass-through consumer props when\n    // not CAN_USE_DOM (SSR) and default to falsy value\n    spellCheck: HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM ? attributes.spellCheck : false,\n    autoCorrect: HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM ? attributes.autoCorrect : 'false',\n    autoCapitalize: HAS_BEFORE_INPUT_SUPPORT || !CAN_USE_DOM ? attributes.autoCapitalize : 'false',\n    \"data-slate-editor\": true,\n    \"data-slate-node\": \"value\",\n    // explicitly set this\n    contentEditable: !readOnly,\n    // in some cases, a decoration needs access to the range / selection to decorate a text node,\n    // then you will select the whole text node when you select part the of text\n    // this magic zIndex=\"-1\" will fix it\n    zindex: -1,\n    suppressContentEditableWarning: true,\n    ref: callbackRef,\n    style: _objectSpread$1(_objectSpread$1({}, disableDefaultStyles ? {} : _objectSpread$1({\n      // Allow positioning relative to the editable element.\n      position: 'relative',\n      // Preserve adjacent whitespace and new lines.\n      whiteSpace: 'pre-wrap',\n      // Allow words to break if they are too long.\n      wordWrap: 'break-word'\n    }, placeholderHeight ? {\n      minHeight: placeholderHeight\n    } : {})), userStyle),\n    onBeforeInput: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n      // fall back to React's leaky polyfill instead just for it. It\n      // only works for the `insertText` input type.\n      if (!HAS_BEFORE_INPUT_SUPPORT && !readOnly && !isEventHandled(event, attributes.onBeforeInput) && ReactEditor.hasSelectableTarget(editor, event.target)) {\n        event.preventDefault();\n        if (!ReactEditor.isComposing(editor)) {\n          var _text2 = event.data;\n          slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, _text2);\n        }\n      }\n    }, [attributes.onBeforeInput, editor, readOnly]),\n    onInput: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (isEventHandled(event, attributes.onInput)) {\n        return;\n      }\n      if (androidInputManagerRef.current) {\n        androidInputManagerRef.current.handleInput();\n        return;\n      }\n      // Flush native operations, as native events will have propogated\n      // and we can correctly compare DOM text values in components\n      // to stop rendering, so that browser functions like autocorrect\n      // and spellcheck work as expected.\n      for (var op of deferredOperations.current) {\n        op();\n      }\n      deferredOperations.current = [];\n      // COMPAT: Since `beforeinput` doesn't fully `preventDefault`,\n      // there's a chance that content might be placed in the browser's undo stack.\n      // This means undo can be triggered even when the div is not focused,\n      // and it only triggers the input event for the node. (2024/10/09)\n      if (!ReactEditor.isFocused(editor)) {\n        var native = event.nativeEvent;\n        var maybeHistoryEditor = editor;\n        if (native.inputType === 'historyUndo' && typeof maybeHistoryEditor.undo === 'function') {\n          maybeHistoryEditor.undo();\n          return;\n        }\n        if (native.inputType === 'historyRedo' && typeof maybeHistoryEditor.redo === 'function') {\n          maybeHistoryEditor.redo();\n          return;\n        }\n      }\n    }, [attributes.onInput, editor]),\n    onBlur: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (readOnly || state.isUpdatingSelection || !ReactEditor.hasSelectableTarget(editor, event.target) || isEventHandled(event, attributes.onBlur)) {\n        return;\n      }\n      // COMPAT: If the current `activeElement` is still the previous\n      // one, this is due to the window being blurred when the tab\n      // itself becomes unfocused, so we want to abort early to allow to\n      // editor to stay focused when the tab becomes focused again.\n      var root = ReactEditor.findDocumentOrShadowRoot(editor);\n      if (state.latestElement === root.activeElement) {\n        return;\n      }\n      var {\n        relatedTarget\n      } = event;\n      var el = ReactEditor.toDOMNode(editor, editor);\n      // COMPAT: The event should be ignored if the focus is returning\n      // to the editor from an embedded editable element (eg. an <input>\n      // element inside a void node).\n      if (relatedTarget === el) {\n        return;\n      }\n      // COMPAT: The event should be ignored if the focus is moving from\n      // the editor to inside a void node's spacer element.\n      if (isDOMElement(relatedTarget) && relatedTarget.hasAttribute('data-slate-spacer')) {\n        return;\n      }\n      // COMPAT: The event should be ignored if the focus is moving to a\n      // non- editable section of an element that isn't a void node (eg.\n      // a list item of the check list example).\n      if (relatedTarget != null && isDOMNode(relatedTarget) && ReactEditor.hasDOMNode(editor, relatedTarget)) {\n        var node = ReactEditor.toSlateNode(editor, relatedTarget);\n        if (slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && !editor.isVoid(node)) {\n          return;\n        }\n      }\n      // COMPAT: Safari doesn't always remove the selection even if the content-\n      // editable element no longer has focus. Refer to:\n      // https://stackoverflow.com/questions/12353247/force-contenteditable-div-to-stop-accepting-input-after-it-loses-focus-under-web\n      if (IS_WEBKIT) {\n        var domSelection = getSelection(root);\n        domSelection === null || domSelection === void 0 || domSelection.removeAllRanges();\n      }\n      IS_FOCUSED.delete(editor);\n    }, [readOnly, state.isUpdatingSelection, state.latestElement, editor, attributes.onBlur]),\n    onClick: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasTarget(editor, event.target) && !isEventHandled(event, attributes.onClick) && isDOMNode(event.target)) {\n        var node = ReactEditor.toSlateNode(editor, event.target);\n        var path = ReactEditor.findPath(editor, node);\n        // At this time, the Slate document may be arbitrarily different,\n        // because onClick handlers can change the document before we get here.\n        // Therefore we must check that this path actually exists,\n        // and that it still refers to the same node.\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Editor.hasPath(editor, path) || slate__WEBPACK_IMPORTED_MODULE_7__.Node.get(editor, path) !== node) {\n          return;\n        }\n        if (event.detail === TRIPLE_CLICK && path.length >= 1) {\n          var blockPath = path;\n          if (!(slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(editor, node))) {\n            var _block$;\n            var block = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.above(editor, {\n              match: n => slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(n) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(editor, n),\n              at: path\n            });\n            blockPath = (_block$ = block === null || block === void 0 ? void 0 : block[1]) !== null && _block$ !== void 0 ? _block$ : path.slice(0, 1);\n          }\n          var range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, blockPath);\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, range);\n          return;\n        }\n        if (readOnly) {\n          return;\n        }\n        var _start = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.start(editor, path);\n        var end = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.end(editor, path);\n        var startVoid = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n          at: _start\n        });\n        var endVoid = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n          at: end\n        });\n        if (startVoid && endVoid && slate__WEBPACK_IMPORTED_MODULE_7__.Path.equals(startVoid[1], endVoid[1])) {\n          var _range2 = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, _start);\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, _range2);\n        }\n      }\n    }, [editor, attributes.onClick, readOnly]),\n    onCompositionEnd: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasSelectableTarget(editor, event.target)) {\n        var _androidInputManagerR3;\n        if (ReactEditor.isComposing(editor)) {\n          Promise.resolve().then(() => {\n            setIsComposing(false);\n            IS_COMPOSING.set(editor, false);\n          });\n        }\n        (_androidInputManagerR3 = androidInputManagerRef.current) === null || _androidInputManagerR3 === void 0 || _androidInputManagerR3.handleCompositionEnd(event);\n        if (isEventHandled(event, attributes.onCompositionEnd) || IS_ANDROID) {\n          return;\n        }\n        // COMPAT: In Chrome, `beforeinput` events for compositions\n        // aren't correct and never fire the \"insertFromComposition\"\n        // type that we need. So instead, insert whenever a composition\n        // ends since it will already have been committed to the DOM.\n        if (!IS_WEBKIT && !IS_FIREFOX_LEGACY && !IS_IOS && !IS_WECHATBROWSER && !IS_UC_MOBILE && event.data) {\n          var placeholderMarks = EDITOR_TO_PENDING_INSERTION_MARKS.get(editor);\n          EDITOR_TO_PENDING_INSERTION_MARKS.delete(editor);\n          // Ensure we insert text with the marks the user was actually seeing\n          if (placeholderMarks !== undefined) {\n            EDITOR_TO_USER_MARKS.set(editor, editor.marks);\n            editor.marks = placeholderMarks;\n          }\n          slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertText(editor, event.data);\n          var userMarks = EDITOR_TO_USER_MARKS.get(editor);\n          EDITOR_TO_USER_MARKS.delete(editor);\n          if (userMarks !== undefined) {\n            editor.marks = userMarks;\n          }\n        }\n      }\n    }, [attributes.onCompositionEnd, editor]),\n    onCompositionUpdate: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasSelectableTarget(editor, event.target) && !isEventHandled(event, attributes.onCompositionUpdate)) {\n        if (!ReactEditor.isComposing(editor)) {\n          setIsComposing(true);\n          IS_COMPOSING.set(editor, true);\n        }\n      }\n    }, [attributes.onCompositionUpdate, editor]),\n    onCompositionStart: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasSelectableTarget(editor, event.target)) {\n        var _androidInputManagerR4;\n        (_androidInputManagerR4 = androidInputManagerRef.current) === null || _androidInputManagerR4 === void 0 || _androidInputManagerR4.handleCompositionStart(event);\n        if (isEventHandled(event, attributes.onCompositionStart) || IS_ANDROID) {\n          return;\n        }\n        setIsComposing(true);\n        var {\n          selection\n        } = editor;\n        if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n          slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor);\n          return;\n        }\n      }\n    }, [attributes.onCompositionStart, editor]),\n    onCopy: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasSelectableTarget(editor, event.target) && !isEventHandled(event, attributes.onCopy) && !isDOMEventTargetInput(event)) {\n        event.preventDefault();\n        ReactEditor.setFragmentData(editor, event.clipboardData, 'copy');\n      }\n    }, [attributes.onCopy, editor]),\n    onCut: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && ReactEditor.hasSelectableTarget(editor, event.target) && !isEventHandled(event, attributes.onCut) && !isDOMEventTargetInput(event)) {\n        event.preventDefault();\n        ReactEditor.setFragmentData(editor, event.clipboardData, 'cut');\n        var {\n          selection\n        } = editor;\n        if (selection) {\n          if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor);\n          } else {\n            var node = slate__WEBPACK_IMPORTED_MODULE_7__.Node.parent(editor, selection.anchor.path);\n            if (slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, node)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.delete(editor);\n            }\n          }\n        }\n      }\n    }, [readOnly, editor, attributes.onCut]),\n    onDragOver: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (ReactEditor.hasTarget(editor, event.target) && !isEventHandled(event, attributes.onDragOver)) {\n        // Only when the target is void, call `preventDefault` to signal\n        // that drops are allowed. Editable content is droppable by\n        // default, and calling `preventDefault` hides the cursor.\n        var node = ReactEditor.toSlateNode(editor, event.target);\n        if (slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, node)) {\n          event.preventDefault();\n        }\n      }\n    }, [attributes.onDragOver, editor]),\n    onDragStart: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && ReactEditor.hasTarget(editor, event.target) && !isEventHandled(event, attributes.onDragStart)) {\n        var node = ReactEditor.toSlateNode(editor, event.target);\n        var path = ReactEditor.findPath(editor, node);\n        var voidMatch = slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(node) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, node) || slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n          at: path,\n          voids: true\n        });\n        // If starting a drag on a void node, make sure it is selected\n        // so that it shows up in the selection's fragment.\n        if (voidMatch) {\n          var range = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, path);\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, range);\n        }\n        state.isDraggingInternally = true;\n        ReactEditor.setFragmentData(editor, event.dataTransfer, 'drag');\n      }\n    }, [readOnly, editor, attributes.onDragStart, state]),\n    onDrop: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && ReactEditor.hasTarget(editor, event.target) && !isEventHandled(event, attributes.onDrop)) {\n        event.preventDefault();\n        // Keep a reference to the dragged range before updating selection\n        var draggedRange = editor.selection;\n        // Find the range where the drop happened\n        var range = ReactEditor.findEventRange(editor, event);\n        var data = event.dataTransfer;\n        slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.select(editor, range);\n        if (state.isDraggingInternally) {\n          if (draggedRange && !slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(draggedRange, range) && !slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(editor, {\n            at: range,\n            voids: true\n          })) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.delete(editor, {\n              at: draggedRange\n            });\n          }\n        }\n        ReactEditor.insertData(editor, data);\n        // When dragging from another source into the editor, it's possible\n        // that the current editor does not have focus.\n        if (!ReactEditor.isFocused(editor)) {\n          ReactEditor.focus(editor);\n        }\n      }\n    }, [readOnly, editor, attributes.onDrop, state]),\n    onDragEnd: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && state.isDraggingInternally && attributes.onDragEnd && ReactEditor.hasTarget(editor, event.target)) {\n        attributes.onDragEnd(event);\n      }\n    }, [readOnly, state, attributes, editor]),\n    onFocus: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && !state.isUpdatingSelection && ReactEditor.hasEditableTarget(editor, event.target) && !isEventHandled(event, attributes.onFocus)) {\n        var el = ReactEditor.toDOMNode(editor, editor);\n        var root = ReactEditor.findDocumentOrShadowRoot(editor);\n        state.latestElement = root.activeElement;\n        // COMPAT: If the editor has nested editable elements, the focus\n        // can go to them. In Firefox, this must be prevented because it\n        // results in issues with keyboard navigation. (2017/03/30)\n        if (IS_FIREFOX && event.target !== el) {\n          el.focus();\n          return;\n        }\n        IS_FOCUSED.set(editor, true);\n      }\n    }, [readOnly, state, editor, attributes.onFocus]),\n    onKeyDown: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && ReactEditor.hasEditableTarget(editor, event.target)) {\n        var _androidInputManagerR5;\n        (_androidInputManagerR5 = androidInputManagerRef.current) === null || _androidInputManagerR5 === void 0 || _androidInputManagerR5.handleKeyDown(event);\n        var {\n          nativeEvent\n        } = event;\n        // COMPAT: The composition end event isn't fired reliably in all browsers,\n        // so we sometimes might end up stuck in a composition state even though we\n        // aren't composing any more.\n        if (ReactEditor.isComposing(editor) && nativeEvent.isComposing === false) {\n          IS_COMPOSING.set(editor, false);\n          setIsComposing(false);\n        }\n        if (isEventHandled(event, attributes.onKeyDown) || ReactEditor.isComposing(editor)) {\n          return;\n        }\n        var {\n          selection\n        } = editor;\n        var element = editor.children[selection !== null ? selection.focus.path[0] : 0];\n        var isRTL = direction__WEBPACK_IMPORTED_MODULE_0___default()(slate__WEBPACK_IMPORTED_MODULE_7__.Node.string(element)) === 'rtl';\n        // COMPAT: Since we prevent the default behavior on\n        // `beforeinput` events, the browser doesn't think there's ever\n        // any history stack to undo or redo, so we have to manage these\n        // hotkeys ourselves. (2019/11/06)\n        if (Hotkeys.isRedo(nativeEvent)) {\n          event.preventDefault();\n          var maybeHistoryEditor = editor;\n          if (typeof maybeHistoryEditor.redo === 'function') {\n            maybeHistoryEditor.redo();\n          }\n          return;\n        }\n        if (Hotkeys.isUndo(nativeEvent)) {\n          event.preventDefault();\n          var _maybeHistoryEditor = editor;\n          if (typeof _maybeHistoryEditor.undo === 'function') {\n            _maybeHistoryEditor.undo();\n          }\n          return;\n        }\n        // COMPAT: Certain browsers don't handle the selection updates\n        // properly. In Chrome, the selection isn't properly extended.\n        // And in Firefox, the selection isn't properly collapsed.\n        // (2017/10/17)\n        if (Hotkeys.isMoveLineBackward(nativeEvent)) {\n          event.preventDefault();\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'line',\n            reverse: true\n          });\n          return;\n        }\n        if (Hotkeys.isMoveLineForward(nativeEvent)) {\n          event.preventDefault();\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'line'\n          });\n          return;\n        }\n        if (Hotkeys.isExtendLineBackward(nativeEvent)) {\n          event.preventDefault();\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'line',\n            edge: 'focus',\n            reverse: true\n          });\n          return;\n        }\n        if (Hotkeys.isExtendLineForward(nativeEvent)) {\n          event.preventDefault();\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'line',\n            edge: 'focus'\n          });\n          return;\n        }\n        // COMPAT: If a void node is selected, or a zero-width text node\n        // adjacent to an inline is selected, we need to handle these\n        // hotkeys manually because browsers won't be able to skip over\n        // the void node with the zero-width space not being an empty\n        // string.\n        if (Hotkeys.isMoveBackward(nativeEvent)) {\n          event.preventDefault();\n          if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(selection)) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n              reverse: !isRTL\n            });\n          } else {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.collapse(editor, {\n              edge: isRTL ? 'end' : 'start'\n            });\n          }\n          return;\n        }\n        if (Hotkeys.isMoveForward(nativeEvent)) {\n          event.preventDefault();\n          if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(selection)) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n              reverse: isRTL\n            });\n          } else {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.collapse(editor, {\n              edge: isRTL ? 'start' : 'end'\n            });\n          }\n          return;\n        }\n        if (Hotkeys.isMoveWordBackward(nativeEvent)) {\n          event.preventDefault();\n          if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.collapse(editor, {\n              edge: 'focus'\n            });\n          }\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'word',\n            reverse: !isRTL\n          });\n          return;\n        }\n        if (Hotkeys.isMoveWordForward(nativeEvent)) {\n          event.preventDefault();\n          if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n            slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.collapse(editor, {\n              edge: 'focus'\n            });\n          }\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.move(editor, {\n            unit: 'word',\n            reverse: isRTL\n          });\n          return;\n        }\n        // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n        // fall back to guessing at the input intention for hotkeys.\n        // COMPAT: In iOS, some of these hotkeys are handled in the\n        if (!HAS_BEFORE_INPUT_SUPPORT) {\n          // We don't have a core behavior for these, but they change the\n          // DOM if we don't prevent them, so we have to.\n          if (Hotkeys.isBold(nativeEvent) || Hotkeys.isItalic(nativeEvent) || Hotkeys.isTransposeCharacter(nativeEvent)) {\n            event.preventDefault();\n            return;\n          }\n          if (Hotkeys.isSoftBreak(nativeEvent)) {\n            event.preventDefault();\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertSoftBreak(editor);\n            return;\n          }\n          if (Hotkeys.isSplitBlock(nativeEvent)) {\n            event.preventDefault();\n            slate__WEBPACK_IMPORTED_MODULE_7__.Editor.insertBreak(editor);\n            return;\n          }\n          if (Hotkeys.isDeleteBackward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'backward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor);\n            }\n            return;\n          }\n          if (Hotkeys.isDeleteForward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'forward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor);\n            }\n            return;\n          }\n          if (Hotkeys.isDeleteLineBackward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'backward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n                unit: 'line'\n              });\n            }\n            return;\n          }\n          if (Hotkeys.isDeleteLineForward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'forward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n                unit: 'line'\n              });\n            }\n            return;\n          }\n          if (Hotkeys.isDeleteWordBackward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'backward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n                unit: 'word'\n              });\n            }\n            return;\n          }\n          if (Hotkeys.isDeleteWordForward(nativeEvent)) {\n            event.preventDefault();\n            if (selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isExpanded(selection)) {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteFragment(editor, {\n                direction: 'forward'\n              });\n            } else {\n              slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteForward(editor, {\n                unit: 'word'\n              });\n            }\n            return;\n          }\n        } else {\n          if (IS_CHROME || IS_WEBKIT) {\n            // COMPAT: Chrome and Safari support `beforeinput` event but do not fire\n            // an event when deleting backwards in a selected void inline node\n            if (selection && (Hotkeys.isDeleteBackward(nativeEvent) || Hotkeys.isDeleteForward(nativeEvent)) && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(selection)) {\n              var currentNode = slate__WEBPACK_IMPORTED_MODULE_7__.Node.parent(editor, selection.anchor.path);\n              if (slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(currentNode) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isVoid(editor, currentNode) && (slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isInline(editor, currentNode) || slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(editor, currentNode))) {\n                event.preventDefault();\n                slate__WEBPACK_IMPORTED_MODULE_7__.Editor.deleteBackward(editor, {\n                  unit: 'block'\n                });\n                return;\n              }\n            }\n          }\n        }\n      }\n    }, [readOnly, editor, attributes.onKeyDown]),\n    onPaste: (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(event => {\n      if (!readOnly && ReactEditor.hasEditableTarget(editor, event.target) && !isEventHandled(event, attributes.onPaste)) {\n        // COMPAT: Certain browsers don't support the `beforeinput` event, so we\n        // fall back to React's `onPaste` here instead.\n        // COMPAT: Firefox, Chrome and Safari don't emit `beforeinput` events\n        // when \"paste without formatting\" is used, so fallback. (2020/02/20)\n        // COMPAT: Safari InputEvents generated by pasting won't include\n        // application/x-slate-fragment items, so use the\n        // ClipboardEvent here. (2023/03/15)\n        if (!HAS_BEFORE_INPUT_SUPPORT || isPlainTextOnlyPaste(event.nativeEvent) || IS_WEBKIT) {\n          event.preventDefault();\n          ReactEditor.insertData(editor, event.clipboardData);\n        }\n      }\n    }, [readOnly, editor, attributes.onPaste])\n  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(Children, {\n    decorations: decorations,\n    node: editor,\n    renderElement: renderElement,\n    renderPlaceholder: renderPlaceholder,\n    renderLeaf: renderLeaf,\n    selection: editor.selection\n  }))))));\n});\n/**\n * The default placeholder element\n */\nvar DefaultPlaceholder = _ref => {\n  var {\n    attributes,\n    children\n  } = _ref;\n  return (\n    /*#__PURE__*/\n    // COMPAT: Artificially add a line-break to the end on the placeholder element\n    // to prevent Android IMEs to pick up its content in autocorrect and to auto-capitalize the first letter\n    react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"span\", _objectSpread$1({}, attributes), children, IS_ANDROID && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(\"br\", null))\n  );\n};\n/**\n * A default memoized decorate function.\n */\nvar defaultDecorate = () => [];\n/**\n * A default implement to scroll dom range into view.\n */\nvar defaultScrollSelectionIntoView = (editor, domRange) => {\n  // This was affecting the selection of multiple blocks and dragging behavior,\n  // so enabled only if the selection has been collapsed.\n  if (domRange.getBoundingClientRect && (!editor.selection || editor.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(editor.selection))) {\n    var leafEl = domRange.startContainer.parentElement;\n    leafEl.getBoundingClientRect = domRange.getBoundingClientRect.bind(domRange);\n    (0,scroll_into_view_if_needed__WEBPACK_IMPORTED_MODULE_8__[\"default\"])(leafEl, {\n      scrollMode: 'if-needed'\n    });\n    // @ts-expect-error an unorthodox delete D:\n    delete leafEl.getBoundingClientRect;\n  }\n};\n/**\n * Check if an event is overrided by a handler.\n */\nvar isEventHandled = (event, handler) => {\n  if (!handler) {\n    return false;\n  }\n  // The custom event handler may return a boolean to specify whether the event\n  // shall be treated as being handled or not.\n  var shouldTreatEventAsHandled = handler(event);\n  if (shouldTreatEventAsHandled != null) {\n    return shouldTreatEventAsHandled;\n  }\n  return event.isDefaultPrevented() || event.isPropagationStopped();\n};\n/**\n * Check if the event's target is an input element\n */\nvar isDOMEventTargetInput = event => {\n  return isDOMNode(event.target) && (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement);\n};\n/**\n * Check if a DOM event is overrided by a handler.\n */\nvar isDOMEventHandled = (event, handler) => {\n  if (!handler) {\n    return false;\n  }\n  // The custom event handler may return a boolean to specify whether the event\n  // shall be treated as being handled or not.\n  var shouldTreatEventAsHandled = handler(event);\n  if (shouldTreatEventAsHandled != null) {\n    return shouldTreatEventAsHandled;\n  }\n  return event.defaultPrevented;\n};\n\n/**\n * A React context for sharing the `focused` state of the editor.\n */\nvar FocusedContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)(false);\n/**\n * Get the current `focused` state of the editor.\n */\nvar useFocused = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(FocusedContext);\n};\n\nfunction isError(error) {\n  return error instanceof Error;\n}\n/**\n * A React context for sharing the editor selector context in a way to control rerenders\n */\nvar SlateSelectorContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_3__.createContext)({});\nvar refEquality = (a, b) => a === b;\n/**\n * use redux style selectors to prevent rerendering on every keystroke.\n * Bear in mind rerendering can only prevented if the returned value is a value type or for reference types (e.g. objects and arrays) add a custom equality function.\n *\n * Example:\n * ```\n *  const isSelectionActive = useSlateSelector(editor => Boolean(editor.selection));\n * ```\n */\nfunction useSlateSelector(selector) {\n  var equalityFn = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : refEquality;\n  var [, forceRender] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useReducer)(s => s + 1, 0);\n  var context = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(SlateSelectorContext);\n  if (!context) {\n    throw new Error(\"The `useSlateSelector` hook must be used inside the <Slate> component's context.\");\n  }\n  var {\n    getSlate,\n    addEventListener\n  } = context;\n  var latestSubscriptionCallbackError = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)();\n  var latestSelector = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(() => null);\n  var latestSelectedState = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n  var selectedState;\n  try {\n    if (selector !== latestSelector.current || latestSubscriptionCallbackError.current) {\n      selectedState = selector(getSlate());\n    } else {\n      selectedState = latestSelectedState.current;\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current && isError(err)) {\n      err.message += \"\\nThe error may be correlated with this previous error:\\n\".concat(latestSubscriptionCallbackError.current.stack, \"\\n\\n\");\n    }\n    throw err;\n  }\n  useIsomorphicLayoutEffect(() => {\n    latestSelector.current = selector;\n    latestSelectedState.current = selectedState;\n    latestSubscriptionCallbackError.current = undefined;\n  });\n  useIsomorphicLayoutEffect(() => {\n    function checkForUpdates() {\n      try {\n        var newSelectedState = latestSelector.current(getSlate());\n        if (equalityFn(newSelectedState, latestSelectedState.current)) {\n          return;\n        }\n        latestSelectedState.current = newSelectedState;\n      } catch (err) {\n        // we ignore all errors here, since when the component\n        // is re-rendered, the selectors are called again, and\n        // will throw again, if neither props nor store state\n        // changed\n        if (err instanceof Error) {\n          latestSubscriptionCallbackError.current = err;\n        } else {\n          latestSubscriptionCallbackError.current = new Error(String(err));\n        }\n      }\n      forceRender();\n    }\n    var unsubscribe = addEventListener(checkForUpdates);\n    checkForUpdates();\n    return () => unsubscribe();\n  },\n  // don't rerender on equalityFn change since we want to be able to define it inline\n  [addEventListener, getSlate]);\n  return selectedState;\n}\n/**\n * Create selector context with editor updating on every editor change\n */\nfunction useSelectorContext(editor) {\n  var eventListeners = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)([]).current;\n  var slateRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)({\n    editor\n  }).current;\n  var onChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(editor => {\n    slateRef.editor = editor;\n    eventListeners.forEach(listener => listener(editor));\n  }, [eventListeners, slateRef]);\n  var selectorContext = (0,react__WEBPACK_IMPORTED_MODULE_3__.useMemo)(() => {\n    return {\n      getSlate: () => slateRef.editor,\n      addEventListener: callback => {\n        eventListeners.push(callback);\n        return () => {\n          eventListeners.splice(eventListeners.indexOf(callback), 1);\n        };\n      }\n    };\n  }, [eventListeners, slateRef]);\n  return {\n    selectorContext,\n    onChange\n  };\n}\n\nvar _excluded = [\"editor\", \"children\", \"onChange\", \"onSelectionChange\", \"onValueChange\", \"initialValue\"];\n/**\n * A wrapper around the provider to handle `onChange` events, because the editor\n * is a mutable singleton so it won't ever register as \"changed\" otherwise.\n */\nvar Slate = props => {\n  var {\n      editor,\n      children,\n      onChange,\n      onSelectionChange,\n      onValueChange,\n      initialValue\n    } = props,\n    rest = _objectWithoutProperties(props, _excluded);\n  var [context, setContext] = react__WEBPACK_IMPORTED_MODULE_3___default().useState(() => {\n    if (!slate__WEBPACK_IMPORTED_MODULE_7__.Node.isNodeList(initialValue)) {\n      throw new Error(\"[Slate] initialValue is invalid! Expected a list of elements but got: \".concat(slate__WEBPACK_IMPORTED_MODULE_7__.Scrubber.stringify(initialValue)));\n    }\n    if (!slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isEditor(editor)) {\n      throw new Error(\"[Slate] editor is invalid! You passed: \".concat(slate__WEBPACK_IMPORTED_MODULE_7__.Scrubber.stringify(editor)));\n    }\n    editor.children = initialValue;\n    Object.assign(editor, rest);\n    return {\n      v: 0,\n      editor\n    };\n  });\n  var {\n    selectorContext,\n    onChange: handleSelectorChange\n  } = useSelectorContext(editor);\n  var onContextChange = (0,react__WEBPACK_IMPORTED_MODULE_3__.useCallback)(options => {\n    var _options$operation;\n    if (onChange) {\n      onChange(editor.children);\n    }\n    switch (options === null || options === void 0 || (_options$operation = options.operation) === null || _options$operation === void 0 ? void 0 : _options$operation.type) {\n      case 'set_selection':\n        onSelectionChange === null || onSelectionChange === void 0 || onSelectionChange(editor.selection);\n        break;\n      default:\n        onValueChange === null || onValueChange === void 0 || onValueChange(editor.children);\n    }\n    setContext(prevContext => ({\n      v: prevContext.v + 1,\n      editor\n    }));\n    handleSelectorChange(editor);\n  }, [editor, handleSelectorChange, onChange, onSelectionChange, onValueChange]);\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    EDITOR_TO_ON_CHANGE.set(editor, onContextChange);\n    return () => {\n      EDITOR_TO_ON_CHANGE.set(editor, () => {});\n    };\n  }, [editor, onContextChange]);\n  var [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_3__.useState)(ReactEditor.isFocused(editor));\n  (0,react__WEBPACK_IMPORTED_MODULE_3__.useEffect)(() => {\n    setIsFocused(ReactEditor.isFocused(editor));\n  }, [editor]);\n  useIsomorphicLayoutEffect(() => {\n    var fn = () => setIsFocused(ReactEditor.isFocused(editor));\n    if (REACT_MAJOR_VERSION >= 17) {\n      // In React >= 17 onFocus and onBlur listen to the focusin and focusout events during the bubbling phase.\n      // Therefore in order for <Editable />'s handlers to run first, which is necessary for ReactEditor.isFocused(editor)\n      // to return the correct value, we have to listen to the focusin and focusout events without useCapture here.\n      document.addEventListener('focusin', fn);\n      document.addEventListener('focusout', fn);\n      return () => {\n        document.removeEventListener('focusin', fn);\n        document.removeEventListener('focusout', fn);\n      };\n    } else {\n      document.addEventListener('focus', fn, true);\n      document.addEventListener('blur', fn, true);\n      return () => {\n        document.removeEventListener('focus', fn, true);\n        document.removeEventListener('blur', fn, true);\n      };\n    }\n  }, []);\n  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(SlateSelectorContext.Provider, {\n    value: selectorContext\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(SlateContext.Provider, {\n    value: context\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(EditorContext.Provider, {\n    value: context.editor\n  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_3___default().createElement(FocusedContext.Provider, {\n    value: isFocused\n  }, children))));\n};\n\n/**\n * Get the current editor object from the React context.\n * @deprecated Use useSlateStatic instead.\n */\nvar useEditor = () => {\n  var editor = (0,react__WEBPACK_IMPORTED_MODULE_3__.useContext)(EditorContext);\n  if (!editor) {\n    throw new Error(\"The `useEditor` hook must be used inside the <Slate> component's context.\");\n  }\n  return editor;\n};\n\n/**\n * Get the current slate selection.\n * Only triggers a rerender when the selection actually changes\n */\nvar useSlateSelection = () => {\n  return useSlateSelector(editor => editor.selection, isSelectionEqual);\n};\nvar isSelectionEqual = (a, b) => {\n  if (!a && !b) return true;\n  if (!a || !b) return false;\n  return slate__WEBPACK_IMPORTED_MODULE_7__.Range.equals(a, b);\n};\n\n/**\n * Utilities for single-line deletion\n */\nvar doRectsIntersect = (rect, compareRect) => {\n  var middle = (compareRect.top + compareRect.bottom) / 2;\n  return rect.top <= middle && rect.bottom >= middle;\n};\nvar areRangesSameLine = (editor, range1, range2) => {\n  var rect1 = ReactEditor.toDOMRange(editor, range1).getBoundingClientRect();\n  var rect2 = ReactEditor.toDOMRange(editor, range2).getBoundingClientRect();\n  return doRectsIntersect(rect1, rect2) && doRectsIntersect(rect2, rect1);\n};\n/**\n * A helper utility that returns the end portion of a `Range`\n * which is located on a single line.\n *\n * @param {Editor} editor The editor object to compare against\n * @param {Range} parentRange The parent range to compare against\n * @returns {Range} A valid portion of the parentRange which is one a single line\n */\nvar findCurrentLineRange = (editor, parentRange) => {\n  var parentRangeBoundary = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, slate__WEBPACK_IMPORTED_MODULE_7__.Range.end(parentRange));\n  var positions = Array.from(slate__WEBPACK_IMPORTED_MODULE_7__.Editor.positions(editor, {\n    at: parentRange\n  }));\n  var left = 0;\n  var right = positions.length;\n  var middle = Math.floor(right / 2);\n  if (areRangesSameLine(editor, slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, positions[left]), parentRangeBoundary)) {\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, positions[left], parentRangeBoundary);\n  }\n  if (positions.length < 2) {\n    return slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, positions[positions.length - 1], parentRangeBoundary);\n  }\n  while (middle !== positions.length && middle !== left) {\n    if (areRangesSameLine(editor, slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, positions[middle]), parentRangeBoundary)) {\n      right = middle;\n    } else {\n      left = middle;\n    }\n    middle = Math.floor((left + right) / 2);\n  }\n  return slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(editor, positions[right], parentRangeBoundary);\n};\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n * `withReact` adds React and DOM specific behaviors to the editor.\n *\n * If you are using TypeScript, you must extend Slate's CustomTypes to use\n * this plugin.\n *\n * See https://docs.slatejs.org/concepts/11-typescript to learn how.\n */\nvar withReact = function withReact(editor) {\n  var clipboardFormatKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'x-slate-fragment';\n  var e = editor;\n  var {\n    apply,\n    onChange,\n    deleteBackward,\n    addMark,\n    removeMark\n  } = e;\n  // The WeakMap which maps a key to a specific HTMLElement must be scoped to the editor instance to\n  // avoid collisions between editors in the DOM that share the same value.\n  EDITOR_TO_KEY_TO_ELEMENT.set(e, new WeakMap());\n  e.addMark = (key, value) => {\n    var _EDITOR_TO_SCHEDULE_F, _EDITOR_TO_PENDING_DI;\n    (_EDITOR_TO_SCHEDULE_F = EDITOR_TO_SCHEDULE_FLUSH.get(e)) === null || _EDITOR_TO_SCHEDULE_F === void 0 || _EDITOR_TO_SCHEDULE_F();\n    if (!EDITOR_TO_PENDING_INSERTION_MARKS.get(e) && (_EDITOR_TO_PENDING_DI = EDITOR_TO_PENDING_DIFFS.get(e)) !== null && _EDITOR_TO_PENDING_DI !== void 0 && _EDITOR_TO_PENDING_DI.length) {\n      // Ensure the current pending diffs originating from changes before the addMark\n      // are applied with the current formatting\n      EDITOR_TO_PENDING_INSERTION_MARKS.set(e, null);\n    }\n    EDITOR_TO_USER_MARKS.delete(e);\n    addMark(key, value);\n  };\n  e.removeMark = key => {\n    var _EDITOR_TO_PENDING_DI2;\n    if (!EDITOR_TO_PENDING_INSERTION_MARKS.get(e) && (_EDITOR_TO_PENDING_DI2 = EDITOR_TO_PENDING_DIFFS.get(e)) !== null && _EDITOR_TO_PENDING_DI2 !== void 0 && _EDITOR_TO_PENDING_DI2.length) {\n      // Ensure the current pending diffs originating from changes before the addMark\n      // are applied with the current formatting\n      EDITOR_TO_PENDING_INSERTION_MARKS.set(e, null);\n    }\n    EDITOR_TO_USER_MARKS.delete(e);\n    removeMark(key);\n  };\n  e.deleteBackward = unit => {\n    if (unit !== 'line') {\n      return deleteBackward(unit);\n    }\n    if (e.selection && slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(e.selection)) {\n      var parentBlockEntry = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.above(e, {\n        match: n => slate__WEBPACK_IMPORTED_MODULE_7__.Element.isElement(n) && slate__WEBPACK_IMPORTED_MODULE_7__.Editor.isBlock(e, n),\n        at: e.selection\n      });\n      if (parentBlockEntry) {\n        var [, parentBlockPath] = parentBlockEntry;\n        var parentElementRange = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.range(e, parentBlockPath, e.selection.anchor);\n        var currentLineRange = findCurrentLineRange(e, parentElementRange);\n        if (!slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(currentLineRange)) {\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.delete(e, {\n            at: currentLineRange\n          });\n        }\n      }\n    }\n  };\n  // This attempts to reset the NODE_TO_KEY entry to the correct value\n  // as apply() changes the object reference and hence invalidates the NODE_TO_KEY entry\n  e.apply = op => {\n    var matches = [];\n    var pathRefMatches = [];\n    var pendingDiffs = EDITOR_TO_PENDING_DIFFS.get(e);\n    if (pendingDiffs !== null && pendingDiffs !== void 0 && pendingDiffs.length) {\n      var transformed = pendingDiffs.map(textDiff => transformTextDiff(textDiff, op)).filter(Boolean);\n      EDITOR_TO_PENDING_DIFFS.set(e, transformed);\n    }\n    var pendingSelection = EDITOR_TO_PENDING_SELECTION.get(e);\n    if (pendingSelection) {\n      EDITOR_TO_PENDING_SELECTION.set(e, transformPendingRange(e, pendingSelection, op));\n    }\n    var pendingAction = EDITOR_TO_PENDING_ACTION.get(e);\n    if (pendingAction !== null && pendingAction !== void 0 && pendingAction.at) {\n      var at = slate__WEBPACK_IMPORTED_MODULE_7__.Point.isPoint(pendingAction === null || pendingAction === void 0 ? void 0 : pendingAction.at) ? transformPendingPoint(e, pendingAction.at, op) : transformPendingRange(e, pendingAction.at, op);\n      EDITOR_TO_PENDING_ACTION.set(e, at ? _objectSpread(_objectSpread({}, pendingAction), {}, {\n        at\n      }) : null);\n    }\n    switch (op.type) {\n      case 'insert_text':\n      case 'remove_text':\n      case 'set_node':\n      case 'split_node':\n        {\n          matches.push(...getMatches(e, op.path));\n          break;\n        }\n      case 'set_selection':\n        {\n          var _EDITOR_TO_USER_SELEC;\n          // Selection was manually set, don't restore the user selection after the change.\n          (_EDITOR_TO_USER_SELEC = EDITOR_TO_USER_SELECTION.get(e)) === null || _EDITOR_TO_USER_SELEC === void 0 || _EDITOR_TO_USER_SELEC.unref();\n          EDITOR_TO_USER_SELECTION.delete(e);\n          break;\n        }\n      case 'insert_node':\n      case 'remove_node':\n        {\n          matches.push(...getMatches(e, slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(op.path)));\n          break;\n        }\n      case 'merge_node':\n        {\n          var prevPath = slate__WEBPACK_IMPORTED_MODULE_7__.Path.previous(op.path);\n          matches.push(...getMatches(e, prevPath));\n          break;\n        }\n      case 'move_node':\n        {\n          var commonPath = slate__WEBPACK_IMPORTED_MODULE_7__.Path.common(slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(op.path), slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(op.newPath));\n          matches.push(...getMatches(e, commonPath));\n          var changedPath;\n          if (slate__WEBPACK_IMPORTED_MODULE_7__.Path.isBefore(op.path, op.newPath)) {\n            matches.push(...getMatches(e, slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(op.path)));\n            changedPath = op.newPath;\n          } else {\n            matches.push(...getMatches(e, slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(op.newPath)));\n            changedPath = op.path;\n          }\n          var changedNode = slate__WEBPACK_IMPORTED_MODULE_7__.Node.get(editor, slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(changedPath));\n          var changedNodeKey = ReactEditor.findKey(e, changedNode);\n          var changedPathRef = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.pathRef(e, slate__WEBPACK_IMPORTED_MODULE_7__.Path.parent(changedPath));\n          pathRefMatches.push([changedPathRef, changedNodeKey]);\n          break;\n        }\n    }\n    apply(op);\n    switch (op.type) {\n      case 'insert_node':\n      case 'remove_node':\n      case 'merge_node':\n      case 'move_node':\n      case 'split_node':\n        {\n          IS_NODE_MAP_DIRTY.set(e, true);\n        }\n    }\n    for (var [path, key] of matches) {\n      var [node] = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.node(e, path);\n      NODE_TO_KEY.set(node, key);\n    }\n    for (var [pathRef, _key] of pathRefMatches) {\n      if (pathRef.current) {\n        var [_node] = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.node(e, pathRef.current);\n        NODE_TO_KEY.set(_node, _key);\n      }\n      pathRef.unref();\n    }\n  };\n  e.setFragmentData = data => {\n    var {\n      selection\n    } = e;\n    if (!selection) {\n      return;\n    }\n    var [start, end] = slate__WEBPACK_IMPORTED_MODULE_7__.Range.edges(selection);\n    var startVoid = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(e, {\n      at: start.path\n    });\n    var endVoid = slate__WEBPACK_IMPORTED_MODULE_7__.Editor.void(e, {\n      at: end.path\n    });\n    if (slate__WEBPACK_IMPORTED_MODULE_7__.Range.isCollapsed(selection) && !startVoid) {\n      return;\n    }\n    // Create a fake selection so that we can add a Base64-encoded copy of the\n    // fragment to the HTML, to decode on future pastes.\n    var domRange = ReactEditor.toDOMRange(e, selection);\n    var contents = domRange.cloneContents();\n    var attach = contents.childNodes[0];\n    // Make sure attach is non-empty, since empty nodes will not get copied.\n    contents.childNodes.forEach(node => {\n      if (node.textContent && node.textContent.trim() !== '') {\n        attach = node;\n      }\n    });\n    // COMPAT: If the end node is a void node, we need to move the end of the\n    // range from the void node's spacer span, to the end of the void node's\n    // content, since the spacer is before void's content in the DOM.\n    if (endVoid) {\n      var [voidNode] = endVoid;\n      var r = domRange.cloneRange();\n      var domNode = ReactEditor.toDOMNode(e, voidNode);\n      r.setEndAfter(domNode);\n      contents = r.cloneContents();\n    }\n    // COMPAT: If the start node is a void node, we need to attach the encoded\n    // fragment to the void node's content node instead of the spacer, because\n    // attaching it to empty `<div>/<span>` nodes will end up having it erased by\n    // most browsers. (2018/04/27)\n    if (startVoid) {\n      attach = contents.querySelector('[data-slate-spacer]');\n    }\n    // Remove any zero-width space spans from the cloned DOM so that they don't\n    // show up elsewhere when pasted.\n    Array.from(contents.querySelectorAll('[data-slate-zero-width]')).forEach(zw => {\n      var isNewline = zw.getAttribute('data-slate-zero-width') === 'n';\n      zw.textContent = isNewline ? '\\n' : '';\n    });\n    // Set a `data-slate-fragment` attribute on a non-empty node, so it shows up\n    // in the HTML, and can be used for intra-Slate pasting. If it's a text\n    // node, wrap it in a `<span>` so we have something to set an attribute on.\n    if (isDOMText(attach)) {\n      var span = attach.ownerDocument.createElement('span');\n      // COMPAT: In Chrome and Safari, if we don't add the `white-space` style\n      // then leading and trailing spaces will be ignored. (2017/09/21)\n      span.style.whiteSpace = 'pre';\n      span.appendChild(attach);\n      contents.appendChild(span);\n      attach = span;\n    }\n    var fragment = e.getFragment();\n    var string = JSON.stringify(fragment);\n    var encoded = window.btoa(encodeURIComponent(string));\n    attach.setAttribute('data-slate-fragment', encoded);\n    data.setData(\"application/\".concat(clipboardFormatKey), encoded);\n    // Add the content to a <div> so that we can get its inner HTML.\n    var div = contents.ownerDocument.createElement('div');\n    div.appendChild(contents);\n    div.setAttribute('hidden', 'true');\n    contents.ownerDocument.body.appendChild(div);\n    data.setData('text/html', div.innerHTML);\n    data.setData('text/plain', getPlainText(div));\n    contents.ownerDocument.body.removeChild(div);\n    return data;\n  };\n  e.insertData = data => {\n    if (!e.insertFragmentData(data)) {\n      e.insertTextData(data);\n    }\n  };\n  e.insertFragmentData = data => {\n    /**\n     * Checking copied fragment from application/x-slate-fragment or data-slate-fragment\n     */\n    var fragment = data.getData(\"application/\".concat(clipboardFormatKey)) || getSlateFragmentAttribute(data);\n    if (fragment) {\n      var decoded = decodeURIComponent(window.atob(fragment));\n      var parsed = JSON.parse(decoded);\n      e.insertFragment(parsed);\n      return true;\n    }\n    return false;\n  };\n  e.insertTextData = data => {\n    var text = data.getData('text/plain');\n    if (text) {\n      var lines = text.split(/\\r\\n|\\r|\\n/);\n      var split = false;\n      for (var line of lines) {\n        if (split) {\n          slate__WEBPACK_IMPORTED_MODULE_7__.Transforms.splitNodes(e, {\n            always: true\n          });\n        }\n        e.insertText(line);\n        split = true;\n      }\n      return true;\n    }\n    return false;\n  };\n  e.onChange = options => {\n    // COMPAT: React < 18 doesn't batch `setState` hook calls, which means\n    // that the children and selection can get out of sync for one render\n    // pass. So we have to use this unstable API to ensure it batches them.\n    // (2019/12/03)\n    // https://github.com/facebook/react/issues/14259#issuecomment-439702367\n    var maybeBatchUpdates = REACT_MAJOR_VERSION < 18 ? (react_dom__WEBPACK_IMPORTED_MODULE_6___default().unstable_batchedUpdates) : callback => callback();\n    maybeBatchUpdates(() => {\n      var onContextChange = EDITOR_TO_ON_CHANGE.get(e);\n      if (onContextChange) {\n        onContextChange(options);\n      }\n      onChange(options);\n    });\n  };\n  return e;\n};\nvar getMatches = (e, path) => {\n  var matches = [];\n  for (var [n, p] of slate__WEBPACK_IMPORTED_MODULE_7__.Editor.levels(e, {\n    at: path\n  })) {\n    var key = ReactEditor.findKey(e, n);\n    matches.push([p, key]);\n  }\n  return matches;\n};\n\n\n//# sourceMappingURL=index.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4/node_modules/slate-react/dist/index.es.js\n");

/***/ })

};
;