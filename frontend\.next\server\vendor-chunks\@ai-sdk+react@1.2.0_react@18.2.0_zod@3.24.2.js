"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+react@1.2.0_react@18.2.0_zod@3.24.2";
exports.ids = ["vendor-chunks/@ai-sdk+react@1.2.0_react@18.2.0_zod@3.24.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+react@1.2.0_react@18.2.0_zod@3.24.2/node_modules/@ai-sdk/react/dist/index.mjs":
/*!******************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+react@1.2.0_react@18.2.0_zod@3.24.2/node_modules/@ai-sdk/react/dist/index.mjs ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   experimental_useObject: () => (/* binding */ experimental_useObject),\n/* harmony export */   useAssistant: () => (/* binding */ useAssistant),\n/* harmony export */   useChat: () => (/* binding */ useChat),\n/* harmony export */   useCompletion: () => (/* binding */ useCompletion)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.0_zod@3.24.2/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ai-sdk/ui-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var swr__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swr */ \"(ssr)/./node_modules/.pnpm/swr@2.3.3_react@18.2.0/node_modules/swr/dist/index/index.mjs\");\n/* harmony import */ var throttleit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! throttleit */ \"(ssr)/./node_modules/.pnpm/throttleit@2.1.0/node_modules/throttleit/index.js\");\n// src/use-assistant.ts\n\n\n\nvar getOriginalFetch = () => fetch;\nfunction useAssistant({\n  api,\n  threadId: threadIdParam,\n  credentials,\n  headers,\n  body,\n  onError,\n  fetch: fetch2\n}) {\n  const [messages, setMessages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"\");\n  const [currentThreadId, setCurrentThreadId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    void 0\n  );\n  const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"awaiting_message\");\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const handleInputChange = (event) => {\n    setInput(event.target.value);\n  };\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const append = async (message, requestOptions) => {\n    var _a, _b;\n    setStatus(\"in_progress\");\n    setMessages((messages2) => {\n      var _a2;\n      return [\n        ...messages2,\n        {\n          ...message,\n          id: (_a2 = message.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)()\n        }\n      ];\n    });\n    setInput(\"\");\n    const abortController = new AbortController();\n    try {\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        credentials,\n        signal: abortController.signal,\n        headers: { \"Content-Type\": \"application/json\", ...headers },\n        body: JSON.stringify({\n          ...body,\n          // always use user-provided threadId when available:\n          threadId: (_a = threadIdParam != null ? threadIdParam : currentThreadId) != null ? _a : null,\n          message: message.content,\n          // optional request data:\n          data: requestOptions == null ? void 0 : requestOptions.data\n        })\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_b = await response.text()) != null ? _b : \"Failed to fetch the assistant response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.processAssistantStream)({\n        stream: response.body,\n        onAssistantMessagePart(value) {\n          setMessages((messages2) => [\n            ...messages2,\n            {\n              id: value.id,\n              role: value.role,\n              content: value.content[0].text.value,\n              parts: []\n            }\n          ]);\n        },\n        onTextPart(value) {\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            return [\n              ...messages2.slice(0, messages2.length - 1),\n              {\n                id: lastMessage.id,\n                role: lastMessage.role,\n                content: lastMessage.content + value,\n                parts: lastMessage.parts\n              }\n            ];\n          });\n        },\n        onAssistantControlDataPart(value) {\n          setCurrentThreadId(value.threadId);\n          setMessages((messages2) => {\n            const lastMessage = messages2[messages2.length - 1];\n            lastMessage.id = value.messageId;\n            return [...messages2.slice(0, messages2.length - 1), lastMessage];\n          });\n        },\n        onDataMessagePart(value) {\n          setMessages((messages2) => {\n            var _a2;\n            return [\n              ...messages2,\n              {\n                id: (_a2 = value.id) != null ? _a2 : (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId)(),\n                role: \"data\",\n                content: \"\",\n                data: value.data,\n                parts: []\n              }\n            ];\n          });\n        },\n        onErrorPart(value) {\n          setError(new Error(value));\n        }\n      });\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2) && abortController.signal.aborted) {\n        abortControllerRef.current = null;\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setError(error2);\n    } finally {\n      abortControllerRef.current = null;\n      setStatus(\"awaiting_message\");\n    }\n  };\n  const submitMessage = async (event, requestOptions) => {\n    var _a;\n    (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n    if (input === \"\") {\n      return;\n    }\n    append({ role: \"user\", content: input, parts: [] }, requestOptions);\n  };\n  const setThreadId = (threadId) => {\n    setCurrentThreadId(threadId);\n    setMessages([]);\n  };\n  return {\n    append,\n    messages,\n    setMessages,\n    threadId: currentThreadId,\n    setThreadId,\n    input,\n    setInput,\n    handleInputChange,\n    submitMessage,\n    status,\n    error,\n    stop\n  };\n}\n\n// src/use-chat.ts\n\n\n\n\n// src/throttle.ts\n\nfunction throttle(fn, waitMs) {\n  return waitMs != null ? throttleit__WEBPACK_IMPORTED_MODULE_3__(fn, waitMs) : fn;\n}\n\n// src/util/use-stable-value.ts\n\n\nfunction useStableValue(latestValue) {\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(latestValue);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestValue, value)) {\n      setValue(latestValue);\n    }\n  }, [latestValue, value]);\n  return value;\n}\n\n// src/use-chat.ts\nfunction useChat({\n  api = \"/api/chat\",\n  id,\n  initialMessages,\n  initialInput = \"\",\n  sendExtraMessageFields,\n  onToolCall,\n  experimental_prepareRequestBody,\n  maxSteps = 1,\n  streamProtocol = \"data\",\n  onResponse,\n  onFinish,\n  onError,\n  credentials,\n  headers,\n  body,\n  generateId: generateId2 = _ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.generateId,\n  fetch: fetch2,\n  keepLastMessageOnError = true,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const [hookId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(generateId2);\n  const chatId = id != null ? id : hookId;\n  const chatKey = typeof api === \"string\" ? [api, chatId] : chatId;\n  const stableInitialMessages = useStableValue(initialMessages != null ? initialMessages : []);\n  const processedInitialMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(\n    () => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(stableInitialMessages),\n    [stableInitialMessages]\n  );\n  const { data: messages, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [chatKey, \"messages\"],\n    null,\n    { fallbackData: processedInitialMessages }\n  );\n  const messagesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(messages || []);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    messagesRef.current = messages || [];\n  }, [messages]);\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"streamData\"], null);\n  const streamDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(streamData);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    streamDataRef.current = streamData;\n  }, [streamData]);\n  const { data: status = \"ready\", mutate: mutateStatus } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"status\"], null);\n  const { data: error = void 0, mutate: setError } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([chatKey, \"error\"], null);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (chatRequest) => {\n      var _a, _b;\n      mutateStatus(\"submitted\");\n      setError(void 0);\n      const chatMessages = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(chatRequest.messages);\n      const messageCount = chatMessages.length;\n      const maxStep = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.extractMaxToolInvocationStep)(\n        (_a = chatMessages[chatMessages.length - 1]) == null ? void 0 : _a.toolInvocations\n      );\n      try {\n        const abortController = new AbortController();\n        abortControllerRef.current = abortController;\n        const throttledMutate = throttle(mutate, throttleWaitMs);\n        const throttledMutateStreamData = throttle(\n          mutateStreamData,\n          throttleWaitMs\n        );\n        const previousMessages = messagesRef.current;\n        throttledMutate(chatMessages, false);\n        const constructedMessagesPayload = sendExtraMessageFields ? chatMessages : chatMessages.map(\n          ({\n            role,\n            content,\n            experimental_attachments,\n            data,\n            annotations,\n            toolInvocations,\n            parts\n          }) => ({\n            role,\n            content,\n            ...experimental_attachments !== void 0 && {\n              experimental_attachments\n            },\n            ...data !== void 0 && { data },\n            ...annotations !== void 0 && { annotations },\n            ...toolInvocations !== void 0 && { toolInvocations },\n            ...parts !== void 0 && { parts }\n          })\n        );\n        const existingData = streamDataRef.current;\n        await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callChatApi)({\n          api,\n          body: (_b = experimental_prepareRequestBody == null ? void 0 : experimental_prepareRequestBody({\n            id: chatId,\n            messages: chatMessages,\n            requestData: chatRequest.data,\n            requestBody: chatRequest.body\n          })) != null ? _b : {\n            id: chatId,\n            messages: constructedMessagesPayload,\n            data: chatRequest.data,\n            ...extraMetadataRef.current.body,\n            ...chatRequest.body\n          },\n          streamProtocol,\n          credentials: extraMetadataRef.current.credentials,\n          headers: {\n            ...extraMetadataRef.current.headers,\n            ...chatRequest.headers\n          },\n          abortController: () => abortControllerRef.current,\n          restoreMessagesOnFailure() {\n            if (!keepLastMessageOnError) {\n              throttledMutate(previousMessages, false);\n            }\n          },\n          onResponse,\n          onUpdate({ message, data, replaceLastMessage }) {\n            mutateStatus(\"streaming\");\n            throttledMutate(\n              [\n                ...replaceLastMessage ? chatMessages.slice(0, chatMessages.length - 1) : chatMessages,\n                message\n              ],\n              false\n            );\n            if (data == null ? void 0 : data.length) {\n              throttledMutateStreamData(\n                [...existingData != null ? existingData : [], ...data],\n                false\n              );\n            }\n          },\n          onToolCall,\n          onFinish,\n          generateId: generateId2,\n          fetch: fetch2,\n          lastMessage: chatMessages[chatMessages.length - 1]\n        });\n        abortControllerRef.current = null;\n        mutateStatus(\"ready\");\n      } catch (err) {\n        if (err.name === \"AbortError\") {\n          abortControllerRef.current = null;\n          mutateStatus(\"ready\");\n          return null;\n        }\n        if (onError && err instanceof Error) {\n          onError(err);\n        }\n        setError(err);\n        mutateStatus(\"error\");\n      }\n      const messages2 = messagesRef.current;\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.shouldResubmitMessages)({\n        originalMaxToolInvocationStep: maxStep,\n        originalMessageCount: messageCount,\n        maxSteps,\n        messages: messages2\n      })) {\n        await triggerRequest({ messages: messages2 });\n      }\n    },\n    [\n      mutate,\n      mutateStatus,\n      api,\n      extraMetadataRef,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      mutateStreamData,\n      streamDataRef,\n      streamProtocol,\n      sendExtraMessageFields,\n      experimental_prepareRequestBody,\n      onToolCall,\n      maxSteps,\n      messagesRef,\n      abortControllerRef,\n      generateId2,\n      fetch2,\n      keepLastMessageOnError,\n      throttleWaitMs,\n      chatId\n    ]\n  );\n  const append = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (message, {\n      data,\n      headers: headers2,\n      body: body2,\n      experimental_attachments\n    } = {}) => {\n      var _a, _b;\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        ...message,\n        id: (_a = message.id) != null ? _a : generateId2(),\n        createdAt: (_b = message.createdAt) != null ? _b : /* @__PURE__ */ new Date(),\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.getMessageParts)(message)\n      });\n      return triggerRequest({ messages: messages2, headers: headers2, body: body2, data });\n    },\n    [triggerRequest, generateId2]\n  );\n  const reload = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async ({ data, headers: headers2, body: body2 } = {}) => {\n      const messages2 = messagesRef.current;\n      if (messages2.length === 0) {\n        return null;\n      }\n      const lastMessage = messages2[messages2.length - 1];\n      return triggerRequest({\n        messages: lastMessage.role === \"assistant\" ? messages2.slice(0, -1) : messages2,\n        headers: headers2,\n        body: body2,\n        data\n      });\n    },\n    [triggerRequest]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortControllerRef.current) {\n      abortControllerRef.current.abort();\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const setMessages = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (messages2) => {\n      if (typeof messages2 === \"function\") {\n        messages2 = messages2(messagesRef.current);\n      }\n      const messagesWithParts = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.fillMessageParts)(messages2);\n      mutate(messagesWithParts, false);\n      messagesRef.current = messagesWithParts;\n    },\n    [mutate]\n  );\n  const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (data) => {\n      if (typeof data === \"function\") {\n        data = data(streamDataRef.current);\n      }\n      mutateStreamData(data, false);\n      streamDataRef.current = data;\n    },\n    [mutateStreamData]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (event, options = {}, metadata) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      if (!input && !options.allowEmptySubmit)\n        return;\n      if (metadata) {\n        extraMetadataRef.current = {\n          ...extraMetadataRef.current,\n          ...metadata\n        };\n      }\n      const attachmentsForRequest = await (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.prepareAttachmentsForRequest)(\n        options.experimental_attachments\n      );\n      const messages2 = messagesRef.current.concat({\n        id: generateId2(),\n        createdAt: /* @__PURE__ */ new Date(),\n        role: \"user\",\n        content: input,\n        experimental_attachments: attachmentsForRequest.length > 0 ? attachmentsForRequest : void 0,\n        parts: [{ type: \"text\", text: input }]\n      });\n      const chatRequest = {\n        messages: messages2,\n        headers: options.headers,\n        body: options.body,\n        data: options.data\n      };\n      triggerRequest(chatRequest);\n      setInput(\"\");\n    },\n    [input, generateId2, triggerRequest]\n  );\n  const handleInputChange = (e) => {\n    setInput(e.target.value);\n  };\n  const addToolResult = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    ({ toolCallId, result }) => {\n      const currentMessages = messagesRef.current;\n      (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.updateToolCallResult)({\n        messages: currentMessages,\n        toolCallId,\n        toolResult: result\n      });\n      mutate(currentMessages, false);\n      const lastMessage = currentMessages[currentMessages.length - 1];\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isAssistantMessageWithCompletedToolCalls)(lastMessage)) {\n        triggerRequest({ messages: currentMessages });\n      }\n    },\n    [mutate, triggerRequest]\n  );\n  return {\n    messages: messages != null ? messages : [],\n    id: chatId,\n    setMessages,\n    data: streamData,\n    setData,\n    error,\n    append,\n    reload,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading: status === \"submitted\" || status === \"streaming\",\n    status,\n    addToolResult\n  };\n}\n\n// src/use-completion.ts\n\n\n\nfunction useCompletion({\n  api = \"/api/completion\",\n  id,\n  initialCompletion = \"\",\n  initialInput = \"\",\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  fetch: fetch2,\n  onResponse,\n  onFinish,\n  onError,\n  experimental_throttle: throttleWaitMs\n} = {}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id || hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([api, completionId], null, {\n    fallbackData: initialCompletion\n  });\n  const { data: isLoading = false, mutate: mutateLoading } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [completionId, \"loading\"],\n    null\n  );\n  const { data: streamData, mutate: mutateStreamData } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])([completionId, \"streamData\"], null);\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const completion = data;\n  const [abortController, setAbortController] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const extraMetadataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    credentials,\n    headers,\n    body\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    extraMetadataRef.current = {\n      credentials,\n      headers,\n      body\n    };\n  }, [credentials, headers, body]);\n  const triggerRequest = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.callCompletionApi)({\n      api,\n      prompt,\n      credentials: extraMetadataRef.current.credentials,\n      headers: { ...extraMetadataRef.current.headers, ...options == null ? void 0 : options.headers },\n      body: {\n        ...extraMetadataRef.current.body,\n        ...options == null ? void 0 : options.body\n      },\n      streamProtocol,\n      fetch: fetch2,\n      // throttle streamed ui updates:\n      setCompletion: throttle(\n        (completion2) => mutate(completion2, false),\n        throttleWaitMs\n      ),\n      onData: throttle(\n        (data2) => mutateStreamData([...streamData != null ? streamData : [], ...data2 != null ? data2 : []], false),\n        throttleWaitMs\n      ),\n      setLoading: mutateLoading,\n      setError,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError\n    }),\n    [\n      mutate,\n      mutateLoading,\n      api,\n      extraMetadataRef,\n      setAbortController,\n      onResponse,\n      onFinish,\n      onError,\n      setError,\n      streamData,\n      streamProtocol,\n      fetch2,\n      mutateStreamData,\n      throttleWaitMs\n    ]\n  );\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (abortController) {\n      abortController.abort();\n      setAbortController(null);\n    }\n  }, [abortController]);\n  const setCompletion = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (completion2) => {\n      mutate(completion2, false);\n    },\n    [mutate]\n  );\n  const complete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    async (prompt, options) => {\n      return triggerRequest(prompt, options);\n    },\n    [triggerRequest]\n  );\n  const [input, setInput] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialInput);\n  const handleSubmit = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (event) => {\n      var _a;\n      (_a = event == null ? void 0 : event.preventDefault) == null ? void 0 : _a.call(event);\n      return input ? complete(input) : void 0;\n    },\n    [input, complete]\n  );\n  const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(\n    (e) => {\n      setInput(e.target.value);\n    },\n    [setInput]\n  );\n  return {\n    completion,\n    complete,\n    error,\n    setCompletion,\n    stop,\n    input,\n    setInput,\n    handleInputChange,\n    handleSubmit,\n    isLoading,\n    data: streamData\n  };\n}\n\n// src/use-object.ts\n\n\n\n\nvar getOriginalFetch2 = () => fetch;\nfunction useObject({\n  api,\n  id,\n  schema,\n  // required, in the future we will use it for validation\n  initialValue,\n  fetch: fetch2,\n  onError,\n  onFinish,\n  headers\n}) {\n  const hookId = (0,react__WEBPACK_IMPORTED_MODULE_0__.useId)();\n  const completionId = id != null ? id : hookId;\n  const { data, mutate } = (0,swr__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(\n    [api, completionId],\n    null,\n    { fallbackData: initialValue }\n  );\n  const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(void 0);\n  const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const abortControllerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    var _a;\n    try {\n      (_a = abortControllerRef.current) == null ? void 0 : _a.abort();\n    } catch (ignored) {\n    } finally {\n      setIsLoading(false);\n      abortControllerRef.current = null;\n    }\n  }, []);\n  const submit = async (input) => {\n    var _a;\n    try {\n      mutate(void 0);\n      setIsLoading(true);\n      setError(void 0);\n      const abortController = new AbortController();\n      abortControllerRef.current = abortController;\n      const actualFetch = fetch2 != null ? fetch2 : getOriginalFetch2();\n      const response = await actualFetch(api, {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...headers\n        },\n        signal: abortController.signal,\n        body: JSON.stringify(input)\n      });\n      if (!response.ok) {\n        throw new Error(\n          (_a = await response.text()) != null ? _a : \"Failed to fetch the response.\"\n        );\n      }\n      if (response.body == null) {\n        throw new Error(\"The response body is empty.\");\n      }\n      let accumulatedText = \"\";\n      let latestObject = void 0;\n      await response.body.pipeThrough(new TextDecoderStream()).pipeTo(\n        new WritableStream({\n          write(chunk) {\n            accumulatedText += chunk;\n            const { value } = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.parsePartialJson)(accumulatedText);\n            const currentObject = value;\n            if (!(0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.isDeepEqualData)(latestObject, currentObject)) {\n              latestObject = currentObject;\n              mutate(currentObject);\n            }\n          },\n          close() {\n            setIsLoading(false);\n            abortControllerRef.current = null;\n            if (onFinish != null) {\n              const validationResult = (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.safeValidateTypes)({\n                value: latestObject,\n                schema: (0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_2__.asSchema)(schema)\n              });\n              onFinish(\n                validationResult.success ? { object: validationResult.value, error: void 0 } : { object: void 0, error: validationResult.error }\n              );\n            }\n          }\n        })\n      );\n    } catch (error2) {\n      if ((0,_ai_sdk_ui_utils__WEBPACK_IMPORTED_MODULE_1__.isAbortError)(error2)) {\n        return;\n      }\n      if (onError && error2 instanceof Error) {\n        onError(error2);\n      }\n      setIsLoading(false);\n      setError(error2 instanceof Error ? error2 : new Error(String(error2)));\n    }\n  };\n  return {\n    submit,\n    object: data,\n    error,\n    isLoading,\n    stop\n  };\n}\nvar experimental_useObject = useObject;\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFpLXNkaytyZWFjdEAxLjIuMF9yZWFjdEAxOC4yLjBfem9kQDMuMjQuMi9ub2RlX21vZHVsZXMvQGFpLXNkay9yZWFjdC9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTtBQUNzRDtBQUk1QjtBQUM0QjtBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0NBQWtDLCtDQUFRO0FBQzFDLDRCQUE0QiwrQ0FBUTtBQUNwQyxnREFBZ0QsK0NBQVE7QUFDeEQ7QUFDQTtBQUNBLDhCQUE4QiwrQ0FBUTtBQUN0Qyw0QkFBNEIsK0NBQVE7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsNkJBQTZCLDZDQUFNO0FBQ25DLGVBQWUsa0RBQVc7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaURBQWlELDREQUFVO0FBQzNEO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUJBQW1CLGdEQUFnRDtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVCxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFlBQVksd0VBQXNCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHFEQUFxRCw0REFBVTtBQUMvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1gsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUCxNQUFNO0FBQ04sVUFBVSw4REFBWTtBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWEseUNBQXlDO0FBQ3REO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFXMEI7QUFDc0c7QUFDdkc7O0FBRXpCO0FBQzBDO0FBQzFDO0FBQ0EsMEJBQTBCLHVDQUFnQjtBQUMxQzs7QUFFQTtBQUNtRDtBQUNNO0FBQ3pEO0FBQ0EsNEJBQTRCLCtDQUFTO0FBQ3JDLEVBQUUsZ0RBQVM7QUFDWCxTQUFTLGlFQUFlO0FBQ3hCO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLHdEQUFjO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLEVBQUUsSUFBSTtBQUNOLG1CQUFtQiwrQ0FBUztBQUM1QjtBQUNBO0FBQ0E7QUFDQSxtQ0FBbUMsOENBQU87QUFDMUMsVUFBVSxrRUFBZ0I7QUFDMUI7QUFDQTtBQUNBLFVBQVUseUJBQXlCLEVBQUUsK0NBQU07QUFDM0M7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLHNCQUFzQiw2Q0FBTztBQUM3QixFQUFFLGdEQUFVO0FBQ1o7QUFDQSxHQUFHO0FBQ0gsVUFBVSw2Q0FBNkMsRUFBRSwrQ0FBTTtBQUMvRCx3QkFBd0IsNkNBQU87QUFDL0IsRUFBRSxnREFBVTtBQUNaO0FBQ0EsR0FBRztBQUNILFVBQVUsK0NBQStDLEVBQUUsK0NBQU07QUFDakUsVUFBVSx5Q0FBeUMsRUFBRSwrQ0FBTTtBQUMzRCw2QkFBNkIsNkNBQU87QUFDcEMsMkJBQTJCLDZDQUFPO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxFQUFFLGdEQUFVO0FBQ1o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCx5QkFBeUIsa0RBQVk7QUFDckM7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsa0VBQWdCO0FBQzNDO0FBQ0Esc0JBQXNCLDhFQUE0QjtBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2Isb0NBQW9DLE1BQU07QUFDMUMsMkNBQTJDLGFBQWE7QUFDeEQsK0NBQStDLGlCQUFpQjtBQUNoRSxxQ0FBcUM7QUFDckMsV0FBVztBQUNYO0FBQ0E7QUFDQSxjQUFjLDZEQUFXO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXO0FBQ1g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBLHFCQUFxQixtQ0FBbUM7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSx3RUFBc0I7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsK0JBQStCLHFCQUFxQjtBQUNwRDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixrREFBWTtBQUM3QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxJQUFJO0FBQ1Y7QUFDQSwwQ0FBMEMsOEVBQTRCO0FBQ3RFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxpRUFBZTtBQUM5QixPQUFPO0FBQ1AsOEJBQThCLDJEQUEyRDtBQUN6RixLQUFLO0FBQ0w7QUFDQTtBQUNBLGlCQUFpQixrREFBWTtBQUM3QixhQUFhLHVDQUF1QyxJQUFJO0FBQ3hEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLEtBQUs7QUFDTDtBQUNBO0FBQ0EsZUFBZSxrREFBWTtBQUMzQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxzQkFBc0Isa0RBQVk7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQ0FBZ0Msa0VBQWdCO0FBQ2hEO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLGtCQUFrQixrREFBWTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLDRCQUE0QiwrQ0FBUztBQUNyQyx1QkFBdUIsa0RBQVk7QUFDbkMsOEJBQThCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMENBQTBDLDhFQUE0QjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLDJCQUEyQjtBQUM3QyxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGtEQUFZO0FBQ3BDLE9BQU8sb0JBQW9CO0FBQzNCO0FBQ0EsTUFBTSxzRUFBb0I7QUFDMUI7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQSxVQUFVLDBGQUF3QztBQUNsRCx5QkFBeUIsMkJBQTJCO0FBQ3BEO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUcwQjtBQUNvRztBQUNwRztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSxJQUFJO0FBQ04saUJBQWlCLDRDQUFLO0FBQ3RCO0FBQ0EsVUFBVSxlQUFlLEVBQUUsK0NBQU87QUFDbEM7QUFDQSxHQUFHO0FBQ0gsVUFBVSxpREFBaUQsRUFBRSwrQ0FBTztBQUNwRTtBQUNBO0FBQ0E7QUFDQSxVQUFVLDZDQUE2QyxFQUFFLCtDQUFPO0FBQ2hFLDRCQUE0QiwrQ0FBUztBQUNyQztBQUNBLGdEQUFnRCwrQ0FBUztBQUN6RCwyQkFBMkIsNkNBQU87QUFDbEM7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILEVBQUUsZ0RBQVU7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILHlCQUF5QixrREFBWTtBQUNyQywrQkFBK0IsbUVBQWlCO0FBQ2hEO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQixvRkFBb0Y7QUFDckc7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsa0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsd0JBQXdCLGtEQUFZO0FBQ3BDO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLG1CQUFtQixrREFBWTtBQUMvQjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSw0QkFBNEIsK0NBQVM7QUFDckMsdUJBQXVCLGtEQUFZO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSw0QkFBNEIsa0RBQVk7QUFDeEM7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUlnQztBQUtOO0FBQ3FGO0FBQ3JGO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsaUJBQWlCLDRDQUFNO0FBQ3ZCO0FBQ0EsVUFBVSxlQUFlLEVBQUUsK0NBQU87QUFDbEM7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBLDRCQUE0QiwrQ0FBUztBQUNyQyxvQ0FBb0MsK0NBQVM7QUFDN0MsNkJBQTZCLDZDQUFPO0FBQ3BDLGVBQWUsa0RBQVk7QUFDM0I7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQkFBb0IsUUFBUSxFQUFFLGtFQUFnQjtBQUM5QztBQUNBLGlCQUFpQixpRUFBZ0I7QUFDakM7QUFDQTtBQUNBO0FBQ0EsV0FBVztBQUNYO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLG1FQUFpQjtBQUN4RDtBQUNBLHdCQUF3QiwwREFBUTtBQUNoQyxlQUFlO0FBQ2Y7QUFDQSw2Q0FBNkMsZ0RBQWdELElBQUk7QUFDakc7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0EsTUFBTTtBQUNOLFVBQVUsOERBQWE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBTUU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYWktc2RrK3JlYWN0QDEuMi4wX3JlYWN0QDE4LjIuMF96b2RAMy4yNC4yL25vZGVfbW9kdWxlcy9AYWktc2RrL3JlYWN0L2Rpc3QvaW5kZXgubWpzP2U3OWUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3VzZS1hc3Npc3RhbnQudHNcbmltcG9ydCB7IGlzQWJvcnRFcnJvciB9IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyLXV0aWxzXCI7XG5pbXBvcnQge1xuICBnZW5lcmF0ZUlkLFxuICBwcm9jZXNzQXNzaXN0YW50U3RyZWFtXG59IGZyb20gXCJAYWktc2RrL3VpLXV0aWxzXCI7XG5pbXBvcnQgeyB1c2VDYWxsYmFjaywgdXNlUmVmLCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xudmFyIGdldE9yaWdpbmFsRmV0Y2ggPSAoKSA9PiBmZXRjaDtcbmZ1bmN0aW9uIHVzZUFzc2lzdGFudCh7XG4gIGFwaSxcbiAgdGhyZWFkSWQ6IHRocmVhZElkUGFyYW0sXG4gIGNyZWRlbnRpYWxzLFxuICBoZWFkZXJzLFxuICBib2R5LFxuICBvbkVycm9yLFxuICBmZXRjaDogZmV0Y2gyXG59KSB7XG4gIGNvbnN0IFttZXNzYWdlcywgc2V0TWVzc2FnZXNdID0gdXNlU3RhdGUoW10pO1xuICBjb25zdCBbaW5wdXQsIHNldElucHV0XSA9IHVzZVN0YXRlKFwiXCIpO1xuICBjb25zdCBbY3VycmVudFRocmVhZElkLCBzZXRDdXJyZW50VGhyZWFkSWRdID0gdXNlU3RhdGUoXG4gICAgdm9pZCAwXG4gICk7XG4gIGNvbnN0IFtzdGF0dXMsIHNldFN0YXR1c10gPSB1c2VTdGF0ZShcImF3YWl0aW5nX21lc3NhZ2VcIik7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGUodm9pZCAwKTtcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZXZlbnQpID0+IHtcbiAgICBzZXRJbnB1dChldmVudC50YXJnZXQudmFsdWUpO1xuICB9O1xuICBjb25zdCBhYm9ydENvbnRyb2xsZXJSZWYgPSB1c2VSZWYobnVsbCk7XG4gIGNvbnN0IHN0b3AgPSB1c2VDYWxsYmFjaygoKSA9PiB7XG4gICAgaWYgKGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudC5hYm9ydCgpO1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgIH1cbiAgfSwgW10pO1xuICBjb25zdCBhcHBlbmQgPSBhc3luYyAobWVzc2FnZSwgcmVxdWVzdE9wdGlvbnMpID0+IHtcbiAgICB2YXIgX2EsIF9iO1xuICAgIHNldFN0YXR1cyhcImluX3Byb2dyZXNzXCIpO1xuICAgIHNldE1lc3NhZ2VzKChtZXNzYWdlczIpID0+IHtcbiAgICAgIHZhciBfYTI7XG4gICAgICByZXR1cm4gW1xuICAgICAgICAuLi5tZXNzYWdlczIsXG4gICAgICAgIHtcbiAgICAgICAgICAuLi5tZXNzYWdlLFxuICAgICAgICAgIGlkOiAoX2EyID0gbWVzc2FnZS5pZCkgIT0gbnVsbCA/IF9hMiA6IGdlbmVyYXRlSWQoKVxuICAgICAgICB9XG4gICAgICBdO1xuICAgIH0pO1xuICAgIHNldElucHV0KFwiXCIpO1xuICAgIGNvbnN0IGFib3J0Q29udHJvbGxlciA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICB0cnkge1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBhYm9ydENvbnRyb2xsZXI7XG4gICAgICBjb25zdCBhY3R1YWxGZXRjaCA9IGZldGNoMiAhPSBudWxsID8gZmV0Y2gyIDogZ2V0T3JpZ2luYWxGZXRjaCgpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBhY3R1YWxGZXRjaChhcGksIHtcbiAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgICAgY3JlZGVudGlhbHMsXG4gICAgICAgIHNpZ25hbDogYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICAgICAgaGVhZGVyczogeyBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIiwgLi4uaGVhZGVycyB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgLi4uYm9keSxcbiAgICAgICAgICAvLyBhbHdheXMgdXNlIHVzZXItcHJvdmlkZWQgdGhyZWFkSWQgd2hlbiBhdmFpbGFibGU6XG4gICAgICAgICAgdGhyZWFkSWQ6IChfYSA9IHRocmVhZElkUGFyYW0gIT0gbnVsbCA/IHRocmVhZElkUGFyYW0gOiBjdXJyZW50VGhyZWFkSWQpICE9IG51bGwgPyBfYSA6IG51bGwsXG4gICAgICAgICAgbWVzc2FnZTogbWVzc2FnZS5jb250ZW50LFxuICAgICAgICAgIC8vIG9wdGlvbmFsIHJlcXVlc3QgZGF0YTpcbiAgICAgICAgICBkYXRhOiByZXF1ZXN0T3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogcmVxdWVzdE9wdGlvbnMuZGF0YVxuICAgICAgICB9KVxuICAgICAgfSk7XG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcbiAgICAgICAgICAoX2IgPSBhd2FpdCByZXNwb25zZS50ZXh0KCkpICE9IG51bGwgPyBfYiA6IFwiRmFpbGVkIHRvIGZldGNoIHRoZSBhc3Npc3RhbnQgcmVzcG9uc2UuXCJcbiAgICAgICAgKTtcbiAgICAgIH1cbiAgICAgIGlmIChyZXNwb25zZS5ib2R5ID09IG51bGwpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhlIHJlc3BvbnNlIGJvZHkgaXMgZW1wdHkuXCIpO1xuICAgICAgfVxuICAgICAgYXdhaXQgcHJvY2Vzc0Fzc2lzdGFudFN0cmVhbSh7XG4gICAgICAgIHN0cmVhbTogcmVzcG9uc2UuYm9keSxcbiAgICAgICAgb25Bc3Npc3RhbnRNZXNzYWdlUGFydCh2YWx1ZSkge1xuICAgICAgICAgIHNldE1lc3NhZ2VzKChtZXNzYWdlczIpID0+IFtcbiAgICAgICAgICAgIC4uLm1lc3NhZ2VzMixcbiAgICAgICAgICAgIHtcbiAgICAgICAgICAgICAgaWQ6IHZhbHVlLmlkLFxuICAgICAgICAgICAgICByb2xlOiB2YWx1ZS5yb2xlLFxuICAgICAgICAgICAgICBjb250ZW50OiB2YWx1ZS5jb250ZW50WzBdLnRleHQudmFsdWUsXG4gICAgICAgICAgICAgIHBhcnRzOiBbXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIF0pO1xuICAgICAgICB9LFxuICAgICAgICBvblRleHRQYXJ0KHZhbHVlKSB7XG4gICAgICAgICAgc2V0TWVzc2FnZXMoKG1lc3NhZ2VzMikgPT4ge1xuICAgICAgICAgICAgY29uc3QgbGFzdE1lc3NhZ2UgPSBtZXNzYWdlczJbbWVzc2FnZXMyLmxlbmd0aCAtIDFdO1xuICAgICAgICAgICAgcmV0dXJuIFtcbiAgICAgICAgICAgICAgLi4ubWVzc2FnZXMyLnNsaWNlKDAsIG1lc3NhZ2VzMi5sZW5ndGggLSAxKSxcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGlkOiBsYXN0TWVzc2FnZS5pZCxcbiAgICAgICAgICAgICAgICByb2xlOiBsYXN0TWVzc2FnZS5yb2xlLFxuICAgICAgICAgICAgICAgIGNvbnRlbnQ6IGxhc3RNZXNzYWdlLmNvbnRlbnQgKyB2YWx1ZSxcbiAgICAgICAgICAgICAgICBwYXJ0czogbGFzdE1lc3NhZ2UucGFydHNcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgXTtcbiAgICAgICAgICB9KTtcbiAgICAgICAgfSxcbiAgICAgICAgb25Bc3Npc3RhbnRDb250cm9sRGF0YVBhcnQodmFsdWUpIHtcbiAgICAgICAgICBzZXRDdXJyZW50VGhyZWFkSWQodmFsdWUudGhyZWFkSWQpO1xuICAgICAgICAgIHNldE1lc3NhZ2VzKChtZXNzYWdlczIpID0+IHtcbiAgICAgICAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gbWVzc2FnZXMyW21lc3NhZ2VzMi5sZW5ndGggLSAxXTtcbiAgICAgICAgICAgIGxhc3RNZXNzYWdlLmlkID0gdmFsdWUubWVzc2FnZUlkO1xuICAgICAgICAgICAgcmV0dXJuIFsuLi5tZXNzYWdlczIuc2xpY2UoMCwgbWVzc2FnZXMyLmxlbmd0aCAtIDEpLCBsYXN0TWVzc2FnZV07XG4gICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uRGF0YU1lc3NhZ2VQYXJ0KHZhbHVlKSB7XG4gICAgICAgICAgc2V0TWVzc2FnZXMoKG1lc3NhZ2VzMikgPT4ge1xuICAgICAgICAgICAgdmFyIF9hMjtcbiAgICAgICAgICAgIHJldHVybiBbXG4gICAgICAgICAgICAgIC4uLm1lc3NhZ2VzMixcbiAgICAgICAgICAgICAge1xuICAgICAgICAgICAgICAgIGlkOiAoX2EyID0gdmFsdWUuaWQpICE9IG51bGwgPyBfYTIgOiBnZW5lcmF0ZUlkKCksXG4gICAgICAgICAgICAgICAgcm9sZTogXCJkYXRhXCIsXG4gICAgICAgICAgICAgICAgY29udGVudDogXCJcIixcbiAgICAgICAgICAgICAgICBkYXRhOiB2YWx1ZS5kYXRhLFxuICAgICAgICAgICAgICAgIHBhcnRzOiBbXVxuICAgICAgICAgICAgICB9XG4gICAgICAgICAgICBdO1xuICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBvbkVycm9yUGFydCh2YWx1ZSkge1xuICAgICAgICAgIHNldEVycm9yKG5ldyBFcnJvcih2YWx1ZSkpO1xuICAgICAgICB9XG4gICAgICB9KTtcbiAgICB9IGNhdGNoIChlcnJvcjIpIHtcbiAgICAgIGlmIChpc0Fib3J0RXJyb3IoZXJyb3IyKSAmJiBhYm9ydENvbnRyb2xsZXIuc2lnbmFsLmFib3J0ZWQpIHtcbiAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBpZiAob25FcnJvciAmJiBlcnJvcjIgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBvbkVycm9yKGVycm9yMik7XG4gICAgICB9XG4gICAgICBzZXRFcnJvcihlcnJvcjIpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IG51bGw7XG4gICAgICBzZXRTdGF0dXMoXCJhd2FpdGluZ19tZXNzYWdlXCIpO1xuICAgIH1cbiAgfTtcbiAgY29uc3Qgc3VibWl0TWVzc2FnZSA9IGFzeW5jIChldmVudCwgcmVxdWVzdE9wdGlvbnMpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgKF9hID0gZXZlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IGV2ZW50LnByZXZlbnREZWZhdWx0KSA9PSBudWxsID8gdm9pZCAwIDogX2EuY2FsbChldmVudCk7XG4gICAgaWYgKGlucHV0ID09PSBcIlwiKSB7XG4gICAgICByZXR1cm47XG4gICAgfVxuICAgIGFwcGVuZCh7IHJvbGU6IFwidXNlclwiLCBjb250ZW50OiBpbnB1dCwgcGFydHM6IFtdIH0sIHJlcXVlc3RPcHRpb25zKTtcbiAgfTtcbiAgY29uc3Qgc2V0VGhyZWFkSWQgPSAodGhyZWFkSWQpID0+IHtcbiAgICBzZXRDdXJyZW50VGhyZWFkSWQodGhyZWFkSWQpO1xuICAgIHNldE1lc3NhZ2VzKFtdKTtcbiAgfTtcbiAgcmV0dXJuIHtcbiAgICBhcHBlbmQsXG4gICAgbWVzc2FnZXMsXG4gICAgc2V0TWVzc2FnZXMsXG4gICAgdGhyZWFkSWQ6IGN1cnJlbnRUaHJlYWRJZCxcbiAgICBzZXRUaHJlYWRJZCxcbiAgICBpbnB1dCxcbiAgICBzZXRJbnB1dCxcbiAgICBoYW5kbGVJbnB1dENoYW5nZSxcbiAgICBzdWJtaXRNZXNzYWdlLFxuICAgIHN0YXR1cyxcbiAgICBlcnJvcixcbiAgICBzdG9wXG4gIH07XG59XG5cbi8vIHNyYy91c2UtY2hhdC50c1xuaW1wb3J0IHtcbiAgY2FsbENoYXRBcGksXG4gIGV4dHJhY3RNYXhUb29sSW52b2NhdGlvblN0ZXAsXG4gIGZpbGxNZXNzYWdlUGFydHMsXG4gIGdlbmVyYXRlSWQgYXMgZ2VuZXJhdGVJZEZ1bmMsXG4gIGdldE1lc3NhZ2VQYXJ0cyxcbiAgaXNBc3Npc3RhbnRNZXNzYWdlV2l0aENvbXBsZXRlZFRvb2xDYWxscyxcbiAgcHJlcGFyZUF0dGFjaG1lbnRzRm9yUmVxdWVzdCxcbiAgc2hvdWxkUmVzdWJtaXRNZXNzYWdlcyxcbiAgdXBkYXRlVG9vbENhbGxSZXN1bHRcbn0gZnJvbSBcIkBhaS1zZGsvdWktdXRpbHNcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrIGFzIHVzZUNhbGxiYWNrMiwgdXNlRWZmZWN0IGFzIHVzZUVmZmVjdDIsIHVzZU1lbW8sIHVzZVJlZiBhcyB1c2VSZWYyLCB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTMgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB1c2VTV1IgZnJvbSBcInN3clwiO1xuXG4vLyBzcmMvdGhyb3R0bGUudHNcbmltcG9ydCB0aHJvdHRsZUZ1bmN0aW9uIGZyb20gXCJ0aHJvdHRsZWl0XCI7XG5mdW5jdGlvbiB0aHJvdHRsZShmbiwgd2FpdE1zKSB7XG4gIHJldHVybiB3YWl0TXMgIT0gbnVsbCA/IHRocm90dGxlRnVuY3Rpb24oZm4sIHdhaXRNcykgOiBmbjtcbn1cblxuLy8gc3JjL3V0aWwvdXNlLXN0YWJsZS12YWx1ZS50c1xuaW1wb3J0IHsgaXNEZWVwRXF1YWxEYXRhIH0gZnJvbSBcIkBhaS1zZGsvdWktdXRpbHNcIjtcbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlU3RhdGUgYXMgdXNlU3RhdGUyIH0gZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VTdGFibGVWYWx1ZShsYXRlc3RWYWx1ZSkge1xuICBjb25zdCBbdmFsdWUsIHNldFZhbHVlXSA9IHVzZVN0YXRlMihsYXRlc3RWYWx1ZSk7XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaWYgKCFpc0RlZXBFcXVhbERhdGEobGF0ZXN0VmFsdWUsIHZhbHVlKSkge1xuICAgICAgc2V0VmFsdWUobGF0ZXN0VmFsdWUpO1xuICAgIH1cbiAgfSwgW2xhdGVzdFZhbHVlLCB2YWx1ZV0pO1xuICByZXR1cm4gdmFsdWU7XG59XG5cbi8vIHNyYy91c2UtY2hhdC50c1xuZnVuY3Rpb24gdXNlQ2hhdCh7XG4gIGFwaSA9IFwiL2FwaS9jaGF0XCIsXG4gIGlkLFxuICBpbml0aWFsTWVzc2FnZXMsXG4gIGluaXRpYWxJbnB1dCA9IFwiXCIsXG4gIHNlbmRFeHRyYU1lc3NhZ2VGaWVsZHMsXG4gIG9uVG9vbENhbGwsXG4gIGV4cGVyaW1lbnRhbF9wcmVwYXJlUmVxdWVzdEJvZHksXG4gIG1heFN0ZXBzID0gMSxcbiAgc3RyZWFtUHJvdG9jb2wgPSBcImRhdGFcIixcbiAgb25SZXNwb25zZSxcbiAgb25GaW5pc2gsXG4gIG9uRXJyb3IsXG4gIGNyZWRlbnRpYWxzLFxuICBoZWFkZXJzLFxuICBib2R5LFxuICBnZW5lcmF0ZUlkOiBnZW5lcmF0ZUlkMiA9IGdlbmVyYXRlSWRGdW5jLFxuICBmZXRjaDogZmV0Y2gyLFxuICBrZWVwTGFzdE1lc3NhZ2VPbkVycm9yID0gdHJ1ZSxcbiAgZXhwZXJpbWVudGFsX3Rocm90dGxlOiB0aHJvdHRsZVdhaXRNc1xufSA9IHt9KSB7XG4gIGNvbnN0IFtob29rSWRdID0gdXNlU3RhdGUzKGdlbmVyYXRlSWQyKTtcbiAgY29uc3QgY2hhdElkID0gaWQgIT0gbnVsbCA/IGlkIDogaG9va0lkO1xuICBjb25zdCBjaGF0S2V5ID0gdHlwZW9mIGFwaSA9PT0gXCJzdHJpbmdcIiA/IFthcGksIGNoYXRJZF0gOiBjaGF0SWQ7XG4gIGNvbnN0IHN0YWJsZUluaXRpYWxNZXNzYWdlcyA9IHVzZVN0YWJsZVZhbHVlKGluaXRpYWxNZXNzYWdlcyAhPSBudWxsID8gaW5pdGlhbE1lc3NhZ2VzIDogW10pO1xuICBjb25zdCBwcm9jZXNzZWRJbml0aWFsTWVzc2FnZXMgPSB1c2VNZW1vKFxuICAgICgpID0+IGZpbGxNZXNzYWdlUGFydHMoc3RhYmxlSW5pdGlhbE1lc3NhZ2VzKSxcbiAgICBbc3RhYmxlSW5pdGlhbE1lc3NhZ2VzXVxuICApO1xuICBjb25zdCB7IGRhdGE6IG1lc3NhZ2VzLCBtdXRhdGUgfSA9IHVzZVNXUihcbiAgICBbY2hhdEtleSwgXCJtZXNzYWdlc1wiXSxcbiAgICBudWxsLFxuICAgIHsgZmFsbGJhY2tEYXRhOiBwcm9jZXNzZWRJbml0aWFsTWVzc2FnZXMgfVxuICApO1xuICBjb25zdCBtZXNzYWdlc1JlZiA9IHVzZVJlZjIobWVzc2FnZXMgfHwgW10pO1xuICB1c2VFZmZlY3QyKCgpID0+IHtcbiAgICBtZXNzYWdlc1JlZi5jdXJyZW50ID0gbWVzc2FnZXMgfHwgW107XG4gIH0sIFttZXNzYWdlc10pO1xuICBjb25zdCB7IGRhdGE6IHN0cmVhbURhdGEsIG11dGF0ZTogbXV0YXRlU3RyZWFtRGF0YSB9ID0gdXNlU1dSKFtjaGF0S2V5LCBcInN0cmVhbURhdGFcIl0sIG51bGwpO1xuICBjb25zdCBzdHJlYW1EYXRhUmVmID0gdXNlUmVmMihzdHJlYW1EYXRhKTtcbiAgdXNlRWZmZWN0MigoKSA9PiB7XG4gICAgc3RyZWFtRGF0YVJlZi5jdXJyZW50ID0gc3RyZWFtRGF0YTtcbiAgfSwgW3N0cmVhbURhdGFdKTtcbiAgY29uc3QgeyBkYXRhOiBzdGF0dXMgPSBcInJlYWR5XCIsIG11dGF0ZTogbXV0YXRlU3RhdHVzIH0gPSB1c2VTV1IoW2NoYXRLZXksIFwic3RhdHVzXCJdLCBudWxsKTtcbiAgY29uc3QgeyBkYXRhOiBlcnJvciA9IHZvaWQgMCwgbXV0YXRlOiBzZXRFcnJvciB9ID0gdXNlU1dSKFtjaGF0S2V5LCBcImVycm9yXCJdLCBudWxsKTtcbiAgY29uc3QgYWJvcnRDb250cm9sbGVyUmVmID0gdXNlUmVmMihudWxsKTtcbiAgY29uc3QgZXh0cmFNZXRhZGF0YVJlZiA9IHVzZVJlZjIoe1xuICAgIGNyZWRlbnRpYWxzLFxuICAgIGhlYWRlcnMsXG4gICAgYm9keVxuICB9KTtcbiAgdXNlRWZmZWN0MigoKSA9PiB7XG4gICAgZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50ID0ge1xuICAgICAgY3JlZGVudGlhbHMsXG4gICAgICBoZWFkZXJzLFxuICAgICAgYm9keVxuICAgIH07XG4gIH0sIFtjcmVkZW50aWFscywgaGVhZGVycywgYm9keV0pO1xuICBjb25zdCB0cmlnZ2VyUmVxdWVzdCA9IHVzZUNhbGxiYWNrMihcbiAgICBhc3luYyAoY2hhdFJlcXVlc3QpID0+IHtcbiAgICAgIHZhciBfYSwgX2I7XG4gICAgICBtdXRhdGVTdGF0dXMoXCJzdWJtaXR0ZWRcIik7XG4gICAgICBzZXRFcnJvcih2b2lkIDApO1xuICAgICAgY29uc3QgY2hhdE1lc3NhZ2VzID0gZmlsbE1lc3NhZ2VQYXJ0cyhjaGF0UmVxdWVzdC5tZXNzYWdlcyk7XG4gICAgICBjb25zdCBtZXNzYWdlQ291bnQgPSBjaGF0TWVzc2FnZXMubGVuZ3RoO1xuICAgICAgY29uc3QgbWF4U3RlcCA9IGV4dHJhY3RNYXhUb29sSW52b2NhdGlvblN0ZXAoXG4gICAgICAgIChfYSA9IGNoYXRNZXNzYWdlc1tjaGF0TWVzc2FnZXMubGVuZ3RoIC0gMV0pID09IG51bGwgPyB2b2lkIDAgOiBfYS50b29sSW52b2NhdGlvbnNcbiAgICAgICk7XG4gICAgICB0cnkge1xuICAgICAgICBjb25zdCBhYm9ydENvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gYWJvcnRDb250cm9sbGVyO1xuICAgICAgICBjb25zdCB0aHJvdHRsZWRNdXRhdGUgPSB0aHJvdHRsZShtdXRhdGUsIHRocm90dGxlV2FpdE1zKTtcbiAgICAgICAgY29uc3QgdGhyb3R0bGVkTXV0YXRlU3RyZWFtRGF0YSA9IHRocm90dGxlKFxuICAgICAgICAgIG11dGF0ZVN0cmVhbURhdGEsXG4gICAgICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICAgICAgKTtcbiAgICAgICAgY29uc3QgcHJldmlvdXNNZXNzYWdlcyA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQ7XG4gICAgICAgIHRocm90dGxlZE11dGF0ZShjaGF0TWVzc2FnZXMsIGZhbHNlKTtcbiAgICAgICAgY29uc3QgY29uc3RydWN0ZWRNZXNzYWdlc1BheWxvYWQgPSBzZW5kRXh0cmFNZXNzYWdlRmllbGRzID8gY2hhdE1lc3NhZ2VzIDogY2hhdE1lc3NhZ2VzLm1hcChcbiAgICAgICAgICAoe1xuICAgICAgICAgICAgcm9sZSxcbiAgICAgICAgICAgIGNvbnRlbnQsXG4gICAgICAgICAgICBleHBlcmltZW50YWxfYXR0YWNobWVudHMsXG4gICAgICAgICAgICBkYXRhLFxuICAgICAgICAgICAgYW5ub3RhdGlvbnMsXG4gICAgICAgICAgICB0b29sSW52b2NhdGlvbnMsXG4gICAgICAgICAgICBwYXJ0c1xuICAgICAgICAgIH0pID0+ICh7XG4gICAgICAgICAgICByb2xlLFxuICAgICAgICAgICAgY29udGVudCxcbiAgICAgICAgICAgIC4uLmV4cGVyaW1lbnRhbF9hdHRhY2htZW50cyAhPT0gdm9pZCAwICYmIHtcbiAgICAgICAgICAgICAgZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgLi4uZGF0YSAhPT0gdm9pZCAwICYmIHsgZGF0YSB9LFxuICAgICAgICAgICAgLi4uYW5ub3RhdGlvbnMgIT09IHZvaWQgMCAmJiB7IGFubm90YXRpb25zIH0sXG4gICAgICAgICAgICAuLi50b29sSW52b2NhdGlvbnMgIT09IHZvaWQgMCAmJiB7IHRvb2xJbnZvY2F0aW9ucyB9LFxuICAgICAgICAgICAgLi4ucGFydHMgIT09IHZvaWQgMCAmJiB7IHBhcnRzIH1cbiAgICAgICAgICB9KVxuICAgICAgICApO1xuICAgICAgICBjb25zdCBleGlzdGluZ0RhdGEgPSBzdHJlYW1EYXRhUmVmLmN1cnJlbnQ7XG4gICAgICAgIGF3YWl0IGNhbGxDaGF0QXBpKHtcbiAgICAgICAgICBhcGksXG4gICAgICAgICAgYm9keTogKF9iID0gZXhwZXJpbWVudGFsX3ByZXBhcmVSZXF1ZXN0Qm9keSA9PSBudWxsID8gdm9pZCAwIDogZXhwZXJpbWVudGFsX3ByZXBhcmVSZXF1ZXN0Qm9keSh7XG4gICAgICAgICAgICBpZDogY2hhdElkLFxuICAgICAgICAgICAgbWVzc2FnZXM6IGNoYXRNZXNzYWdlcyxcbiAgICAgICAgICAgIHJlcXVlc3REYXRhOiBjaGF0UmVxdWVzdC5kYXRhLFxuICAgICAgICAgICAgcmVxdWVzdEJvZHk6IGNoYXRSZXF1ZXN0LmJvZHlcbiAgICAgICAgICB9KSkgIT0gbnVsbCA/IF9iIDoge1xuICAgICAgICAgICAgaWQ6IGNoYXRJZCxcbiAgICAgICAgICAgIG1lc3NhZ2VzOiBjb25zdHJ1Y3RlZE1lc3NhZ2VzUGF5bG9hZCxcbiAgICAgICAgICAgIGRhdGE6IGNoYXRSZXF1ZXN0LmRhdGEsXG4gICAgICAgICAgICAuLi5leHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuYm9keSxcbiAgICAgICAgICAgIC4uLmNoYXRSZXF1ZXN0LmJvZHlcbiAgICAgICAgICB9LFxuICAgICAgICAgIHN0cmVhbVByb3RvY29sLFxuICAgICAgICAgIGNyZWRlbnRpYWxzOiBleHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuY3JlZGVudGlhbHMsXG4gICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgLi4uZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50LmhlYWRlcnMsXG4gICAgICAgICAgICAuLi5jaGF0UmVxdWVzdC5oZWFkZXJzXG4gICAgICAgICAgfSxcbiAgICAgICAgICBhYm9ydENvbnRyb2xsZXI6ICgpID0+IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50LFxuICAgICAgICAgIHJlc3RvcmVNZXNzYWdlc09uRmFpbHVyZSgpIHtcbiAgICAgICAgICAgIGlmICgha2VlcExhc3RNZXNzYWdlT25FcnJvcikge1xuICAgICAgICAgICAgICB0aHJvdHRsZWRNdXRhdGUocHJldmlvdXNNZXNzYWdlcywgZmFsc2UpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgb25SZXNwb25zZSxcbiAgICAgICAgICBvblVwZGF0ZSh7IG1lc3NhZ2UsIGRhdGEsIHJlcGxhY2VMYXN0TWVzc2FnZSB9KSB7XG4gICAgICAgICAgICBtdXRhdGVTdGF0dXMoXCJzdHJlYW1pbmdcIik7XG4gICAgICAgICAgICB0aHJvdHRsZWRNdXRhdGUoXG4gICAgICAgICAgICAgIFtcbiAgICAgICAgICAgICAgICAuLi5yZXBsYWNlTGFzdE1lc3NhZ2UgPyBjaGF0TWVzc2FnZXMuc2xpY2UoMCwgY2hhdE1lc3NhZ2VzLmxlbmd0aCAtIDEpIDogY2hhdE1lc3NhZ2VzLFxuICAgICAgICAgICAgICAgIG1lc3NhZ2VcbiAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgZmFsc2VcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgICBpZiAoZGF0YSA9PSBudWxsID8gdm9pZCAwIDogZGF0YS5sZW5ndGgpIHtcbiAgICAgICAgICAgICAgdGhyb3R0bGVkTXV0YXRlU3RyZWFtRGF0YShcbiAgICAgICAgICAgICAgICBbLi4uZXhpc3RpbmdEYXRhICE9IG51bGwgPyBleGlzdGluZ0RhdGEgOiBbXSwgLi4uZGF0YV0sXG4gICAgICAgICAgICAgICAgZmFsc2VcbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9LFxuICAgICAgICAgIG9uVG9vbENhbGwsXG4gICAgICAgICAgb25GaW5pc2gsXG4gICAgICAgICAgZ2VuZXJhdGVJZDogZ2VuZXJhdGVJZDIsXG4gICAgICAgICAgZmV0Y2g6IGZldGNoMixcbiAgICAgICAgICBsYXN0TWVzc2FnZTogY2hhdE1lc3NhZ2VzW2NoYXRNZXNzYWdlcy5sZW5ndGggLSAxXVxuICAgICAgICB9KTtcbiAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICBtdXRhdGVTdGF0dXMoXCJyZWFkeVwiKTtcbiAgICAgIH0gY2F0Y2ggKGVycikge1xuICAgICAgICBpZiAoZXJyLm5hbWUgPT09IFwiQWJvcnRFcnJvclwiKSB7XG4gICAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICAgIG11dGF0ZVN0YXR1cyhcInJlYWR5XCIpO1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICAgIGlmIChvbkVycm9yICYmIGVyciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgICAgICAgb25FcnJvcihlcnIpO1xuICAgICAgICB9XG4gICAgICAgIHNldEVycm9yKGVycik7XG4gICAgICAgIG11dGF0ZVN0YXR1cyhcImVycm9yXCIpO1xuICAgICAgfVxuICAgICAgY29uc3QgbWVzc2FnZXMyID0gbWVzc2FnZXNSZWYuY3VycmVudDtcbiAgICAgIGlmIChzaG91bGRSZXN1Ym1pdE1lc3NhZ2VzKHtcbiAgICAgICAgb3JpZ2luYWxNYXhUb29sSW52b2NhdGlvblN0ZXA6IG1heFN0ZXAsXG4gICAgICAgIG9yaWdpbmFsTWVzc2FnZUNvdW50OiBtZXNzYWdlQ291bnQsXG4gICAgICAgIG1heFN0ZXBzLFxuICAgICAgICBtZXNzYWdlczogbWVzc2FnZXMyXG4gICAgICB9KSkge1xuICAgICAgICBhd2FpdCB0cmlnZ2VyUmVxdWVzdCh7IG1lc3NhZ2VzOiBtZXNzYWdlczIgfSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbXG4gICAgICBtdXRhdGUsXG4gICAgICBtdXRhdGVTdGF0dXMsXG4gICAgICBhcGksXG4gICAgICBleHRyYU1ldGFkYXRhUmVmLFxuICAgICAgb25SZXNwb25zZSxcbiAgICAgIG9uRmluaXNoLFxuICAgICAgb25FcnJvcixcbiAgICAgIHNldEVycm9yLFxuICAgICAgbXV0YXRlU3RyZWFtRGF0YSxcbiAgICAgIHN0cmVhbURhdGFSZWYsXG4gICAgICBzdHJlYW1Qcm90b2NvbCxcbiAgICAgIHNlbmRFeHRyYU1lc3NhZ2VGaWVsZHMsXG4gICAgICBleHBlcmltZW50YWxfcHJlcGFyZVJlcXVlc3RCb2R5LFxuICAgICAgb25Ub29sQ2FsbCxcbiAgICAgIG1heFN0ZXBzLFxuICAgICAgbWVzc2FnZXNSZWYsXG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYsXG4gICAgICBnZW5lcmF0ZUlkMixcbiAgICAgIGZldGNoMixcbiAgICAgIGtlZXBMYXN0TWVzc2FnZU9uRXJyb3IsXG4gICAgICB0aHJvdHRsZVdhaXRNcyxcbiAgICAgIGNoYXRJZFxuICAgIF1cbiAgKTtcbiAgY29uc3QgYXBwZW5kID0gdXNlQ2FsbGJhY2syKFxuICAgIGFzeW5jIChtZXNzYWdlLCB7XG4gICAgICBkYXRhLFxuICAgICAgaGVhZGVyczogaGVhZGVyczIsXG4gICAgICBib2R5OiBib2R5MixcbiAgICAgIGV4cGVyaW1lbnRhbF9hdHRhY2htZW50c1xuICAgIH0gPSB7fSkgPT4ge1xuICAgICAgdmFyIF9hLCBfYjtcbiAgICAgIGNvbnN0IGF0dGFjaG1lbnRzRm9yUmVxdWVzdCA9IGF3YWl0IHByZXBhcmVBdHRhY2htZW50c0ZvclJlcXVlc3QoXG4gICAgICAgIGV4cGVyaW1lbnRhbF9hdHRhY2htZW50c1xuICAgICAgKTtcbiAgICAgIGNvbnN0IG1lc3NhZ2VzMiA9IG1lc3NhZ2VzUmVmLmN1cnJlbnQuY29uY2F0KHtcbiAgICAgICAgLi4ubWVzc2FnZSxcbiAgICAgICAgaWQ6IChfYSA9IG1lc3NhZ2UuaWQpICE9IG51bGwgPyBfYSA6IGdlbmVyYXRlSWQyKCksXG4gICAgICAgIGNyZWF0ZWRBdDogKF9iID0gbWVzc2FnZS5jcmVhdGVkQXQpICE9IG51bGwgPyBfYiA6IC8qIEBfX1BVUkVfXyAqLyBuZXcgRGF0ZSgpLFxuICAgICAgICBleHBlcmltZW50YWxfYXR0YWNobWVudHM6IGF0dGFjaG1lbnRzRm9yUmVxdWVzdC5sZW5ndGggPiAwID8gYXR0YWNobWVudHNGb3JSZXF1ZXN0IDogdm9pZCAwLFxuICAgICAgICBwYXJ0czogZ2V0TWVzc2FnZVBhcnRzKG1lc3NhZ2UpXG4gICAgICB9KTtcbiAgICAgIHJldHVybiB0cmlnZ2VyUmVxdWVzdCh7IG1lc3NhZ2VzOiBtZXNzYWdlczIsIGhlYWRlcnM6IGhlYWRlcnMyLCBib2R5OiBib2R5MiwgZGF0YSB9KTtcbiAgICB9LFxuICAgIFt0cmlnZ2VyUmVxdWVzdCwgZ2VuZXJhdGVJZDJdXG4gICk7XG4gIGNvbnN0IHJlbG9hZCA9IHVzZUNhbGxiYWNrMihcbiAgICBhc3luYyAoeyBkYXRhLCBoZWFkZXJzOiBoZWFkZXJzMiwgYm9keTogYm9keTIgfSA9IHt9KSA9PiB7XG4gICAgICBjb25zdCBtZXNzYWdlczIgPSBtZXNzYWdlc1JlZi5jdXJyZW50O1xuICAgICAgaWYgKG1lc3NhZ2VzMi5sZW5ndGggPT09IDApIHtcbiAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICB9XG4gICAgICBjb25zdCBsYXN0TWVzc2FnZSA9IG1lc3NhZ2VzMlttZXNzYWdlczIubGVuZ3RoIC0gMV07XG4gICAgICByZXR1cm4gdHJpZ2dlclJlcXVlc3Qoe1xuICAgICAgICBtZXNzYWdlczogbGFzdE1lc3NhZ2Uucm9sZSA9PT0gXCJhc3Npc3RhbnRcIiA/IG1lc3NhZ2VzMi5zbGljZSgwLCAtMSkgOiBtZXNzYWdlczIsXG4gICAgICAgIGhlYWRlcnM6IGhlYWRlcnMyLFxuICAgICAgICBib2R5OiBib2R5MixcbiAgICAgICAgZGF0YVxuICAgICAgfSk7XG4gICAgfSxcbiAgICBbdHJpZ2dlclJlcXVlc3RdXG4gICk7XG4gIGNvbnN0IHN0b3AgPSB1c2VDYWxsYmFjazIoKCkgPT4ge1xuICAgIGlmIChhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCkge1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQuYWJvcnQoKTtcbiAgICAgIGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG4gIH0sIFtdKTtcbiAgY29uc3Qgc2V0TWVzc2FnZXMgPSB1c2VDYWxsYmFjazIoXG4gICAgKG1lc3NhZ2VzMikgPT4ge1xuICAgICAgaWYgKHR5cGVvZiBtZXNzYWdlczIgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICBtZXNzYWdlczIgPSBtZXNzYWdlczIobWVzc2FnZXNSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgICBjb25zdCBtZXNzYWdlc1dpdGhQYXJ0cyA9IGZpbGxNZXNzYWdlUGFydHMobWVzc2FnZXMyKTtcbiAgICAgIG11dGF0ZShtZXNzYWdlc1dpdGhQYXJ0cywgZmFsc2UpO1xuICAgICAgbWVzc2FnZXNSZWYuY3VycmVudCA9IG1lc3NhZ2VzV2l0aFBhcnRzO1xuICAgIH0sXG4gICAgW211dGF0ZV1cbiAgKTtcbiAgY29uc3Qgc2V0RGF0YSA9IHVzZUNhbGxiYWNrMihcbiAgICAoZGF0YSkgPT4ge1xuICAgICAgaWYgKHR5cGVvZiBkYXRhID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgZGF0YSA9IGRhdGEoc3RyZWFtRGF0YVJlZi5jdXJyZW50KTtcbiAgICAgIH1cbiAgICAgIG11dGF0ZVN0cmVhbURhdGEoZGF0YSwgZmFsc2UpO1xuICAgICAgc3RyZWFtRGF0YVJlZi5jdXJyZW50ID0gZGF0YTtcbiAgICB9LFxuICAgIFttdXRhdGVTdHJlYW1EYXRhXVxuICApO1xuICBjb25zdCBbaW5wdXQsIHNldElucHV0XSA9IHVzZVN0YXRlMyhpbml0aWFsSW5wdXQpO1xuICBjb25zdCBoYW5kbGVTdWJtaXQgPSB1c2VDYWxsYmFjazIoXG4gICAgYXN5bmMgKGV2ZW50LCBvcHRpb25zID0ge30sIG1ldGFkYXRhKSA9PiB7XG4gICAgICB2YXIgX2E7XG4gICAgICAoX2EgPSBldmVudCA9PSBudWxsID8gdm9pZCAwIDogZXZlbnQucHJldmVudERlZmF1bHQpID09IG51bGwgPyB2b2lkIDAgOiBfYS5jYWxsKGV2ZW50KTtcbiAgICAgIGlmICghaW5wdXQgJiYgIW9wdGlvbnMuYWxsb3dFbXB0eVN1Ym1pdClcbiAgICAgICAgcmV0dXJuO1xuICAgICAgaWYgKG1ldGFkYXRhKSB7XG4gICAgICAgIGV4dHJhTWV0YWRhdGFSZWYuY3VycmVudCA9IHtcbiAgICAgICAgICAuLi5leHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQsXG4gICAgICAgICAgLi4ubWV0YWRhdGFcbiAgICAgICAgfTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGF0dGFjaG1lbnRzRm9yUmVxdWVzdCA9IGF3YWl0IHByZXBhcmVBdHRhY2htZW50c0ZvclJlcXVlc3QoXG4gICAgICAgIG9wdGlvbnMuZXhwZXJpbWVudGFsX2F0dGFjaG1lbnRzXG4gICAgICApO1xuICAgICAgY29uc3QgbWVzc2FnZXMyID0gbWVzc2FnZXNSZWYuY3VycmVudC5jb25jYXQoe1xuICAgICAgICBpZDogZ2VuZXJhdGVJZDIoKSxcbiAgICAgICAgY3JlYXRlZEF0OiAvKiBAX19QVVJFX18gKi8gbmV3IERhdGUoKSxcbiAgICAgICAgcm9sZTogXCJ1c2VyXCIsXG4gICAgICAgIGNvbnRlbnQ6IGlucHV0LFxuICAgICAgICBleHBlcmltZW50YWxfYXR0YWNobWVudHM6IGF0dGFjaG1lbnRzRm9yUmVxdWVzdC5sZW5ndGggPiAwID8gYXR0YWNobWVudHNGb3JSZXF1ZXN0IDogdm9pZCAwLFxuICAgICAgICBwYXJ0czogW3sgdHlwZTogXCJ0ZXh0XCIsIHRleHQ6IGlucHV0IH1dXG4gICAgICB9KTtcbiAgICAgIGNvbnN0IGNoYXRSZXF1ZXN0ID0ge1xuICAgICAgICBtZXNzYWdlczogbWVzc2FnZXMyLFxuICAgICAgICBoZWFkZXJzOiBvcHRpb25zLmhlYWRlcnMsXG4gICAgICAgIGJvZHk6IG9wdGlvbnMuYm9keSxcbiAgICAgICAgZGF0YTogb3B0aW9ucy5kYXRhXG4gICAgICB9O1xuICAgICAgdHJpZ2dlclJlcXVlc3QoY2hhdFJlcXVlc3QpO1xuICAgICAgc2V0SW5wdXQoXCJcIik7XG4gICAgfSxcbiAgICBbaW5wdXQsIGdlbmVyYXRlSWQyLCB0cmlnZ2VyUmVxdWVzdF1cbiAgKTtcbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSAoZSkgPT4ge1xuICAgIHNldElucHV0KGUudGFyZ2V0LnZhbHVlKTtcbiAgfTtcbiAgY29uc3QgYWRkVG9vbFJlc3VsdCA9IHVzZUNhbGxiYWNrMihcbiAgICAoeyB0b29sQ2FsbElkLCByZXN1bHQgfSkgPT4ge1xuICAgICAgY29uc3QgY3VycmVudE1lc3NhZ2VzID0gbWVzc2FnZXNSZWYuY3VycmVudDtcbiAgICAgIHVwZGF0ZVRvb2xDYWxsUmVzdWx0KHtcbiAgICAgICAgbWVzc2FnZXM6IGN1cnJlbnRNZXNzYWdlcyxcbiAgICAgICAgdG9vbENhbGxJZCxcbiAgICAgICAgdG9vbFJlc3VsdDogcmVzdWx0XG4gICAgICB9KTtcbiAgICAgIG11dGF0ZShjdXJyZW50TWVzc2FnZXMsIGZhbHNlKTtcbiAgICAgIGNvbnN0IGxhc3RNZXNzYWdlID0gY3VycmVudE1lc3NhZ2VzW2N1cnJlbnRNZXNzYWdlcy5sZW5ndGggLSAxXTtcbiAgICAgIGlmIChpc0Fzc2lzdGFudE1lc3NhZ2VXaXRoQ29tcGxldGVkVG9vbENhbGxzKGxhc3RNZXNzYWdlKSkge1xuICAgICAgICB0cmlnZ2VyUmVxdWVzdCh7IG1lc3NhZ2VzOiBjdXJyZW50TWVzc2FnZXMgfSk7XG4gICAgICB9XG4gICAgfSxcbiAgICBbbXV0YXRlLCB0cmlnZ2VyUmVxdWVzdF1cbiAgKTtcbiAgcmV0dXJuIHtcbiAgICBtZXNzYWdlczogbWVzc2FnZXMgIT0gbnVsbCA/IG1lc3NhZ2VzIDogW10sXG4gICAgaWQ6IGNoYXRJZCxcbiAgICBzZXRNZXNzYWdlcyxcbiAgICBkYXRhOiBzdHJlYW1EYXRhLFxuICAgIHNldERhdGEsXG4gICAgZXJyb3IsXG4gICAgYXBwZW5kLFxuICAgIHJlbG9hZCxcbiAgICBzdG9wLFxuICAgIGlucHV0LFxuICAgIHNldElucHV0LFxuICAgIGhhbmRsZUlucHV0Q2hhbmdlLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBpc0xvYWRpbmc6IHN0YXR1cyA9PT0gXCJzdWJtaXR0ZWRcIiB8fCBzdGF0dXMgPT09IFwic3RyZWFtaW5nXCIsXG4gICAgc3RhdHVzLFxuICAgIGFkZFRvb2xSZXN1bHRcbiAgfTtcbn1cblxuLy8gc3JjL3VzZS1jb21wbGV0aW9uLnRzXG5pbXBvcnQge1xuICBjYWxsQ29tcGxldGlvbkFwaVxufSBmcm9tIFwiQGFpLXNkay91aS11dGlsc1wiO1xuaW1wb3J0IHsgdXNlQ2FsbGJhY2sgYXMgdXNlQ2FsbGJhY2szLCB1c2VFZmZlY3QgYXMgdXNlRWZmZWN0MywgdXNlSWQsIHVzZVJlZiBhcyB1c2VSZWYzLCB1c2VTdGF0ZSBhcyB1c2VTdGF0ZTQgfSBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB1c2VTV1IyIGZyb20gXCJzd3JcIjtcbmZ1bmN0aW9uIHVzZUNvbXBsZXRpb24oe1xuICBhcGkgPSBcIi9hcGkvY29tcGxldGlvblwiLFxuICBpZCxcbiAgaW5pdGlhbENvbXBsZXRpb24gPSBcIlwiLFxuICBpbml0aWFsSW5wdXQgPSBcIlwiLFxuICBjcmVkZW50aWFscyxcbiAgaGVhZGVycyxcbiAgYm9keSxcbiAgc3RyZWFtUHJvdG9jb2wgPSBcImRhdGFcIixcbiAgZmV0Y2g6IGZldGNoMixcbiAgb25SZXNwb25zZSxcbiAgb25GaW5pc2gsXG4gIG9uRXJyb3IsXG4gIGV4cGVyaW1lbnRhbF90aHJvdHRsZTogdGhyb3R0bGVXYWl0TXNcbn0gPSB7fSkge1xuICBjb25zdCBob29rSWQgPSB1c2VJZCgpO1xuICBjb25zdCBjb21wbGV0aW9uSWQgPSBpZCB8fCBob29rSWQ7XG4gIGNvbnN0IHsgZGF0YSwgbXV0YXRlIH0gPSB1c2VTV1IyKFthcGksIGNvbXBsZXRpb25JZF0sIG51bGwsIHtcbiAgICBmYWxsYmFja0RhdGE6IGluaXRpYWxDb21wbGV0aW9uXG4gIH0pO1xuICBjb25zdCB7IGRhdGE6IGlzTG9hZGluZyA9IGZhbHNlLCBtdXRhdGU6IG11dGF0ZUxvYWRpbmcgfSA9IHVzZVNXUjIoXG4gICAgW2NvbXBsZXRpb25JZCwgXCJsb2FkaW5nXCJdLFxuICAgIG51bGxcbiAgKTtcbiAgY29uc3QgeyBkYXRhOiBzdHJlYW1EYXRhLCBtdXRhdGU6IG11dGF0ZVN0cmVhbURhdGEgfSA9IHVzZVNXUjIoW2NvbXBsZXRpb25JZCwgXCJzdHJlYW1EYXRhXCJdLCBudWxsKTtcbiAgY29uc3QgW2Vycm9yLCBzZXRFcnJvcl0gPSB1c2VTdGF0ZTQodm9pZCAwKTtcbiAgY29uc3QgY29tcGxldGlvbiA9IGRhdGE7XG4gIGNvbnN0IFthYm9ydENvbnRyb2xsZXIsIHNldEFib3J0Q29udHJvbGxlcl0gPSB1c2VTdGF0ZTQobnVsbCk7XG4gIGNvbnN0IGV4dHJhTWV0YWRhdGFSZWYgPSB1c2VSZWYzKHtcbiAgICBjcmVkZW50aWFscyxcbiAgICBoZWFkZXJzLFxuICAgIGJvZHlcbiAgfSk7XG4gIHVzZUVmZmVjdDMoKCkgPT4ge1xuICAgIGV4dHJhTWV0YWRhdGFSZWYuY3VycmVudCA9IHtcbiAgICAgIGNyZWRlbnRpYWxzLFxuICAgICAgaGVhZGVycyxcbiAgICAgIGJvZHlcbiAgICB9O1xuICB9LCBbY3JlZGVudGlhbHMsIGhlYWRlcnMsIGJvZHldKTtcbiAgY29uc3QgdHJpZ2dlclJlcXVlc3QgPSB1c2VDYWxsYmFjazMoXG4gICAgYXN5bmMgKHByb21wdCwgb3B0aW9ucykgPT4gY2FsbENvbXBsZXRpb25BcGkoe1xuICAgICAgYXBpLFxuICAgICAgcHJvbXB0LFxuICAgICAgY3JlZGVudGlhbHM6IGV4dHJhTWV0YWRhdGFSZWYuY3VycmVudC5jcmVkZW50aWFscyxcbiAgICAgIGhlYWRlcnM6IHsgLi4uZXh0cmFNZXRhZGF0YVJlZi5jdXJyZW50LmhlYWRlcnMsIC4uLm9wdGlvbnMgPT0gbnVsbCA/IHZvaWQgMCA6IG9wdGlvbnMuaGVhZGVycyB9LFxuICAgICAgYm9keToge1xuICAgICAgICAuLi5leHRyYU1ldGFkYXRhUmVmLmN1cnJlbnQuYm9keSxcbiAgICAgICAgLi4ub3B0aW9ucyA9PSBudWxsID8gdm9pZCAwIDogb3B0aW9ucy5ib2R5XG4gICAgICB9LFxuICAgICAgc3RyZWFtUHJvdG9jb2wsXG4gICAgICBmZXRjaDogZmV0Y2gyLFxuICAgICAgLy8gdGhyb3R0bGUgc3RyZWFtZWQgdWkgdXBkYXRlczpcbiAgICAgIHNldENvbXBsZXRpb246IHRocm90dGxlKFxuICAgICAgICAoY29tcGxldGlvbjIpID0+IG11dGF0ZShjb21wbGV0aW9uMiwgZmFsc2UpLFxuICAgICAgICB0aHJvdHRsZVdhaXRNc1xuICAgICAgKSxcbiAgICAgIG9uRGF0YTogdGhyb3R0bGUoXG4gICAgICAgIChkYXRhMikgPT4gbXV0YXRlU3RyZWFtRGF0YShbLi4uc3RyZWFtRGF0YSAhPSBudWxsID8gc3RyZWFtRGF0YSA6IFtdLCAuLi5kYXRhMiAhPSBudWxsID8gZGF0YTIgOiBbXV0sIGZhbHNlKSxcbiAgICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICAgICksXG4gICAgICBzZXRMb2FkaW5nOiBtdXRhdGVMb2FkaW5nLFxuICAgICAgc2V0RXJyb3IsXG4gICAgICBzZXRBYm9ydENvbnRyb2xsZXIsXG4gICAgICBvblJlc3BvbnNlLFxuICAgICAgb25GaW5pc2gsXG4gICAgICBvbkVycm9yXG4gICAgfSksXG4gICAgW1xuICAgICAgbXV0YXRlLFxuICAgICAgbXV0YXRlTG9hZGluZyxcbiAgICAgIGFwaSxcbiAgICAgIGV4dHJhTWV0YWRhdGFSZWYsXG4gICAgICBzZXRBYm9ydENvbnRyb2xsZXIsXG4gICAgICBvblJlc3BvbnNlLFxuICAgICAgb25GaW5pc2gsXG4gICAgICBvbkVycm9yLFxuICAgICAgc2V0RXJyb3IsXG4gICAgICBzdHJlYW1EYXRhLFxuICAgICAgc3RyZWFtUHJvdG9jb2wsXG4gICAgICBmZXRjaDIsXG4gICAgICBtdXRhdGVTdHJlYW1EYXRhLFxuICAgICAgdGhyb3R0bGVXYWl0TXNcbiAgICBdXG4gICk7XG4gIGNvbnN0IHN0b3AgPSB1c2VDYWxsYmFjazMoKCkgPT4ge1xuICAgIGlmIChhYm9ydENvbnRyb2xsZXIpIHtcbiAgICAgIGFib3J0Q29udHJvbGxlci5hYm9ydCgpO1xuICAgICAgc2V0QWJvcnRDb250cm9sbGVyKG51bGwpO1xuICAgIH1cbiAgfSwgW2Fib3J0Q29udHJvbGxlcl0pO1xuICBjb25zdCBzZXRDb21wbGV0aW9uID0gdXNlQ2FsbGJhY2szKFxuICAgIChjb21wbGV0aW9uMikgPT4ge1xuICAgICAgbXV0YXRlKGNvbXBsZXRpb24yLCBmYWxzZSk7XG4gICAgfSxcbiAgICBbbXV0YXRlXVxuICApO1xuICBjb25zdCBjb21wbGV0ZSA9IHVzZUNhbGxiYWNrMyhcbiAgICBhc3luYyAocHJvbXB0LCBvcHRpb25zKSA9PiB7XG4gICAgICByZXR1cm4gdHJpZ2dlclJlcXVlc3QocHJvbXB0LCBvcHRpb25zKTtcbiAgICB9LFxuICAgIFt0cmlnZ2VyUmVxdWVzdF1cbiAgKTtcbiAgY29uc3QgW2lucHV0LCBzZXRJbnB1dF0gPSB1c2VTdGF0ZTQoaW5pdGlhbElucHV0KTtcbiAgY29uc3QgaGFuZGxlU3VibWl0ID0gdXNlQ2FsbGJhY2szKFxuICAgIChldmVudCkgPT4ge1xuICAgICAgdmFyIF9hO1xuICAgICAgKF9hID0gZXZlbnQgPT0gbnVsbCA/IHZvaWQgMCA6IGV2ZW50LnByZXZlbnREZWZhdWx0KSA9PSBudWxsID8gdm9pZCAwIDogX2EuY2FsbChldmVudCk7XG4gICAgICByZXR1cm4gaW5wdXQgPyBjb21wbGV0ZShpbnB1dCkgOiB2b2lkIDA7XG4gICAgfSxcbiAgICBbaW5wdXQsIGNvbXBsZXRlXVxuICApO1xuICBjb25zdCBoYW5kbGVJbnB1dENoYW5nZSA9IHVzZUNhbGxiYWNrMyhcbiAgICAoZSkgPT4ge1xuICAgICAgc2V0SW5wdXQoZS50YXJnZXQudmFsdWUpO1xuICAgIH0sXG4gICAgW3NldElucHV0XVxuICApO1xuICByZXR1cm4ge1xuICAgIGNvbXBsZXRpb24sXG4gICAgY29tcGxldGUsXG4gICAgZXJyb3IsXG4gICAgc2V0Q29tcGxldGlvbixcbiAgICBzdG9wLFxuICAgIGlucHV0LFxuICAgIHNldElucHV0LFxuICAgIGhhbmRsZUlucHV0Q2hhbmdlLFxuICAgIGhhbmRsZVN1Ym1pdCxcbiAgICBpc0xvYWRpbmcsXG4gICAgZGF0YTogc3RyZWFtRGF0YVxuICB9O1xufVxuXG4vLyBzcmMvdXNlLW9iamVjdC50c1xuaW1wb3J0IHtcbiAgaXNBYm9ydEVycm9yIGFzIGlzQWJvcnRFcnJvcjIsXG4gIHNhZmVWYWxpZGF0ZVR5cGVzXG59IGZyb20gXCJAYWktc2RrL3Byb3ZpZGVyLXV0aWxzXCI7XG5pbXBvcnQge1xuICBhc1NjaGVtYSxcbiAgaXNEZWVwRXF1YWxEYXRhIGFzIGlzRGVlcEVxdWFsRGF0YTIsXG4gIHBhcnNlUGFydGlhbEpzb25cbn0gZnJvbSBcIkBhaS1zZGsvdWktdXRpbHNcIjtcbmltcG9ydCB7IHVzZUNhbGxiYWNrIGFzIHVzZUNhbGxiYWNrNCwgdXNlSWQgYXMgdXNlSWQyLCB1c2VSZWYgYXMgdXNlUmVmNCwgdXNlU3RhdGUgYXMgdXNlU3RhdGU1IH0gZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgdXNlU1dSMyBmcm9tIFwic3dyXCI7XG52YXIgZ2V0T3JpZ2luYWxGZXRjaDIgPSAoKSA9PiBmZXRjaDtcbmZ1bmN0aW9uIHVzZU9iamVjdCh7XG4gIGFwaSxcbiAgaWQsXG4gIHNjaGVtYSxcbiAgLy8gcmVxdWlyZWQsIGluIHRoZSBmdXR1cmUgd2Ugd2lsbCB1c2UgaXQgZm9yIHZhbGlkYXRpb25cbiAgaW5pdGlhbFZhbHVlLFxuICBmZXRjaDogZmV0Y2gyLFxuICBvbkVycm9yLFxuICBvbkZpbmlzaCxcbiAgaGVhZGVyc1xufSkge1xuICBjb25zdCBob29rSWQgPSB1c2VJZDIoKTtcbiAgY29uc3QgY29tcGxldGlvbklkID0gaWQgIT0gbnVsbCA/IGlkIDogaG9va0lkO1xuICBjb25zdCB7IGRhdGEsIG11dGF0ZSB9ID0gdXNlU1dSMyhcbiAgICBbYXBpLCBjb21wbGV0aW9uSWRdLFxuICAgIG51bGwsXG4gICAgeyBmYWxsYmFja0RhdGE6IGluaXRpYWxWYWx1ZSB9XG4gICk7XG4gIGNvbnN0IFtlcnJvciwgc2V0RXJyb3JdID0gdXNlU3RhdGU1KHZvaWQgMCk7XG4gIGNvbnN0IFtpc0xvYWRpbmcsIHNldElzTG9hZGluZ10gPSB1c2VTdGF0ZTUoZmFsc2UpO1xuICBjb25zdCBhYm9ydENvbnRyb2xsZXJSZWYgPSB1c2VSZWY0KG51bGwpO1xuICBjb25zdCBzdG9wID0gdXNlQ2FsbGJhY2s0KCgpID0+IHtcbiAgICB2YXIgX2E7XG4gICAgdHJ5IHtcbiAgICAgIChfYSA9IGFib3J0Q29udHJvbGxlclJlZi5jdXJyZW50KSA9PSBudWxsID8gdm9pZCAwIDogX2EuYWJvcnQoKTtcbiAgICB9IGNhdGNoIChpZ25vcmVkKSB7XG4gICAgfSBmaW5hbGx5IHtcbiAgICAgIHNldElzTG9hZGluZyhmYWxzZSk7XG4gICAgICBhYm9ydENvbnRyb2xsZXJSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuICB9LCBbXSk7XG4gIGNvbnN0IHN1Ym1pdCA9IGFzeW5jIChpbnB1dCkgPT4ge1xuICAgIHZhciBfYTtcbiAgICB0cnkge1xuICAgICAgbXV0YXRlKHZvaWQgMCk7XG4gICAgICBzZXRJc0xvYWRpbmcodHJ1ZSk7XG4gICAgICBzZXRFcnJvcih2b2lkIDApO1xuICAgICAgY29uc3QgYWJvcnRDb250cm9sbGVyID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBhYm9ydENvbnRyb2xsZXI7XG4gICAgICBjb25zdCBhY3R1YWxGZXRjaCA9IGZldGNoMiAhPSBudWxsID8gZmV0Y2gyIDogZ2V0T3JpZ2luYWxGZXRjaDIoKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgYWN0dWFsRmV0Y2goYXBpLCB7XG4gICAgICAgIG1ldGhvZDogXCJQT1NUXCIsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICBcIkNvbnRlbnQtVHlwZVwiOiBcImFwcGxpY2F0aW9uL2pzb25cIixcbiAgICAgICAgICAuLi5oZWFkZXJzXG4gICAgICAgIH0sXG4gICAgICAgIHNpZ25hbDogYWJvcnRDb250cm9sbGVyLnNpZ25hbCxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoaW5wdXQpXG4gICAgICB9KTtcbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFxuICAgICAgICAgIChfYSA9IGF3YWl0IHJlc3BvbnNlLnRleHQoKSkgIT0gbnVsbCA/IF9hIDogXCJGYWlsZWQgdG8gZmV0Y2ggdGhlIHJlc3BvbnNlLlwiXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgICBpZiAocmVzcG9uc2UuYm9keSA9PSBudWxsKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIlRoZSByZXNwb25zZSBib2R5IGlzIGVtcHR5LlwiKTtcbiAgICAgIH1cbiAgICAgIGxldCBhY2N1bXVsYXRlZFRleHQgPSBcIlwiO1xuICAgICAgbGV0IGxhdGVzdE9iamVjdCA9IHZvaWQgMDtcbiAgICAgIGF3YWl0IHJlc3BvbnNlLmJvZHkucGlwZVRocm91Z2gobmV3IFRleHREZWNvZGVyU3RyZWFtKCkpLnBpcGVUbyhcbiAgICAgICAgbmV3IFdyaXRhYmxlU3RyZWFtKHtcbiAgICAgICAgICB3cml0ZShjaHVuaykge1xuICAgICAgICAgICAgYWNjdW11bGF0ZWRUZXh0ICs9IGNodW5rO1xuICAgICAgICAgICAgY29uc3QgeyB2YWx1ZSB9ID0gcGFyc2VQYXJ0aWFsSnNvbihhY2N1bXVsYXRlZFRleHQpO1xuICAgICAgICAgICAgY29uc3QgY3VycmVudE9iamVjdCA9IHZhbHVlO1xuICAgICAgICAgICAgaWYgKCFpc0RlZXBFcXVhbERhdGEyKGxhdGVzdE9iamVjdCwgY3VycmVudE9iamVjdCkpIHtcbiAgICAgICAgICAgICAgbGF0ZXN0T2JqZWN0ID0gY3VycmVudE9iamVjdDtcbiAgICAgICAgICAgICAgbXV0YXRlKGN1cnJlbnRPYmplY3QpO1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH0sXG4gICAgICAgICAgY2xvc2UoKSB7XG4gICAgICAgICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgICAgICAgYWJvcnRDb250cm9sbGVyUmVmLmN1cnJlbnQgPSBudWxsO1xuICAgICAgICAgICAgaWYgKG9uRmluaXNoICE9IG51bGwpIHtcbiAgICAgICAgICAgICAgY29uc3QgdmFsaWRhdGlvblJlc3VsdCA9IHNhZmVWYWxpZGF0ZVR5cGVzKHtcbiAgICAgICAgICAgICAgICB2YWx1ZTogbGF0ZXN0T2JqZWN0LFxuICAgICAgICAgICAgICAgIHNjaGVtYTogYXNTY2hlbWEoc2NoZW1hKVxuICAgICAgICAgICAgICB9KTtcbiAgICAgICAgICAgICAgb25GaW5pc2goXG4gICAgICAgICAgICAgICAgdmFsaWRhdGlvblJlc3VsdC5zdWNjZXNzID8geyBvYmplY3Q6IHZhbGlkYXRpb25SZXN1bHQudmFsdWUsIGVycm9yOiB2b2lkIDAgfSA6IHsgb2JqZWN0OiB2b2lkIDAsIGVycm9yOiB2YWxpZGF0aW9uUmVzdWx0LmVycm9yIH1cbiAgICAgICAgICAgICAgKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgIH0pXG4gICAgICApO1xuICAgIH0gY2F0Y2ggKGVycm9yMikge1xuICAgICAgaWYgKGlzQWJvcnRFcnJvcjIoZXJyb3IyKSkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBpZiAob25FcnJvciAmJiBlcnJvcjIgaW5zdGFuY2VvZiBFcnJvcikge1xuICAgICAgICBvbkVycm9yKGVycm9yMik7XG4gICAgICB9XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgICAgc2V0RXJyb3IoZXJyb3IyIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvcjIgOiBuZXcgRXJyb3IoU3RyaW5nKGVycm9yMikpKTtcbiAgICB9XG4gIH07XG4gIHJldHVybiB7XG4gICAgc3VibWl0LFxuICAgIG9iamVjdDogZGF0YSxcbiAgICBlcnJvcixcbiAgICBpc0xvYWRpbmcsXG4gICAgc3RvcFxuICB9O1xufVxudmFyIGV4cGVyaW1lbnRhbF91c2VPYmplY3QgPSB1c2VPYmplY3Q7XG5leHBvcnQge1xuICBleHBlcmltZW50YWxfdXNlT2JqZWN0LFxuICB1c2VBc3Npc3RhbnQsXG4gIHVzZUNoYXQsXG4gIHVzZUNvbXBsZXRpb25cbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+react@1.2.0_react@18.2.0_zod@3.24.2/node_modules/@ai-sdk/react/dist/index.mjs\n");

/***/ })

};
;