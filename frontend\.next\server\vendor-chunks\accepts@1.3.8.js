"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/accepts@1.3.8";
exports.ids = ["vendor-chunks/accepts@1.3.8"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/accepts@1.3.8/node_modules/accepts/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/.pnpm/accepts@1.3.8/node_modules/accepts/index.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * accepts\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar Negotiator = __webpack_require__(/*! negotiator */ \"(rsc)/./node_modules/.pnpm/negotiator@0.6.3/node_modules/negotiator/index.js\")\nvar mime = __webpack_require__(/*! mime-types */ \"(rsc)/./node_modules/.pnpm/mime-types@2.1.35/node_modules/mime-types/index.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Accepts\n\n/**\n * Create a new Accepts object for the given req.\n *\n * @param {object} req\n * @public\n */\n\nfunction Accepts (req) {\n  if (!(this instanceof Accepts)) {\n    return new Accepts(req)\n  }\n\n  this.headers = req.headers\n  this.negotiator = new Negotiator(req)\n}\n\n/**\n * Check if the given `type(s)` is acceptable, returning\n * the best match when true, otherwise `undefined`, in which\n * case you should respond with 406 \"Not Acceptable\".\n *\n * The `type` value may be a single mime type string\n * such as \"application/json\", the extension name\n * such as \"json\" or an array `[\"json\", \"html\", \"text/plain\"]`. When a list\n * or array is given the _best_ match, if any is returned.\n *\n * Examples:\n *\n *     // Accept: text/html\n *     this.types('html');\n *     // => \"html\"\n *\n *     // Accept: text/*, application/json\n *     this.types('html');\n *     // => \"html\"\n *     this.types('text/html');\n *     // => \"text/html\"\n *     this.types('json', 'text');\n *     // => \"json\"\n *     this.types('application/json');\n *     // => \"application/json\"\n *\n *     // Accept: text/*, application/json\n *     this.types('image/png');\n *     this.types('png');\n *     // => undefined\n *\n *     // Accept: text/*;q=.5, application/json\n *     this.types(['html', 'json']);\n *     this.types('html', 'json');\n *     // => \"json\"\n *\n * @param {String|Array} types...\n * @return {String|Array|Boolean}\n * @public\n */\n\nAccepts.prototype.type =\nAccepts.prototype.types = function (types_) {\n  var types = types_\n\n  // support flattened arguments\n  if (types && !Array.isArray(types)) {\n    types = new Array(arguments.length)\n    for (var i = 0; i < types.length; i++) {\n      types[i] = arguments[i]\n    }\n  }\n\n  // no types, return all requested types\n  if (!types || types.length === 0) {\n    return this.negotiator.mediaTypes()\n  }\n\n  // no accept header, return first given type\n  if (!this.headers.accept) {\n    return types[0]\n  }\n\n  var mimes = types.map(extToMime)\n  var accepts = this.negotiator.mediaTypes(mimes.filter(validMime))\n  var first = accepts[0]\n\n  return first\n    ? types[mimes.indexOf(first)]\n    : false\n}\n\n/**\n * Return accepted encodings or best fit based on `encodings`.\n *\n * Given `Accept-Encoding: gzip, deflate`\n * an array sorted by quality is returned:\n *\n *     ['gzip', 'deflate']\n *\n * @param {String|Array} encodings...\n * @return {String|Array}\n * @public\n */\n\nAccepts.prototype.encoding =\nAccepts.prototype.encodings = function (encodings_) {\n  var encodings = encodings_\n\n  // support flattened arguments\n  if (encodings && !Array.isArray(encodings)) {\n    encodings = new Array(arguments.length)\n    for (var i = 0; i < encodings.length; i++) {\n      encodings[i] = arguments[i]\n    }\n  }\n\n  // no encodings, return all requested encodings\n  if (!encodings || encodings.length === 0) {\n    return this.negotiator.encodings()\n  }\n\n  return this.negotiator.encodings(encodings)[0] || false\n}\n\n/**\n * Return accepted charsets or best fit based on `charsets`.\n *\n * Given `Accept-Charset: utf-8, iso-8859-1;q=0.2, utf-7;q=0.5`\n * an array sorted by quality is returned:\n *\n *     ['utf-8', 'utf-7', 'iso-8859-1']\n *\n * @param {String|Array} charsets...\n * @return {String|Array}\n * @public\n */\n\nAccepts.prototype.charset =\nAccepts.prototype.charsets = function (charsets_) {\n  var charsets = charsets_\n\n  // support flattened arguments\n  if (charsets && !Array.isArray(charsets)) {\n    charsets = new Array(arguments.length)\n    for (var i = 0; i < charsets.length; i++) {\n      charsets[i] = arguments[i]\n    }\n  }\n\n  // no charsets, return all requested charsets\n  if (!charsets || charsets.length === 0) {\n    return this.negotiator.charsets()\n  }\n\n  return this.negotiator.charsets(charsets)[0] || false\n}\n\n/**\n * Return accepted languages or best fit based on `langs`.\n *\n * Given `Accept-Language: en;q=0.8, es, pt`\n * an array sorted by quality is returned:\n *\n *     ['es', 'pt', 'en']\n *\n * @param {String|Array} langs...\n * @return {Array|String}\n * @public\n */\n\nAccepts.prototype.lang =\nAccepts.prototype.langs =\nAccepts.prototype.language =\nAccepts.prototype.languages = function (languages_) {\n  var languages = languages_\n\n  // support flattened arguments\n  if (languages && !Array.isArray(languages)) {\n    languages = new Array(arguments.length)\n    for (var i = 0; i < languages.length; i++) {\n      languages[i] = arguments[i]\n    }\n  }\n\n  // no languages, return all requested languages\n  if (!languages || languages.length === 0) {\n    return this.negotiator.languages()\n  }\n\n  return this.negotiator.languages(languages)[0] || false\n}\n\n/**\n * Convert extnames to mime.\n *\n * @param {String} type\n * @return {String}\n * @private\n */\n\nfunction extToMime (type) {\n  return type.indexOf('/') === -1\n    ? mime.lookup(type)\n    : type\n}\n\n/**\n * Check if mime is valid.\n *\n * @param {String} type\n * @return {String}\n * @private\n */\n\nfunction validMime (type) {\n  return typeof type === 'string'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/accepts@1.3.8/node_modules/accepts/index.js\n");

/***/ })

};
;