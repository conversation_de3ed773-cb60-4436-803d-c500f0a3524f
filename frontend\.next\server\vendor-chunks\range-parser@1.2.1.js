"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/range-parser@1.2.1";
exports.ids = ["vendor-chunks/range-parser@1.2.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/range-parser@1.2.1/node_modules/range-parser/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/range-parser@1.2.1/node_modules/range-parser/index.js ***!
  \**********************************************************************************/
/***/ ((module) => {

eval("/*!\n * range-parser\n * Copyright(c) 2012-2014 TJ Holowaychuk\n * Copyright(c) 2015-2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = rangeParser\n\n/**\n * Parse \"Range\" header `str` relative to the given file `size`.\n *\n * @param {Number} size\n * @param {String} str\n * @param {Object} [options]\n * @return {Array}\n * @public\n */\n\nfunction rangeParser (size, str, options) {\n  if (typeof str !== 'string') {\n    throw new TypeError('argument str must be a string')\n  }\n\n  var index = str.indexOf('=')\n\n  if (index === -1) {\n    return -2\n  }\n\n  // split the range string\n  var arr = str.slice(index + 1).split(',')\n  var ranges = []\n\n  // add ranges type\n  ranges.type = str.slice(0, index)\n\n  // parse all ranges\n  for (var i = 0; i < arr.length; i++) {\n    var range = arr[i].split('-')\n    var start = parseInt(range[0], 10)\n    var end = parseInt(range[1], 10)\n\n    // -nnn\n    if (isNaN(start)) {\n      start = size - end\n      end = size - 1\n    // nnn-\n    } else if (isNaN(end)) {\n      end = size - 1\n    }\n\n    // limit last-byte-pos to current length\n    if (end > size - 1) {\n      end = size - 1\n    }\n\n    // invalid or unsatisifiable\n    if (isNaN(start) || isNaN(end) || start > end || start < 0) {\n      continue\n    }\n\n    // add range\n    ranges.push({\n      start: start,\n      end: end\n    })\n  }\n\n  if (ranges.length < 1) {\n    // unsatisifiable\n    return -1\n  }\n\n  return options && options.combine\n    ? combineRanges(ranges)\n    : ranges\n}\n\n/**\n * Combine overlapping & adjacent ranges.\n * @private\n */\n\nfunction combineRanges (ranges) {\n  var ordered = ranges.map(mapWithIndex).sort(sortByRangeStart)\n\n  for (var j = 0, i = 1; i < ordered.length; i++) {\n    var range = ordered[i]\n    var current = ordered[j]\n\n    if (range.start > current.end + 1) {\n      // next range\n      ordered[++j] = range\n    } else if (range.end > current.end) {\n      // extend range\n      current.end = range.end\n      current.index = Math.min(current.index, range.index)\n    }\n  }\n\n  // trim ordered array\n  ordered.length = j + 1\n\n  // generate combined range\n  var combined = ordered.sort(sortByRangeIndex).map(mapWithoutIndex)\n\n  // copy ranges type\n  combined.type = ranges.type\n\n  return combined\n}\n\n/**\n * Map function to add index value to ranges.\n * @private\n */\n\nfunction mapWithIndex (range, index) {\n  return {\n    start: range.start,\n    end: range.end,\n    index: index\n  }\n}\n\n/**\n * Map function to remove index value from ranges.\n * @private\n */\n\nfunction mapWithoutIndex (range) {\n  return {\n    start: range.start,\n    end: range.end\n  }\n}\n\n/**\n * Sort function to sort ranges by index.\n * @private\n */\n\nfunction sortByRangeIndex (a, b) {\n  return a.index - b.index\n}\n\n/**\n * Sort function to sort ranges by start position.\n * @private\n */\n\nfunction sortByRangeStart (a, b) {\n  return a.start - b.start\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/range-parser@1.2.1/node_modules/range-parser/index.js\n");

/***/ })

};
;