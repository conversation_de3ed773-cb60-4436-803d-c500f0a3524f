"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+react-hotkeys@37.0_0ced0e67047e23cf44986a957ed41462";
exports.ids = ["vendor-chunks/@udecode+react-hotkeys@37.0_0ced0e67047e23cf44986a957ed41462"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+react-hotkeys@37.0_0ced0e67047e23cf44986a957ed41462/node_modules/@udecode/react-hotkeys/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+react-hotkeys@37.0_0ced0e67047e23cf44986a957ed41462/node_modules/@udecode/react-hotkeys/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HotkeysProvider: () => (/* binding */ HotkeysProvider),\n/* harmony export */   Key: () => (/* binding */ Key),\n/* harmony export */   isHotkeyPressed: () => (/* binding */ isHotkeyPressed),\n/* harmony export */   useHotkeys: () => (/* binding */ useHotkeys),\n/* harmony export */   useHotkeysContext: () => (/* binding */ useHotkeysContext),\n/* harmony export */   useRecordHotkeys: () => (/* binding */ useRecordHotkeys)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\n\n// src/internal/HotkeysProvider.tsx\n\n\n// src/internal/BoundHotkeysProxyProvider.tsx\n\nvar BoundHotkeysProxyProvider = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(void 0);\nvar useBoundHotkeysProxy = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(BoundHotkeysProxyProvider);\n};\nfunction BoundHotkeysProxyProviderProvider({\n  addHotkey,\n  children,\n  removeHotkey\n}) {\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(BoundHotkeysProxyProvider.Provider, { value: { addHotkey, removeHotkey } }, children);\n}\n\n// src/internal/deepEqual.ts\nfunction deepEqual(x, y) {\n  return x && y && typeof x === \"object\" && typeof y === \"object\" ? Object.keys(x).length === Object.keys(y).length && Object.keys(x).reduce(\n    (isEqual, key) => isEqual && deepEqual(x[key], y[key]),\n    true\n  ) : x === y;\n}\n\n// src/internal/HotkeysProvider.tsx\nvar HotkeysContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n  activeScopes: [],\n  // This array has to be empty instead of containing '*' as default, to check if the provider is set or not\n  disableScope: () => {\n  },\n  enableScope: () => {\n  },\n  hotkeys: [],\n  toggleScope: () => {\n  }\n});\nvar useHotkeysContext = () => {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(HotkeysContext);\n};\nvar HotkeysProvider = ({\n  children,\n  initiallyActiveScopes = [\"*\"]\n}) => {\n  const [internalActiveScopes, setInternalActiveScopes] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\n    initiallyActiveScopes\n  );\n  const [boundHotkeys, setBoundHotkeys] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const enableScope = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((scope) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes(\"*\")) {\n        return [scope];\n      }\n      return Array.from(/* @__PURE__ */ new Set([...prev, scope]));\n    });\n  }, []);\n  const disableScope = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((scope) => {\n    setInternalActiveScopes((prev) => {\n      return prev.filter((s) => s !== scope);\n    });\n  }, []);\n  const toggleScope = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((scope) => {\n    setInternalActiveScopes((prev) => {\n      if (prev.includes(scope)) {\n        return prev.filter((s) => s !== scope);\n      } else {\n        if (prev.includes(\"*\")) {\n          return [scope];\n        }\n        return Array.from(/* @__PURE__ */ new Set([...prev, scope]));\n      }\n    });\n  }, []);\n  const addBoundHotkey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((hotkey) => {\n    setBoundHotkeys((prev) => [...prev, hotkey]);\n  }, []);\n  const removeBoundHotkey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((hotkey) => {\n    setBoundHotkeys((prev) => prev.filter((h) => !deepEqual(h, hotkey)));\n  }, []);\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n    HotkeysContext.Provider,\n    {\n      value: {\n        activeScopes: internalActiveScopes,\n        disableScope,\n        enableScope,\n        hotkeys: boundHotkeys,\n        toggleScope\n      }\n    },\n    /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n      BoundHotkeysProxyProviderProvider,\n      {\n        addHotkey: addBoundHotkey,\n        removeHotkey: removeBoundHotkey\n      },\n      children\n    )\n  );\n};\n\n// src/internal/parseHotkeys.ts\nvar reservedModifierKeywords = /* @__PURE__ */ new Set([\n  \"shift\",\n  \"alt\",\n  \"meta\",\n  \"mod\",\n  \"ctrl\",\n  \"control\"\n]);\nvar mappedKeys = {\n  AltLeft: \"alt\",\n  AltRight: \"alt\",\n  ControlLeft: \"ctrl\",\n  ControlRight: \"ctrl\",\n  MetaLeft: \"meta\",\n  MetaRight: \"meta\",\n  OSLeft: \"meta\",\n  OSRight: \"meta\",\n  ShiftLeft: \"shift\",\n  ShiftRight: \"shift\",\n  down: \"arrowdown\",\n  esc: \"escape\",\n  left: \"arrowleft\",\n  return: \"enter\",\n  right: \"arrowright\",\n  up: \"arrowup\"\n};\nfunction mapKey(key) {\n  return (mappedKeys[key.trim()] || key.trim()).toLowerCase().replace(/key|digit|numpad/, \"\");\n}\nfunction isHotkeyModifier(key) {\n  return reservedModifierKeywords.has(key);\n}\nfunction parseKeysHookInput(keys, delimiter = \",\") {\n  return keys.toLowerCase().split(delimiter);\n}\nfunction parseHotkey(hotkey, splitKey = \"+\", useKey = false, description) {\n  const keys = hotkey.toLocaleLowerCase().split(splitKey).map((k) => mapKey(k));\n  const modifiers = {\n    alt: keys.includes(\"alt\"),\n    ctrl: keys.includes(\"ctrl\") || keys.includes(\"control\"),\n    meta: keys.includes(\"meta\"),\n    mod: keys.includes(\"mod\"),\n    shift: keys.includes(\"shift\"),\n    useKey\n  };\n  const singleCharKeys = keys.filter((k) => !reservedModifierKeywords.has(k));\n  return __spreadProps(__spreadValues({}, modifiers), {\n    description,\n    keys: singleCharKeys\n  });\n}\n\n// src/internal/isHotkeyPressed.ts\n(() => {\n  if (typeof document !== \"undefined\") {\n    document.addEventListener(\"keydown\", (e) => {\n      if (e.code === void 0) {\n        return;\n      }\n      pushToCurrentlyPressedKeys([mapKey(e.code)]);\n    });\n    document.addEventListener(\"keyup\", (e) => {\n      if (e.code === void 0) {\n        return;\n      }\n      removeFromCurrentlyPressedKeys([mapKey(e.code)]);\n    });\n  }\n  if (typeof window !== \"undefined\") {\n    window.addEventListener(\"blur\", () => {\n      currentlyPressedKeys.clear();\n    });\n  }\n})();\nvar currentlyPressedKeys = /* @__PURE__ */ new Set();\nfunction isReadonlyArray(value) {\n  return Array.isArray(value);\n}\nfunction isHotkeyPressed(key, delimiter = \",\") {\n  const hotkeyArray = isReadonlyArray(key) ? key : key.split(delimiter);\n  return hotkeyArray.every(\n    (hotkey) => currentlyPressedKeys.has(hotkey.trim().toLowerCase())\n  );\n}\nfunction pushToCurrentlyPressedKeys(key) {\n  const hotkeyArray = Array.isArray(key) ? key : [key];\n  if (currentlyPressedKeys.has(\"meta\")) {\n    currentlyPressedKeys.forEach(\n      (key2) => !isHotkeyModifier(key2) && currentlyPressedKeys.delete(key2.toLowerCase())\n    );\n  }\n  hotkeyArray.forEach(\n    (hotkey) => currentlyPressedKeys.add(hotkey.toLowerCase())\n  );\n}\nfunction removeFromCurrentlyPressedKeys(key) {\n  const hotkeyArray = Array.isArray(key) ? key : [key];\n  if (key === \"meta\") {\n    currentlyPressedKeys.clear();\n  } else {\n    hotkeyArray.forEach(\n      (hotkey) => currentlyPressedKeys.delete(hotkey.toLowerCase())\n    );\n  }\n}\n\n// src/internal/key.ts\nvar Key = {\n  /** Changes the input mode on an external audio/video receiver (AVR) unit. */\n  AVRInput: \"AVRInput\",\n  /** Toggles the power on an external AVR unit. */\n  AVRPower: \"AVRPower\",\n  /**\n   * The Accept, Commit, or OK key or button. Accepts the currently selected\n   * option or input method sequence conversion.\n   */\n  Accept: \"Accept\",\n  /** The numeric keypad's addition key, +. */\n  Add: \"Add\",\n  /** The Again key. Redoes or repeats a previous action. */\n  Again: \"Again\",\n  /**\n   * The All Candidates key, which starts multi-candidate mode, in which\n   * multiple candidates are displayed for the ongoing input.\n   */\n  AllCandidates: \"AllCandidates\",\n  /** The Alphanumeric key. */\n  Alphanumeric: \"Alphanumeric\",\n  /**\n   * The Alt (Alternative) key. This is the Option ⌥ key on Mac, or the Alt key\n   * on Windows.\n   */\n  Alt: \"Alt\",\n  /**\n   * The AltGr or AltGraph (Alternate Graphics) key. Enables the ISO Level 3\n   * shift modifier (where Shift is the level 2 modifier).\n   */\n  AltGraph: \"AltGraph\",\n  /**\n   * Presents a list of recently-used applications which lets the user change\n   * apps quickly.\n   */\n  AppSwitch: \"AppSwitch\",\n  /** The down arrow key. */\n  ArrowDown: \"ArrowDown\",\n  /** The left arrow key. */\n  ArrowLeft: \"ArrowLeft\",\n  /** The right arrow key. */\n  ArrowRight: \"ArrowRight\",\n  /** The up arrow key. */\n  ArrowUp: \"ArrowUp\",\n  /** The Attn (Attention) key. */\n  Attn: \"Attn\",\n  /** Adjusts audio balance toward the left. */\n  AudioBalanceLeft: \"AudioBalanceLeft\",\n  /** Adjusts audio balance twoard the right. */\n  AudioBalanceRight: \"AudioBalanceRight\",\n  /**\n   * Reduces bass boosting or cycles downward through bass boost modes or\n   * states.\n   */\n  AudioBassBoostDown: \"AudioBassBoostDown\",\n  /** Toggles bass boosting on and off. */\n  AudioBassBoostToggle: \"AudioBassBoostToggle\",\n  /**\n   * Increases the amoung of bass boosting, or cycles upward through a set of\n   * bass boost modes or states.\n   */\n  AudioBassBoostUp: \"AudioBassBoostUp\",\n  /** Decreases the amount of bass. */\n  AudioBassDown: \"AudioBassDown\",\n  /** Increases the amount of bass. */\n  AudioBassUp: \"AudioBassUp\",\n  /** Adjusts the audio fader toward the front. */\n  AudioFaderFront: \"AudioFaderFront\",\n  /** Adjustts the audio fader toward the rear. */\n  AudioFaderRear: \"AudioFaderRear\",\n  /** Selects the next available surround sound mode. */\n  AudioSurroundModeNext: \"AudioSurroundModeNext\",\n  /** Decreases the amount of treble. */\n  AudioTrebleDown: \"AudioTrebleDown\",\n  /** Increases the amount of treble. */\n  AudioTrebleUp: \"AudioTrebleUp\",\n  /** Decreases the audio volume. */\n  AudioVolumeDown: \"AudioVolumeDown\",\n  /** Mutes the audio. */\n  AudioVolumeMute: \"AudioVolumeMute\",\n  /** Increases the audio volume. */\n  AudioVolumeUp: \"AudioVolumeUp\",\n  /** The Backspace key. This key is labeled Delete on Mac keyboards. */\n  Backspace: \"Backspace\",\n  /**\n   * The Brightness Down key. Typically used to reduce the brightness of the\n   * display.\n   */\n  BrightnessDown: \"BrightnessDown\",\n  /** The Brightness Up key. Typically increases the brightness of the display. */\n  BrightnessUp: \"BrightnessUp\",\n  /**\n   * Navigates to the previous content or page in the current Web view's\n   * history.\n   */\n  BrowserBack: \"BrowserBack\",\n  /** Opens the user's list of bookmarks/favorites. */\n  BrowserFavorites: \"BrowserFavorites\",\n  /** Navigates to the next content or page in the current Web view's history. */\n  BrowserForward: \"BrowserForward\",\n  /** Navigates to the user's preferred home page. */\n  BrowserHome: \"BrowserHome\",\n  /** Refreshes the current page or contentl. */\n  BrowserRefresh: \"BrowserRefresh\",\n  /**\n   * Activates the user's preferred search engine or the search interface within\n   * their browser.\n   */\n  BrowserSearch: \"BrowserSearch\",\n  /** Stops loading the currently displayed Web view or content. */\n  BrowserStop: \"BrowserStop\",\n  /** The Call key; dials the number which has been entered. */\n  Call: \"Call\",\n  /** The Camera key; activates the camera. */\n  Camera: \"Camera\",\n  /** The Focus key; focuses the camera. */\n  CameraFocus: \"CameraFocus\",\n  /** The Cancel key. */\n  Cancel: \"Cancel\",\n  /**\n   * The Caps Lock key. Toggles the capital character lock on and off for\n   * subsequent input.\n   */\n  CapsLock: \"CapsLock\",\n  /** Switches to the previous channel. */\n  ChannelDown: \"ChannelDown\",\n  /** Switches to the next channel. */\n  ChannelUp: \"ChannelUp\",\n  /** The Clear key. Removes the currently selected input. */\n  Clear: \"Clear\",\n  /** Closes the current document or message. Must not exit the application. */\n  Close: \"Close\",\n  /** Toggles closed captioning on and off. */\n  ClosedCaptionToggle: \"ClosedCaptionToggle\",\n  /**\n   * The Code Input key, which enables code input mode, which lets the user\n   * enter characters by typing their code points (their Unicode character\n   * numbers, typically).\n   */\n  CodeInput: \"CodeInput\",\n  /**\n   * General-purpose media function key, color-coded red; this has index 0 among\n   * the colored keys.\n   */\n  ColorF0Red: \"ColorF0Red\",\n  /**\n   * General-purpose media funciton key, color-coded green; this has index 1\n   * among the colored keys.\n   */\n  ColorF1Green: \"ColorF1Green\",\n  /**\n   * General-purpose media funciton key, color-coded yellow; this has index 2\n   * among the colored keys.\n   */\n  ColorF2Yellow: \"ColorF2Yellow\",\n  /**\n   * General-purpose media funciton key, color-coded blue; this has index 3\n   * among the colored keys.\n   */\n  ColorF3Blue: \"ColorF3Blue\",\n  /**\n   * General-purpose media funciton key, color-coded grey; this has index 4\n   * among the colored keys.\n   */\n  ColorF4Grey: \"ColorF4Grey\",\n  /**\n   * General-purpose media funciton key, color-coded brown; this has index 5\n   * among the colored keys.\n   */\n  ColorF5Brown: \"ColorF5Brown\",\n  /** The Compose key. */\n  Compose: \"Compose\",\n  /**\n   * Shows the context menu. Typically found between the Windows (or OS) key and\n   * the Control key on the right side of the keyboard.\n   */\n  ContextMenu: \"ContextMenu\",\n  /** The Control, Ctrl, or Ctl key. Allows typing control characters. */\n  Control: \"Control\",\n  /**\n   * The Convert key, which instructs the IME to convert the current input\n   * method sequence into the resulting character.\n   */\n  Convert: \"Convert\",\n  /** The Copy key (on certain extended keyboards). */\n  Copy: \"Copy\",\n  /** The Cursor Select key, CrSel. */\n  CrSel: \"CrSel\",\n  /** The Cut key (on certain extended keyboards). */\n  Cut: \"Cut\",\n  /** Switches the input source to the Digital Video Recorder (DVR). */\n  DVR: \"DVR\",\n  /**\n   * A dead \"combining\" key; that is, a key which is used in tandem with other\n   * keys to generate accented and other modified characters. If pressed by\n   * itself, it doesn't generate a character. If you wish to identify which\n   * specific dead key was pressed (in cases where more than one exists), you\n   * can do so by examining the KeyboardEvent's associated compositionupdate\n   * event's data property.\n   */\n  Dead: \"Dead\",\n  /**\n   * The decimal point key (typically . or , depending on the region. In newer\n   * browsers, this value to simply be the character generated by the decimal\n   * key (one of those two characters). [1]\n   */\n  Decimal: \"Decimal\",\n  /** The Delete key, Del. */\n  Delete: \"Delete\",\n  /**\n   * Adjusts the brightness of the device by toggling between two brightness\n   * levels or by cycling among multiple brightness levels.\n   */\n  Dimmer: \"Dimmer\",\n  /** Cycles among video sources. */\n  DisplaySwap: \"DisplaySwap\",\n  /** The numeric keypad's division key, /. */\n  Divide: \"Divide\",\n  /**\n   * The Eisu key. This key's purpose is defined by the IME, but may be used to\n   * close the IME.\n   */\n  Eisu: \"Eisu\",\n  /**\n   * The Eject key. Ejects removable media (or toggles an optical storage device\n   * tray open and closed).\n   */\n  Eject: \"Eject\",\n  /** The End key. Moves to the end of content. */\n  End: \"End\",\n  /** The End Call or Hang Up button. */\n  EndCall: \"EndCall\",\n  /** The Enter or ↵ key (sometimes labeled Return). */\n  Enter: \"Enter\",\n  /**\n   * Erase to End of Field. Deletes all characters from the current cursor\n   * position to the end of the current field.\n   */\n  EraseEof: \"EraseEof\",\n  /**\n   * The Esc (Escape) key. Typically used as an exit, cancel, or \"escape this\n   * operation\" button. Historically, the Escape character was used to signal\n   * the start of a special control sequence of characters called an \"escape\n   * sequence.\"\n   */\n  Escape: \"Escape\",\n  /** The ExSel (Extend Selection) key. */\n  ExSel: \"ExSel\",\n  /** The Execute key. */\n  Execute: \"Execute\",\n  /** The Exit button, which exits the curreent application or menu. */\n  Exit: \"Exit\",\n  /** The first general-purpose function key, F1. */\n  F1: \"F1\",\n  /** The F2 key. */\n  F2: \"F2\",\n  /** The F3 key. */\n  F3: \"F3\",\n  /** The F4 key. */\n  F4: \"F4\",\n  /** The F5 key. */\n  F5: \"F5\",\n  /** The F6 key. */\n  F6: \"F6\",\n  /** The F7 key. */\n  F7: \"F7\",\n  /** The F8 key. */\n  F8: \"F8\",\n  /** The F9 key. */\n  F9: \"F9\",\n  /** The F10 key. */\n  F10: \"F10\",\n  /** The F11 key. */\n  F11: \"F11\",\n  /** The F12 key. */\n  F12: \"F12\",\n  /** The F13 key. */\n  F13: \"F13\",\n  /** The F14 key. */\n  F14: \"F14\",\n  /** The F15 key. */\n  F15: \"F15\",\n  /** The F16 key. */\n  F16: \"F16\",\n  /** The F17 key. */\n  F17: \"F17\",\n  /** The F18 key. */\n  F18: \"F18\",\n  /** The F19 key. */\n  F19: \"F19\",\n  /** The F20 key. */\n  F20: \"F20\",\n  /** Clears the program or content stored in the first favorites list slot. */\n  FavoriteClear0: \"FavoriteClear0\",\n  /** Clears the program or content stored in the second favorites list slot. */\n  FavoriteClear1: \"FavoriteClear1\",\n  /** Clears the program or content stored in the third favorites list slot. */\n  FavoriteClear2: \"FavoriteClear2\",\n  /** Clears the program or content stored in the fourth favorites list slot. */\n  FavoriteClear3: \"FavoriteClear3\",\n  /**\n   * Selects (recalls) the program or content stored in the first favorites list\n   * slot.\n   */\n  FavoriteRecall0: \"FavoriteRecall0\",\n  /**\n   * Selects (recalls) the program or content stored in the second favorites\n   * list slot.\n   */\n  FavoriteRecall1: \"FavoriteRecall1\",\n  /**\n   * Selects (recalls) the program or content stored in the third favorites list\n   * slot.\n   */\n  FavoriteRecall2: \"FavoriteRecall2\",\n  /**\n   * Selects (recalls) the program or content stored in the fourth favorites\n   * list slot.\n   */\n  FavoriteRecall3: \"FavoriteRecall3\",\n  /** Stores the current program or content into the first favorites list slot. */\n  FavoriteStore0: \"FavoriteStore0\",\n  /** Stores the current program or content into the second favorites list slot. */\n  FavoriteStore1: \"FavoriteStore1\",\n  /** Stores the current program or content into the third favorites list slot. */\n  FavoriteStore2: \"FavoriteStore2\",\n  /** Stores the current program or content into the fourth favorites list slot. */\n  FavoriteStore3: \"FavoriteStore3\",\n  /**\n   * The Final (Final Mode) key is used on some Asian keyboards to enter final\n   * mode when using IMEs.\n   */\n  FinalMode: \"FinalMode\",\n  /**\n   * The Find key. Opens an interface (typically a dialog box) for performing a\n   * find/search operation.\n   */\n  Find: \"Find\",\n  /** The Finish key. */\n  Finish: \"Finish\",\n  /**\n   * The Fn (Function modifier) key. Used to allow generating function key\n   * (F1-F15, for instance) characters on keyboards without a dedicated function\n   * key area. Often handled in hardware so that events aren't generated for\n   * this key.\n   */\n  Fn: \"Fn\",\n  /**\n   * The FnLock or F-Lock (Function Lock) key.Toggles the function key mode\n   * described by \"Fn\" on and off. Often handled in hardware so that events\n   * aren't generated for this key.\n   */\n  FnLock: \"FnLock\",\n  /** The Back button. */\n  GoBack: \"GoBack\",\n  /**\n   * The Home button, which takes the user to the phone's main screen (usually\n   * an application launcher).\n   */\n  GoHome: \"GoHome\",\n  /**\n   * Switches to the first character group on an ISO/IEC 9995 keyboard. Each key\n   * may have multiple groups of characters, each in its own column. Pressing\n   * this key instructs the device to interpret keypresses as coming from the\n   * first column on subsequent keystrokes.\n   */\n  GroupFirst: \"GroupFirst\",\n  /** Switches to the last character group on an ISO/IEC 9995 keyboard. */\n  GroupLast: \"GroupLast\",\n  /** Switches to the next character group on an ISO/IEC 9995 keyboard. */\n  GroupNext: \"GroupNext\",\n  /** Switches to the previous character group on an ISO/IEC 9995 keyboard. */\n  GroupPrevious: \"GroupPrevious\",\n  /** Toggles the display of the program or content guide. */\n  Guide: \"Guide\",\n  /**\n   * If the guide is currently displayed, this button tells the guide to display\n   * the next day's content.\n   */\n  GuideNextDay: \"GuideNextDay\",\n  /**\n   * If the guide is currently displayed, this button tells the guide to display\n   * the previous day's content.\n   */\n  GuidePreviousDay: \"GuidePreviousDay\",\n  /**\n   * The Hangul (Korean character set) mode key, which toggles between Hangul\n   * and English entry modes.\n   */\n  HangulMode: \"HangulMode\",\n  /**\n   * Selects the Hanja mode, for converting Hangul characters to the more\n   * specific Hanja characters.\n   */\n  HanjaMode: \"HanjaMode\",\n  /** The Hankaku (half-width characters) key. */\n  Hankaku: \"Hankaku\",\n  /**\n   * The Headset Hook key. This is typically actually a button on the headset\n   * which is used to hang up calls and play or pause media.\n   */\n  HeadsetHook: \"HeadsetHook\",\n  /** The Help key. Opens or toggles the display of help information. */\n  Help: \"Help\",\n  /**\n   * The Hibernate key. This saves the state of the computer to disk and then\n   * shuts down; the computer can be returned to its previous state by restoring\n   * the saved state information.\n   */\n  Hibernate: \"Hibernate\",\n  /** The Hiragana key; selects Kana characters mode. */\n  Hiragana: \"Hiragana\",\n  /** Toggles between the Hiragana and Katakana writing systems. */\n  HiraganaKatakana: \"HiraganaKatakana\",\n  /** The Home key. Moves to the start of content. */\n  Home: \"Home\",\n  /** The Hyper key. */\n  Hyper: \"Hyper\",\n  /**\n   * Toggles the display of information about the currently selected content,\n   * program, or media.\n   */\n  Info: \"Info\",\n  /** The Insert key, Ins. Toggles between inserting and overwriting text. */\n  Insert: \"Insert\",\n  /**\n   * Tellls the device to perform an instant replay (typically some form of\n   * jumping back a short amount of time then playing it again, possibly but not\n   * usually in slow motion).\n   */\n  InstantReplay: \"InstantReplay\",\n  /**\n   * Selects the Junja mode, in which Korean is represented using single-byte\n   * Latin characters.\n   */\n  JunjaMode: \"JunjaMode\",\n  /** The Kana Mode (Kana Lock) key. */\n  KanaMode: \"KanaMode\",\n  /**\n   * The Kanji Mode key. Enables entering Japanese text using the ideographic\n   * characters of Chinese origin.\n   */\n  KanjiMode: \"KanjiMode\",\n  /** The Katakana key. */\n  Katakana: \"Katakana\",\n  /** The 11 key found on certain media numeric keypads. */\n  Key11: \"Key11\",\n  /** The 12 key found on certain media numeric keypads. */\n  Key12: \"Key12\",\n  /** The Redial button, which redials the last-called number. */\n  LastNumberRedial: \"LastNumberRedial\",\n  /** The first generic application launcher button. */\n  LaunchApplication1: \"LaunchApplication1\",\n  /** The second generic application launcher button. */\n  LaunchApplication2: \"LaunchApplication2\",\n  /** The third generic application launcher button. */\n  LaunchApplication3: \"LaunchApplication3\",\n  /** The fourth generic application launcher button. */\n  LaunchApplication4: \"LaunchApplication4\",\n  /** The fifth generic application launcher button. */\n  LaunchApplication5: \"LaunchApplication5\",\n  /** The sixth generic application launcher button. */\n  LaunchApplication6: \"LaunchApplication6\",\n  /** The seventh generic application launcher button. */\n  LaunchApplication7: \"LaunchApplication7\",\n  /** The eighth generic application launcher button. */\n  LaunchApplication8: \"LaunchApplication8\",\n  /** The ninth generic application launcher button. */\n  LaunchApplication9: \"LaunchApplication9\",\n  /** The 10th generic application launcher button. */\n  LaunchApplication10: \"LaunchApplication10\",\n  /** The 11th generic application launcher button. */\n  LaunchApplication11: \"LaunchApplication11\",\n  /** The 12th generic application launcher button. */\n  LaunchApplication12: \"LaunchApplication12\",\n  /** The 13th generic application launcher button. */\n  LaunchApplication13: \"LaunchApplication13\",\n  /** The 14th generic application launcher button. */\n  LaunchApplication14: \"LaunchApplication14\",\n  /** The 15th generic application launcher button. */\n  LaunchApplication15: \"LaunchApplication15\",\n  /** The 16th generic application launcher button. */\n  LaunchApplication16: \"LaunchApplication16\",\n  /**\n   * The Calculator key, often labeled with an icon such as . This is often used\n   * as a generic application launcher key (APPCOMMAND_LAUNCH_APP2).\n   */\n  LaunchCalculator: \"LaunchCalculator\",\n  /** The Calendar key, often labeled with an icon like . */\n  LaunchCalendar: \"LaunchCalendar\",\n  /** The Contacts key. */\n  LaunchContacts: \"LaunchContacts\",\n  /** The Mail key. This is often displayed as . */\n  LaunchMail: \"LaunchMail\",\n  /** The Media Player key. */\n  LaunchMediaPlayer: \"LaunchMediaPlayer\",\n  /** The Music Player key, often labeled with an icon such as . */\n  LaunchMusicPlayer: \"LaunchMusicPlayer\",\n  /**\n   * The My Computer key on Windows keyboards. This is often used as a generic\n   * application launcher key (APPCOMMAND_LAUNCH_APP1).\n   */\n  LaunchMyComputer: \"LaunchMyComputer\",\n  /** The Phone key, to open the phone dialer application if one is present. */\n  LaunchPhone: \"LaunchPhone\",\n  /** The Screen Saver key. */\n  LaunchScreenSaver: \"LaunchScreenSaver\",\n  /**\n   * The Spreadsheet key. This key may be labeled with an icon such as or that\n   * of a specific spreadsheet application.\n   */\n  LaunchSpreadsheet: \"LaunchSpreadsheet\",\n  /**\n   * The Web Browser key. This key is frequently labeled with an icon such as or\n   * the icon of a specific browser, depending on the device manufacturer.\n   */\n  LaunchWebBrowser: \"LaunchWebBrowser\",\n  /** The WebCam key. Opens the webcam application. */\n  LaunchWebCam: \"LaunchWebCam\",\n  /**\n   * The Word Processor key. This may be an icon of a specific word processor\n   * application, or a generic document icon.\n   */\n  LaunchWordProcessor: \"LaunchWordProcessor\",\n  /** Opens content liniked to the current program, if available and possible. */\n  Link: \"Link\",\n  /** Lists the current program. */\n  ListProgram: \"ListProgram\",\n  /** Toggles a display listing currently available live content or programs. */\n  LiveContent: \"LiveContent\",\n  /** Locks or unlocks the currently selected content or pgoram. */\n  Lock: \"Lock\",\n  /** The LogOff key. */\n  LogOff: \"LogOff\",\n  /** Opens the user interface to forward a message. */\n  MailForward: \"MailForward\",\n  /** Opens the user interface to reply to a message. */\n  MailReply: \"MailReply\",\n  /** Sends the current message. */\n  MailSend: \"MailSend\",\n  /**\n   * A button which cycles among the notification modes: silent, vibrate, ring,\n   * and so forth.\n   */\n  MannerMode: \"MannerMode\",\n  /**\n   * Presents a list of media applications, such as photo viewers, audio and\n   * video players, and games. [1]\n   */\n  MediaApps: \"MediaApps\",\n  /** The Audio Track key. */\n  MediaAudioTrack: \"MediaAudioTrack\",\n  /** Starts, continues, or increases the speed of fast forwarding the media. */\n  MediaFastForward: \"MediaFastForward\",\n  /** Jumps back to the last-viewed content, program, or other media. */\n  MediaLast: \"MediaLast\",\n  /**\n   * Pauses the currently playing media. Some older applications use simply\n   * \"Pause\" but this is not correct.\n   */\n  MediaPause: \"MediaPause\",\n  /**\n   * Starts or continues playing media at normal speed, if not already doing so.\n   * Has no effect otherwise.\n   */\n  MediaPlay: \"MediaPlay\",\n  /** Toggles between playing and pausing the current media. */\n  MediaPlayPause: \"MediaPlayPause\",\n  /** Starts or resumes recording media. */\n  MediaRecord: \"MediaRecord\",\n  /** Starts, continues, or increases the speed of rewinding the media. */\n  MediaRewind: \"MediaRewind\",\n  /** Skips backward to the previous content or program. */\n  MediaSkipBackward: \"MediaSkipBackward\",\n  /** Skips forward to the next content or program. */\n  MediaSkipForward: \"MediaSkipForward\",\n  /** Steps backward to the previous content or program. */\n  MediaStepBackward: \"MediaStepBackward\",\n  /** Steps forward to the next content or program. */\n  MediaStepForward: \"MediaStepForward\",\n  /**\n   * Stops the current media activity (such as playing, recording, pausing,\n   * forwarding, or rewinding). Has no effect if the media is currently stopped\n   * already.\n   */\n  MediaStop: \"MediaStop\",\n  /**\n   * Top Menu button; opens the media's main menu, such as on a DVD or Blu-Ray\n   * disc.\n   */\n  MediaTopMenu: \"MediaTopMenu\",\n  /** Seeks to the next media or program track. */\n  MediaTrackNext: \"MediaTrackNext\",\n  /** Seeks to the previous media or program track. */\n  MediaTrackPrevious: \"MediaTrackPrevious\",\n  /**\n   * The Meta key. Allows issuing special command inputs. This is the Windows\n   * logo key, or the Command ⌘ key on Mac.\n   */\n  Meta: \"Meta\",\n  /** Toggles the microphone on and off. */\n  MicrophoneToggle: \"MicrophoneToggle\",\n  /** Decreases the microphone's input volume. */\n  MicrophoneVolumeDown: \"MicrophoneVolumeDown\",\n  /** Mutes the microphone input. */\n  MicrophoneVolumeMute: \"MicrophoneVolumeMute\",\n  /** Increases the microphone's input volume. */\n  MicrophoneVolumeUp: \"MicrophoneVolumeUp\",\n  /** The Mod key. This is the Command ⌘ on Mac, or the Control key on Windows. */\n  Mod: \"Mod\",\n  /** The Mode Change key. Toggles or cycles among input modes of IMEs. */\n  ModeChange: \"ModeChange\",\n  /** The numeric keypad's multiplication key, *. */\n  Multiply: \"Multiply\",\n  /** Navigates into a submenu or option. */\n  NavigateIn: \"NavigateIn\",\n  /** Navigates to the next item. */\n  NavigateNext: \"NavigateNext\",\n  /** Navigates out of the current screen or menu. */\n  NavigateOut: \"NavigateOut\",\n  /** Navigates to the previous item. */\n  NavigatePrevious: \"NavigatePrevious\",\n  /** Creates a new document or message. */\n  New: \"New\",\n  /**\n   * The Next Candidate function key. Selects the next possible match for the\n   * ongoing input.\n   */\n  NextCandidate: \"NextCandidate\",\n  /** Cycles to the next channel in the favorites list. */\n  NextFavoriteChannel: \"NextFavoriteChannel\",\n  /**\n   * Cycles to the next saved user profile, if this feature is supported and\n   * multiple profiles exist.\n   */\n  NextUserProfile: \"NextUserProfile\",\n  /**\n   * The NonConvert (\"Don't convert\") key. This accepts the current input method\n   * sequence without running conversion when using an IME.\n   */\n  NonConvert: \"NonConvert\",\n  /** The Notification key. */\n  Notification: \"Notification\",\n  /**\n   * The NumLock (Number Lock) key. Toggles the numeric keypad between number\n   * entry some other mode (often directional arrows).\n   */\n  NumLock: \"NumLock\",\n  /**\n   * Opens the user interface for selecting on demand content or programs to\n   * watch.\n   */\n  OnDemand: \"OnDemand\",\n  /** Opens an existing document or message. */\n  Open: \"Open\",\n  /**\n   * The Page Down (or PgDn) key. Scrolls down or displays the next page of\n   * content.\n   */\n  PageDown: \"PageDown\",\n  /**\n   * The Page Up (or PgUp) key. Scrolls up or displays the previous page of\n   * content.\n   */\n  PageUp: \"PageUp\",\n  /** Starts the process of pairing the remote with a device to be controlled. */\n  Pairing: \"Pairing\",\n  /** Paste from the clipboard. */\n  Paste: \"Paste\",\n  /**\n   * The Pause key. Pauses the current application or state, if applicable. This\n   * shouldn't be confused with the \"MediaPause\" key value, which is used for\n   * media controllers, rather than to control applications and processes.\n   */\n  Pause: \"Pause\",\n  /** A button to move the picture-in-picture view downward. */\n  PinPDown: \"PinPDown\",\n  /** A button to control moving the picture-in-picture view. */\n  PinPMove: \"PinPMove\",\n  /** Toggles display of th epicture-in-picture view on and off. */\n  PinPToggle: \"PinPToggle\",\n  /** A button to move the picture-in-picture view upward. */\n  PinPUp: \"PinPUp\",\n  /**\n   * The Play key. Resumes a previously paused application, if applicable. This\n   * shouldn't be confused with the \"MediaPlay\" key value, which is used for\n   * media controllers, rather than to control applications and processes.\n   */\n  Play: \"Play\",\n  /** Decreases the media playback rate. */\n  PlaySpeedDown: \"PlaySpeedDown\",\n  /** Returns the media playback rate to normal. */\n  PlaySpeedReset: \"PlaySpeedReset\",\n  /** Increases the media playback rate. */\n  PlaySpeedUp: \"PlaySpeedUp\",\n  /**\n   * The Power button or key, to toggle power on and off. Not all systems pass\n   * this key through to to the user agent.\n   */\n  Power: \"Power\",\n  /** The PowerOff or PowerDown key. Shuts off the system. */\n  PowerOff: \"PowerOff\",\n  /**\n   * The Previous Candidate key. Selects the previous possible match for the\n   * ongoing input.\n   */\n  PreviousCandidate: \"PreviousCandidate\",\n  /** Prints the current document or message. */\n  Print: \"Print\",\n  /**\n   * The PrintScreen or PrtScr key. Sometimes SnapShot. Captures the screen and\n   * prints it or saves it to disk.\n   */\n  PrintScreen: \"PrintScreen\",\n  /** The Process key. Instructs the IME to process the conversion. */\n  Process: \"Process\",\n  /** The Props (Properties) key. */\n  Props: \"Props\",\n  /** Toggles random media (also known as \"shuffle mode\") on and off. */\n  RandomToggle: \"RandomToggle\",\n  /**\n   * A code sent when the remote control's battery is low. This doesn't actually\n   * correspond to a physical key at all.\n   */\n  RcLowBattery: \"RcLowBattery\",\n  /** Cycles among the available media recording speeds. */\n  RecordSpeedNext: \"RecordSpeedNext\",\n  /** Redo the last action. */\n  Redo: \"Redo\",\n  /**\n   * Toggles radio frequency (RF) input bypass mode on and off. RF bypass mode\n   * passes RF input directly to the RF output without any processing or\n   * filtering.\n   */\n  RfBypass: \"RfBypass\",\n  /** The Romaji key; selects the Roman character set. */\n  Romaji: \"Romaji\",\n  /** Cycles among input modes on an external set-top box (STB). */\n  STBInput: \"STBInput\",\n  /** Toggles on and off an external STB. */\n  STBPower: \"STBPower\",\n  /** Saves the current document or message. */\n  Save: \"Save\",\n  /**\n   * Toggles the channel scan mode on and off; this is a mode which flips\n   * through channels automatically until the user stops the scan.\n   */\n  ScanChannelsToggle: \"ScanChannelsToggle\",\n  /** Cycles through the available screen display modes. */\n  ScreenModeNext: \"ScreenModeNext\",\n  /** The Scroll Lock key. Toggles beteen scrolling and cursor movement modes. */\n  ScrollLock: \"ScrollLock\",\n  /** The Select key. */\n  Select: \"Select\",\n  /**\n   * The numeric keypad's places separator character (in the United States, this\n   * is a comma, but elsewhere it is frequently a period).\n   */\n  Separator: \"Separator\",\n  /** Toggles display of the device's settings screen on and off. */\n  Settings: \"Settings\",\n  /**\n   * The Shift key. Modifies keystrokes to allow typing upper (or other) case\n   * letters, and to support typing punctuation and other special characters.\n   */\n  Shift: \"Shift\",\n  /**\n   * The Single Candidate key. Enables single candidate mode (as opposed to\n   * multi-candidate mode); in this mode, only one candidate is displayed at a\n   * time.\n   */\n  SingleCandidate: \"SingleCandidate\",\n  /** The first general-purpose virtual function key. */\n  Soft1: \"Soft1\",\n  /** The second general-purpose virtual function key. */\n  Soft2: \"Soft2\",\n  /** The third general-purpose virtual function key. */\n  Soft3: \"Soft3\",\n  /** The fourth general-purpose virtual function key. */\n  Soft4: \"Soft4\",\n  /**\n   * Presents a list of possible corrections for a word which was incorrectly\n   * identified.\n   */\n  SpeechCorrectionList: \"SpeechCorrectionList\",\n  /**\n   * Toggles between dictation mode and command/control mode. This lets the\n   * speech engine know whether to interpret spoken words as input text or as\n   * commands.\n   */\n  SpeechInputToggle: \"SpeechInputToggle\",\n  /** Starts spell checking the current document. */\n  SpellCheck: \"SpellCheck\",\n  /** Toggles split screen display mode on and off. */\n  SplitScreenToggle: \"SplitScreenToggle\",\n  /**\n   * The Standby key; also known as Suspend or Sleep. This turns off the display\n   * and puts the computer in a low power consumption mode, without completely\n   * powering off.\n   */\n  Standby: \"Standby\",\n  /** Toggles the display of subtitles on and off if they're available. */\n  Subtitle: \"Subtitle\",\n  /** The numeric keypad's subtraction key, -. */\n  Subtract: \"Subtract\",\n  /** The Super key. */\n  Super: \"Super\",\n  /** The Symbol modifier key (found on certain virtual keyboards). */\n  Symbol: \"Symbol\",\n  /** The Symbol Lock key. */\n  SymbolLock: \"SymbolLock\",\n  /** Switches into TV viewing mode. */\n  TV: \"TV\",\n  /** Toggles 3D TV mode on and off. */\n  TV3DMode: \"TV3DMode\",\n  /** Toggles between antenna and cable inputs. */\n  TVAntennaCable: \"TVAntennaCable\",\n  /** Toggles audio description mode on and off. */\n  TVAudioDescription: \"TVAudioDescription\",\n  /**\n   * Decreases trhe audio description's mixing volume; reduces the volume of the\n   * audio descriptions relative to the program sound.\n   */\n  TVAudioDescriptionMixDown: \"TVAudioDescriptionMixDown\",\n  /**\n   * Increases the audio description's mixing volume; increases the volume of\n   * the audio descriptions relative to the program sound.\n   */\n  TVAudioDescriptionMixUp: \"TVAudioDescriptionMixUp\",\n  /**\n   * Displays or hides the media contents available for playback (this may be a\n   * channel guide showing the currently airing programs, or a list of media\n   * files to play).\n   */\n  TVContentsMenu: \"TVContentsMenu\",\n  /** Displays or hides the TV's data service menu. */\n  TVDataService: \"TVDataService\",\n  /** Cycles the input mode on an external TV. */\n  TVInput: \"TVInput\",\n  /** Switches to the input \"Component 1.\" */\n  TVInputComponent1: \"TVInputComponent1\",\n  /** Switches to the input \"Component 2.\" */\n  TVInputComponent2: \"TVInputComponent2\",\n  /** Switches to the input \"Composite 1.\" */\n  TVInputComposite1: \"TVInputComposite1\",\n  /** Switches to the input \"Composite 2.\" */\n  TVInputComposite2: \"TVInputComposite2\",\n  /** Switches to the input \"HDMI 1.\" */\n  TVInputHDMI1: \"TVInputHDMI1\",\n  /** Switches to the input \"HDMI 2.\" */\n  TVInputHDMI2: \"TVInputHDMI2\",\n  /** Switches to the input \"HDMI 3.\" */\n  TVInputHDMI3: \"TVInputHDMI3\",\n  /** Switches to the input \"HDMI 4.\" */\n  TVInputHDMI4: \"TVInputHDMI4\",\n  /** Switches to the input \"VGA 1.\" */\n  TVInputVGA1: \"TVInputVGA1\",\n  /** The Media Context menu key. */\n  TVMediaContext: \"TVMediaContext\",\n  /** Toggle the TV's network connection on and off. */\n  TVNetwork: \"TVNetwork\",\n  /** Put the TV into number entry mode. */\n  TVNumberEntry: \"TVNumberEntry\",\n  /** The device's power button. */\n  TVPower: \"TVPower\",\n  /** Radio button. */\n  TVRadioService: \"TVRadioService\",\n  /** Satellite button. */\n  TVSatellite: \"TVSatellite\",\n  /** Broadcast Satellite button. */\n  TVSatelliteBS: \"TVSatelliteBS\",\n  /** Communication Satellite button. */\n  TVSatelliteCS: \"TVSatelliteCS\",\n  /** Toggles among available satellites. */\n  TVSatelliteToggle: \"TVSatelliteToggle\",\n  /**\n   * Selects analog terrestrial television service (analog cable or antenna\n   * reception).\n   */\n  TVTerrestrialAnalog: \"TVTerrestrialAnalog\",\n  /**\n   * Selects digital terrestrial television service (digital cable or antenna\n   * receiption).\n   */\n  TVTerrestrialDigital: \"TVTerrestrialDigital\",\n  /** Timer programming button. */\n  TVTimer: \"TVTimer\",\n  /** The Horizontal Tab key, Tab. */\n  Tab: \"Tab\",\n  /** Toggles display of teletext, if available. */\n  Teletext: \"Teletext\",\n  /** Undo the last action. */\n  Undo: \"Undo\",\n  /**\n   * The user agent wasn't able to map the event's virtual keycode to a specific\n   * key value. This can happen due to hardware or software constraints, or\n   * because of constraints around the platform on which the user agent is\n   * running.\n   */\n  Unidentified: \"Unidentified\",\n  /** Cycles through the available video modes. */\n  VideoModeNext: \"VideoModeNext\",\n  /** The Voice Dial key. Initiates voice dialing. */\n  VoiceDial: \"VoiceDial\",\n  /**\n   * The WakeUp key; used to wake the computer from the hibernation or standby\n   * modes.\n   */\n  WakeUp: \"WakeUp\",\n  /**\n   * Causes the device to identify itself in some fashion, such as by flashing a\n   * light, briefly changing the brightness of indicator lights, or emitting a\n   * tone.\n   */\n  Wink: \"Wink\",\n  /** The Zenkaku (full width) characters key. */\n  Zenkaku: \"Zenkaku\",\n  /** The Zenkaku/Hankaku (full width/half width) toggle key. */\n  ZenkakuHanaku: \"ZenkakuHanaku\",\n  /** The ZoomIn key. */\n  ZoomIn: \"ZoomIn\",\n  /** The ZoomOut key. */\n  ZoomOut: \"ZoomOut\",\n  /**\n   * Toggles between full-screen and scaled content display, or otherwise change\n   * the magnification level.\n   */\n  ZoomToggle: \"ZoomToggle\"\n};\n\n// src/internal/useRecordHotkeys.ts\n\nfunction useRecordHotkeys() {\n  const [keys, setKeys] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(/* @__PURE__ */ new Set());\n  const [isRecording, setIsRecording] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const handler = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event) => {\n    if (event.code === void 0) {\n      return;\n    }\n    event.preventDefault();\n    event.stopPropagation();\n    setKeys((prev) => {\n      const newKeys = new Set(prev);\n      newKeys.add(mapKey(event.code));\n      return newKeys;\n    });\n  }, []);\n  const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (typeof document !== \"undefined\") {\n      document.removeEventListener(\"keydown\", handler);\n      setIsRecording(false);\n    }\n  }, [handler]);\n  const start = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setKeys(/* @__PURE__ */ new Set());\n    if (typeof document !== \"undefined\") {\n      stop();\n      document.addEventListener(\"keydown\", handler);\n      setIsRecording(true);\n    }\n  }, [handler, stop]);\n  const resetKeys = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setKeys(/* @__PURE__ */ new Set());\n  }, []);\n  return [keys, { isRecording, resetKeys, start, stop }];\n}\n\n// src/internal/useHotkeys.ts\n\n\n// src/internal/useDeepEqualMemo.ts\n\nfunction useDeepEqualMemo(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  if (!deepEqual(ref.current, value)) {\n    ref.current = value;\n  }\n  return ref.current;\n}\n\n// src/internal/validators.ts\nfunction maybePreventDefault(e, hotkey, preventDefault) {\n  if (typeof preventDefault === \"function\" && preventDefault(e, hotkey) || preventDefault === true) {\n    e.preventDefault();\n  }\n}\nfunction isHotkeyEnabled(e, hotkey, enabled) {\n  if (typeof enabled === \"function\") {\n    return enabled(e, hotkey);\n  }\n  return enabled === true || enabled === void 0;\n}\nfunction isKeyboardEventTriggeredByInput(ev) {\n  return isHotkeyEnabledOnTag(ev, [\"input\", \"textarea\", \"select\"]);\n}\nfunction isHotkeyEnabledOnTag({ target }, enabledOnTags = false) {\n  const targetTagName = target && target.tagName;\n  if (isReadonlyArray(enabledOnTags)) {\n    return Boolean(\n      targetTagName && (enabledOnTags == null ? void 0 : enabledOnTags.some(\n        (tag) => tag.toLowerCase() === targetTagName.toLowerCase()\n      ))\n    );\n  }\n  return Boolean(targetTagName && enabledOnTags && enabledOnTags);\n}\nfunction isScopeActive(activeScopes, scopes) {\n  if (activeScopes.length === 0 && scopes) {\n    console.warn(\n      'A hotkey has the \"scopes\" option set, however no active scopes were found. If you want to use the global scopes feature, you need to wrap your app in a <HotkeysProvider>'\n    );\n    return true;\n  }\n  if (!scopes) {\n    return true;\n  }\n  return activeScopes.some((scope) => scopes.includes(scope)) || activeScopes.includes(\"*\");\n}\nvar isHotkeyMatchingKeyboardEvent = (e, hotkey, ignoreModifiers = false) => {\n  const { alt, ctrl, keys, meta, mod, shift, useKey } = hotkey;\n  const { altKey, code, ctrlKey, key: producedKey, metaKey, shiftKey } = e;\n  const mappedCode = mapKey(code);\n  if (useKey && (keys == null ? void 0 : keys.length) === 1 && keys.includes(producedKey)) {\n    return true;\n  }\n  if (!(keys == null ? void 0 : keys.includes(mappedCode)) && ![\"alt\", \"control\", \"ctrl\", \"meta\", \"os\", \"shift\", \"unknown\"].includes(\n    mappedCode\n  )) {\n    return false;\n  }\n  if (!ignoreModifiers) {\n    if (alt !== altKey && mappedCode !== \"alt\") {\n      return false;\n    }\n    if (shift !== shiftKey && mappedCode !== \"shift\") {\n      return false;\n    }\n    if (mod) {\n      if (!metaKey && !ctrlKey) {\n        return false;\n      }\n    } else {\n      if (meta !== metaKey && mappedCode !== \"meta\" && mappedCode !== \"os\") {\n        return false;\n      }\n      if (ctrl !== ctrlKey && mappedCode !== \"ctrl\" && mappedCode !== \"control\") {\n        return false;\n      }\n    }\n  }\n  if (keys && keys.length === 1 && keys.includes(mappedCode)) {\n    return true;\n  } else if (keys) {\n    return isHotkeyPressed(keys);\n  } else if (!keys) {\n    return true;\n  }\n  return false;\n};\n\n// src/internal/useHotkeys.ts\nvar stopPropagation = (e) => {\n  e.stopPropagation();\n  e.preventDefault();\n  e.stopImmediatePropagation();\n};\nvar useSafeLayoutEffect = typeof window === \"undefined\" ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\nfunction useHotkeys(keys, callback, options, dependencies) {\n  const [ref, setRef] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const hasTriggeredRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n  const _options = Array.isArray(options) ? Array.isArray(dependencies) ? void 0 : dependencies : options;\n  const _keys = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (Array.isArray(keys) && keys.length > 0 && Array.isArray(keys[0])) {\n      return keys.map(\n        (keyCombo) => keyCombo.map((k) => k.toString()).join((_options == null ? void 0 : _options.splitKey) || \"+\")\n      ).join((_options == null ? void 0 : _options.delimiter) || \",\");\n    } else if (Array.isArray(keys)) {\n      return keys.join((_options == null ? void 0 : _options.delimiter) || \",\");\n    }\n    return keys;\n  }, [keys, _options == null ? void 0 : _options.splitKey, _options == null ? void 0 : _options.delimiter]);\n  const _deps = Array.isArray(options) ? options : Array.isArray(dependencies) ? dependencies : void 0;\n  const memoisedCB = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(callback, _deps != null ? _deps : []);\n  const cbRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(memoisedCB);\n  cbRef.current = _deps ? memoisedCB : callback;\n  const memoisedOptions = useDeepEqualMemo(_options);\n  const { activeScopes } = useHotkeysContext();\n  const proxy = useBoundHotkeysProxy();\n  useSafeLayoutEffect(() => {\n    if ((memoisedOptions == null ? void 0 : memoisedOptions.enabled) === false || !isScopeActive(activeScopes, memoisedOptions == null ? void 0 : memoisedOptions.scopes)) {\n      return;\n    }\n    const listener = (e, isKeyUp = false) => {\n      var _a;\n      if (isKeyboardEventTriggeredByInput(e) && !isHotkeyEnabledOnTag(e, memoisedOptions == null ? void 0 : memoisedOptions.enableOnFormTags)) {\n        return;\n      }\n      if (ref !== null) {\n        const rootNode = ref.getRootNode();\n        if ((rootNode instanceof Document || rootNode instanceof ShadowRoot) && rootNode.activeElement !== ref && !ref.contains(rootNode.activeElement)) {\n          stopPropagation(e);\n          return;\n        }\n      }\n      if (((_a = e.target) == null ? void 0 : _a.isContentEditable) && !(memoisedOptions == null ? void 0 : memoisedOptions.enableOnContentEditable)) {\n        return;\n      }\n      parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.delimiter).forEach((key) => {\n        var _a2, _b, _c;\n        const hotkey = parseHotkey(\n          key,\n          memoisedOptions == null ? void 0 : memoisedOptions.splitKey,\n          memoisedOptions == null ? void 0 : memoisedOptions.useKey\n        );\n        if (isHotkeyMatchingKeyboardEvent(\n          e,\n          hotkey,\n          memoisedOptions == null ? void 0 : memoisedOptions.ignoreModifiers\n        ) || ((_a2 = hotkey.keys) == null ? void 0 : _a2.includes(\"*\"))) {\n          if (((_b = memoisedOptions == null ? void 0 : memoisedOptions.ignoreEventWhenPrevented) != null ? _b : true) && e.defaultPrevented) {\n            return;\n          }\n          if ((_c = memoisedOptions == null ? void 0 : memoisedOptions.ignoreEventWhen) == null ? void 0 : _c.call(memoisedOptions, e)) {\n            return;\n          }\n          if (isKeyUp && hasTriggeredRef.current) {\n            return;\n          }\n          if (!isHotkeyEnabled(e, hotkey, memoisedOptions == null ? void 0 : memoisedOptions.enabled)) {\n            stopPropagation(e);\n            return;\n          }\n          cbRef.current(e, hotkey);\n          maybePreventDefault(e, hotkey, memoisedOptions == null ? void 0 : memoisedOptions.preventDefault);\n          if (!isKeyUp) {\n            hasTriggeredRef.current = true;\n          }\n        }\n      });\n    };\n    const handleKeyDown = (event) => {\n      if (event.code === void 0) {\n        return;\n      }\n      pushToCurrentlyPressedKeys(mapKey(event.code));\n      if ((memoisedOptions == null ? void 0 : memoisedOptions.keydown) === void 0 && (memoisedOptions == null ? void 0 : memoisedOptions.keyup) !== true || (memoisedOptions == null ? void 0 : memoisedOptions.keydown)) {\n        listener(event);\n      }\n    };\n    const handleKeyUp = (event) => {\n      if (event.code === void 0) {\n        return;\n      }\n      removeFromCurrentlyPressedKeys(mapKey(event.code));\n      hasTriggeredRef.current = false;\n      if (memoisedOptions == null ? void 0 : memoisedOptions.keyup) {\n        listener(event, true);\n      }\n    };\n    const domNode = ref || (_options == null ? void 0 : _options.document) || document;\n    domNode.addEventListener(\"keyup\", handleKeyUp);\n    domNode.addEventListener(\"keydown\", handleKeyDown);\n    if (proxy) {\n      parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.delimiter).forEach(\n        (key) => proxy.addHotkey(\n          parseHotkey(\n            key,\n            memoisedOptions == null ? void 0 : memoisedOptions.splitKey,\n            memoisedOptions == null ? void 0 : memoisedOptions.useKey,\n            memoisedOptions == null ? void 0 : memoisedOptions.description\n          )\n        )\n      );\n    }\n    return () => {\n      domNode.removeEventListener(\"keyup\", handleKeyUp);\n      domNode.removeEventListener(\"keydown\", handleKeyDown);\n      if (proxy) {\n        parseKeysHookInput(_keys, memoisedOptions == null ? void 0 : memoisedOptions.delimiter).forEach(\n          (key) => proxy.removeHotkey(\n            parseHotkey(\n              key,\n              memoisedOptions == null ? void 0 : memoisedOptions.splitKey,\n              memoisedOptions == null ? void 0 : memoisedOptions.useKey,\n              memoisedOptions == null ? void 0 : memoisedOptions.description\n            )\n          )\n        );\n      }\n    };\n  }, [ref, _keys, memoisedOptions, activeScopes]);\n  return setRef;\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+react-hotkeys@37.0_0ced0e67047e23cf44986a957ed41462/node_modules/@udecode/react-hotkeys/dist/index.mjs\n");

/***/ })

};
;