"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/destroy@1.2.0";
exports.ids = ["vendor-chunks/destroy@1.2.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/destroy@1.2.0/node_modules/destroy/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/.pnpm/destroy@1.2.0/node_modules/destroy/index.js ***!
  \************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * destroy\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2015-2022 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter)\nvar ReadStream = (__webpack_require__(/*! fs */ \"fs\").ReadStream)\nvar Stream = __webpack_require__(/*! stream */ \"stream\")\nvar Zlib = __webpack_require__(/*! zlib */ \"zlib\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = destroy\n\n/**\n * Destroy the given stream, and optionally suppress any future `error` events.\n *\n * @param {object} stream\n * @param {boolean} suppress\n * @public\n */\n\nfunction destroy (stream, suppress) {\n  if (isFsReadStream(stream)) {\n    destroyReadStream(stream)\n  } else if (isZlibStream(stream)) {\n    destroyZlibStream(stream)\n  } else if (hasDestroy(stream)) {\n    stream.destroy()\n  }\n\n  if (isEventEmitter(stream) && suppress) {\n    stream.removeAllListeners('error')\n    stream.addListener('error', noop)\n  }\n\n  return stream\n}\n\n/**\n * Destroy a ReadStream.\n *\n * @param {object} stream\n * @private\n */\n\nfunction destroyReadStream (stream) {\n  stream.destroy()\n\n  if (typeof stream.close === 'function') {\n    // node.js core bug work-around\n    stream.on('open', onOpenClose)\n  }\n}\n\n/**\n * Close a Zlib stream.\n *\n * Zlib streams below Node.js 4.5.5 have a buggy implementation\n * of .close() when zlib encountered an error.\n *\n * @param {object} stream\n * @private\n */\n\nfunction closeZlibStream (stream) {\n  if (stream._hadError === true) {\n    var prop = stream._binding === null\n      ? '_binding'\n      : '_handle'\n\n    stream[prop] = {\n      close: function () { this[prop] = null }\n    }\n  }\n\n  stream.close()\n}\n\n/**\n * Destroy a Zlib stream.\n *\n * Zlib streams don't have a destroy function in Node.js 6. On top of that\n * simply calling destroy on a zlib stream in Node.js 8+ will result in a\n * memory leak. So until that is fixed, we need to call both close AND destroy.\n *\n * PR to fix memory leak: https://github.com/nodejs/node/pull/23734\n *\n * In Node.js 6+8, it's important that destroy is called before close as the\n * stream would otherwise emit the error 'zlib binding closed'.\n *\n * @param {object} stream\n * @private\n */\n\nfunction destroyZlibStream (stream) {\n  if (typeof stream.destroy === 'function') {\n    // node.js core bug work-around\n    // istanbul ignore if: node.js 0.8\n    if (stream._binding) {\n      // node.js < 0.10.0\n      stream.destroy()\n      if (stream._processing) {\n        stream._needDrain = true\n        stream.once('drain', onDrainClearBinding)\n      } else {\n        stream._binding.clear()\n      }\n    } else if (stream._destroy && stream._destroy !== Stream.Transform.prototype._destroy) {\n      // node.js >= 12, ^11.1.0, ^10.15.1\n      stream.destroy()\n    } else if (stream._destroy && typeof stream.close === 'function') {\n      // node.js 7, 8\n      stream.destroyed = true\n      stream.close()\n    } else {\n      // fallback\n      // istanbul ignore next\n      stream.destroy()\n    }\n  } else if (typeof stream.close === 'function') {\n    // node.js < 8 fallback\n    closeZlibStream(stream)\n  }\n}\n\n/**\n * Determine if stream has destroy.\n * @private\n */\n\nfunction hasDestroy (stream) {\n  return stream instanceof Stream &&\n    typeof stream.destroy === 'function'\n}\n\n/**\n * Determine if val is EventEmitter.\n * @private\n */\n\nfunction isEventEmitter (val) {\n  return val instanceof EventEmitter\n}\n\n/**\n * Determine if stream is fs.ReadStream stream.\n * @private\n */\n\nfunction isFsReadStream (stream) {\n  return stream instanceof ReadStream\n}\n\n/**\n * Determine if stream is Zlib stream.\n * @private\n */\n\nfunction isZlibStream (stream) {\n  return stream instanceof Zlib.Gzip ||\n    stream instanceof Zlib.Gunzip ||\n    stream instanceof Zlib.Deflate ||\n    stream instanceof Zlib.DeflateRaw ||\n    stream instanceof Zlib.Inflate ||\n    stream instanceof Zlib.InflateRaw ||\n    stream instanceof Zlib.Unzip\n}\n\n/**\n * No-op function.\n * @private\n */\n\nfunction noop () {}\n\n/**\n * On drain handler to clear binding.\n * @private\n */\n\n// istanbul ignore next: node.js 0.8\nfunction onDrainClearBinding () {\n  this._binding.clear()\n}\n\n/**\n * On open handler to close stream.\n * @private\n */\n\nfunction onOpenClose () {\n  if (typeof this.fd === 'number') {\n    // actually close down the fd\n    this.close()\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/destroy@1.2.0/node_modules/destroy/index.js\n");

/***/ })

};
;