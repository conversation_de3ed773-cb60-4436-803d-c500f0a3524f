"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit+sortable@10.0.0_@d_49b065a3e8e29071c7c7cfdb2705f220";
exports.ids = ["vendor-chunks/@dnd-kit+sortable@10.0.0_@d_49b065a3e8e29071c7c7cfdb2705f220"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@dnd-kit+sortable@10.0.0_@d_49b065a3e8e29071c7c7cfdb2705f220/node_modules/@dnd-kit/sortable/dist/sortable.esm.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@dnd-kit+sortable@10.0.0_@d_49b065a3e8e29071c7c7cfdb2705f220/node_modules/@dnd-kit/sortable/dist/sortable.esm.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SortableContext: () => (/* binding */ SortableContext),\n/* harmony export */   arrayMove: () => (/* binding */ arrayMove),\n/* harmony export */   arraySwap: () => (/* binding */ arraySwap),\n/* harmony export */   defaultAnimateLayoutChanges: () => (/* binding */ defaultAnimateLayoutChanges),\n/* harmony export */   defaultNewIndexGetter: () => (/* binding */ defaultNewIndexGetter),\n/* harmony export */   hasSortableData: () => (/* binding */ hasSortableData),\n/* harmony export */   horizontalListSortingStrategy: () => (/* binding */ horizontalListSortingStrategy),\n/* harmony export */   rectSortingStrategy: () => (/* binding */ rectSortingStrategy),\n/* harmony export */   rectSwappingStrategy: () => (/* binding */ rectSwappingStrategy),\n/* harmony export */   sortableKeyboardCoordinates: () => (/* binding */ sortableKeyboardCoordinates),\n/* harmony export */   useSortable: () => (/* binding */ useSortable),\n/* harmony export */   verticalListSortingStrategy: () => (/* binding */ verticalListSortingStrategy)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @dnd-kit/core */ \"(ssr)/./node_modules/.pnpm/@dnd-kit+core@6.3.1_react-d_ac0082293d4bb6ee8db9ad14f3ee262f/node_modules/@dnd-kit/core/dist/core.esm.js\");\n/* harmony import */ var _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @dnd-kit/utilities */ \"(ssr)/./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@18.2.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js\");\n\n\n\n\n/**\r\n * Move an array item to a different position. Returns a new array with the item moved to the new position.\r\n */\nfunction arrayMove(array, from, to) {\n  const newArray = array.slice();\n  newArray.splice(to < 0 ? newArray.length + to : to, 0, newArray.splice(from, 1)[0]);\n  return newArray;\n}\n\n/**\r\n * Swap an array item to a different position. Returns a new array with the item swapped to the new position.\r\n */\nfunction arraySwap(array, from, to) {\n  const newArray = array.slice();\n  newArray[from] = array[to];\n  newArray[to] = array[from];\n  return newArray;\n}\n\nfunction getSortedRects(items, rects) {\n  return items.reduce((accumulator, id, index) => {\n    const rect = rects.get(id);\n\n    if (rect) {\n      accumulator[index] = rect;\n    }\n\n    return accumulator;\n  }, Array(items.length));\n}\n\nfunction isValidIndex(index) {\n  return index !== null && index >= 0;\n}\n\nfunction itemsEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n\n  if (a.length !== b.length) {\n    return false;\n  }\n\n  for (let i = 0; i < a.length; i++) {\n    if (a[i] !== b[i]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction normalizeDisabled(disabled) {\n  if (typeof disabled === 'boolean') {\n    return {\n      draggable: disabled,\n      droppable: disabled\n    };\n  }\n\n  return disabled;\n}\n\n// To-do: We should be calculating scale transformation\nconst defaultScale = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst horizontalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    rects,\n    activeNodeRect: fallbackActiveRect,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  const itemGap = getItemGap(rects, index, activeIndex);\n\n  if (index === activeIndex) {\n    const newIndexRect = rects[overIndex];\n\n    if (!newIndexRect) {\n      return null;\n    }\n\n    return {\n      x: activeIndex < overIndex ? newIndexRect.left + newIndexRect.width - (activeNodeRect.left + activeNodeRect.width) : newIndexRect.left - activeNodeRect.left,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: -activeNodeRect.width - itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: activeNodeRect.width + itemGap,\n      y: 0,\n      ...defaultScale\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale\n  };\n};\n\nfunction getItemGap(rects, index, activeIndex) {\n  const currentRect = rects[index];\n  const previousRect = rects[index - 1];\n  const nextRect = rects[index + 1];\n\n  if (!currentRect || !previousRect && !nextRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.left - (previousRect.left + previousRect.width) : nextRect.left - (currentRect.left + currentRect.width);\n  }\n\n  return nextRect ? nextRect.left - (currentRect.left + currentRect.width) : currentRect.left - (previousRect.left + previousRect.width);\n}\n\nconst rectSortingStrategy = _ref => {\n  let {\n    rects,\n    activeIndex,\n    overIndex,\n    index\n  } = _ref;\n  const newRects = arrayMove(rects, overIndex, activeIndex);\n  const oldRect = rects[index];\n  const newRect = newRects[index];\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\nconst rectSwappingStrategy = _ref => {\n  let {\n    activeIndex,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  let oldRect;\n  let newRect;\n\n  if (index === activeIndex) {\n    oldRect = rects[index];\n    newRect = rects[overIndex];\n  }\n\n  if (index === overIndex) {\n    oldRect = rects[index];\n    newRect = rects[activeIndex];\n  }\n\n  if (!newRect || !oldRect) {\n    return null;\n  }\n\n  return {\n    x: newRect.left - oldRect.left,\n    y: newRect.top - oldRect.top,\n    scaleX: newRect.width / oldRect.width,\n    scaleY: newRect.height / oldRect.height\n  };\n};\n\n// To-do: We should be calculating scale transformation\nconst defaultScale$1 = {\n  scaleX: 1,\n  scaleY: 1\n};\nconst verticalListSortingStrategy = _ref => {\n  var _rects$activeIndex;\n\n  let {\n    activeIndex,\n    activeNodeRect: fallbackActiveRect,\n    index,\n    rects,\n    overIndex\n  } = _ref;\n  const activeNodeRect = (_rects$activeIndex = rects[activeIndex]) != null ? _rects$activeIndex : fallbackActiveRect;\n\n  if (!activeNodeRect) {\n    return null;\n  }\n\n  if (index === activeIndex) {\n    const overIndexRect = rects[overIndex];\n\n    if (!overIndexRect) {\n      return null;\n    }\n\n    return {\n      x: 0,\n      y: activeIndex < overIndex ? overIndexRect.top + overIndexRect.height - (activeNodeRect.top + activeNodeRect.height) : overIndexRect.top - activeNodeRect.top,\n      ...defaultScale$1\n    };\n  }\n\n  const itemGap = getItemGap$1(rects, index, activeIndex);\n\n  if (index > activeIndex && index <= overIndex) {\n    return {\n      x: 0,\n      y: -activeNodeRect.height - itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  if (index < activeIndex && index >= overIndex) {\n    return {\n      x: 0,\n      y: activeNodeRect.height + itemGap,\n      ...defaultScale$1\n    };\n  }\n\n  return {\n    x: 0,\n    y: 0,\n    ...defaultScale$1\n  };\n};\n\nfunction getItemGap$1(clientRects, index, activeIndex) {\n  const currentRect = clientRects[index];\n  const previousRect = clientRects[index - 1];\n  const nextRect = clientRects[index + 1];\n\n  if (!currentRect) {\n    return 0;\n  }\n\n  if (activeIndex < index) {\n    return previousRect ? currentRect.top - (previousRect.top + previousRect.height) : nextRect ? nextRect.top - (currentRect.top + currentRect.height) : 0;\n  }\n\n  return nextRect ? nextRect.top - (currentRect.top + currentRect.height) : previousRect ? currentRect.top - (previousRect.top + previousRect.height) : 0;\n}\n\nconst ID_PREFIX = 'Sortable';\nconst Context = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({\n  activeIndex: -1,\n  containerId: ID_PREFIX,\n  disableTransforms: false,\n  items: [],\n  overIndex: -1,\n  useDragOverlay: false,\n  sortedRects: [],\n  strategy: rectSortingStrategy,\n  disabled: {\n    draggable: false,\n    droppable: false\n  }\n});\nfunction SortableContext(_ref) {\n  let {\n    children,\n    id,\n    items: userDefinedItems,\n    strategy = rectSortingStrategy,\n    disabled: disabledProp = false\n  } = _ref;\n  const {\n    active,\n    dragOverlay,\n    droppableRects,\n    over,\n    measureDroppableContainers\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDndContext)();\n  const containerId = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useUniqueId)(ID_PREFIX, id);\n  const useDragOverlay = Boolean(dragOverlay.rect !== null);\n  const items = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => userDefinedItems.map(item => typeof item === 'object' && 'id' in item ? item.id : item), [userDefinedItems]);\n  const isDragging = active != null;\n  const activeIndex = active ? items.indexOf(active.id) : -1;\n  const overIndex = over ? items.indexOf(over.id) : -1;\n  const previousItemsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(items);\n  const itemsHaveChanged = !itemsEqual(items, previousItemsRef.current);\n  const disableTransforms = overIndex !== -1 && activeIndex === -1 || itemsHaveChanged;\n  const disabled = normalizeDisabled(disabledProp);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (itemsHaveChanged && isDragging) {\n      measureDroppableContainers(items);\n    }\n  }, [itemsHaveChanged, items, isDragging, measureDroppableContainers]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    previousItemsRef.current = items;\n  }, [items]);\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    activeIndex,\n    containerId,\n    disabled,\n    disableTransforms,\n    items,\n    overIndex,\n    useDragOverlay,\n    sortedRects: getSortedRects(items, droppableRects),\n    strategy\n  }), // eslint-disable-next-line react-hooks/exhaustive-deps\n  [activeIndex, containerId, disabled.draggable, disabled.droppable, disableTransforms, items, overIndex, droppableRects, useDragOverlay, strategy]);\n  return react__WEBPACK_IMPORTED_MODULE_0___default().createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nconst defaultNewIndexGetter = _ref => {\n  let {\n    id,\n    items,\n    activeIndex,\n    overIndex\n  } = _ref;\n  return arrayMove(items, activeIndex, overIndex).indexOf(id);\n};\nconst defaultAnimateLayoutChanges = _ref2 => {\n  let {\n    containerId,\n    isSorting,\n    wasDragging,\n    index,\n    items,\n    newIndex,\n    previousItems,\n    previousContainerId,\n    transition\n  } = _ref2;\n\n  if (!transition || !wasDragging) {\n    return false;\n  }\n\n  if (previousItems !== items && index === newIndex) {\n    return false;\n  }\n\n  if (isSorting) {\n    return true;\n  }\n\n  return newIndex !== index && containerId === previousContainerId;\n};\nconst defaultTransition = {\n  duration: 200,\n  easing: 'ease'\n};\nconst transitionProperty = 'transform';\nconst disabledTransition = /*#__PURE__*/_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({\n  property: transitionProperty,\n  duration: 0,\n  easing: 'linear'\n});\nconst defaultAttributes = {\n  roleDescription: 'sortable'\n};\n\n/*\r\n * When the index of an item changes while sorting,\r\n * we need to temporarily disable the transforms\r\n */\n\nfunction useDerivedTransform(_ref) {\n  let {\n    disabled,\n    index,\n    node,\n    rect\n  } = _ref;\n  const [derivedTransform, setDerivedtransform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const previousIndex = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(index);\n  (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useIsomorphicLayoutEffect)(() => {\n    if (!disabled && index !== previousIndex.current && node.current) {\n      const initial = rect.current;\n\n      if (initial) {\n        const current = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getClientRect)(node.current, {\n          ignoreTransform: true\n        });\n        const delta = {\n          x: initial.left - current.left,\n          y: initial.top - current.top,\n          scaleX: initial.width / current.width,\n          scaleY: initial.height / current.height\n        };\n\n        if (delta.x || delta.y) {\n          setDerivedtransform(delta);\n        }\n      }\n    }\n\n    if (index !== previousIndex.current) {\n      previousIndex.current = index;\n    }\n  }, [disabled, index, node, rect]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (derivedTransform) {\n      setDerivedtransform(null);\n    }\n  }, [derivedTransform]);\n  return derivedTransform;\n}\n\nfunction useSortable(_ref) {\n  let {\n    animateLayoutChanges = defaultAnimateLayoutChanges,\n    attributes: userDefinedAttributes,\n    disabled: localDisabled,\n    data: customData,\n    getNewIndex = defaultNewIndexGetter,\n    id,\n    strategy: localStrategy,\n    resizeObserverConfig,\n    transition = defaultTransition\n  } = _ref;\n  const {\n    items,\n    containerId,\n    activeIndex,\n    disabled: globalDisabled,\n    disableTransforms,\n    sortedRects,\n    overIndex,\n    useDragOverlay,\n    strategy: globalStrategy\n  } = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(Context);\n  const disabled = normalizeLocalDisabled(localDisabled, globalDisabled);\n  const index = items.indexOf(id);\n  const data = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    sortable: {\n      containerId,\n      index,\n      items\n    },\n    ...customData\n  }), [containerId, customData, index, items]);\n  const itemsAfterCurrentSortable = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => items.slice(items.indexOf(id)), [items, id]);\n  const {\n    rect,\n    node,\n    isOver,\n    setNodeRef: setDroppableNodeRef\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDroppable)({\n    id,\n    data,\n    disabled: disabled.droppable,\n    resizeObserverConfig: {\n      updateMeasurementsFor: itemsAfterCurrentSortable,\n      ...resizeObserverConfig\n    }\n  });\n  const {\n    active,\n    activatorEvent,\n    activeNodeRect,\n    attributes,\n    setNodeRef: setDraggableNodeRef,\n    listeners,\n    isDragging,\n    over,\n    setActivatorNodeRef,\n    transform\n  } = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.useDraggable)({\n    id,\n    data,\n    attributes: { ...defaultAttributes,\n      ...userDefinedAttributes\n    },\n    disabled: disabled.draggable\n  });\n  const setNodeRef = (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.useCombinedRefs)(setDroppableNodeRef, setDraggableNodeRef);\n  const isSorting = Boolean(active);\n  const displaceItem = isSorting && !disableTransforms && isValidIndex(activeIndex) && isValidIndex(overIndex);\n  const shouldDisplaceDragSource = !useDragOverlay && isDragging;\n  const dragSourceDisplacement = shouldDisplaceDragSource && displaceItem ? transform : null;\n  const strategy = localStrategy != null ? localStrategy : globalStrategy;\n  const finalTransform = displaceItem ? dragSourceDisplacement != null ? dragSourceDisplacement : strategy({\n    rects: sortedRects,\n    activeNodeRect,\n    activeIndex,\n    overIndex,\n    index\n  }) : null;\n  const newIndex = isValidIndex(activeIndex) && isValidIndex(overIndex) ? getNewIndex({\n    id,\n    items,\n    activeIndex,\n    overIndex\n  }) : index;\n  const activeId = active == null ? void 0 : active.id;\n  const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    activeId,\n    items,\n    newIndex,\n    containerId\n  });\n  const itemsHaveChanged = items !== previous.current.items;\n  const shouldAnimateLayoutChanges = animateLayoutChanges({\n    active,\n    containerId,\n    isDragging,\n    isSorting,\n    id,\n    index,\n    items,\n    newIndex: previous.current.newIndex,\n    previousItems: previous.current.items,\n    previousContainerId: previous.current.containerId,\n    transition,\n    wasDragging: previous.current.activeId != null\n  });\n  const derivedTransform = useDerivedTransform({\n    disabled: !shouldAnimateLayoutChanges,\n    index,\n    node,\n    rect\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isSorting && previous.current.newIndex !== newIndex) {\n      previous.current.newIndex = newIndex;\n    }\n\n    if (containerId !== previous.current.containerId) {\n      previous.current.containerId = containerId;\n    }\n\n    if (items !== previous.current.items) {\n      previous.current.items = items;\n    }\n  }, [isSorting, newIndex, containerId, items]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (activeId === previous.current.activeId) {\n      return;\n    }\n\n    if (activeId != null && previous.current.activeId == null) {\n      previous.current.activeId = activeId;\n      return;\n    }\n\n    const timeoutId = setTimeout(() => {\n      previous.current.activeId = activeId;\n    }, 50);\n    return () => clearTimeout(timeoutId);\n  }, [activeId]);\n  return {\n    active,\n    activeIndex,\n    attributes,\n    data,\n    rect,\n    index,\n    newIndex,\n    items,\n    isOver,\n    isSorting,\n    isDragging,\n    listeners,\n    node,\n    overIndex,\n    over,\n    setNodeRef,\n    setActivatorNodeRef,\n    setDroppableNodeRef,\n    setDraggableNodeRef,\n    transform: derivedTransform != null ? derivedTransform : finalTransform,\n    transition: getTransition()\n  };\n\n  function getTransition() {\n    if ( // Temporarily disable transitions for a single frame to set up derived transforms\n    derivedTransform || // Or to prevent items jumping to back to their \"new\" position when items change\n    itemsHaveChanged && previous.current.newIndex === index) {\n      return disabledTransition;\n    }\n\n    if (shouldDisplaceDragSource && !(0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.isKeyboardEvent)(activatorEvent) || !transition) {\n      return undefined;\n    }\n\n    if (isSorting || shouldAnimateLayoutChanges) {\n      return _dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.CSS.Transition.toString({ ...transition,\n        property: transitionProperty\n      });\n    }\n\n    return undefined;\n  }\n}\n\nfunction normalizeLocalDisabled(localDisabled, globalDisabled) {\n  var _localDisabled$dragga, _localDisabled$droppa;\n\n  if (typeof localDisabled === 'boolean') {\n    return {\n      draggable: localDisabled,\n      // Backwards compatibility\n      droppable: false\n    };\n  }\n\n  return {\n    draggable: (_localDisabled$dragga = localDisabled == null ? void 0 : localDisabled.draggable) != null ? _localDisabled$dragga : globalDisabled.draggable,\n    droppable: (_localDisabled$droppa = localDisabled == null ? void 0 : localDisabled.droppable) != null ? _localDisabled$droppa : globalDisabled.droppable\n  };\n}\n\nfunction hasSortableData(entry) {\n  if (!entry) {\n    return false;\n  }\n\n  const data = entry.data.current;\n\n  if (data && 'sortable' in data && typeof data.sortable === 'object' && 'containerId' in data.sortable && 'items' in data.sortable && 'index' in data.sortable) {\n    return true;\n  }\n\n  return false;\n}\n\nconst directions = [_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up, _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left];\nconst sortableKeyboardCoordinates = (event, _ref) => {\n  let {\n    context: {\n      active,\n      collisionRect,\n      droppableRects,\n      droppableContainers,\n      over,\n      scrollableAncestors\n    }\n  } = _ref;\n\n  if (directions.includes(event.code)) {\n    event.preventDefault();\n\n    if (!active || !collisionRect) {\n      return;\n    }\n\n    const filteredContainers = [];\n    droppableContainers.getEnabled().forEach(entry => {\n      if (!entry || entry != null && entry.disabled) {\n        return;\n      }\n\n      const rect = droppableRects.get(entry.id);\n\n      if (!rect) {\n        return;\n      }\n\n      switch (event.code) {\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Down:\n          if (collisionRect.top < rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Up:\n          if (collisionRect.top > rect.top) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Left:\n          if (collisionRect.left > rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n\n        case _dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.KeyboardCode.Right:\n          if (collisionRect.left < rect.left) {\n            filteredContainers.push(entry);\n          }\n\n          break;\n      }\n    });\n    const collisions = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.closestCorners)({\n      active,\n      collisionRect: collisionRect,\n      droppableRects,\n      droppableContainers: filteredContainers,\n      pointerCoordinates: null\n    });\n    let closestId = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getFirstCollision)(collisions, 'id');\n\n    if (closestId === (over == null ? void 0 : over.id) && collisions.length > 1) {\n      closestId = collisions[1].id;\n    }\n\n    if (closestId != null) {\n      const activeDroppable = droppableContainers.get(active.id);\n      const newDroppable = droppableContainers.get(closestId);\n      const newRect = newDroppable ? droppableRects.get(newDroppable.id) : null;\n      const newNode = newDroppable == null ? void 0 : newDroppable.node.current;\n\n      if (newNode && newRect && activeDroppable && newDroppable) {\n        const newScrollAncestors = (0,_dnd_kit_core__WEBPACK_IMPORTED_MODULE_1__.getScrollableAncestors)(newNode);\n        const hasDifferentScrollAncestors = newScrollAncestors.some((element, index) => scrollableAncestors[index] !== element);\n        const hasSameContainer = isSameContainer(activeDroppable, newDroppable);\n        const isAfterActive = isAfter(activeDroppable, newDroppable);\n        const offset = hasDifferentScrollAncestors || !hasSameContainer ? {\n          x: 0,\n          y: 0\n        } : {\n          x: isAfterActive ? collisionRect.width - newRect.width : 0,\n          y: isAfterActive ? collisionRect.height - newRect.height : 0\n        };\n        const rectCoordinates = {\n          x: newRect.left,\n          y: newRect.top\n        };\n        const newCoordinates = offset.x && offset.y ? rectCoordinates : (0,_dnd_kit_utilities__WEBPACK_IMPORTED_MODULE_2__.subtract)(rectCoordinates, offset);\n        return newCoordinates;\n      }\n    }\n  }\n\n  return undefined;\n};\n\nfunction isSameContainer(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.containerId === b.data.current.sortable.containerId;\n}\n\nfunction isAfter(a, b) {\n  if (!hasSortableData(a) || !hasSortableData(b)) {\n    return false;\n  }\n\n  if (!isSameContainer(a, b)) {\n    return false;\n  }\n\n  return a.data.current.sortable.index < b.data.current.sortable.index;\n}\n\n\n//# sourceMappingURL=sortable.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@dnd-kit+sortable@10.0.0_@d_49b065a3e8e29071c7c7cfdb2705f220/node_modules/@dnd-kit/sortable/dist/sortable.esm.js\n");

/***/ })

};
;