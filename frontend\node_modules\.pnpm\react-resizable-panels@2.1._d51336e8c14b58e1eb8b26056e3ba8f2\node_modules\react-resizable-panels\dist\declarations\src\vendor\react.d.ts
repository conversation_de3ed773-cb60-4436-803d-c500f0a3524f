import * as React from "react";
import type { CSSProperties, ElementType, ForwardedRef, HTMLAttributes, MouseEvent, PropsWithChildren, ReactElement, ReactNode, RefObject, TouchEvent } from "react";
declare const createElement: typeof React.createElement, createContext: typeof React.createContext, createRef: typeof React.createRef, forwardRef: typeof React.forwardRef, useCallback: typeof React.useCallback, useContext: typeof React.useContext, useEffect: typeof React.useEffect, useImperativeHandle: typeof React.useImperativeHandle, useMemo: typeof React.useMemo, useRef: typeof React.useRef, useState: typeof React.useState;
declare const useId: () => string;
declare const useLayoutEffect_do_not_use_directly: typeof React.useLayoutEffect;
export { createElement, createContext, createRef, forwardRef, useCallback, useContext, useEffect, useId, useImperativeHandle, useLayoutEffect_do_not_use_directly, useMemo, useRef, useState, };
export type { CSSProperties, ElementType, ForwardedRef, HTMLAttributes, MouseEvent, PropsWithChildren, ReactElement, ReactNode, RefObject, TouchEvent, };
