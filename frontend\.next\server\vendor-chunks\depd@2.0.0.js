/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/depd@2.0.0";
exports.ids = ["vendor-chunks/depd@2.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * depd\n * Copyright(c) 2014-2018 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n/**\n * Module dependencies.\n */\n\nvar relative = (__webpack_require__(/*! path */ \"path\").relative)\n\n/**\n * Module exports.\n */\n\nmodule.exports = depd\n\n/**\n * Get the path to base files on.\n */\n\nvar basePath = process.cwd()\n\n/**\n * Determine if namespace is contained in the string.\n */\n\nfunction containsNamespace (str, namespace) {\n  var vals = str.split(/[ ,]+/)\n  var ns = String(namespace).toLowerCase()\n\n  for (var i = 0; i < vals.length; i++) {\n    var val = vals[i]\n\n    // namespace contained\n    if (val && (val === '*' || val.toLowerCase() === ns)) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Convert a data descriptor to accessor descriptor.\n */\n\nfunction convertDataDescriptorToAccessor (obj, prop, message) {\n  var descriptor = Object.getOwnPropertyDescriptor(obj, prop)\n  var value = descriptor.value\n\n  descriptor.get = function getter () { return value }\n\n  if (descriptor.writable) {\n    descriptor.set = function setter (val) { return (value = val) }\n  }\n\n  delete descriptor.value\n  delete descriptor.writable\n\n  Object.defineProperty(obj, prop, descriptor)\n\n  return descriptor\n}\n\n/**\n * Create arguments string to keep arity.\n */\n\nfunction createArgumentsString (arity) {\n  var str = ''\n\n  for (var i = 0; i < arity; i++) {\n    str += ', arg' + i\n  }\n\n  return str.substr(2)\n}\n\n/**\n * Create stack string from stack.\n */\n\nfunction createStackString (stack) {\n  var str = this.name + ': ' + this.namespace\n\n  if (this.message) {\n    str += ' deprecated ' + this.message\n  }\n\n  for (var i = 0; i < stack.length; i++) {\n    str += '\\n    at ' + stack[i].toString()\n  }\n\n  return str\n}\n\n/**\n * Create deprecate for namespace in caller.\n */\n\nfunction depd (namespace) {\n  if (!namespace) {\n    throw new TypeError('argument namespace is required')\n  }\n\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n  var file = site[0]\n\n  function deprecate (message) {\n    // call to self as log\n    log.call(deprecate, message)\n  }\n\n  deprecate._file = file\n  deprecate._ignored = isignored(namespace)\n  deprecate._namespace = namespace\n  deprecate._traced = istraced(namespace)\n  deprecate._warned = Object.create(null)\n\n  deprecate.function = wrapfunction\n  deprecate.property = wrapproperty\n\n  return deprecate\n}\n\n/**\n * Determine if event emitter has listeners of a given type.\n *\n * The way to do this check is done three different ways in Node.js >= 0.8\n * so this consolidates them into a minimal set using instance methods.\n *\n * @param {EventEmitter} emitter\n * @param {string} type\n * @returns {boolean}\n * @private\n */\n\nfunction eehaslisteners (emitter, type) {\n  var count = typeof emitter.listenerCount !== 'function'\n    ? emitter.listeners(type).length\n    : emitter.listenerCount(type)\n\n  return count > 0\n}\n\n/**\n * Determine if namespace is ignored.\n */\n\nfunction isignored (namespace) {\n  if (process.noDeprecation) {\n    // --no-deprecation support\n    return true\n  }\n\n  var str = process.env.NO_DEPRECATION || ''\n\n  // namespace ignored\n  return containsNamespace(str, namespace)\n}\n\n/**\n * Determine if namespace is traced.\n */\n\nfunction istraced (namespace) {\n  if (process.traceDeprecation) {\n    // --trace-deprecation support\n    return true\n  }\n\n  var str = process.env.TRACE_DEPRECATION || ''\n\n  // namespace traced\n  return containsNamespace(str, namespace)\n}\n\n/**\n * Display deprecation message.\n */\n\nfunction log (message, site) {\n  var haslisteners = eehaslisteners(process, 'deprecation')\n\n  // abort early if no destination\n  if (!haslisteners && this._ignored) {\n    return\n  }\n\n  var caller\n  var callFile\n  var callSite\n  var depSite\n  var i = 0\n  var seen = false\n  var stack = getStack()\n  var file = this._file\n\n  if (site) {\n    // provided site\n    depSite = site\n    callSite = callSiteLocation(stack[1])\n    callSite.name = depSite.name\n    file = callSite[0]\n  } else {\n    // get call site\n    i = 2\n    depSite = callSiteLocation(stack[i])\n    callSite = depSite\n  }\n\n  // get caller of deprecated thing in relation to file\n  for (; i < stack.length; i++) {\n    caller = callSiteLocation(stack[i])\n    callFile = caller[0]\n\n    if (callFile === file) {\n      seen = true\n    } else if (callFile === this._file) {\n      file = this._file\n    } else if (seen) {\n      break\n    }\n  }\n\n  var key = caller\n    ? depSite.join(':') + '__' + caller.join(':')\n    : undefined\n\n  if (key !== undefined && key in this._warned) {\n    // already warned\n    return\n  }\n\n  this._warned[key] = true\n\n  // generate automatic message from call site\n  var msg = message\n  if (!msg) {\n    msg = callSite === depSite || !callSite.name\n      ? defaultMessage(depSite)\n      : defaultMessage(callSite)\n  }\n\n  // emit deprecation if listeners exist\n  if (haslisteners) {\n    var err = DeprecationError(this._namespace, msg, stack.slice(i))\n    process.emit('deprecation', err)\n    return\n  }\n\n  // format and write message\n  var format = process.stderr.isTTY\n    ? formatColor\n    : formatPlain\n  var output = format.call(this, msg, caller, stack.slice(i))\n  process.stderr.write(output + '\\n', 'utf8')\n}\n\n/**\n * Get call site location as array.\n */\n\nfunction callSiteLocation (callSite) {\n  var file = callSite.getFileName() || '<anonymous>'\n  var line = callSite.getLineNumber()\n  var colm = callSite.getColumnNumber()\n\n  if (callSite.isEval()) {\n    file = callSite.getEvalOrigin() + ', ' + file\n  }\n\n  var site = [file, line, colm]\n\n  site.callSite = callSite\n  site.name = callSite.getFunctionName()\n\n  return site\n}\n\n/**\n * Generate a default message from the site.\n */\n\nfunction defaultMessage (site) {\n  var callSite = site.callSite\n  var funcName = site.name\n\n  // make useful anonymous name\n  if (!funcName) {\n    funcName = '<anonymous@' + formatLocation(site) + '>'\n  }\n\n  var context = callSite.getThis()\n  var typeName = context && callSite.getTypeName()\n\n  // ignore useless type name\n  if (typeName === 'Object') {\n    typeName = undefined\n  }\n\n  // make useful type name\n  if (typeName === 'Function') {\n    typeName = context.name || typeName\n  }\n\n  return typeName && callSite.getMethodName()\n    ? typeName + '.' + funcName\n    : funcName\n}\n\n/**\n * Format deprecation message without color.\n */\n\nfunction formatPlain (msg, caller, stack) {\n  var timestamp = new Date().toUTCString()\n\n  var formatted = timestamp +\n    ' ' + this._namespace +\n    ' deprecated ' + msg\n\n  // add stack trace\n  if (this._traced) {\n    for (var i = 0; i < stack.length; i++) {\n      formatted += '\\n    at ' + stack[i].toString()\n    }\n\n    return formatted\n  }\n\n  if (caller) {\n    formatted += ' at ' + formatLocation(caller)\n  }\n\n  return formatted\n}\n\n/**\n * Format deprecation message with color.\n */\n\nfunction formatColor (msg, caller, stack) {\n  var formatted = '\\x1b[36;1m' + this._namespace + '\\x1b[22;39m' + // bold cyan\n    ' \\x1b[33;1mdeprecated\\x1b[22;39m' + // bold yellow\n    ' \\x1b[0m' + msg + '\\x1b[39m' // reset\n\n  // add stack trace\n  if (this._traced) {\n    for (var i = 0; i < stack.length; i++) {\n      formatted += '\\n    \\x1b[36mat ' + stack[i].toString() + '\\x1b[39m' // cyan\n    }\n\n    return formatted\n  }\n\n  if (caller) {\n    formatted += ' \\x1b[36m' + formatLocation(caller) + '\\x1b[39m' // cyan\n  }\n\n  return formatted\n}\n\n/**\n * Format call site location.\n */\n\nfunction formatLocation (callSite) {\n  return relative(basePath, callSite[0]) +\n    ':' + callSite[1] +\n    ':' + callSite[2]\n}\n\n/**\n * Get the stack as array of call sites.\n */\n\nfunction getStack () {\n  var limit = Error.stackTraceLimit\n  var obj = {}\n  var prep = Error.prepareStackTrace\n\n  Error.prepareStackTrace = prepareObjectStackTrace\n  Error.stackTraceLimit = Math.max(10, limit)\n\n  // capture the stack\n  Error.captureStackTrace(obj)\n\n  // slice this function off the top\n  var stack = obj.stack.slice(1)\n\n  Error.prepareStackTrace = prep\n  Error.stackTraceLimit = limit\n\n  return stack\n}\n\n/**\n * Capture call site stack from v8.\n */\n\nfunction prepareObjectStackTrace (obj, stack) {\n  return stack\n}\n\n/**\n * Return a wrapped function in a deprecation message.\n */\n\nfunction wrapfunction (fn, message) {\n  if (typeof fn !== 'function') {\n    throw new TypeError('argument fn must be a function')\n  }\n\n  var args = createArgumentsString(fn.length)\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n\n  site.name = fn.name\n\n  // eslint-disable-next-line no-new-func\n  var deprecatedfn = new Function('fn', 'log', 'deprecate', 'message', 'site',\n    '\"use strict\"\\n' +\n    'return function (' + args + ') {' +\n    'log.call(deprecate, message, site)\\n' +\n    'return fn.apply(this, arguments)\\n' +\n    '}')(fn, log, this, message, site)\n\n  return deprecatedfn\n}\n\n/**\n * Wrap property in a deprecation message.\n */\n\nfunction wrapproperty (obj, prop, message) {\n  if (!obj || (typeof obj !== 'object' && typeof obj !== 'function')) {\n    throw new TypeError('argument obj must be object')\n  }\n\n  var descriptor = Object.getOwnPropertyDescriptor(obj, prop)\n\n  if (!descriptor) {\n    throw new TypeError('must call property on owner object')\n  }\n\n  if (!descriptor.configurable) {\n    throw new TypeError('property must be configurable')\n  }\n\n  var deprecate = this\n  var stack = getStack()\n  var site = callSiteLocation(stack[1])\n\n  // set site name\n  site.name = prop\n\n  // convert data descriptor\n  if ('value' in descriptor) {\n    descriptor = convertDataDescriptorToAccessor(obj, prop, message)\n  }\n\n  var get = descriptor.get\n  var set = descriptor.set\n\n  // wrap getter\n  if (typeof get === 'function') {\n    descriptor.get = function getter () {\n      log.call(deprecate, message, site)\n      return get.apply(this, arguments)\n    }\n  }\n\n  // wrap setter\n  if (typeof set === 'function') {\n    descriptor.set = function setter () {\n      log.call(deprecate, message, site)\n      return set.apply(this, arguments)\n    }\n  }\n\n  Object.defineProperty(obj, prop, descriptor)\n}\n\n/**\n * Create DeprecationError for deprecation\n */\n\nfunction DeprecationError (namespace, message, stack) {\n  var error = new Error()\n  var stackString\n\n  Object.defineProperty(error, 'constructor', {\n    value: DeprecationError\n  })\n\n  Object.defineProperty(error, 'message', {\n    configurable: true,\n    enumerable: false,\n    value: message,\n    writable: true\n  })\n\n  Object.defineProperty(error, 'name', {\n    enumerable: false,\n    configurable: true,\n    value: 'DeprecationError',\n    writable: true\n  })\n\n  Object.defineProperty(error, 'namespace', {\n    configurable: true,\n    enumerable: false,\n    value: namespace,\n    writable: true\n  })\n\n  Object.defineProperty(error, 'stack', {\n    configurable: true,\n    enumerable: false,\n    get: function () {\n      if (stackString !== undefined) {\n        return stackString\n      }\n\n      // prepare stack trace\n      return (stackString = createStackString.call(this, stack))\n    },\n    set: function setter (val) {\n      stackString = val\n    }\n  })\n\n  return error\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZGVwZEAyLjAuMC9ub2RlX21vZHVsZXMvZGVwZC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxlQUFlLGtEQUF3Qjs7QUFFdkM7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBLGtCQUFrQixpQkFBaUI7QUFDbkM7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUEsd0NBQXdDOztBQUV4QztBQUNBLDZDQUE2QztBQUM3Qzs7QUFFQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUEsa0JBQWtCLFdBQVc7QUFDN0I7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQSxrQkFBa0Isa0JBQWtCO0FBQ3BDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsY0FBYztBQUN6QixXQUFXLFFBQVE7QUFDbkIsYUFBYTtBQUNiO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxTQUFTLGtCQUFrQjtBQUMzQjtBQUNBOztBQUVBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EsMkJBQTJCLGlDQUFpQztBQUM1RCxjQUFjLG9CQUFvQjtBQUNsQzs7QUFFQTtBQUNBO0FBQ0Esb0JBQW9CLGtCQUFrQjtBQUN0QztBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0EscUNBQXFDO0FBQ3JDO0FBQ0E7QUFDQSxNQUFNOztBQUVOO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7O0FBRUg7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxHQUFHOztBQUVIO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vZGVwZEAyLjAuMC9ub2RlX21vZHVsZXMvZGVwZC9pbmRleC5qcz8xNmMyIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogZGVwZFxuICogQ29weXJpZ2h0KGMpIDIwMTQtMjAxOCBEb3VnbGFzIENocmlzdG9waGVyIFdpbHNvblxuICogTUlUIExpY2Vuc2VkXG4gKi9cblxuLyoqXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzLlxuICovXG5cbnZhciByZWxhdGl2ZSA9IHJlcXVpcmUoJ3BhdGgnKS5yZWxhdGl2ZVxuXG4vKipcbiAqIE1vZHVsZSBleHBvcnRzLlxuICovXG5cbm1vZHVsZS5leHBvcnRzID0gZGVwZFxuXG4vKipcbiAqIEdldCB0aGUgcGF0aCB0byBiYXNlIGZpbGVzIG9uLlxuICovXG5cbnZhciBiYXNlUGF0aCA9IHByb2Nlc3MuY3dkKClcblxuLyoqXG4gKiBEZXRlcm1pbmUgaWYgbmFtZXNwYWNlIGlzIGNvbnRhaW5lZCBpbiB0aGUgc3RyaW5nLlxuICovXG5cbmZ1bmN0aW9uIGNvbnRhaW5zTmFtZXNwYWNlIChzdHIsIG5hbWVzcGFjZSkge1xuICB2YXIgdmFscyA9IHN0ci5zcGxpdCgvWyAsXSsvKVxuICB2YXIgbnMgPSBTdHJpbmcobmFtZXNwYWNlKS50b0xvd2VyQ2FzZSgpXG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCB2YWxzLmxlbmd0aDsgaSsrKSB7XG4gICAgdmFyIHZhbCA9IHZhbHNbaV1cblxuICAgIC8vIG5hbWVzcGFjZSBjb250YWluZWRcbiAgICBpZiAodmFsICYmICh2YWwgPT09ICcqJyB8fCB2YWwudG9Mb3dlckNhc2UoKSA9PT0gbnMpKSB7XG4gICAgICByZXR1cm4gdHJ1ZVxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZVxufVxuXG4vKipcbiAqIENvbnZlcnQgYSBkYXRhIGRlc2NyaXB0b3IgdG8gYWNjZXNzb3IgZGVzY3JpcHRvci5cbiAqL1xuXG5mdW5jdGlvbiBjb252ZXJ0RGF0YURlc2NyaXB0b3JUb0FjY2Vzc29yIChvYmosIHByb3AsIG1lc3NhZ2UpIHtcbiAgdmFyIGRlc2NyaXB0b3IgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG9iaiwgcHJvcClcbiAgdmFyIHZhbHVlID0gZGVzY3JpcHRvci52YWx1ZVxuXG4gIGRlc2NyaXB0b3IuZ2V0ID0gZnVuY3Rpb24gZ2V0dGVyICgpIHsgcmV0dXJuIHZhbHVlIH1cblxuICBpZiAoZGVzY3JpcHRvci53cml0YWJsZSkge1xuICAgIGRlc2NyaXB0b3Iuc2V0ID0gZnVuY3Rpb24gc2V0dGVyICh2YWwpIHsgcmV0dXJuICh2YWx1ZSA9IHZhbCkgfVxuICB9XG5cbiAgZGVsZXRlIGRlc2NyaXB0b3IudmFsdWVcbiAgZGVsZXRlIGRlc2NyaXB0b3Iud3JpdGFibGVcblxuICBPYmplY3QuZGVmaW5lUHJvcGVydHkob2JqLCBwcm9wLCBkZXNjcmlwdG9yKVxuXG4gIHJldHVybiBkZXNjcmlwdG9yXG59XG5cbi8qKlxuICogQ3JlYXRlIGFyZ3VtZW50cyBzdHJpbmcgdG8ga2VlcCBhcml0eS5cbiAqL1xuXG5mdW5jdGlvbiBjcmVhdGVBcmd1bWVudHNTdHJpbmcgKGFyaXR5KSB7XG4gIHZhciBzdHIgPSAnJ1xuXG4gIGZvciAodmFyIGkgPSAwOyBpIDwgYXJpdHk7IGkrKykge1xuICAgIHN0ciArPSAnLCBhcmcnICsgaVxuICB9XG5cbiAgcmV0dXJuIHN0ci5zdWJzdHIoMilcbn1cblxuLyoqXG4gKiBDcmVhdGUgc3RhY2sgc3RyaW5nIGZyb20gc3RhY2suXG4gKi9cblxuZnVuY3Rpb24gY3JlYXRlU3RhY2tTdHJpbmcgKHN0YWNrKSB7XG4gIHZhciBzdHIgPSB0aGlzLm5hbWUgKyAnOiAnICsgdGhpcy5uYW1lc3BhY2VcblxuICBpZiAodGhpcy5tZXNzYWdlKSB7XG4gICAgc3RyICs9ICcgZGVwcmVjYXRlZCAnICsgdGhpcy5tZXNzYWdlXG4gIH1cblxuICBmb3IgKHZhciBpID0gMDsgaSA8IHN0YWNrLmxlbmd0aDsgaSsrKSB7XG4gICAgc3RyICs9ICdcXG4gICAgYXQgJyArIHN0YWNrW2ldLnRvU3RyaW5nKClcbiAgfVxuXG4gIHJldHVybiBzdHJcbn1cblxuLyoqXG4gKiBDcmVhdGUgZGVwcmVjYXRlIGZvciBuYW1lc3BhY2UgaW4gY2FsbGVyLlxuICovXG5cbmZ1bmN0aW9uIGRlcGQgKG5hbWVzcGFjZSkge1xuICBpZiAoIW5hbWVzcGFjZSkge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ2FyZ3VtZW50IG5hbWVzcGFjZSBpcyByZXF1aXJlZCcpXG4gIH1cblxuICB2YXIgc3RhY2sgPSBnZXRTdGFjaygpXG4gIHZhciBzaXRlID0gY2FsbFNpdGVMb2NhdGlvbihzdGFja1sxXSlcbiAgdmFyIGZpbGUgPSBzaXRlWzBdXG5cbiAgZnVuY3Rpb24gZGVwcmVjYXRlIChtZXNzYWdlKSB7XG4gICAgLy8gY2FsbCB0byBzZWxmIGFzIGxvZ1xuICAgIGxvZy5jYWxsKGRlcHJlY2F0ZSwgbWVzc2FnZSlcbiAgfVxuXG4gIGRlcHJlY2F0ZS5fZmlsZSA9IGZpbGVcbiAgZGVwcmVjYXRlLl9pZ25vcmVkID0gaXNpZ25vcmVkKG5hbWVzcGFjZSlcbiAgZGVwcmVjYXRlLl9uYW1lc3BhY2UgPSBuYW1lc3BhY2VcbiAgZGVwcmVjYXRlLl90cmFjZWQgPSBpc3RyYWNlZChuYW1lc3BhY2UpXG4gIGRlcHJlY2F0ZS5fd2FybmVkID0gT2JqZWN0LmNyZWF0ZShudWxsKVxuXG4gIGRlcHJlY2F0ZS5mdW5jdGlvbiA9IHdyYXBmdW5jdGlvblxuICBkZXByZWNhdGUucHJvcGVydHkgPSB3cmFwcHJvcGVydHlcblxuICByZXR1cm4gZGVwcmVjYXRlXG59XG5cbi8qKlxuICogRGV0ZXJtaW5lIGlmIGV2ZW50IGVtaXR0ZXIgaGFzIGxpc3RlbmVycyBvZiBhIGdpdmVuIHR5cGUuXG4gKlxuICogVGhlIHdheSB0byBkbyB0aGlzIGNoZWNrIGlzIGRvbmUgdGhyZWUgZGlmZmVyZW50IHdheXMgaW4gTm9kZS5qcyA+PSAwLjhcbiAqIHNvIHRoaXMgY29uc29saWRhdGVzIHRoZW0gaW50byBhIG1pbmltYWwgc2V0IHVzaW5nIGluc3RhbmNlIG1ldGhvZHMuXG4gKlxuICogQHBhcmFtIHtFdmVudEVtaXR0ZXJ9IGVtaXR0ZXJcbiAqIEBwYXJhbSB7c3RyaW5nfSB0eXBlXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqIEBwcml2YXRlXG4gKi9cblxuZnVuY3Rpb24gZWVoYXNsaXN0ZW5lcnMgKGVtaXR0ZXIsIHR5cGUpIHtcbiAgdmFyIGNvdW50ID0gdHlwZW9mIGVtaXR0ZXIubGlzdGVuZXJDb3VudCAhPT0gJ2Z1bmN0aW9uJ1xuICAgID8gZW1pdHRlci5saXN0ZW5lcnModHlwZSkubGVuZ3RoXG4gICAgOiBlbWl0dGVyLmxpc3RlbmVyQ291bnQodHlwZSlcblxuICByZXR1cm4gY291bnQgPiAwXG59XG5cbi8qKlxuICogRGV0ZXJtaW5lIGlmIG5hbWVzcGFjZSBpcyBpZ25vcmVkLlxuICovXG5cbmZ1bmN0aW9uIGlzaWdub3JlZCAobmFtZXNwYWNlKSB7XG4gIGlmIChwcm9jZXNzLm5vRGVwcmVjYXRpb24pIHtcbiAgICAvLyAtLW5vLWRlcHJlY2F0aW9uIHN1cHBvcnRcbiAgICByZXR1cm4gdHJ1ZVxuICB9XG5cbiAgdmFyIHN0ciA9IHByb2Nlc3MuZW52Lk5PX0RFUFJFQ0FUSU9OIHx8ICcnXG5cbiAgLy8gbmFtZXNwYWNlIGlnbm9yZWRcbiAgcmV0dXJuIGNvbnRhaW5zTmFtZXNwYWNlKHN0ciwgbmFtZXNwYWNlKVxufVxuXG4vKipcbiAqIERldGVybWluZSBpZiBuYW1lc3BhY2UgaXMgdHJhY2VkLlxuICovXG5cbmZ1bmN0aW9uIGlzdHJhY2VkIChuYW1lc3BhY2UpIHtcbiAgaWYgKHByb2Nlc3MudHJhY2VEZXByZWNhdGlvbikge1xuICAgIC8vIC0tdHJhY2UtZGVwcmVjYXRpb24gc3VwcG9ydFxuICAgIHJldHVybiB0cnVlXG4gIH1cblxuICB2YXIgc3RyID0gcHJvY2Vzcy5lbnYuVFJBQ0VfREVQUkVDQVRJT04gfHwgJydcblxuICAvLyBuYW1lc3BhY2UgdHJhY2VkXG4gIHJldHVybiBjb250YWluc05hbWVzcGFjZShzdHIsIG5hbWVzcGFjZSlcbn1cblxuLyoqXG4gKiBEaXNwbGF5IGRlcHJlY2F0aW9uIG1lc3NhZ2UuXG4gKi9cblxuZnVuY3Rpb24gbG9nIChtZXNzYWdlLCBzaXRlKSB7XG4gIHZhciBoYXNsaXN0ZW5lcnMgPSBlZWhhc2xpc3RlbmVycyhwcm9jZXNzLCAnZGVwcmVjYXRpb24nKVxuXG4gIC8vIGFib3J0IGVhcmx5IGlmIG5vIGRlc3RpbmF0aW9uXG4gIGlmICghaGFzbGlzdGVuZXJzICYmIHRoaXMuX2lnbm9yZWQpIHtcbiAgICByZXR1cm5cbiAgfVxuXG4gIHZhciBjYWxsZXJcbiAgdmFyIGNhbGxGaWxlXG4gIHZhciBjYWxsU2l0ZVxuICB2YXIgZGVwU2l0ZVxuICB2YXIgaSA9IDBcbiAgdmFyIHNlZW4gPSBmYWxzZVxuICB2YXIgc3RhY2sgPSBnZXRTdGFjaygpXG4gIHZhciBmaWxlID0gdGhpcy5fZmlsZVxuXG4gIGlmIChzaXRlKSB7XG4gICAgLy8gcHJvdmlkZWQgc2l0ZVxuICAgIGRlcFNpdGUgPSBzaXRlXG4gICAgY2FsbFNpdGUgPSBjYWxsU2l0ZUxvY2F0aW9uKHN0YWNrWzFdKVxuICAgIGNhbGxTaXRlLm5hbWUgPSBkZXBTaXRlLm5hbWVcbiAgICBmaWxlID0gY2FsbFNpdGVbMF1cbiAgfSBlbHNlIHtcbiAgICAvLyBnZXQgY2FsbCBzaXRlXG4gICAgaSA9IDJcbiAgICBkZXBTaXRlID0gY2FsbFNpdGVMb2NhdGlvbihzdGFja1tpXSlcbiAgICBjYWxsU2l0ZSA9IGRlcFNpdGVcbiAgfVxuXG4gIC8vIGdldCBjYWxsZXIgb2YgZGVwcmVjYXRlZCB0aGluZyBpbiByZWxhdGlvbiB0byBmaWxlXG4gIGZvciAoOyBpIDwgc3RhY2subGVuZ3RoOyBpKyspIHtcbiAgICBjYWxsZXIgPSBjYWxsU2l0ZUxvY2F0aW9uKHN0YWNrW2ldKVxuICAgIGNhbGxGaWxlID0gY2FsbGVyWzBdXG5cbiAgICBpZiAoY2FsbEZpbGUgPT09IGZpbGUpIHtcbiAgICAgIHNlZW4gPSB0cnVlXG4gICAgfSBlbHNlIGlmIChjYWxsRmlsZSA9PT0gdGhpcy5fZmlsZSkge1xuICAgICAgZmlsZSA9IHRoaXMuX2ZpbGVcbiAgICB9IGVsc2UgaWYgKHNlZW4pIHtcbiAgICAgIGJyZWFrXG4gICAgfVxuICB9XG5cbiAgdmFyIGtleSA9IGNhbGxlclxuICAgID8gZGVwU2l0ZS5qb2luKCc6JykgKyAnX18nICsgY2FsbGVyLmpvaW4oJzonKVxuICAgIDogdW5kZWZpbmVkXG5cbiAgaWYgKGtleSAhPT0gdW5kZWZpbmVkICYmIGtleSBpbiB0aGlzLl93YXJuZWQpIHtcbiAgICAvLyBhbHJlYWR5IHdhcm5lZFxuICAgIHJldHVyblxuICB9XG5cbiAgdGhpcy5fd2FybmVkW2tleV0gPSB0cnVlXG5cbiAgLy8gZ2VuZXJhdGUgYXV0b21hdGljIG1lc3NhZ2UgZnJvbSBjYWxsIHNpdGVcbiAgdmFyIG1zZyA9IG1lc3NhZ2VcbiAgaWYgKCFtc2cpIHtcbiAgICBtc2cgPSBjYWxsU2l0ZSA9PT0gZGVwU2l0ZSB8fCAhY2FsbFNpdGUubmFtZVxuICAgICAgPyBkZWZhdWx0TWVzc2FnZShkZXBTaXRlKVxuICAgICAgOiBkZWZhdWx0TWVzc2FnZShjYWxsU2l0ZSlcbiAgfVxuXG4gIC8vIGVtaXQgZGVwcmVjYXRpb24gaWYgbGlzdGVuZXJzIGV4aXN0XG4gIGlmIChoYXNsaXN0ZW5lcnMpIHtcbiAgICB2YXIgZXJyID0gRGVwcmVjYXRpb25FcnJvcih0aGlzLl9uYW1lc3BhY2UsIG1zZywgc3RhY2suc2xpY2UoaSkpXG4gICAgcHJvY2Vzcy5lbWl0KCdkZXByZWNhdGlvbicsIGVycilcbiAgICByZXR1cm5cbiAgfVxuXG4gIC8vIGZvcm1hdCBhbmQgd3JpdGUgbWVzc2FnZVxuICB2YXIgZm9ybWF0ID0gcHJvY2Vzcy5zdGRlcnIuaXNUVFlcbiAgICA/IGZvcm1hdENvbG9yXG4gICAgOiBmb3JtYXRQbGFpblxuICB2YXIgb3V0cHV0ID0gZm9ybWF0LmNhbGwodGhpcywgbXNnLCBjYWxsZXIsIHN0YWNrLnNsaWNlKGkpKVxuICBwcm9jZXNzLnN0ZGVyci53cml0ZShvdXRwdXQgKyAnXFxuJywgJ3V0ZjgnKVxufVxuXG4vKipcbiAqIEdldCBjYWxsIHNpdGUgbG9jYXRpb24gYXMgYXJyYXkuXG4gKi9cblxuZnVuY3Rpb24gY2FsbFNpdGVMb2NhdGlvbiAoY2FsbFNpdGUpIHtcbiAgdmFyIGZpbGUgPSBjYWxsU2l0ZS5nZXRGaWxlTmFtZSgpIHx8ICc8YW5vbnltb3VzPidcbiAgdmFyIGxpbmUgPSBjYWxsU2l0ZS5nZXRMaW5lTnVtYmVyKClcbiAgdmFyIGNvbG0gPSBjYWxsU2l0ZS5nZXRDb2x1bW5OdW1iZXIoKVxuXG4gIGlmIChjYWxsU2l0ZS5pc0V2YWwoKSkge1xuICAgIGZpbGUgPSBjYWxsU2l0ZS5nZXRFdmFsT3JpZ2luKCkgKyAnLCAnICsgZmlsZVxuICB9XG5cbiAgdmFyIHNpdGUgPSBbZmlsZSwgbGluZSwgY29sbV1cblxuICBzaXRlLmNhbGxTaXRlID0gY2FsbFNpdGVcbiAgc2l0ZS5uYW1lID0gY2FsbFNpdGUuZ2V0RnVuY3Rpb25OYW1lKClcblxuICByZXR1cm4gc2l0ZVxufVxuXG4vKipcbiAqIEdlbmVyYXRlIGEgZGVmYXVsdCBtZXNzYWdlIGZyb20gdGhlIHNpdGUuXG4gKi9cblxuZnVuY3Rpb24gZGVmYXVsdE1lc3NhZ2UgKHNpdGUpIHtcbiAgdmFyIGNhbGxTaXRlID0gc2l0ZS5jYWxsU2l0ZVxuICB2YXIgZnVuY05hbWUgPSBzaXRlLm5hbWVcblxuICAvLyBtYWtlIHVzZWZ1bCBhbm9ueW1vdXMgbmFtZVxuICBpZiAoIWZ1bmNOYW1lKSB7XG4gICAgZnVuY05hbWUgPSAnPGFub255bW91c0AnICsgZm9ybWF0TG9jYXRpb24oc2l0ZSkgKyAnPidcbiAgfVxuXG4gIHZhciBjb250ZXh0ID0gY2FsbFNpdGUuZ2V0VGhpcygpXG4gIHZhciB0eXBlTmFtZSA9IGNvbnRleHQgJiYgY2FsbFNpdGUuZ2V0VHlwZU5hbWUoKVxuXG4gIC8vIGlnbm9yZSB1c2VsZXNzIHR5cGUgbmFtZVxuICBpZiAodHlwZU5hbWUgPT09ICdPYmplY3QnKSB7XG4gICAgdHlwZU5hbWUgPSB1bmRlZmluZWRcbiAgfVxuXG4gIC8vIG1ha2UgdXNlZnVsIHR5cGUgbmFtZVxuICBpZiAodHlwZU5hbWUgPT09ICdGdW5jdGlvbicpIHtcbiAgICB0eXBlTmFtZSA9IGNvbnRleHQubmFtZSB8fCB0eXBlTmFtZVxuICB9XG5cbiAgcmV0dXJuIHR5cGVOYW1lICYmIGNhbGxTaXRlLmdldE1ldGhvZE5hbWUoKVxuICAgID8gdHlwZU5hbWUgKyAnLicgKyBmdW5jTmFtZVxuICAgIDogZnVuY05hbWVcbn1cblxuLyoqXG4gKiBGb3JtYXQgZGVwcmVjYXRpb24gbWVzc2FnZSB3aXRob3V0IGNvbG9yLlxuICovXG5cbmZ1bmN0aW9uIGZvcm1hdFBsYWluIChtc2csIGNhbGxlciwgc3RhY2spIHtcbiAgdmFyIHRpbWVzdGFtcCA9IG5ldyBEYXRlKCkudG9VVENTdHJpbmcoKVxuXG4gIHZhciBmb3JtYXR0ZWQgPSB0aW1lc3RhbXAgK1xuICAgICcgJyArIHRoaXMuX25hbWVzcGFjZSArXG4gICAgJyBkZXByZWNhdGVkICcgKyBtc2dcblxuICAvLyBhZGQgc3RhY2sgdHJhY2VcbiAgaWYgKHRoaXMuX3RyYWNlZCkge1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgc3RhY2subGVuZ3RoOyBpKyspIHtcbiAgICAgIGZvcm1hdHRlZCArPSAnXFxuICAgIGF0ICcgKyBzdGFja1tpXS50b1N0cmluZygpXG4gICAgfVxuXG4gICAgcmV0dXJuIGZvcm1hdHRlZFxuICB9XG5cbiAgaWYgKGNhbGxlcikge1xuICAgIGZvcm1hdHRlZCArPSAnIGF0ICcgKyBmb3JtYXRMb2NhdGlvbihjYWxsZXIpXG4gIH1cblxuICByZXR1cm4gZm9ybWF0dGVkXG59XG5cbi8qKlxuICogRm9ybWF0IGRlcHJlY2F0aW9uIG1lc3NhZ2Ugd2l0aCBjb2xvci5cbiAqL1xuXG5mdW5jdGlvbiBmb3JtYXRDb2xvciAobXNnLCBjYWxsZXIsIHN0YWNrKSB7XG4gIHZhciBmb3JtYXR0ZWQgPSAnXFx4MWJbMzY7MW0nICsgdGhpcy5fbmFtZXNwYWNlICsgJ1xceDFiWzIyOzM5bScgKyAvLyBib2xkIGN5YW5cbiAgICAnIFxceDFiWzMzOzFtZGVwcmVjYXRlZFxceDFiWzIyOzM5bScgKyAvLyBib2xkIHllbGxvd1xuICAgICcgXFx4MWJbMG0nICsgbXNnICsgJ1xceDFiWzM5bScgLy8gcmVzZXRcblxuICAvLyBhZGQgc3RhY2sgdHJhY2VcbiAgaWYgKHRoaXMuX3RyYWNlZCkge1xuICAgIGZvciAodmFyIGkgPSAwOyBpIDwgc3RhY2subGVuZ3RoOyBpKyspIHtcbiAgICAgIGZvcm1hdHRlZCArPSAnXFxuICAgIFxceDFiWzM2bWF0ICcgKyBzdGFja1tpXS50b1N0cmluZygpICsgJ1xceDFiWzM5bScgLy8gY3lhblxuICAgIH1cblxuICAgIHJldHVybiBmb3JtYXR0ZWRcbiAgfVxuXG4gIGlmIChjYWxsZXIpIHtcbiAgICBmb3JtYXR0ZWQgKz0gJyBcXHgxYlszNm0nICsgZm9ybWF0TG9jYXRpb24oY2FsbGVyKSArICdcXHgxYlszOW0nIC8vIGN5YW5cbiAgfVxuXG4gIHJldHVybiBmb3JtYXR0ZWRcbn1cblxuLyoqXG4gKiBGb3JtYXQgY2FsbCBzaXRlIGxvY2F0aW9uLlxuICovXG5cbmZ1bmN0aW9uIGZvcm1hdExvY2F0aW9uIChjYWxsU2l0ZSkge1xuICByZXR1cm4gcmVsYXRpdmUoYmFzZVBhdGgsIGNhbGxTaXRlWzBdKSArXG4gICAgJzonICsgY2FsbFNpdGVbMV0gK1xuICAgICc6JyArIGNhbGxTaXRlWzJdXG59XG5cbi8qKlxuICogR2V0IHRoZSBzdGFjayBhcyBhcnJheSBvZiBjYWxsIHNpdGVzLlxuICovXG5cbmZ1bmN0aW9uIGdldFN0YWNrICgpIHtcbiAgdmFyIGxpbWl0ID0gRXJyb3Iuc3RhY2tUcmFjZUxpbWl0XG4gIHZhciBvYmogPSB7fVxuICB2YXIgcHJlcCA9IEVycm9yLnByZXBhcmVTdGFja1RyYWNlXG5cbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBwcmVwYXJlT2JqZWN0U3RhY2tUcmFjZVxuICBFcnJvci5zdGFja1RyYWNlTGltaXQgPSBNYXRoLm1heCgxMCwgbGltaXQpXG5cbiAgLy8gY2FwdHVyZSB0aGUgc3RhY2tcbiAgRXJyb3IuY2FwdHVyZVN0YWNrVHJhY2Uob2JqKVxuXG4gIC8vIHNsaWNlIHRoaXMgZnVuY3Rpb24gb2ZmIHRoZSB0b3BcbiAgdmFyIHN0YWNrID0gb2JqLnN0YWNrLnNsaWNlKDEpXG5cbiAgRXJyb3IucHJlcGFyZVN0YWNrVHJhY2UgPSBwcmVwXG4gIEVycm9yLnN0YWNrVHJhY2VMaW1pdCA9IGxpbWl0XG5cbiAgcmV0dXJuIHN0YWNrXG59XG5cbi8qKlxuICogQ2FwdHVyZSBjYWxsIHNpdGUgc3RhY2sgZnJvbSB2OC5cbiAqL1xuXG5mdW5jdGlvbiBwcmVwYXJlT2JqZWN0U3RhY2tUcmFjZSAob2JqLCBzdGFjaykge1xuICByZXR1cm4gc3RhY2tcbn1cblxuLyoqXG4gKiBSZXR1cm4gYSB3cmFwcGVkIGZ1bmN0aW9uIGluIGEgZGVwcmVjYXRpb24gbWVzc2FnZS5cbiAqL1xuXG5mdW5jdGlvbiB3cmFwZnVuY3Rpb24gKGZuLCBtZXNzYWdlKSB7XG4gIGlmICh0eXBlb2YgZm4gIT09ICdmdW5jdGlvbicpIHtcbiAgICB0aHJvdyBuZXcgVHlwZUVycm9yKCdhcmd1bWVudCBmbiBtdXN0IGJlIGEgZnVuY3Rpb24nKVxuICB9XG5cbiAgdmFyIGFyZ3MgPSBjcmVhdGVBcmd1bWVudHNTdHJpbmcoZm4ubGVuZ3RoKVxuICB2YXIgc3RhY2sgPSBnZXRTdGFjaygpXG4gIHZhciBzaXRlID0gY2FsbFNpdGVMb2NhdGlvbihzdGFja1sxXSlcblxuICBzaXRlLm5hbWUgPSBmbi5uYW1lXG5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLW5ldy1mdW5jXG4gIHZhciBkZXByZWNhdGVkZm4gPSBuZXcgRnVuY3Rpb24oJ2ZuJywgJ2xvZycsICdkZXByZWNhdGUnLCAnbWVzc2FnZScsICdzaXRlJyxcbiAgICAnXCJ1c2Ugc3RyaWN0XCJcXG4nICtcbiAgICAncmV0dXJuIGZ1bmN0aW9uICgnICsgYXJncyArICcpIHsnICtcbiAgICAnbG9nLmNhbGwoZGVwcmVjYXRlLCBtZXNzYWdlLCBzaXRlKVxcbicgK1xuICAgICdyZXR1cm4gZm4uYXBwbHkodGhpcywgYXJndW1lbnRzKVxcbicgK1xuICAgICd9JykoZm4sIGxvZywgdGhpcywgbWVzc2FnZSwgc2l0ZSlcblxuICByZXR1cm4gZGVwcmVjYXRlZGZuXG59XG5cbi8qKlxuICogV3JhcCBwcm9wZXJ0eSBpbiBhIGRlcHJlY2F0aW9uIG1lc3NhZ2UuXG4gKi9cblxuZnVuY3Rpb24gd3JhcHByb3BlcnR5IChvYmosIHByb3AsIG1lc3NhZ2UpIHtcbiAgaWYgKCFvYmogfHwgKHR5cGVvZiBvYmogIT09ICdvYmplY3QnICYmIHR5cGVvZiBvYmogIT09ICdmdW5jdGlvbicpKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignYXJndW1lbnQgb2JqIG11c3QgYmUgb2JqZWN0JylcbiAgfVxuXG4gIHZhciBkZXNjcmlwdG9yID0gT2JqZWN0LmdldE93blByb3BlcnR5RGVzY3JpcHRvcihvYmosIHByb3ApXG5cbiAgaWYgKCFkZXNjcmlwdG9yKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcignbXVzdCBjYWxsIHByb3BlcnR5IG9uIG93bmVyIG9iamVjdCcpXG4gIH1cblxuICBpZiAoIWRlc2NyaXB0b3IuY29uZmlndXJhYmxlKSB7XG4gICAgdGhyb3cgbmV3IFR5cGVFcnJvcigncHJvcGVydHkgbXVzdCBiZSBjb25maWd1cmFibGUnKVxuICB9XG5cbiAgdmFyIGRlcHJlY2F0ZSA9IHRoaXNcbiAgdmFyIHN0YWNrID0gZ2V0U3RhY2soKVxuICB2YXIgc2l0ZSA9IGNhbGxTaXRlTG9jYXRpb24oc3RhY2tbMV0pXG5cbiAgLy8gc2V0IHNpdGUgbmFtZVxuICBzaXRlLm5hbWUgPSBwcm9wXG5cbiAgLy8gY29udmVydCBkYXRhIGRlc2NyaXB0b3JcbiAgaWYgKCd2YWx1ZScgaW4gZGVzY3JpcHRvcikge1xuICAgIGRlc2NyaXB0b3IgPSBjb252ZXJ0RGF0YURlc2NyaXB0b3JUb0FjY2Vzc29yKG9iaiwgcHJvcCwgbWVzc2FnZSlcbiAgfVxuXG4gIHZhciBnZXQgPSBkZXNjcmlwdG9yLmdldFxuICB2YXIgc2V0ID0gZGVzY3JpcHRvci5zZXRcblxuICAvLyB3cmFwIGdldHRlclxuICBpZiAodHlwZW9mIGdldCA9PT0gJ2Z1bmN0aW9uJykge1xuICAgIGRlc2NyaXB0b3IuZ2V0ID0gZnVuY3Rpb24gZ2V0dGVyICgpIHtcbiAgICAgIGxvZy5jYWxsKGRlcHJlY2F0ZSwgbWVzc2FnZSwgc2l0ZSlcbiAgICAgIHJldHVybiBnZXQuYXBwbHkodGhpcywgYXJndW1lbnRzKVxuICAgIH1cbiAgfVxuXG4gIC8vIHdyYXAgc2V0dGVyXG4gIGlmICh0eXBlb2Ygc2V0ID09PSAnZnVuY3Rpb24nKSB7XG4gICAgZGVzY3JpcHRvci5zZXQgPSBmdW5jdGlvbiBzZXR0ZXIgKCkge1xuICAgICAgbG9nLmNhbGwoZGVwcmVjYXRlLCBtZXNzYWdlLCBzaXRlKVxuICAgICAgcmV0dXJuIHNldC5hcHBseSh0aGlzLCBhcmd1bWVudHMpXG4gICAgfVxuICB9XG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KG9iaiwgcHJvcCwgZGVzY3JpcHRvcilcbn1cblxuLyoqXG4gKiBDcmVhdGUgRGVwcmVjYXRpb25FcnJvciBmb3IgZGVwcmVjYXRpb25cbiAqL1xuXG5mdW5jdGlvbiBEZXByZWNhdGlvbkVycm9yIChuYW1lc3BhY2UsIG1lc3NhZ2UsIHN0YWNrKSB7XG4gIHZhciBlcnJvciA9IG5ldyBFcnJvcigpXG4gIHZhciBzdGFja1N0cmluZ1xuXG4gIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShlcnJvciwgJ2NvbnN0cnVjdG9yJywge1xuICAgIHZhbHVlOiBEZXByZWNhdGlvbkVycm9yXG4gIH0pXG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGVycm9yLCAnbWVzc2FnZScsIHtcbiAgICBjb25maWd1cmFibGU6IHRydWUsXG4gICAgZW51bWVyYWJsZTogZmFsc2UsXG4gICAgdmFsdWU6IG1lc3NhZ2UsXG4gICAgd3JpdGFibGU6IHRydWVcbiAgfSlcblxuICBPYmplY3QuZGVmaW5lUHJvcGVydHkoZXJyb3IsICduYW1lJywge1xuICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICB2YWx1ZTogJ0RlcHJlY2F0aW9uRXJyb3InLFxuICAgIHdyaXRhYmxlOiB0cnVlXG4gIH0pXG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGVycm9yLCAnbmFtZXNwYWNlJywge1xuICAgIGNvbmZpZ3VyYWJsZTogdHJ1ZSxcbiAgICBlbnVtZXJhYmxlOiBmYWxzZSxcbiAgICB2YWx1ZTogbmFtZXNwYWNlLFxuICAgIHdyaXRhYmxlOiB0cnVlXG4gIH0pXG5cbiAgT2JqZWN0LmRlZmluZVByb3BlcnR5KGVycm9yLCAnc3RhY2snLCB7XG4gICAgY29uZmlndXJhYmxlOiB0cnVlLFxuICAgIGVudW1lcmFibGU6IGZhbHNlLFxuICAgIGdldDogZnVuY3Rpb24gKCkge1xuICAgICAgaWYgKHN0YWNrU3RyaW5nICE9PSB1bmRlZmluZWQpIHtcbiAgICAgICAgcmV0dXJuIHN0YWNrU3RyaW5nXG4gICAgICB9XG5cbiAgICAgIC8vIHByZXBhcmUgc3RhY2sgdHJhY2VcbiAgICAgIHJldHVybiAoc3RhY2tTdHJpbmcgPSBjcmVhdGVTdGFja1N0cmluZy5jYWxsKHRoaXMsIHN0YWNrKSlcbiAgICB9LFxuICAgIHNldDogZnVuY3Rpb24gc2V0dGVyICh2YWwpIHtcbiAgICAgIHN0YWNrU3RyaW5nID0gdmFsXG4gICAgfVxuICB9KVxuXG4gIHJldHVybiBlcnJvclxufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\n");

/***/ })

};
;