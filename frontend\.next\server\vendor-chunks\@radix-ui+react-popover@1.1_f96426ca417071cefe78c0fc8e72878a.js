"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popover@1.1_f96426ca417071cefe78c0fc8e72878a";
exports.ids = ["vendor-chunks/@radix-ui+react-popover@1.1_f96426ca417071cefe78c0fc8e72878a"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1_f96426ca417071cefe78c0fc8e72878a/node_modules/@radix-ui/react-popover/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popover@1.1_f96426ca417071cefe78c0fc8e72878a/node_modules/@radix-ui/react-popover/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Popover: () => (/* binding */ Popover),\n/* harmony export */   PopoverAnchor: () => (/* binding */ PopoverAnchor),\n/* harmony export */   PopoverArrow: () => (/* binding */ PopoverArrow),\n/* harmony export */   PopoverClose: () => (/* binding */ PopoverClose),\n/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),\n/* harmony export */   PopoverPortal: () => (/* binding */ PopoverPortal),\n/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createPopoverScope: () => (/* binding */ createPopoverScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_f0d8d248d4450f605cbe95a52bc62448/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_42d3dbabb2c0e10ad90dea04084e0f18/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_a7c3b12f00f397e0a9115766f9969ef3/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_61d195191520d637c5afa16ba55c7c57/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.20_react@18.2.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._f86bd25910bc9d814f2a908b3bdd82bd/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._1b28f38417320d44c2781ca5ef3763c8/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._b86981b2f1d44efeb0dfddb8c6d3023e/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_62c082c8d2b4a336e6eb97cc77a43f16/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@18.3.20_react@18.2.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_4e2937427a332b08c3804ede9290d1ee/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.6.3_@types+react@18.3.20_react@18.2.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,Close,Content,Popover,PopoverAnchor,PopoverArrow,PopoverClose,PopoverContent,PopoverPortal,PopoverTrigger,Portal,Root,Trigger,createPopoverScope auto */ // packages/react/popover/src/popover.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPOVER_NAME, [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props)=>{\n    const { __scopePopover, children, open: openProp, defaultOpen, onOpenChange, modal = false } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [hasCustomAnchor, setHasCustomAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [open = false, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen,\n        onChange: onOpenChange\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverProvider, {\n            scope: __scopePopover,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n            triggerRef,\n            open,\n            onOpenChange: setOpen,\n            onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n                setOpen\n            ]),\n            hasCustomAnchor,\n            onCustomAnchorAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(true), []),\n            onCustomAnchorRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(false), []),\n            modal,\n            children\n        })\n    });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onCustomAnchorAdd();\n        return ()=>onCustomAnchorRemove();\n    }, [\n        onCustomAnchorAdd,\n        onCustomAnchorRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: trigger\n    });\n});\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar PopoverPortal = (props)=>{\n    const { __scopePopover, forceMount, children, container } = props;\n    const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopePopover,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nPopoverContent.displayName = CONTENT_NAME;\nvar PopoverContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, contentRef);\n    const isRightClickOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_13__.Slot,\n        allowPinchZoom: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n            ...props,\n            ref: composedRefs,\n            trapFocus: context.open,\n            disableOutsidePointerEvents: true,\n            onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n                event.preventDefault();\n                if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const originalEvent = event.detail.originalEvent;\n                const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n                const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n                isRightClickOutsideRef.current = isRightClick;\n            }, {\n                checkForDefaultPrevented: false\n            }),\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nvar PopoverContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar PopoverContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__.FocusScope, {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: ()=>context.onOpenChange(false),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...contentProps.style,\n                    // re-namespace exposed content custom properties\n                    ...{\n                        \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                        \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                        \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                        \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                        \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                    }\n                }\n            })\n        })\n    });\n});\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHJhZGl4LXVpK3JlYWN0LXBvcG92ZXJAMS4xX2Y5NjQyNmNhNDE3MDcxY2VmZTc4YzBmYzhlNzI4NzhhL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtcG9wb3Zlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBdUI7QUFDYztBQUNMO0FBQ0c7QUFDRjtBQUNGO0FBQ0o7QUFDTDtBQUNXO0FBQ0M7QUFDUTtBQUNqQjtBQUNDO0FBQ0w7QUFDZ0I7QUFDVjtBQUNFO0FBMkR2QjtBQW5ETixJQUFNbUIsZUFBZTtBQUdyQixJQUFNLENBQUNDLHNCQUFzQkMsbUJBQWtCLEdBQUlsQiwyRUFBa0JBLENBQUNnQixjQUFjO0lBQ2xGVixxRUFBaUJBO0NBQ2xCO0FBQ0QsSUFBTWEsaUJBQWlCYix5RUFBaUJBO0FBY3hDLElBQU0sQ0FBQ2MsaUJBQWlCQyxrQkFBaUIsR0FDdkNKLHFCQUEwQ0Q7QUFVNUMsSUFBTU0sVUFBa0MsQ0FBQ0M7SUFDdkMsTUFBTSxFQUNKQyxjQUFBLEVBQ0FDLFFBQUEsRUFDQUMsTUFBTUMsUUFBQSxFQUNOQyxXQUFBLEVBQ0FDLFlBQUEsRUFDQUMsUUFBUSxPQUNWLEdBQUlQO0lBQ0osTUFBTVEsY0FBY1osZUFBZUs7SUFDbkMsTUFBTVEsYUFBbUJuQyx5Q0FBQSxDQUEwQjtJQUNuRCxNQUFNLENBQUNxQyxpQkFBaUJDLG1CQUFrQixHQUFVdEMsMkNBQUEsQ0FBUztJQUM3RCxNQUFNLENBQUM2QixPQUFPLE9BQU9XLFFBQU8sR0FBSXpCLDRGQUFvQkEsQ0FBQztRQUNuRDBCLE1BQU1YO1FBQ05ZLGFBQWFYO1FBQ2JZLFVBQVVYO0lBQ1o7SUFFQSxPQUNFLGdCQUFBZCxzREFBQUEsQ0FBaUJWLHdEQUFBLEVBQWhCO1FBQXNCLEdBQUcwQixXQUFBO1FBQ3hCTixVQUFBLGdCQUFBVixzREFBQUEsQ0FBQ0ssaUJBQUE7WUFDQ3NCLE9BQU9sQjtZQUNQbUIsV0FBV3ZDLHlEQUFLQTtZQUNoQjRCO1lBQ0FOO1lBQ0FHLGNBQWNRO1lBQ2RPLGNBQW9CL0MsOENBQUEsQ0FBWSxJQUFNd0MsUUFBUSxDQUFDUyxXQUFhLENBQUNBLFdBQVc7Z0JBQUNUO2FBQVE7WUFDakZIO1lBQ0FhLG1CQUF5QmxELDhDQUFBLENBQVksSUFBTXNDLG1CQUFtQixPQUFPLEVBQUU7WUFDdkVhLHNCQUE0Qm5ELDhDQUFBLENBQVksSUFBTXNDLG1CQUFtQixRQUFRLEVBQUU7WUFDM0VMO1lBRUNMO1FBQUE7SUFDSDtBQUdOO0FBRUFILFFBQVEyQixXQUFBLEdBQWNqQztBQU10QixJQUFNa0MsY0FBYztBQU1wQixJQUFNQyw4QkFBc0J0RCw2Q0FBQSxDQUMxQixDQUFDMEIsT0FBd0M4QjtJQUN2QyxNQUFNLEVBQUU3QixjQUFBLEVBQWdCLEdBQUc4QixhQUFZLEdBQUkvQjtJQUMzQyxNQUFNZ0MsVUFBVWxDLGtCQUFrQjZCLGFBQWExQjtJQUMvQyxNQUFNTyxjQUFjWixlQUFlSztJQUNuQyxNQUFNLEVBQUV1QixpQkFBQSxFQUFtQkMsb0JBQUEsRUFBcUIsR0FBSU87SUFFOUMxRCw0Q0FBQSxDQUFVO1FBQ2RrRDtRQUNBLE9BQU8sSUFBTUM7SUFDZixHQUFHO1FBQUNEO1FBQW1CQztLQUFxQjtJQUU1QyxPQUFPLGdCQUFBakMsc0RBQUFBLENBQWlCViwwREFBQSxFQUFoQjtRQUF3QixHQUFHMEIsV0FBQTtRQUFjLEdBQUd1QixXQUFBO1FBQWFJLEtBQUtMO0lBQUE7QUFDeEU7QUFHRkYsY0FBY0YsV0FBQSxHQUFjQztBQU01QixJQUFNUyxlQUFlO0FBTXJCLElBQU1DLCtCQUF1Qi9ELDZDQUFBLENBQzNCLENBQUMwQixPQUF5QzhCO0lBQ3hDLE1BQU0sRUFBRTdCLGNBQUEsRUFBZ0IsR0FBR3FDLGNBQWEsR0FBSXRDO0lBQzVDLE1BQU1nQyxVQUFVbEMsa0JBQWtCc0MsY0FBY25DO0lBQ2hELE1BQU1PLGNBQWNaLGVBQWVLO0lBQ25DLE1BQU1zQyxxQkFBcUIvRCw2RUFBZUEsQ0FBQ3NELGNBQWNFLFFBQVF2QixVQUFVO0lBRTNFLE1BQU0rQixVQUNKLGdCQUFBaEQsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDc0QsTUFBQSxFQUFWO1FBQ0NDLE1BQUs7UUFDTCxpQkFBYztRQUNkLGlCQUFlVixRQUFRN0IsSUFBQTtRQUN2QixpQkFBZTZCLFFBQVFaLFNBQUE7UUFDdkIsY0FBWXVCLFNBQVNYLFFBQVE3QixJQUFJO1FBQ2hDLEdBQUdtQyxZQUFBO1FBQ0pILEtBQUtJO1FBQ0xLLFNBQVNyRSx5RUFBb0JBLENBQUN5QixNQUFNNEMsT0FBQSxFQUFTWixRQUFRWCxZQUFZO0lBQUE7SUFJckUsT0FBT1csUUFBUXJCLGVBQUEsR0FDYjZCLFVBRUEsZ0JBQUFoRCxzREFBQUEsQ0FBaUJWLDBEQUFBLEVBQWhCO1FBQXVCK0QsU0FBTztRQUFFLEdBQUdyQyxXQUFBO1FBQ2pDTixVQUFBc0M7SUFBQTtBQUdQO0FBR0ZILGVBQWVYLFdBQUEsR0FBY1U7QUFNN0IsSUFBTVUsY0FBYztBQUdwQixJQUFNLENBQUNDLGdCQUFnQkMsaUJBQWdCLEdBQUl0RCxxQkFBeUNvRCxhQUFhO0lBQy9GRyxZQUFZO0FBQ2Q7QUFnQkEsSUFBTUMsZ0JBQThDLENBQUNsRDtJQUNuRCxNQUFNLEVBQUVDLGNBQUEsRUFBZ0JnRCxVQUFBLEVBQVkvQyxRQUFBLEVBQVVpRCxTQUFBLEVBQVUsR0FBSW5EO0lBQzVELE1BQU1nQyxVQUFVbEMsa0JBQWtCZ0QsYUFBYTdDO0lBQy9DLE9BQ0UsZ0JBQUFULHNEQUFBQSxDQUFDdUQsZ0JBQUE7UUFBZTVCLE9BQU9sQjtRQUFnQmdEO1FBQ3JDL0MsVUFBQSxnQkFBQVYsc0RBQUFBLENBQUNOLDhEQUFRQSxFQUFSO1lBQVNrRSxTQUFTSCxjQUFjakIsUUFBUTdCLElBQUE7WUFDdkNELFVBQUEsZ0JBQUFWLHNEQUFBQSxDQUFDUCwyREFBZUEsRUFBZjtnQkFBZ0I0RCxTQUFPO2dCQUFDTTtnQkFDdEJqRDtZQUFBO1FBQ0g7SUFDRjtBQUdOO0FBRUFnRCxjQUFjeEIsV0FBQSxHQUFjb0I7QUFNNUIsSUFBTU8sZUFBZTtBQVVyQixJQUFNQywrQkFBdUJoRiw2Q0FBQSxDQUMzQixDQUFDMEIsT0FBeUM4QjtJQUN4QyxNQUFNeUIsZ0JBQWdCUCxpQkFBaUJLLGNBQWNyRCxNQUFNQyxjQUFjO0lBQ3pFLE1BQU0sRUFBRWdELGFBQWFNLGNBQWNOLFVBQUEsRUFBWSxHQUFHTyxjQUFhLEdBQUl4RDtJQUNuRSxNQUFNZ0MsVUFBVWxDLGtCQUFrQnVELGNBQWNyRCxNQUFNQyxjQUFjO0lBQ3BFLE9BQ0UsZ0JBQUFULHNEQUFBQSxDQUFDTiw4REFBUUEsRUFBUjtRQUFTa0UsU0FBU0gsY0FBY2pCLFFBQVE3QixJQUFBO1FBQ3RDRCxVQUFBOEIsUUFBUXpCLEtBQUEsR0FDUCxnQkFBQWYsc0RBQUFBLENBQUNpRSxxQkFBQTtZQUFxQixHQUFHRCxZQUFBO1lBQWNyQixLQUFLTDtRQUFBLEtBRTVDLGdCQUFBdEMsc0RBQUFBLENBQUNrRSx3QkFBQTtZQUF3QixHQUFHRixZQUFBO1lBQWNyQixLQUFLTDtRQUFBO0lBQWM7QUFJckU7QUFHRndCLGVBQWU1QixXQUFBLEdBQWMyQjtBQVE3QixJQUFNSSxvQ0FBNEJuRiw2Q0FBQSxDQUNoQyxDQUFDMEIsT0FBNkM4QjtJQUM1QyxNQUFNRSxVQUFVbEMsa0JBQWtCdUQsY0FBY3JELE1BQU1DLGNBQWM7SUFDcEUsTUFBTTBELGFBQW1CckYseUNBQUEsQ0FBdUI7SUFDaEQsTUFBTXNGLGVBQWVwRiw2RUFBZUEsQ0FBQ3NELGNBQWM2QjtJQUNuRCxNQUFNRSx5QkFBK0J2Rix5Q0FBQSxDQUFPO0lBR3RDQSw0Q0FBQSxDQUFVO1FBQ2QsTUFBTXdGLFVBQVVILFdBQVdJLE9BQUE7UUFDM0IsSUFBSUQsU0FBUyxPQUFPeEUsd0RBQVVBLENBQUN3RTtJQUNqQyxHQUFHLEVBQUU7SUFFTCxPQUNFLGdCQUFBdEUsc0RBQUFBLENBQUNELDREQUFZQSxFQUFaO1FBQWF5RSxJQUFJNUUsdURBQUlBO1FBQUU2RSxnQkFBYztRQUNwQy9ELFVBQUEsZ0JBQUFWLHNEQUFBQSxDQUFDMEUsb0JBQUE7WUFDRSxHQUFHbEUsS0FBQTtZQUNKbUMsS0FBS3lCO1lBR0xPLFdBQVduQyxRQUFRN0IsSUFBQTtZQUNuQmlFLDZCQUEyQjtZQUMzQkMsa0JBQWtCOUYseUVBQW9CQSxDQUFDeUIsTUFBTXFFLGdCQUFBLEVBQWtCLENBQUNDO2dCQUM5REEsTUFBTUMsY0FBQTtnQkFDTixJQUFJLENBQUNWLHVCQUF1QkUsT0FBQSxFQUFTL0IsUUFBUXZCLFVBQUEsQ0FBV3NELE9BQUEsRUFBU1M7WUFDbkU7WUFDQUMsc0JBQXNCbEcseUVBQW9CQSxDQUN4Q3lCLE1BQU15RSxvQkFBQSxFQUNOLENBQUNIO2dCQUNDLE1BQU1JLGdCQUFnQkosTUFBTUssTUFBQSxDQUFPRCxhQUFBO2dCQUNuQyxNQUFNRSxnQkFBZ0JGLGNBQWNqQyxNQUFBLEtBQVcsS0FBS2lDLGNBQWNHLE9BQUEsS0FBWTtnQkFDOUUsTUFBTUMsZUFBZUosY0FBY2pDLE1BQUEsS0FBVyxLQUFLbUM7Z0JBRW5EZix1QkFBdUJFLE9BQUEsR0FBVWU7WUFDbkMsR0FDQTtnQkFBRUMsMEJBQTBCO1lBQU07WUFJcENDLGdCQUFnQnpHLHlFQUFvQkEsQ0FDbEN5QixNQUFNZ0YsY0FBQSxFQUNOLENBQUNWLFFBQVVBLE1BQU1DLGNBQUEsSUFDakI7Z0JBQUVRLDBCQUEwQjtZQUFNO1FBQ3BDO0lBQ0Y7QUFHTjtBQUdGLElBQU1yQix1Q0FBK0JwRiw2Q0FBQSxDQUNuQyxDQUFDMEIsT0FBNkM4QjtJQUM1QyxNQUFNRSxVQUFVbEMsa0JBQWtCdUQsY0FBY3JELE1BQU1DLGNBQWM7SUFDcEUsTUFBTWdGLDBCQUFnQzNHLHlDQUFBLENBQU87SUFDN0MsTUFBTTRHLDJCQUFpQzVHLHlDQUFBLENBQU87SUFFOUMsT0FDRSxnQkFBQWtCLHNEQUFBQSxDQUFDMEUsb0JBQUE7UUFDRSxHQUFHbEUsS0FBQTtRQUNKbUMsS0FBS0w7UUFDTHFDLFdBQVc7UUFDWEMsNkJBQTZCO1FBQzdCQyxrQkFBa0IsQ0FBQ0M7WUFDakJ0RSxNQUFNcUUsZ0JBQUEsR0FBbUJDO1lBRXpCLElBQUksQ0FBQ0EsTUFBTWEsZ0JBQUEsRUFBa0I7Z0JBQzNCLElBQUksQ0FBQ0Ysd0JBQXdCbEIsT0FBQSxFQUFTL0IsUUFBUXZCLFVBQUEsQ0FBV3NELE9BQUEsRUFBU1M7Z0JBRWxFRixNQUFNQyxjQUFBO1lBQ1I7WUFFQVUsd0JBQXdCbEIsT0FBQSxHQUFVO1lBQ2xDbUIseUJBQXlCbkIsT0FBQSxHQUFVO1FBQ3JDO1FBQ0FxQixtQkFBbUIsQ0FBQ2Q7WUFDbEJ0RSxNQUFNb0YsaUJBQUEsR0FBb0JkO1lBRTFCLElBQUksQ0FBQ0EsTUFBTWEsZ0JBQUEsRUFBa0I7Z0JBQzNCRix3QkFBd0JsQixPQUFBLEdBQVU7Z0JBQ2xDLElBQUlPLE1BQU1LLE1BQUEsQ0FBT0QsYUFBQSxDQUFjaEMsSUFBQSxLQUFTLGVBQWU7b0JBQ3JEd0MseUJBQXlCbkIsT0FBQSxHQUFVO2dCQUNyQztZQUNGO1lBS0EsTUFBTXNCLFNBQVNmLE1BQU1lLE1BQUE7WUFDckIsTUFBTUMsa0JBQWtCdEQsUUFBUXZCLFVBQUEsQ0FBV3NELE9BQUEsRUFBU3dCLFNBQVNGO1lBQzdELElBQUlDLGlCQUFpQmhCLE1BQU1DLGNBQUE7WUFNM0IsSUFBSUQsTUFBTUssTUFBQSxDQUFPRCxhQUFBLENBQWNoQyxJQUFBLEtBQVMsYUFBYXdDLHlCQUF5Qm5CLE9BQUEsRUFBUztnQkFDckZPLE1BQU1DLGNBQUE7WUFDUjtRQUNGO0lBQUE7QUFHTjtBQStCRixJQUFNTCxtQ0FBMkI1Riw2Q0FBQSxDQUMvQixDQUFDMEIsT0FBNkM4QjtJQUM1QyxNQUFNLEVBQ0o3QixjQUFBLEVBQ0FrRSxTQUFBLEVBQ0FxQixlQUFBLEVBQ0FuQixnQkFBQSxFQUNBRCwyQkFBQSxFQUNBcUIsZUFBQSxFQUNBaEIsb0JBQUEsRUFDQU8sY0FBQSxFQUNBSSxpQkFBQSxFQUNBLEdBQUc1QixjQUNMLEdBQUl4RDtJQUNKLE1BQU1nQyxVQUFVbEMsa0JBQWtCdUQsY0FBY3BEO0lBQ2hELE1BQU1PLGNBQWNaLGVBQWVLO0lBSW5DdEIsNkVBQWNBO0lBRWQsT0FDRSxnQkFBQWEsc0RBQUFBLENBQUNaLG9FQUFVQSxFQUFWO1FBQ0NpRSxTQUFPO1FBQ1A2QyxNQUFJO1FBQ0pDLFNBQVN4QjtRQUNUeUIsa0JBQWtCSjtRQUNsQkssb0JBQW9CeEI7UUFFcEJuRSxVQUFBLGdCQUFBVixzREFBQUEsQ0FBQ2QsZ0ZBQWdCQSxFQUFoQjtZQUNDbUUsU0FBTztZQUNQdUI7WUFDQWdCO1lBQ0FLO1lBQ0FoQjtZQUNBTztZQUNBYyxXQUFXLElBQU05RCxRQUFRMUIsWUFBQSxDQUFhO1lBRXRDSixVQUFBLGdCQUFBVixzREFBQUEsQ0FBaUJWLDJEQUFBLEVBQWhCO2dCQUNDLGNBQVk2RCxTQUFTWCxRQUFRN0IsSUFBSTtnQkFDakM2RixNQUFLO2dCQUNMQyxJQUFJakUsUUFBUVosU0FBQTtnQkFDWCxHQUFHWixXQUFBO2dCQUNILEdBQUdnRCxZQUFBO2dCQUNKckIsS0FBS0w7Z0JBQ0xvRSxPQUFPO29CQUNMLEdBQUcxQyxhQUFhMEMsS0FBQTtvQkFBQTtvQkFFaEIsR0FBRzt3QkFDRCw0Q0FBNEM7d0JBQzVDLDJDQUEyQzt3QkFDM0MsNENBQTRDO3dCQUM1QyxpQ0FBaUM7d0JBQ2pDLGtDQUFrQztvQkFDcEM7Z0JBQ0Y7WUFBQTtRQUNGO0lBQ0Y7QUFHTjtBQU9GLElBQU1DLGFBQWE7QUFLbkIsSUFBTUMsNkJBQXFCOUgsNkNBQUEsQ0FDekIsQ0FBQzBCLE9BQXVDOEI7SUFDdEMsTUFBTSxFQUFFN0IsY0FBQSxFQUFnQixHQUFHb0csWUFBVyxHQUFJckc7SUFDMUMsTUFBTWdDLFVBQVVsQyxrQkFBa0JxRyxZQUFZbEc7SUFDOUMsT0FDRSxnQkFBQVQsc0RBQUFBLENBQUNMLGdFQUFTQSxDQUFDc0QsTUFBQSxFQUFWO1FBQ0NDLE1BQUs7UUFDSixHQUFHMkQsVUFBQTtRQUNKbEUsS0FBS0w7UUFDTGMsU0FBU3JFLHlFQUFvQkEsQ0FBQ3lCLE1BQU00QyxPQUFBLEVBQVMsSUFBTVosUUFBUTFCLFlBQUEsQ0FBYTtJQUFNO0FBR3BGO0FBR0Y4RixhQUFhMUUsV0FBQSxHQUFjeUU7QUFNM0IsSUFBTUcsYUFBYTtBQU1uQixJQUFNQyw2QkFBcUJqSSw2Q0FBQSxDQUN6QixDQUFDMEIsT0FBdUM4QjtJQUN0QyxNQUFNLEVBQUU3QixjQUFBLEVBQWdCLEdBQUd1RyxZQUFXLEdBQUl4RztJQUMxQyxNQUFNUSxjQUFjWixlQUFlSztJQUNuQyxPQUFPLGdCQUFBVCxzREFBQUEsQ0FBaUJWLHlEQUFBLEVBQWhCO1FBQXVCLEdBQUcwQixXQUFBO1FBQWMsR0FBR2dHLFVBQUE7UUFBWXJFLEtBQUtMO0lBQUE7QUFDdEU7QUFHRnlFLGFBQWE3RSxXQUFBLEdBQWM0RTtBQUkzQixTQUFTM0QsU0FBU3hDLElBQUE7SUFDaEIsT0FBT0EsT0FBTyxTQUFTO0FBQ3pCO0FBRUEsSUFBTWUsUUFBT25CO0FBQ2IsSUFBTW1DLFVBQVNOO0FBQ2YsSUFBTThFLFVBQVVyRTtBQUNoQixJQUFNckQsU0FBU2tFO0FBQ2YsSUFBTTZDLFdBQVV6QztBQUNoQixJQUFNcUQsUUFBUVA7QUFDZCxJQUFNSyxTQUFRRiIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uLi9zcmMvcG9wb3Zlci50c3g/MmI0NSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBjb21wb3NlRXZlbnRIYW5kbGVycyB9IGZyb20gJ0ByYWRpeC11aS9wcmltaXRpdmUnO1xuaW1wb3J0IHsgdXNlQ29tcG9zZWRSZWZzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcyc7XG5pbXBvcnQgeyBjcmVhdGVDb250ZXh0U2NvcGUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtY29udGV4dCc7XG5pbXBvcnQgeyBEaXNtaXNzYWJsZUxheWVyIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWRpc21pc3NhYmxlLWxheWVyJztcbmltcG9ydCB7IHVzZUZvY3VzR3VhcmRzIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWZvY3VzLWd1YXJkcyc7XG5pbXBvcnQgeyBGb2N1c1Njb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWZvY3VzLXNjb3BlJztcbmltcG9ydCB7IHVzZUlkIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LWlkJztcbmltcG9ydCAqIGFzIFBvcHBlclByaW1pdGl2ZSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcG9wcGVyJztcbmltcG9ydCB7IGNyZWF0ZVBvcHBlclNjb3BlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXBvcHBlcic7XG5pbXBvcnQgeyBQb3J0YWwgYXMgUG9ydGFsUHJpbWl0aXZlIH0gZnJvbSAnQHJhZGl4LXVpL3JlYWN0LXBvcnRhbCc7XG5pbXBvcnQgeyBQcmVzZW5jZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1wcmVzZW5jZSc7XG5pbXBvcnQgeyBQcmltaXRpdmUgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3QtcHJpbWl0aXZlJztcbmltcG9ydCB7IFNsb3QgfSBmcm9tICdAcmFkaXgtdWkvcmVhY3Qtc2xvdCc7XG5pbXBvcnQgeyB1c2VDb250cm9sbGFibGVTdGF0ZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC11c2UtY29udHJvbGxhYmxlLXN0YXRlJztcbmltcG9ydCB7IGhpZGVPdGhlcnMgfSBmcm9tICdhcmlhLWhpZGRlbic7XG5pbXBvcnQgeyBSZW1vdmVTY3JvbGwgfSBmcm9tICdyZWFjdC1yZW1vdmUtc2Nyb2xsJztcblxuaW1wb3J0IHR5cGUgeyBTY29wZSB9IGZyb20gJ0ByYWRpeC11aS9yZWFjdC1jb250ZXh0JztcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUG9wb3ZlclxuICogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG5jb25zdCBQT1BPVkVSX05BTUUgPSAnUG9wb3Zlcic7XG5cbnR5cGUgU2NvcGVkUHJvcHM8UD4gPSBQICYgeyBfX3Njb3BlUG9wb3Zlcj86IFNjb3BlIH07XG5jb25zdCBbY3JlYXRlUG9wb3ZlckNvbnRleHQsIGNyZWF0ZVBvcG92ZXJTY29wZV0gPSBjcmVhdGVDb250ZXh0U2NvcGUoUE9QT1ZFUl9OQU1FLCBbXG4gIGNyZWF0ZVBvcHBlclNjb3BlLFxuXSk7XG5jb25zdCB1c2VQb3BwZXJTY29wZSA9IGNyZWF0ZVBvcHBlclNjb3BlKCk7XG5cbnR5cGUgUG9wb3ZlckNvbnRleHRWYWx1ZSA9IHtcbiAgdHJpZ2dlclJlZjogUmVhY3QuUmVmT2JqZWN0PEhUTUxCdXR0b25FbGVtZW50IHwgbnVsbD47XG4gIGNvbnRlbnRJZDogc3RyaW5nO1xuICBvcGVuOiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2Uob3BlbjogYm9vbGVhbik6IHZvaWQ7XG4gIG9uT3BlblRvZ2dsZSgpOiB2b2lkO1xuICBoYXNDdXN0b21BbmNob3I6IGJvb2xlYW47XG4gIG9uQ3VzdG9tQW5jaG9yQWRkKCk6IHZvaWQ7XG4gIG9uQ3VzdG9tQW5jaG9yUmVtb3ZlKCk6IHZvaWQ7XG4gIG1vZGFsOiBib29sZWFuO1xufTtcblxuY29uc3QgW1BvcG92ZXJQcm92aWRlciwgdXNlUG9wb3ZlckNvbnRleHRdID1cbiAgY3JlYXRlUG9wb3ZlckNvbnRleHQ8UG9wb3ZlckNvbnRleHRWYWx1ZT4oUE9QT1ZFUl9OQU1FKTtcblxuaW50ZXJmYWNlIFBvcG92ZXJQcm9wcyB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlO1xuICBvcGVuPzogYm9vbGVhbjtcbiAgZGVmYXVsdE9wZW4/OiBib29sZWFuO1xuICBvbk9wZW5DaGFuZ2U/OiAob3BlbjogYm9vbGVhbikgPT4gdm9pZDtcbiAgbW9kYWw/OiBib29sZWFuO1xufVxuXG5jb25zdCBQb3BvdmVyOiBSZWFjdC5GQzxQb3BvdmVyUHJvcHM+ID0gKHByb3BzOiBTY29wZWRQcm9wczxQb3BvdmVyUHJvcHM+KSA9PiB7XG4gIGNvbnN0IHtcbiAgICBfX3Njb3BlUG9wb3ZlcixcbiAgICBjaGlsZHJlbixcbiAgICBvcGVuOiBvcGVuUHJvcCxcbiAgICBkZWZhdWx0T3BlbixcbiAgICBvbk9wZW5DaGFuZ2UsXG4gICAgbW9kYWwgPSBmYWxzZSxcbiAgfSA9IHByb3BzO1xuICBjb25zdCBwb3BwZXJTY29wZSA9IHVzZVBvcHBlclNjb3BlKF9fc2NvcGVQb3BvdmVyKTtcbiAgY29uc3QgdHJpZ2dlclJlZiA9IFJlYWN0LnVzZVJlZjxIVE1MQnV0dG9uRWxlbWVudD4obnVsbCk7XG4gIGNvbnN0IFtoYXNDdXN0b21BbmNob3IsIHNldEhhc0N1c3RvbUFuY2hvcl0gPSBSZWFjdC51c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IFtvcGVuID0gZmFsc2UsIHNldE9wZW5dID0gdXNlQ29udHJvbGxhYmxlU3RhdGUoe1xuICAgIHByb3A6IG9wZW5Qcm9wLFxuICAgIGRlZmF1bHRQcm9wOiBkZWZhdWx0T3BlbixcbiAgICBvbkNoYW5nZTogb25PcGVuQ2hhbmdlLFxuICB9KTtcblxuICByZXR1cm4gKFxuICAgIDxQb3BwZXJQcmltaXRpdmUuUm9vdCB7Li4ucG9wcGVyU2NvcGV9PlxuICAgICAgPFBvcG92ZXJQcm92aWRlclxuICAgICAgICBzY29wZT17X19zY29wZVBvcG92ZXJ9XG4gICAgICAgIGNvbnRlbnRJZD17dXNlSWQoKX1cbiAgICAgICAgdHJpZ2dlclJlZj17dHJpZ2dlclJlZn1cbiAgICAgICAgb3Blbj17b3Blbn1cbiAgICAgICAgb25PcGVuQ2hhbmdlPXtzZXRPcGVufVxuICAgICAgICBvbk9wZW5Ub2dnbGU9e1JlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHNldE9wZW4oKHByZXZPcGVuKSA9PiAhcHJldk9wZW4pLCBbc2V0T3Blbl0pfVxuICAgICAgICBoYXNDdXN0b21BbmNob3I9e2hhc0N1c3RvbUFuY2hvcn1cbiAgICAgICAgb25DdXN0b21BbmNob3JBZGQ9e1JlYWN0LnVzZUNhbGxiYWNrKCgpID0+IHNldEhhc0N1c3RvbUFuY2hvcih0cnVlKSwgW10pfVxuICAgICAgICBvbkN1c3RvbUFuY2hvclJlbW92ZT17UmVhY3QudXNlQ2FsbGJhY2soKCkgPT4gc2V0SGFzQ3VzdG9tQW5jaG9yKGZhbHNlKSwgW10pfVxuICAgICAgICBtb2RhbD17bW9kYWx9XG4gICAgICA+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvUG9wb3ZlclByb3ZpZGVyPlxuICAgIDwvUG9wcGVyUHJpbWl0aXZlLlJvb3Q+XG4gICk7XG59O1xuXG5Qb3BvdmVyLmRpc3BsYXlOYW1lID0gUE9QT1ZFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBQb3BvdmVyQW5jaG9yXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEFOQ0hPUl9OQU1FID0gJ1BvcG92ZXJBbmNob3InO1xuXG50eXBlIFBvcG92ZXJBbmNob3JFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUG9wcGVyUHJpbWl0aXZlLkFuY2hvcj47XG50eXBlIFBvcHBlckFuY2hvclByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQb3BwZXJQcmltaXRpdmUuQW5jaG9yPjtcbmludGVyZmFjZSBQb3BvdmVyQW5jaG9yUHJvcHMgZXh0ZW5kcyBQb3BwZXJBbmNob3JQcm9wcyB7fVxuXG5jb25zdCBQb3BvdmVyQW5jaG9yID0gUmVhY3QuZm9yd2FyZFJlZjxQb3BvdmVyQW5jaG9yRWxlbWVudCwgUG9wb3ZlckFuY2hvclByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxQb3BvdmVyQW5jaG9yUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVQb3BvdmVyLCAuLi5hbmNob3JQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVBvcG92ZXJDb250ZXh0KEFOQ0hPUl9OQU1FLCBfX3Njb3BlUG9wb3Zlcik7XG4gICAgY29uc3QgcG9wcGVyU2NvcGUgPSB1c2VQb3BwZXJTY29wZShfX3Njb3BlUG9wb3Zlcik7XG4gICAgY29uc3QgeyBvbkN1c3RvbUFuY2hvckFkZCwgb25DdXN0b21BbmNob3JSZW1vdmUgfSA9IGNvbnRleHQ7XG5cbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgb25DdXN0b21BbmNob3JBZGQoKTtcbiAgICAgIHJldHVybiAoKSA9PiBvbkN1c3RvbUFuY2hvclJlbW92ZSgpO1xuICAgIH0sIFtvbkN1c3RvbUFuY2hvckFkZCwgb25DdXN0b21BbmNob3JSZW1vdmVdKTtcblxuICAgIHJldHVybiA8UG9wcGVyUHJpbWl0aXZlLkFuY2hvciB7Li4ucG9wcGVyU2NvcGV9IHsuLi5hbmNob3JQcm9wc30gcmVmPXtmb3J3YXJkZWRSZWZ9IC8+O1xuICB9XG4pO1xuXG5Qb3BvdmVyQW5jaG9yLmRpc3BsYXlOYW1lID0gQU5DSE9SX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcG92ZXJUcmlnZ2VyXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFRSSUdHRVJfTkFNRSA9ICdQb3BvdmVyVHJpZ2dlcic7XG5cbnR5cGUgUG9wb3ZlclRyaWdnZXJFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUHJpbWl0aXZlLmJ1dHRvbj47XG50eXBlIFByaW1pdGl2ZUJ1dHRvblByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQcmltaXRpdmUuYnV0dG9uPjtcbmludGVyZmFjZSBQb3BvdmVyVHJpZ2dlclByb3BzIGV4dGVuZHMgUHJpbWl0aXZlQnV0dG9uUHJvcHMge31cblxuY29uc3QgUG9wb3ZlclRyaWdnZXIgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcG92ZXJUcmlnZ2VyRWxlbWVudCwgUG9wb3ZlclRyaWdnZXJQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8UG9wb3ZlclRyaWdnZXJQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgX19zY29wZVBvcG92ZXIsIC4uLnRyaWdnZXJQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVBvcG92ZXJDb250ZXh0KFRSSUdHRVJfTkFNRSwgX19zY29wZVBvcG92ZXIpO1xuICAgIGNvbnN0IHBvcHBlclNjb3BlID0gdXNlUG9wcGVyU2NvcGUoX19zY29wZVBvcG92ZXIpO1xuICAgIGNvbnN0IGNvbXBvc2VkVHJpZ2dlclJlZiA9IHVzZUNvbXBvc2VkUmVmcyhmb3J3YXJkZWRSZWYsIGNvbnRleHQudHJpZ2dlclJlZik7XG5cbiAgICBjb25zdCB0cmlnZ2VyID0gKFxuICAgICAgPFByaW1pdGl2ZS5idXR0b25cbiAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgIGFyaWEtaGFzcG9wdXA9XCJkaWFsb2dcIlxuICAgICAgICBhcmlhLWV4cGFuZGVkPXtjb250ZXh0Lm9wZW59XG4gICAgICAgIGFyaWEtY29udHJvbHM9e2NvbnRleHQuY29udGVudElkfVxuICAgICAgICBkYXRhLXN0YXRlPXtnZXRTdGF0ZShjb250ZXh0Lm9wZW4pfVxuICAgICAgICB7Li4udHJpZ2dlclByb3BzfVxuICAgICAgICByZWY9e2NvbXBvc2VkVHJpZ2dlclJlZn1cbiAgICAgICAgb25DbGljaz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25DbGljaywgY29udGV4dC5vbk9wZW5Ub2dnbGUpfVxuICAgICAgLz5cbiAgICApO1xuXG4gICAgcmV0dXJuIGNvbnRleHQuaGFzQ3VzdG9tQW5jaG9yID8gKFxuICAgICAgdHJpZ2dlclxuICAgICkgOiAoXG4gICAgICA8UG9wcGVyUHJpbWl0aXZlLkFuY2hvciBhc0NoaWxkIHsuLi5wb3BwZXJTY29wZX0+XG4gICAgICAgIHt0cmlnZ2VyfVxuICAgICAgPC9Qb3BwZXJQcmltaXRpdmUuQW5jaG9yPlxuICAgICk7XG4gIH1cbik7XG5cblBvcG92ZXJUcmlnZ2VyLmRpc3BsYXlOYW1lID0gVFJJR0dFUl9OQU1FO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBQb3BvdmVyUG9ydGFsXG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IFBPUlRBTF9OQU1FID0gJ1BvcG92ZXJQb3J0YWwnO1xuXG50eXBlIFBvcnRhbENvbnRleHRWYWx1ZSA9IHsgZm9yY2VNb3VudD86IHRydWUgfTtcbmNvbnN0IFtQb3J0YWxQcm92aWRlciwgdXNlUG9ydGFsQ29udGV4dF0gPSBjcmVhdGVQb3BvdmVyQ29udGV4dDxQb3J0YWxDb250ZXh0VmFsdWU+KFBPUlRBTF9OQU1FLCB7XG4gIGZvcmNlTW91bnQ6IHVuZGVmaW5lZCxcbn0pO1xuXG50eXBlIFBvcnRhbFByb3BzID0gUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBQb3J0YWxQcmltaXRpdmU+O1xuaW50ZXJmYWNlIFBvcG92ZXJQb3J0YWxQcm9wcyB7XG4gIGNoaWxkcmVuPzogUmVhY3QuUmVhY3ROb2RlO1xuICAvKipcbiAgICogU3BlY2lmeSBhIGNvbnRhaW5lciBlbGVtZW50IHRvIHBvcnRhbCB0aGUgY29udGVudCBpbnRvLlxuICAgKi9cbiAgY29udGFpbmVyPzogUG9ydGFsUHJvcHNbJ2NvbnRhaW5lciddO1xuICAvKipcbiAgICogVXNlZCB0byBmb3JjZSBtb3VudGluZyB3aGVuIG1vcmUgY29udHJvbCBpcyBuZWVkZWQuIFVzZWZ1bCB3aGVuXG4gICAqIGNvbnRyb2xsaW5nIGFuaW1hdGlvbiB3aXRoIFJlYWN0IGFuaW1hdGlvbiBsaWJyYXJpZXMuXG4gICAqL1xuICBmb3JjZU1vdW50PzogdHJ1ZTtcbn1cblxuY29uc3QgUG9wb3ZlclBvcnRhbDogUmVhY3QuRkM8UG9wb3ZlclBvcnRhbFByb3BzPiA9IChwcm9wczogU2NvcGVkUHJvcHM8UG9wb3ZlclBvcnRhbFByb3BzPikgPT4ge1xuICBjb25zdCB7IF9fc2NvcGVQb3BvdmVyLCBmb3JjZU1vdW50LCBjaGlsZHJlbiwgY29udGFpbmVyIH0gPSBwcm9wcztcbiAgY29uc3QgY29udGV4dCA9IHVzZVBvcG92ZXJDb250ZXh0KFBPUlRBTF9OQU1FLCBfX3Njb3BlUG9wb3Zlcik7XG4gIHJldHVybiAoXG4gICAgPFBvcnRhbFByb3ZpZGVyIHNjb3BlPXtfX3Njb3BlUG9wb3Zlcn0gZm9yY2VNb3VudD17Zm9yY2VNb3VudH0+XG4gICAgICA8UHJlc2VuY2UgcHJlc2VudD17Zm9yY2VNb3VudCB8fCBjb250ZXh0Lm9wZW59PlxuICAgICAgICA8UG9ydGFsUHJpbWl0aXZlIGFzQ2hpbGQgY29udGFpbmVyPXtjb250YWluZXJ9PlxuICAgICAgICAgIHtjaGlsZHJlbn1cbiAgICAgICAgPC9Qb3J0YWxQcmltaXRpdmU+XG4gICAgICA8L1ByZXNlbmNlPlxuICAgIDwvUG9ydGFsUHJvdmlkZXI+XG4gICk7XG59O1xuXG5Qb3BvdmVyUG9ydGFsLmRpc3BsYXlOYW1lID0gUE9SVEFMX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS1cbiAqIFBvcG92ZXJDb250ZW50XG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IENPTlRFTlRfTkFNRSA9ICdQb3BvdmVyQ29udGVudCc7XG5cbmludGVyZmFjZSBQb3BvdmVyQ29udGVudFByb3BzIGV4dGVuZHMgUG9wb3ZlckNvbnRlbnRUeXBlUHJvcHMge1xuICAvKipcbiAgICogVXNlZCB0byBmb3JjZSBtb3VudGluZyB3aGVuIG1vcmUgY29udHJvbCBpcyBuZWVkZWQuIFVzZWZ1bCB3aGVuXG4gICAqIGNvbnRyb2xsaW5nIGFuaW1hdGlvbiB3aXRoIFJlYWN0IGFuaW1hdGlvbiBsaWJyYXJpZXMuXG4gICAqL1xuICBmb3JjZU1vdW50PzogdHJ1ZTtcbn1cblxuY29uc3QgUG9wb3ZlckNvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcG92ZXJDb250ZW50VHlwZUVsZW1lbnQsIFBvcG92ZXJDb250ZW50UHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFBvcG92ZXJDb250ZW50UHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCBwb3J0YWxDb250ZXh0ID0gdXNlUG9ydGFsQ29udGV4dChDT05URU5UX05BTUUsIHByb3BzLl9fc2NvcGVQb3BvdmVyKTtcbiAgICBjb25zdCB7IGZvcmNlTW91bnQgPSBwb3J0YWxDb250ZXh0LmZvcmNlTW91bnQsIC4uLmNvbnRlbnRQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVBvcG92ZXJDb250ZXh0KENPTlRFTlRfTkFNRSwgcHJvcHMuX19zY29wZVBvcG92ZXIpO1xuICAgIHJldHVybiAoXG4gICAgICA8UHJlc2VuY2UgcHJlc2VudD17Zm9yY2VNb3VudCB8fCBjb250ZXh0Lm9wZW59PlxuICAgICAgICB7Y29udGV4dC5tb2RhbCA/IChcbiAgICAgICAgICA8UG9wb3ZlckNvbnRlbnRNb2RhbCB7Li4uY29udGVudFByb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gLz5cbiAgICAgICAgKSA6IChcbiAgICAgICAgICA8UG9wb3ZlckNvbnRlbnROb25Nb2RhbCB7Li4uY29udGVudFByb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gLz5cbiAgICAgICAgKX1cbiAgICAgIDwvUHJlc2VuY2U+XG4gICAgKTtcbiAgfVxuKTtcblxuUG9wb3ZlckNvbnRlbnQuZGlzcGxheU5hbWUgPSBDT05URU5UX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxudHlwZSBQb3BvdmVyQ29udGVudFR5cGVFbGVtZW50ID0gUG9wb3ZlckNvbnRlbnRJbXBsRWxlbWVudDtcbmludGVyZmFjZSBQb3BvdmVyQ29udGVudFR5cGVQcm9wc1xuICBleHRlbmRzIE9taXQ8UG9wb3ZlckNvbnRlbnRJbXBsUHJvcHMsICd0cmFwRm9jdXMnIHwgJ2Rpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cyc+IHt9XG5cbmNvbnN0IFBvcG92ZXJDb250ZW50TW9kYWwgPSBSZWFjdC5mb3J3YXJkUmVmPFBvcG92ZXJDb250ZW50VHlwZUVsZW1lbnQsIFBvcG92ZXJDb250ZW50VHlwZVByb3BzPihcbiAgKHByb3BzOiBTY29wZWRQcm9wczxQb3BvdmVyQ29udGVudFR5cGVQcm9wcz4sIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VQb3BvdmVyQ29udGV4dChDT05URU5UX05BTUUsIHByb3BzLl9fc2NvcGVQb3BvdmVyKTtcbiAgICBjb25zdCBjb250ZW50UmVmID0gUmVhY3QudXNlUmVmPEhUTUxEaXZFbGVtZW50PihudWxsKTtcbiAgICBjb25zdCBjb21wb3NlZFJlZnMgPSB1c2VDb21wb3NlZFJlZnMoZm9yd2FyZGVkUmVmLCBjb250ZW50UmVmKTtcbiAgICBjb25zdCBpc1JpZ2h0Q2xpY2tPdXRzaWRlUmVmID0gUmVhY3QudXNlUmVmKGZhbHNlKTtcblxuICAgIC8vIGFyaWEtaGlkZSBldmVyeXRoaW5nIGV4Y2VwdCB0aGUgY29udGVudCAoYmV0dGVyIHN1cHBvcnRlZCBlcXVpdmFsZW50IHRvIHNldHRpbmcgYXJpYS1tb2RhbClcbiAgICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgICAgY29uc3QgY29udGVudCA9IGNvbnRlbnRSZWYuY3VycmVudDtcbiAgICAgIGlmIChjb250ZW50KSByZXR1cm4gaGlkZU90aGVycyhjb250ZW50KTtcbiAgICB9LCBbXSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFJlbW92ZVNjcm9sbCBhcz17U2xvdH0gYWxsb3dQaW5jaFpvb20+XG4gICAgICAgIDxQb3BvdmVyQ29udGVudEltcGxcbiAgICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgICAgcmVmPXtjb21wb3NlZFJlZnN9XG4gICAgICAgICAgLy8gd2UgbWFrZSBzdXJlIHdlJ3JlIG5vdCB0cmFwcGluZyBvbmNlIGl0J3MgYmVlbiBjbG9zZWRcbiAgICAgICAgICAvLyAoY2xvc2VkICE9PSB1bm1vdW50ZWQgd2hlbiBhbmltYXRpbmcgb3V0KVxuICAgICAgICAgIHRyYXBGb2N1cz17Y29udGV4dC5vcGVufVxuICAgICAgICAgIGRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50c1xuICAgICAgICAgIG9uQ2xvc2VBdXRvRm9jdXM9e2NvbXBvc2VFdmVudEhhbmRsZXJzKHByb3BzLm9uQ2xvc2VBdXRvRm9jdXMsIChldmVudCkgPT4ge1xuICAgICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICAgIGlmICghaXNSaWdodENsaWNrT3V0c2lkZVJlZi5jdXJyZW50KSBjb250ZXh0LnRyaWdnZXJSZWYuY3VycmVudD8uZm9jdXMoKTtcbiAgICAgICAgICB9KX1cbiAgICAgICAgICBvblBvaW50ZXJEb3duT3V0c2lkZT17Y29tcG9zZUV2ZW50SGFuZGxlcnMoXG4gICAgICAgICAgICBwcm9wcy5vblBvaW50ZXJEb3duT3V0c2lkZSxcbiAgICAgICAgICAgIChldmVudCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBvcmlnaW5hbEV2ZW50ID0gZXZlbnQuZGV0YWlsLm9yaWdpbmFsRXZlbnQ7XG4gICAgICAgICAgICAgIGNvbnN0IGN0cmxMZWZ0Q2xpY2sgPSBvcmlnaW5hbEV2ZW50LmJ1dHRvbiA9PT0gMCAmJiBvcmlnaW5hbEV2ZW50LmN0cmxLZXkgPT09IHRydWU7XG4gICAgICAgICAgICAgIGNvbnN0IGlzUmlnaHRDbGljayA9IG9yaWdpbmFsRXZlbnQuYnV0dG9uID09PSAyIHx8IGN0cmxMZWZ0Q2xpY2s7XG5cbiAgICAgICAgICAgICAgaXNSaWdodENsaWNrT3V0c2lkZVJlZi5jdXJyZW50ID0gaXNSaWdodENsaWNrO1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkOiBmYWxzZSB9XG4gICAgICAgICAgKX1cbiAgICAgICAgICAvLyBXaGVuIGZvY3VzIGlzIHRyYXBwZWQsIGEgYGZvY3Vzb3V0YCBldmVudCBtYXkgc3RpbGwgaGFwcGVuLlxuICAgICAgICAgIC8vIFdlIG1ha2Ugc3VyZSB3ZSBkb24ndCB0cmlnZ2VyIG91ciBgb25EaXNtaXNzYCBpbiBzdWNoIGNhc2UuXG4gICAgICAgICAgb25Gb2N1c091dHNpZGU9e2NvbXBvc2VFdmVudEhhbmRsZXJzKFxuICAgICAgICAgICAgcHJvcHMub25Gb2N1c091dHNpZGUsXG4gICAgICAgICAgICAoZXZlbnQpID0+IGV2ZW50LnByZXZlbnREZWZhdWx0KCksXG4gICAgICAgICAgICB7IGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZDogZmFsc2UgfVxuICAgICAgICAgICl9XG4gICAgICAgIC8+XG4gICAgICA8L1JlbW92ZVNjcm9sbD5cbiAgICApO1xuICB9XG4pO1xuXG5jb25zdCBQb3BvdmVyQ29udGVudE5vbk1vZGFsID0gUmVhY3QuZm9yd2FyZFJlZjxQb3BvdmVyQ29udGVudFR5cGVFbGVtZW50LCBQb3BvdmVyQ29udGVudFR5cGVQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8UG9wb3ZlckNvbnRlbnRUeXBlUHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCBjb250ZXh0ID0gdXNlUG9wb3ZlckNvbnRleHQoQ09OVEVOVF9OQU1FLCBwcm9wcy5fX3Njb3BlUG9wb3Zlcik7XG4gICAgY29uc3QgaGFzSW50ZXJhY3RlZE91dHNpZGVSZWYgPSBSZWFjdC51c2VSZWYoZmFsc2UpO1xuICAgIGNvbnN0IGhhc1BvaW50ZXJEb3duT3V0c2lkZVJlZiA9IFJlYWN0LnVzZVJlZihmYWxzZSk7XG5cbiAgICByZXR1cm4gKFxuICAgICAgPFBvcG92ZXJDb250ZW50SW1wbFxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICAgIHJlZj17Zm9yd2FyZGVkUmVmfVxuICAgICAgICB0cmFwRm9jdXM9e2ZhbHNlfVxuICAgICAgICBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHM9e2ZhbHNlfVxuICAgICAgICBvbkNsb3NlQXV0b0ZvY3VzPXsoZXZlbnQpID0+IHtcbiAgICAgICAgICBwcm9wcy5vbkNsb3NlQXV0b0ZvY3VzPy4oZXZlbnQpO1xuXG4gICAgICAgICAgaWYgKCFldmVudC5kZWZhdWx0UHJldmVudGVkKSB7XG4gICAgICAgICAgICBpZiAoIWhhc0ludGVyYWN0ZWRPdXRzaWRlUmVmLmN1cnJlbnQpIGNvbnRleHQudHJpZ2dlclJlZi5jdXJyZW50Py5mb2N1cygpO1xuICAgICAgICAgICAgLy8gQWx3YXlzIHByZXZlbnQgYXV0byBmb2N1cyBiZWNhdXNlIHdlIGVpdGhlciBmb2N1cyBtYW51YWxseSBvciB3YW50IHVzZXIgYWdlbnQgZm9jdXNcbiAgICAgICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgaGFzSW50ZXJhY3RlZE91dHNpZGVSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgICAgIGhhc1BvaW50ZXJEb3duT3V0c2lkZVJlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIH19XG4gICAgICAgIG9uSW50ZXJhY3RPdXRzaWRlPXsoZXZlbnQpID0+IHtcbiAgICAgICAgICBwcm9wcy5vbkludGVyYWN0T3V0c2lkZT8uKGV2ZW50KTtcblxuICAgICAgICAgIGlmICghZXZlbnQuZGVmYXVsdFByZXZlbnRlZCkge1xuICAgICAgICAgICAgaGFzSW50ZXJhY3RlZE91dHNpZGVSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgICBpZiAoZXZlbnQuZGV0YWlsLm9yaWdpbmFsRXZlbnQudHlwZSA9PT0gJ3BvaW50ZXJkb3duJykge1xuICAgICAgICAgICAgICBoYXNQb2ludGVyRG93bk91dHNpZGVSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gUHJldmVudCBkaXNtaXNzaW5nIHdoZW4gY2xpY2tpbmcgdGhlIHRyaWdnZXIuXG4gICAgICAgICAgLy8gQXMgdGhlIHRyaWdnZXIgaXMgYWxyZWFkeSBzZXR1cCB0byBjbG9zZSwgd2l0aG91dCBkb2luZyBzbyB3b3VsZFxuICAgICAgICAgIC8vIGNhdXNlIGl0IHRvIGNsb3NlIGFuZCBpbW1lZGlhdGVseSBvcGVuLlxuICAgICAgICAgIGNvbnN0IHRhcmdldCA9IGV2ZW50LnRhcmdldCBhcyBIVE1MRWxlbWVudDtcbiAgICAgICAgICBjb25zdCB0YXJnZXRJc1RyaWdnZXIgPSBjb250ZXh0LnRyaWdnZXJSZWYuY3VycmVudD8uY29udGFpbnModGFyZ2V0KTtcbiAgICAgICAgICBpZiAodGFyZ2V0SXNUcmlnZ2VyKSBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuXG4gICAgICAgICAgLy8gT24gU2FmYXJpIGlmIHRoZSB0cmlnZ2VyIGlzIGluc2lkZSBhIGNvbnRhaW5lciB3aXRoIHRhYkluZGV4PXswfSwgd2hlbiBjbGlja2VkXG4gICAgICAgICAgLy8gd2Ugd2lsbCBnZXQgdGhlIHBvaW50ZXIgZG93biBvdXRzaWRlIGV2ZW50IG9uIHRoZSB0cmlnZ2VyLCBidXQgdGhlbiBhIHN1YnNlcXVlbnRcbiAgICAgICAgICAvLyBmb2N1cyBvdXRzaWRlIGV2ZW50IG9uIHRoZSBjb250YWluZXIsIHdlIGlnbm9yZSBhbnkgZm9jdXMgb3V0c2lkZSBldmVudCB3aGVuIHdlJ3ZlXG4gICAgICAgICAgLy8gYWxyZWFkeSBoYWQgYSBwb2ludGVyIGRvd24gb3V0c2lkZSBldmVudC5cbiAgICAgICAgICBpZiAoZXZlbnQuZGV0YWlsLm9yaWdpbmFsRXZlbnQudHlwZSA9PT0gJ2ZvY3VzaW4nICYmIGhhc1BvaW50ZXJEb3duT3V0c2lkZVJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICAgIH1cbiAgICAgICAgfX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0qL1xuXG50eXBlIFBvcG92ZXJDb250ZW50SW1wbEVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQb3BwZXJQcmltaXRpdmUuQ29udGVudD47XG50eXBlIEZvY3VzU2NvcGVQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRm9jdXNTY29wZT47XG50eXBlIERpc21pc3NhYmxlTGF5ZXJQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRGlzbWlzc2FibGVMYXllcj47XG50eXBlIFBvcHBlckNvbnRlbnRQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUG9wcGVyUHJpbWl0aXZlLkNvbnRlbnQ+O1xuaW50ZXJmYWNlIFBvcG92ZXJDb250ZW50SW1wbFByb3BzXG4gIGV4dGVuZHMgT21pdDxQb3BwZXJDb250ZW50UHJvcHMsICdvblBsYWNlZCc+LFxuICAgIE9taXQ8RGlzbWlzc2FibGVMYXllclByb3BzLCAnb25EaXNtaXNzJz4ge1xuICAvKipcbiAgICogV2hldGhlciBmb2N1cyBzaG91bGQgYmUgdHJhcHBlZCB3aXRoaW4gdGhlIGBQb3BvdmVyYFxuICAgKiAoZGVmYXVsdDogZmFsc2UpXG4gICAqL1xuICB0cmFwRm9jdXM/OiBGb2N1c1Njb3BlUHJvcHNbJ3RyYXBwZWQnXTtcblxuICAvKipcbiAgICogRXZlbnQgaGFuZGxlciBjYWxsZWQgd2hlbiBhdXRvLWZvY3VzaW5nIG9uIG9wZW4uXG4gICAqIENhbiBiZSBwcmV2ZW50ZWQuXG4gICAqL1xuICBvbk9wZW5BdXRvRm9jdXM/OiBGb2N1c1Njb3BlUHJvcHNbJ29uTW91bnRBdXRvRm9jdXMnXTtcblxuICAvKipcbiAgICogRXZlbnQgaGFuZGxlciBjYWxsZWQgd2hlbiBhdXRvLWZvY3VzaW5nIG9uIGNsb3NlLlxuICAgKiBDYW4gYmUgcHJldmVudGVkLlxuICAgKi9cbiAgb25DbG9zZUF1dG9Gb2N1cz86IEZvY3VzU2NvcGVQcm9wc1snb25Vbm1vdW50QXV0b0ZvY3VzJ107XG59XG5cbmNvbnN0IFBvcG92ZXJDb250ZW50SW1wbCA9IFJlYWN0LmZvcndhcmRSZWY8UG9wb3ZlckNvbnRlbnRJbXBsRWxlbWVudCwgUG9wb3ZlckNvbnRlbnRJbXBsUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFBvcG92ZXJDb250ZW50SW1wbFByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3Qge1xuICAgICAgX19zY29wZVBvcG92ZXIsXG4gICAgICB0cmFwRm9jdXMsXG4gICAgICBvbk9wZW5BdXRvRm9jdXMsXG4gICAgICBvbkNsb3NlQXV0b0ZvY3VzLFxuICAgICAgZGlzYWJsZU91dHNpZGVQb2ludGVyRXZlbnRzLFxuICAgICAgb25Fc2NhcGVLZXlEb3duLFxuICAgICAgb25Qb2ludGVyRG93bk91dHNpZGUsXG4gICAgICBvbkZvY3VzT3V0c2lkZSxcbiAgICAgIG9uSW50ZXJhY3RPdXRzaWRlLFxuICAgICAgLi4uY29udGVudFByb3BzXG4gICAgfSA9IHByb3BzO1xuICAgIGNvbnN0IGNvbnRleHQgPSB1c2VQb3BvdmVyQ29udGV4dChDT05URU5UX05BTUUsIF9fc2NvcGVQb3BvdmVyKTtcbiAgICBjb25zdCBwb3BwZXJTY29wZSA9IHVzZVBvcHBlclNjb3BlKF9fc2NvcGVQb3BvdmVyKTtcblxuICAgIC8vIE1ha2Ugc3VyZSB0aGUgd2hvbGUgdHJlZSBoYXMgZm9jdXMgZ3VhcmRzIGFzIG91ciBgUG9wb3ZlcmAgbWF5IGJlXG4gICAgLy8gdGhlIGxhc3QgZWxlbWVudCBpbiB0aGUgRE9NIChiZWNhdXNlIG9mIHRoZSBgUG9ydGFsYClcbiAgICB1c2VGb2N1c0d1YXJkcygpO1xuXG4gICAgcmV0dXJuIChcbiAgICAgIDxGb2N1c1Njb3BlXG4gICAgICAgIGFzQ2hpbGRcbiAgICAgICAgbG9vcFxuICAgICAgICB0cmFwcGVkPXt0cmFwRm9jdXN9XG4gICAgICAgIG9uTW91bnRBdXRvRm9jdXM9e29uT3BlbkF1dG9Gb2N1c31cbiAgICAgICAgb25Vbm1vdW50QXV0b0ZvY3VzPXtvbkNsb3NlQXV0b0ZvY3VzfVxuICAgICAgPlxuICAgICAgICA8RGlzbWlzc2FibGVMYXllclxuICAgICAgICAgIGFzQ2hpbGRcbiAgICAgICAgICBkaXNhYmxlT3V0c2lkZVBvaW50ZXJFdmVudHM9e2Rpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50c31cbiAgICAgICAgICBvbkludGVyYWN0T3V0c2lkZT17b25JbnRlcmFjdE91dHNpZGV9XG4gICAgICAgICAgb25Fc2NhcGVLZXlEb3duPXtvbkVzY2FwZUtleURvd259XG4gICAgICAgICAgb25Qb2ludGVyRG93bk91dHNpZGU9e29uUG9pbnRlckRvd25PdXRzaWRlfVxuICAgICAgICAgIG9uRm9jdXNPdXRzaWRlPXtvbkZvY3VzT3V0c2lkZX1cbiAgICAgICAgICBvbkRpc21pc3M9eygpID0+IGNvbnRleHQub25PcGVuQ2hhbmdlKGZhbHNlKX1cbiAgICAgICAgPlxuICAgICAgICAgIDxQb3BwZXJQcmltaXRpdmUuQ29udGVudFxuICAgICAgICAgICAgZGF0YS1zdGF0ZT17Z2V0U3RhdGUoY29udGV4dC5vcGVuKX1cbiAgICAgICAgICAgIHJvbGU9XCJkaWFsb2dcIlxuICAgICAgICAgICAgaWQ9e2NvbnRleHQuY29udGVudElkfVxuICAgICAgICAgICAgey4uLnBvcHBlclNjb3BlfVxuICAgICAgICAgICAgey4uLmNvbnRlbnRQcm9wc31cbiAgICAgICAgICAgIHJlZj17Zm9yd2FyZGVkUmVmfVxuICAgICAgICAgICAgc3R5bGU9e3tcbiAgICAgICAgICAgICAgLi4uY29udGVudFByb3BzLnN0eWxlLFxuICAgICAgICAgICAgICAvLyByZS1uYW1lc3BhY2UgZXhwb3NlZCBjb250ZW50IGN1c3RvbSBwcm9wZXJ0aWVzXG4gICAgICAgICAgICAgIC4uLntcbiAgICAgICAgICAgICAgICAnLS1yYWRpeC1wb3BvdmVyLWNvbnRlbnQtdHJhbnNmb3JtLW9yaWdpbic6ICd2YXIoLS1yYWRpeC1wb3BwZXItdHJhbnNmb3JtLW9yaWdpbiknLFxuICAgICAgICAgICAgICAgICctLXJhZGl4LXBvcG92ZXItY29udGVudC1hdmFpbGFibGUtd2lkdGgnOiAndmFyKC0tcmFkaXgtcG9wcGVyLWF2YWlsYWJsZS13aWR0aCknLFxuICAgICAgICAgICAgICAgICctLXJhZGl4LXBvcG92ZXItY29udGVudC1hdmFpbGFibGUtaGVpZ2h0JzogJ3ZhcigtLXJhZGl4LXBvcHBlci1hdmFpbGFibGUtaGVpZ2h0KScsXG4gICAgICAgICAgICAgICAgJy0tcmFkaXgtcG9wb3Zlci10cmlnZ2VyLXdpZHRoJzogJ3ZhcigtLXJhZGl4LXBvcHBlci1hbmNob3Itd2lkdGgpJyxcbiAgICAgICAgICAgICAgICAnLS1yYWRpeC1wb3BvdmVyLXRyaWdnZXItaGVpZ2h0JzogJ3ZhcigtLXJhZGl4LXBvcHBlci1hbmNob3ItaGVpZ2h0KScsXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB9fVxuICAgICAgICAgIC8+XG4gICAgICAgIDwvRGlzbWlzc2FibGVMYXllcj5cbiAgICAgIDwvRm9jdXNTY29wZT5cbiAgICApO1xuICB9XG4pO1xuXG4vKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tXG4gKiBQb3BvdmVyQ2xvc2VcbiAqIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuY29uc3QgQ0xPU0VfTkFNRSA9ICdQb3BvdmVyQ2xvc2UnO1xuXG50eXBlIFBvcG92ZXJDbG9zZUVsZW1lbnQgPSBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBQcmltaXRpdmUuYnV0dG9uPjtcbmludGVyZmFjZSBQb3BvdmVyQ2xvc2VQcm9wcyBleHRlbmRzIFByaW1pdGl2ZUJ1dHRvblByb3BzIHt9XG5cbmNvbnN0IFBvcG92ZXJDbG9zZSA9IFJlYWN0LmZvcndhcmRSZWY8UG9wb3ZlckNsb3NlRWxlbWVudCwgUG9wb3ZlckNsb3NlUHJvcHM+KFxuICAocHJvcHM6IFNjb3BlZFByb3BzPFBvcG92ZXJDbG9zZVByb3BzPiwgZm9yd2FyZGVkUmVmKSA9PiB7XG4gICAgY29uc3QgeyBfX3Njb3BlUG9wb3ZlciwgLi4uY2xvc2VQcm9wcyB9ID0gcHJvcHM7XG4gICAgY29uc3QgY29udGV4dCA9IHVzZVBvcG92ZXJDb250ZXh0KENMT1NFX05BTUUsIF9fc2NvcGVQb3BvdmVyKTtcbiAgICByZXR1cm4gKFxuICAgICAgPFByaW1pdGl2ZS5idXR0b25cbiAgICAgICAgdHlwZT1cImJ1dHRvblwiXG4gICAgICAgIHsuLi5jbG9zZVByb3BzfVxuICAgICAgICByZWY9e2ZvcndhcmRlZFJlZn1cbiAgICAgICAgb25DbGljaz17Y29tcG9zZUV2ZW50SGFuZGxlcnMocHJvcHMub25DbGljaywgKCkgPT4gY29udGV4dC5vbk9wZW5DaGFuZ2UoZmFsc2UpKX1cbiAgICAgIC8+XG4gICAgKTtcbiAgfVxuKTtcblxuUG9wb3ZlckNsb3NlLmRpc3BsYXlOYW1lID0gQ0xPU0VfTkFNRTtcblxuLyogLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLVxuICogUG9wb3ZlckFycm93XG4gKiAtLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLSovXG5cbmNvbnN0IEFSUk9XX05BTUUgPSAnUG9wb3ZlckFycm93JztcblxudHlwZSBQb3BvdmVyQXJyb3dFbGVtZW50ID0gUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgUG9wcGVyUHJpbWl0aXZlLkFycm93PjtcbnR5cGUgUG9wcGVyQXJyb3dQcm9wcyA9IFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgUG9wcGVyUHJpbWl0aXZlLkFycm93PjtcbmludGVyZmFjZSBQb3BvdmVyQXJyb3dQcm9wcyBleHRlbmRzIFBvcHBlckFycm93UHJvcHMge31cblxuY29uc3QgUG9wb3ZlckFycm93ID0gUmVhY3QuZm9yd2FyZFJlZjxQb3BvdmVyQXJyb3dFbGVtZW50LCBQb3BvdmVyQXJyb3dQcm9wcz4oXG4gIChwcm9wczogU2NvcGVkUHJvcHM8UG9wb3ZlckFycm93UHJvcHM+LCBmb3J3YXJkZWRSZWYpID0+IHtcbiAgICBjb25zdCB7IF9fc2NvcGVQb3BvdmVyLCAuLi5hcnJvd1Byb3BzIH0gPSBwcm9wcztcbiAgICBjb25zdCBwb3BwZXJTY29wZSA9IHVzZVBvcHBlclNjb3BlKF9fc2NvcGVQb3BvdmVyKTtcbiAgICByZXR1cm4gPFBvcHBlclByaW1pdGl2ZS5BcnJvdyB7Li4ucG9wcGVyU2NvcGV9IHsuLi5hcnJvd1Byb3BzfSByZWY9e2ZvcndhcmRlZFJlZn0gLz47XG4gIH1cbik7XG5cblBvcG92ZXJBcnJvdy5kaXNwbGF5TmFtZSA9IEFSUk9XX05BTUU7XG5cbi8qIC0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tLS0tKi9cblxuZnVuY3Rpb24gZ2V0U3RhdGUob3BlbjogYm9vbGVhbikge1xuICByZXR1cm4gb3BlbiA/ICdvcGVuJyA6ICdjbG9zZWQnO1xufVxuXG5jb25zdCBSb290ID0gUG9wb3ZlcjtcbmNvbnN0IEFuY2hvciA9IFBvcG92ZXJBbmNob3I7XG5jb25zdCBUcmlnZ2VyID0gUG9wb3ZlclRyaWdnZXI7XG5jb25zdCBQb3J0YWwgPSBQb3BvdmVyUG9ydGFsO1xuY29uc3QgQ29udGVudCA9IFBvcG92ZXJDb250ZW50O1xuY29uc3QgQ2xvc2UgPSBQb3BvdmVyQ2xvc2U7XG5jb25zdCBBcnJvdyA9IFBvcG92ZXJBcnJvdztcblxuZXhwb3J0IHtcbiAgY3JlYXRlUG9wb3ZlclNjb3BlLFxuICAvL1xuICBQb3BvdmVyLFxuICBQb3BvdmVyQW5jaG9yLFxuICBQb3BvdmVyVHJpZ2dlcixcbiAgUG9wb3ZlclBvcnRhbCxcbiAgUG9wb3ZlckNvbnRlbnQsXG4gIFBvcG92ZXJDbG9zZSxcbiAgUG9wb3ZlckFycm93LFxuICAvL1xuICBSb290LFxuICBBbmNob3IsXG4gIFRyaWdnZXIsXG4gIFBvcnRhbCxcbiAgQ29udGVudCxcbiAgQ2xvc2UsXG4gIEFycm93LFxufTtcbmV4cG9ydCB0eXBlIHtcbiAgUG9wb3ZlclByb3BzLFxuICBQb3BvdmVyQW5jaG9yUHJvcHMsXG4gIFBvcG92ZXJUcmlnZ2VyUHJvcHMsXG4gIFBvcG92ZXJQb3J0YWxQcm9wcyxcbiAgUG9wb3ZlckNvbnRlbnRQcm9wcyxcbiAgUG9wb3ZlckNsb3NlUHJvcHMsXG4gIFBvcG92ZXJBcnJvd1Byb3BzLFxufTtcbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNvbXBvc2VFdmVudEhhbmRsZXJzIiwidXNlQ29tcG9zZWRSZWZzIiwiY3JlYXRlQ29udGV4dFNjb3BlIiwiRGlzbWlzc2FibGVMYXllciIsInVzZUZvY3VzR3VhcmRzIiwiRm9jdXNTY29wZSIsInVzZUlkIiwiUG9wcGVyUHJpbWl0aXZlIiwiY3JlYXRlUG9wcGVyU2NvcGUiLCJQb3J0YWwiLCJQb3J0YWxQcmltaXRpdmUiLCJQcmVzZW5jZSIsIlByaW1pdGl2ZSIsIlNsb3QiLCJ1c2VDb250cm9sbGFibGVTdGF0ZSIsImhpZGVPdGhlcnMiLCJSZW1vdmVTY3JvbGwiLCJqc3giLCJQT1BPVkVSX05BTUUiLCJjcmVhdGVQb3BvdmVyQ29udGV4dCIsImNyZWF0ZVBvcG92ZXJTY29wZSIsInVzZVBvcHBlclNjb3BlIiwiUG9wb3ZlclByb3ZpZGVyIiwidXNlUG9wb3ZlckNvbnRleHQiLCJQb3BvdmVyIiwicHJvcHMiLCJfX3Njb3BlUG9wb3ZlciIsImNoaWxkcmVuIiwib3BlbiIsIm9wZW5Qcm9wIiwiZGVmYXVsdE9wZW4iLCJvbk9wZW5DaGFuZ2UiLCJtb2RhbCIsInBvcHBlclNjb3BlIiwidHJpZ2dlclJlZiIsInVzZVJlZiIsImhhc0N1c3RvbUFuY2hvciIsInNldEhhc0N1c3RvbUFuY2hvciIsInVzZVN0YXRlIiwic2V0T3BlbiIsInByb3AiLCJkZWZhdWx0UHJvcCIsIm9uQ2hhbmdlIiwiUm9vdCIsInNjb3BlIiwiY29udGVudElkIiwib25PcGVuVG9nZ2xlIiwidXNlQ2FsbGJhY2siLCJwcmV2T3BlbiIsIm9uQ3VzdG9tQW5jaG9yQWRkIiwib25DdXN0b21BbmNob3JSZW1vdmUiLCJkaXNwbGF5TmFtZSIsIkFOQ0hPUl9OQU1FIiwiUG9wb3ZlckFuY2hvciIsImZvcndhcmRSZWYiLCJmb3J3YXJkZWRSZWYiLCJhbmNob3JQcm9wcyIsImNvbnRleHQiLCJ1c2VFZmZlY3QiLCJBbmNob3IiLCJyZWYiLCJUUklHR0VSX05BTUUiLCJQb3BvdmVyVHJpZ2dlciIsInRyaWdnZXJQcm9wcyIsImNvbXBvc2VkVHJpZ2dlclJlZiIsInRyaWdnZXIiLCJidXR0b24iLCJ0eXBlIiwiZ2V0U3RhdGUiLCJvbkNsaWNrIiwiYXNDaGlsZCIsIlBPUlRBTF9OQU1FIiwiUG9ydGFsUHJvdmlkZXIiLCJ1c2VQb3J0YWxDb250ZXh0IiwiZm9yY2VNb3VudCIsIlBvcG92ZXJQb3J0YWwiLCJjb250YWluZXIiLCJwcmVzZW50IiwiQ09OVEVOVF9OQU1FIiwiUG9wb3ZlckNvbnRlbnQiLCJwb3J0YWxDb250ZXh0IiwiY29udGVudFByb3BzIiwiUG9wb3ZlckNvbnRlbnRNb2RhbCIsIlBvcG92ZXJDb250ZW50Tm9uTW9kYWwiLCJjb250ZW50UmVmIiwiY29tcG9zZWRSZWZzIiwiaXNSaWdodENsaWNrT3V0c2lkZVJlZiIsImNvbnRlbnQiLCJjdXJyZW50IiwiYXMiLCJhbGxvd1BpbmNoWm9vbSIsIlBvcG92ZXJDb250ZW50SW1wbCIsInRyYXBGb2N1cyIsImRpc2FibGVPdXRzaWRlUG9pbnRlckV2ZW50cyIsIm9uQ2xvc2VBdXRvRm9jdXMiLCJldmVudCIsInByZXZlbnREZWZhdWx0IiwiZm9jdXMiLCJvblBvaW50ZXJEb3duT3V0c2lkZSIsIm9yaWdpbmFsRXZlbnQiLCJkZXRhaWwiLCJjdHJsTGVmdENsaWNrIiwiY3RybEtleSIsImlzUmlnaHRDbGljayIsImNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCIsIm9uRm9jdXNPdXRzaWRlIiwiaGFzSW50ZXJhY3RlZE91dHNpZGVSZWYiLCJoYXNQb2ludGVyRG93bk91dHNpZGVSZWYiLCJkZWZhdWx0UHJldmVudGVkIiwib25JbnRlcmFjdE91dHNpZGUiLCJ0YXJnZXQiLCJ0YXJnZXRJc1RyaWdnZXIiLCJjb250YWlucyIsIm9uT3BlbkF1dG9Gb2N1cyIsIm9uRXNjYXBlS2V5RG93biIsImxvb3AiLCJ0cmFwcGVkIiwib25Nb3VudEF1dG9Gb2N1cyIsIm9uVW5tb3VudEF1dG9Gb2N1cyIsIm9uRGlzbWlzcyIsIkNvbnRlbnQiLCJyb2xlIiwiaWQiLCJzdHlsZSIsIkNMT1NFX05BTUUiLCJQb3BvdmVyQ2xvc2UiLCJjbG9zZVByb3BzIiwiQVJST1dfTkFNRSIsIlBvcG92ZXJBcnJvdyIsImFycm93UHJvcHMiLCJBcnJvdyIsIlRyaWdnZXIiLCJDbG9zZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1_f96426ca417071cefe78c0fc8e72878a/node_modules/@radix-ui/react-popover/dist/index.mjs\n");

/***/ })

};
;