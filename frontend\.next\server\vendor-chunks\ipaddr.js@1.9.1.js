/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/ipaddr.js@1.9.1";
exports.ids = ["vendor-chunks/ipaddr.js@1.9.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/ipaddr.js@1.9.1/node_modules/ipaddr.js/lib/ipaddr.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/ipaddr.js@1.9.1/node_modules/ipaddr.js/lib/ipaddr.js ***!
  \*********************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("/* module decorator */ module = __webpack_require__.nmd(module);\n(function() {\n  var expandIPv6, ipaddr, ipv4Part, ipv4Regexes, ipv6Part, ipv6Regexes, matchCIDR, root, zoneIndex;\n\n  ipaddr = {};\n\n  root = this;\n\n  if (( true && module !== null) && module.exports) {\n    module.exports = ipaddr;\n  } else {\n    root['ipaddr'] = ipaddr;\n  }\n\n  matchCIDR = function(first, second, partSize, cidrBits) {\n    var part, shift;\n    if (first.length !== second.length) {\n      throw new Error(\"ipaddr: cannot match CIDR for objects with different lengths\");\n    }\n    part = 0;\n    while (cidrBits > 0) {\n      shift = partSize - cidrBits;\n      if (shift < 0) {\n        shift = 0;\n      }\n      if (first[part] >> shift !== second[part] >> shift) {\n        return false;\n      }\n      cidrBits -= partSize;\n      part += 1;\n    }\n    return true;\n  };\n\n  ipaddr.subnetMatch = function(address, rangeList, defaultName) {\n    var k, len, rangeName, rangeSubnets, subnet;\n    if (defaultName == null) {\n      defaultName = 'unicast';\n    }\n    for (rangeName in rangeList) {\n      rangeSubnets = rangeList[rangeName];\n      if (rangeSubnets[0] && !(rangeSubnets[0] instanceof Array)) {\n        rangeSubnets = [rangeSubnets];\n      }\n      for (k = 0, len = rangeSubnets.length; k < len; k++) {\n        subnet = rangeSubnets[k];\n        if (address.kind() === subnet[0].kind()) {\n          if (address.match.apply(address, subnet)) {\n            return rangeName;\n          }\n        }\n      }\n    }\n    return defaultName;\n  };\n\n  ipaddr.IPv4 = (function() {\n    function IPv4(octets) {\n      var k, len, octet;\n      if (octets.length !== 4) {\n        throw new Error(\"ipaddr: ipv4 octet count should be 4\");\n      }\n      for (k = 0, len = octets.length; k < len; k++) {\n        octet = octets[k];\n        if (!((0 <= octet && octet <= 255))) {\n          throw new Error(\"ipaddr: ipv4 octet should fit in 8 bits\");\n        }\n      }\n      this.octets = octets;\n    }\n\n    IPv4.prototype.kind = function() {\n      return 'ipv4';\n    };\n\n    IPv4.prototype.toString = function() {\n      return this.octets.join(\".\");\n    };\n\n    IPv4.prototype.toNormalizedString = function() {\n      return this.toString();\n    };\n\n    IPv4.prototype.toByteArray = function() {\n      return this.octets.slice(0);\n    };\n\n    IPv4.prototype.match = function(other, cidrRange) {\n      var ref;\n      if (cidrRange === void 0) {\n        ref = other, other = ref[0], cidrRange = ref[1];\n      }\n      if (other.kind() !== 'ipv4') {\n        throw new Error(\"ipaddr: cannot match ipv4 address with non-ipv4 one\");\n      }\n      return matchCIDR(this.octets, other.octets, 8, cidrRange);\n    };\n\n    IPv4.prototype.SpecialRanges = {\n      unspecified: [[new IPv4([0, 0, 0, 0]), 8]],\n      broadcast: [[new IPv4([255, 255, 255, 255]), 32]],\n      multicast: [[new IPv4([224, 0, 0, 0]), 4]],\n      linkLocal: [[new IPv4([169, 254, 0, 0]), 16]],\n      loopback: [[new IPv4([127, 0, 0, 0]), 8]],\n      carrierGradeNat: [[new IPv4([100, 64, 0, 0]), 10]],\n      \"private\": [[new IPv4([10, 0, 0, 0]), 8], [new IPv4([172, 16, 0, 0]), 12], [new IPv4([192, 168, 0, 0]), 16]],\n      reserved: [[new IPv4([192, 0, 0, 0]), 24], [new IPv4([192, 0, 2, 0]), 24], [new IPv4([192, 88, 99, 0]), 24], [new IPv4([198, 51, 100, 0]), 24], [new IPv4([203, 0, 113, 0]), 24], [new IPv4([240, 0, 0, 0]), 4]]\n    };\n\n    IPv4.prototype.range = function() {\n      return ipaddr.subnetMatch(this, this.SpecialRanges);\n    };\n\n    IPv4.prototype.toIPv4MappedAddress = function() {\n      return ipaddr.IPv6.parse(\"::ffff:\" + (this.toString()));\n    };\n\n    IPv4.prototype.prefixLengthFromSubnetMask = function() {\n      var cidr, i, k, octet, stop, zeros, zerotable;\n      zerotable = {\n        0: 8,\n        128: 7,\n        192: 6,\n        224: 5,\n        240: 4,\n        248: 3,\n        252: 2,\n        254: 1,\n        255: 0\n      };\n      cidr = 0;\n      stop = false;\n      for (i = k = 3; k >= 0; i = k += -1) {\n        octet = this.octets[i];\n        if (octet in zerotable) {\n          zeros = zerotable[octet];\n          if (stop && zeros !== 0) {\n            return null;\n          }\n          if (zeros !== 8) {\n            stop = true;\n          }\n          cidr += zeros;\n        } else {\n          return null;\n        }\n      }\n      return 32 - cidr;\n    };\n\n    return IPv4;\n\n  })();\n\n  ipv4Part = \"(0?\\\\d+|0x[a-f0-9]+)\";\n\n  ipv4Regexes = {\n    fourOctet: new RegExp(\"^\" + ipv4Part + \"\\\\.\" + ipv4Part + \"\\\\.\" + ipv4Part + \"\\\\.\" + ipv4Part + \"$\", 'i'),\n    longValue: new RegExp(\"^\" + ipv4Part + \"$\", 'i')\n  };\n\n  ipaddr.IPv4.parser = function(string) {\n    var match, parseIntAuto, part, shift, value;\n    parseIntAuto = function(string) {\n      if (string[0] === \"0\" && string[1] !== \"x\") {\n        return parseInt(string, 8);\n      } else {\n        return parseInt(string);\n      }\n    };\n    if (match = string.match(ipv4Regexes.fourOctet)) {\n      return (function() {\n        var k, len, ref, results;\n        ref = match.slice(1, 6);\n        results = [];\n        for (k = 0, len = ref.length; k < len; k++) {\n          part = ref[k];\n          results.push(parseIntAuto(part));\n        }\n        return results;\n      })();\n    } else if (match = string.match(ipv4Regexes.longValue)) {\n      value = parseIntAuto(match[1]);\n      if (value > 0xffffffff || value < 0) {\n        throw new Error(\"ipaddr: address outside defined range\");\n      }\n      return ((function() {\n        var k, results;\n        results = [];\n        for (shift = k = 0; k <= 24; shift = k += 8) {\n          results.push((value >> shift) & 0xff);\n        }\n        return results;\n      })()).reverse();\n    } else {\n      return null;\n    }\n  };\n\n  ipaddr.IPv6 = (function() {\n    function IPv6(parts, zoneId) {\n      var i, k, l, len, part, ref;\n      if (parts.length === 16) {\n        this.parts = [];\n        for (i = k = 0; k <= 14; i = k += 2) {\n          this.parts.push((parts[i] << 8) | parts[i + 1]);\n        }\n      } else if (parts.length === 8) {\n        this.parts = parts;\n      } else {\n        throw new Error(\"ipaddr: ipv6 part count should be 8 or 16\");\n      }\n      ref = this.parts;\n      for (l = 0, len = ref.length; l < len; l++) {\n        part = ref[l];\n        if (!((0 <= part && part <= 0xffff))) {\n          throw new Error(\"ipaddr: ipv6 part should fit in 16 bits\");\n        }\n      }\n      if (zoneId) {\n        this.zoneId = zoneId;\n      }\n    }\n\n    IPv6.prototype.kind = function() {\n      return 'ipv6';\n    };\n\n    IPv6.prototype.toString = function() {\n      return this.toNormalizedString().replace(/((^|:)(0(:|$))+)/, '::');\n    };\n\n    IPv6.prototype.toRFC5952String = function() {\n      var bestMatchIndex, bestMatchLength, match, regex, string;\n      regex = /((^|:)(0(:|$)){2,})/g;\n      string = this.toNormalizedString();\n      bestMatchIndex = 0;\n      bestMatchLength = -1;\n      while ((match = regex.exec(string))) {\n        if (match[0].length > bestMatchLength) {\n          bestMatchIndex = match.index;\n          bestMatchLength = match[0].length;\n        }\n      }\n      if (bestMatchLength < 0) {\n        return string;\n      }\n      return string.substring(0, bestMatchIndex) + '::' + string.substring(bestMatchIndex + bestMatchLength);\n    };\n\n    IPv6.prototype.toByteArray = function() {\n      var bytes, k, len, part, ref;\n      bytes = [];\n      ref = this.parts;\n      for (k = 0, len = ref.length; k < len; k++) {\n        part = ref[k];\n        bytes.push(part >> 8);\n        bytes.push(part & 0xff);\n      }\n      return bytes;\n    };\n\n    IPv6.prototype.toNormalizedString = function() {\n      var addr, part, suffix;\n      addr = ((function() {\n        var k, len, ref, results;\n        ref = this.parts;\n        results = [];\n        for (k = 0, len = ref.length; k < len; k++) {\n          part = ref[k];\n          results.push(part.toString(16));\n        }\n        return results;\n      }).call(this)).join(\":\");\n      suffix = '';\n      if (this.zoneId) {\n        suffix = '%' + this.zoneId;\n      }\n      return addr + suffix;\n    };\n\n    IPv6.prototype.toFixedLengthString = function() {\n      var addr, part, suffix;\n      addr = ((function() {\n        var k, len, ref, results;\n        ref = this.parts;\n        results = [];\n        for (k = 0, len = ref.length; k < len; k++) {\n          part = ref[k];\n          results.push(part.toString(16).padStart(4, '0'));\n        }\n        return results;\n      }).call(this)).join(\":\");\n      suffix = '';\n      if (this.zoneId) {\n        suffix = '%' + this.zoneId;\n      }\n      return addr + suffix;\n    };\n\n    IPv6.prototype.match = function(other, cidrRange) {\n      var ref;\n      if (cidrRange === void 0) {\n        ref = other, other = ref[0], cidrRange = ref[1];\n      }\n      if (other.kind() !== 'ipv6') {\n        throw new Error(\"ipaddr: cannot match ipv6 address with non-ipv6 one\");\n      }\n      return matchCIDR(this.parts, other.parts, 16, cidrRange);\n    };\n\n    IPv6.prototype.SpecialRanges = {\n      unspecified: [new IPv6([0, 0, 0, 0, 0, 0, 0, 0]), 128],\n      linkLocal: [new IPv6([0xfe80, 0, 0, 0, 0, 0, 0, 0]), 10],\n      multicast: [new IPv6([0xff00, 0, 0, 0, 0, 0, 0, 0]), 8],\n      loopback: [new IPv6([0, 0, 0, 0, 0, 0, 0, 1]), 128],\n      uniqueLocal: [new IPv6([0xfc00, 0, 0, 0, 0, 0, 0, 0]), 7],\n      ipv4Mapped: [new IPv6([0, 0, 0, 0, 0, 0xffff, 0, 0]), 96],\n      rfc6145: [new IPv6([0, 0, 0, 0, 0xffff, 0, 0, 0]), 96],\n      rfc6052: [new IPv6([0x64, 0xff9b, 0, 0, 0, 0, 0, 0]), 96],\n      '6to4': [new IPv6([0x2002, 0, 0, 0, 0, 0, 0, 0]), 16],\n      teredo: [new IPv6([0x2001, 0, 0, 0, 0, 0, 0, 0]), 32],\n      reserved: [[new IPv6([0x2001, 0xdb8, 0, 0, 0, 0, 0, 0]), 32]]\n    };\n\n    IPv6.prototype.range = function() {\n      return ipaddr.subnetMatch(this, this.SpecialRanges);\n    };\n\n    IPv6.prototype.isIPv4MappedAddress = function() {\n      return this.range() === 'ipv4Mapped';\n    };\n\n    IPv6.prototype.toIPv4Address = function() {\n      var high, low, ref;\n      if (!this.isIPv4MappedAddress()) {\n        throw new Error(\"ipaddr: trying to convert a generic ipv6 address to ipv4\");\n      }\n      ref = this.parts.slice(-2), high = ref[0], low = ref[1];\n      return new ipaddr.IPv4([high >> 8, high & 0xff, low >> 8, low & 0xff]);\n    };\n\n    IPv6.prototype.prefixLengthFromSubnetMask = function() {\n      var cidr, i, k, part, stop, zeros, zerotable;\n      zerotable = {\n        0: 16,\n        32768: 15,\n        49152: 14,\n        57344: 13,\n        61440: 12,\n        63488: 11,\n        64512: 10,\n        65024: 9,\n        65280: 8,\n        65408: 7,\n        65472: 6,\n        65504: 5,\n        65520: 4,\n        65528: 3,\n        65532: 2,\n        65534: 1,\n        65535: 0\n      };\n      cidr = 0;\n      stop = false;\n      for (i = k = 7; k >= 0; i = k += -1) {\n        part = this.parts[i];\n        if (part in zerotable) {\n          zeros = zerotable[part];\n          if (stop && zeros !== 0) {\n            return null;\n          }\n          if (zeros !== 16) {\n            stop = true;\n          }\n          cidr += zeros;\n        } else {\n          return null;\n        }\n      }\n      return 128 - cidr;\n    };\n\n    return IPv6;\n\n  })();\n\n  ipv6Part = \"(?:[0-9a-f]+::?)+\";\n\n  zoneIndex = \"%[0-9a-z]{1,}\";\n\n  ipv6Regexes = {\n    zoneIndex: new RegExp(zoneIndex, 'i'),\n    \"native\": new RegExp(\"^(::)?(\" + ipv6Part + \")?([0-9a-f]+)?(::)?(\" + zoneIndex + \")?$\", 'i'),\n    transitional: new RegExp((\"^((?:\" + ipv6Part + \")|(?:::)(?:\" + ipv6Part + \")?)\") + (ipv4Part + \"\\\\.\" + ipv4Part + \"\\\\.\" + ipv4Part + \"\\\\.\" + ipv4Part) + (\"(\" + zoneIndex + \")?$\"), 'i')\n  };\n\n  expandIPv6 = function(string, parts) {\n    var colonCount, lastColon, part, replacement, replacementCount, zoneId;\n    if (string.indexOf('::') !== string.lastIndexOf('::')) {\n      return null;\n    }\n    zoneId = (string.match(ipv6Regexes['zoneIndex']) || [])[0];\n    if (zoneId) {\n      zoneId = zoneId.substring(1);\n      string = string.replace(/%.+$/, '');\n    }\n    colonCount = 0;\n    lastColon = -1;\n    while ((lastColon = string.indexOf(':', lastColon + 1)) >= 0) {\n      colonCount++;\n    }\n    if (string.substr(0, 2) === '::') {\n      colonCount--;\n    }\n    if (string.substr(-2, 2) === '::') {\n      colonCount--;\n    }\n    if (colonCount > parts) {\n      return null;\n    }\n    replacementCount = parts - colonCount;\n    replacement = ':';\n    while (replacementCount--) {\n      replacement += '0:';\n    }\n    string = string.replace('::', replacement);\n    if (string[0] === ':') {\n      string = string.slice(1);\n    }\n    if (string[string.length - 1] === ':') {\n      string = string.slice(0, -1);\n    }\n    parts = (function() {\n      var k, len, ref, results;\n      ref = string.split(\":\");\n      results = [];\n      for (k = 0, len = ref.length; k < len; k++) {\n        part = ref[k];\n        results.push(parseInt(part, 16));\n      }\n      return results;\n    })();\n    return {\n      parts: parts,\n      zoneId: zoneId\n    };\n  };\n\n  ipaddr.IPv6.parser = function(string) {\n    var addr, k, len, match, octet, octets, zoneId;\n    if (ipv6Regexes['native'].test(string)) {\n      return expandIPv6(string, 8);\n    } else if (match = string.match(ipv6Regexes['transitional'])) {\n      zoneId = match[6] || '';\n      addr = expandIPv6(match[1].slice(0, -1) + zoneId, 6);\n      if (addr.parts) {\n        octets = [parseInt(match[2]), parseInt(match[3]), parseInt(match[4]), parseInt(match[5])];\n        for (k = 0, len = octets.length; k < len; k++) {\n          octet = octets[k];\n          if (!((0 <= octet && octet <= 255))) {\n            return null;\n          }\n        }\n        addr.parts.push(octets[0] << 8 | octets[1]);\n        addr.parts.push(octets[2] << 8 | octets[3]);\n        return {\n          parts: addr.parts,\n          zoneId: addr.zoneId\n        };\n      }\n    }\n    return null;\n  };\n\n  ipaddr.IPv4.isIPv4 = ipaddr.IPv6.isIPv6 = function(string) {\n    return this.parser(string) !== null;\n  };\n\n  ipaddr.IPv4.isValid = function(string) {\n    var e;\n    try {\n      new this(this.parser(string));\n      return true;\n    } catch (error1) {\n      e = error1;\n      return false;\n    }\n  };\n\n  ipaddr.IPv4.isValidFourPartDecimal = function(string) {\n    if (ipaddr.IPv4.isValid(string) && string.match(/^(0|[1-9]\\d*)(\\.(0|[1-9]\\d*)){3}$/)) {\n      return true;\n    } else {\n      return false;\n    }\n  };\n\n  ipaddr.IPv6.isValid = function(string) {\n    var addr, e;\n    if (typeof string === \"string\" && string.indexOf(\":\") === -1) {\n      return false;\n    }\n    try {\n      addr = this.parser(string);\n      new this(addr.parts, addr.zoneId);\n      return true;\n    } catch (error1) {\n      e = error1;\n      return false;\n    }\n  };\n\n  ipaddr.IPv4.parse = function(string) {\n    var parts;\n    parts = this.parser(string);\n    if (parts === null) {\n      throw new Error(\"ipaddr: string is not formatted like ip address\");\n    }\n    return new this(parts);\n  };\n\n  ipaddr.IPv6.parse = function(string) {\n    var addr;\n    addr = this.parser(string);\n    if (addr.parts === null) {\n      throw new Error(\"ipaddr: string is not formatted like ip address\");\n    }\n    return new this(addr.parts, addr.zoneId);\n  };\n\n  ipaddr.IPv4.parseCIDR = function(string) {\n    var maskLength, match, parsed;\n    if (match = string.match(/^(.+)\\/(\\d+)$/)) {\n      maskLength = parseInt(match[2]);\n      if (maskLength >= 0 && maskLength <= 32) {\n        parsed = [this.parse(match[1]), maskLength];\n        Object.defineProperty(parsed, 'toString', {\n          value: function() {\n            return this.join('/');\n          }\n        });\n        return parsed;\n      }\n    }\n    throw new Error(\"ipaddr: string is not formatted like an IPv4 CIDR range\");\n  };\n\n  ipaddr.IPv4.subnetMaskFromPrefixLength = function(prefix) {\n    var filledOctetCount, j, octets;\n    prefix = parseInt(prefix);\n    if (prefix < 0 || prefix > 32) {\n      throw new Error('ipaddr: invalid IPv4 prefix length');\n    }\n    octets = [0, 0, 0, 0];\n    j = 0;\n    filledOctetCount = Math.floor(prefix / 8);\n    while (j < filledOctetCount) {\n      octets[j] = 255;\n      j++;\n    }\n    if (filledOctetCount < 4) {\n      octets[filledOctetCount] = Math.pow(2, prefix % 8) - 1 << 8 - (prefix % 8);\n    }\n    return new this(octets);\n  };\n\n  ipaddr.IPv4.broadcastAddressFromCIDR = function(string) {\n    var cidr, error, i, ipInterfaceOctets, octets, subnetMaskOctets;\n    try {\n      cidr = this.parseCIDR(string);\n      ipInterfaceOctets = cidr[0].toByteArray();\n      subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n      octets = [];\n      i = 0;\n      while (i < 4) {\n        octets.push(parseInt(ipInterfaceOctets[i], 10) | parseInt(subnetMaskOctets[i], 10) ^ 255);\n        i++;\n      }\n      return new this(octets);\n    } catch (error1) {\n      error = error1;\n      throw new Error('ipaddr: the address does not have IPv4 CIDR format');\n    }\n  };\n\n  ipaddr.IPv4.networkAddressFromCIDR = function(string) {\n    var cidr, error, i, ipInterfaceOctets, octets, subnetMaskOctets;\n    try {\n      cidr = this.parseCIDR(string);\n      ipInterfaceOctets = cidr[0].toByteArray();\n      subnetMaskOctets = this.subnetMaskFromPrefixLength(cidr[1]).toByteArray();\n      octets = [];\n      i = 0;\n      while (i < 4) {\n        octets.push(parseInt(ipInterfaceOctets[i], 10) & parseInt(subnetMaskOctets[i], 10));\n        i++;\n      }\n      return new this(octets);\n    } catch (error1) {\n      error = error1;\n      throw new Error('ipaddr: the address does not have IPv4 CIDR format');\n    }\n  };\n\n  ipaddr.IPv6.parseCIDR = function(string) {\n    var maskLength, match, parsed;\n    if (match = string.match(/^(.+)\\/(\\d+)$/)) {\n      maskLength = parseInt(match[2]);\n      if (maskLength >= 0 && maskLength <= 128) {\n        parsed = [this.parse(match[1]), maskLength];\n        Object.defineProperty(parsed, 'toString', {\n          value: function() {\n            return this.join('/');\n          }\n        });\n        return parsed;\n      }\n    }\n    throw new Error(\"ipaddr: string is not formatted like an IPv6 CIDR range\");\n  };\n\n  ipaddr.isValid = function(string) {\n    return ipaddr.IPv6.isValid(string) || ipaddr.IPv4.isValid(string);\n  };\n\n  ipaddr.parse = function(string) {\n    if (ipaddr.IPv6.isValid(string)) {\n      return ipaddr.IPv6.parse(string);\n    } else if (ipaddr.IPv4.isValid(string)) {\n      return ipaddr.IPv4.parse(string);\n    } else {\n      throw new Error(\"ipaddr: the address has neither IPv6 nor IPv4 format\");\n    }\n  };\n\n  ipaddr.parseCIDR = function(string) {\n    var e;\n    try {\n      return ipaddr.IPv6.parseCIDR(string);\n    } catch (error1) {\n      e = error1;\n      try {\n        return ipaddr.IPv4.parseCIDR(string);\n      } catch (error1) {\n        e = error1;\n        throw new Error(\"ipaddr: the address has neither IPv6 nor IPv4 CIDR format\");\n      }\n    }\n  };\n\n  ipaddr.fromByteArray = function(bytes) {\n    var length;\n    length = bytes.length;\n    if (length === 4) {\n      return new ipaddr.IPv4(bytes);\n    } else if (length === 16) {\n      return new ipaddr.IPv6(bytes);\n    } else {\n      throw new Error(\"ipaddr: the binary input is neither an IPv6 nor IPv4 address\");\n    }\n  };\n\n  ipaddr.process = function(string) {\n    var addr;\n    addr = this.parse(string);\n    if (addr.kind() === 'ipv6' && addr.isIPv4MappedAddress()) {\n      return addr.toIPv4Address();\n    } else {\n      return addr;\n    }\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vaXBhZGRyLmpzQDEuOS4xL25vZGVfbW9kdWxlcy9pcGFkZHIuanMvbGliL2lwYWRkci5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7QUFDQTs7QUFFQTs7QUFFQTs7QUFFQSxPQUFPLEtBQTZCO0FBQ3BDO0FBQ0EsSUFBSTtBQUNKO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2Q0FBNkMsU0FBUztBQUN0RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx1Q0FBdUMsU0FBUztBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsUUFBUTtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBLEdBQUc7O0FBRUg7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1AsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNEJBQTRCLFNBQVM7QUFDckM7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QixTQUFTO0FBQ2pDO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxRQUFRO0FBQ1I7QUFDQTtBQUNBO0FBQ0Esb0NBQW9DLFNBQVM7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLDhCQUE4QixHQUFHO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MsU0FBUztBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDLFNBQVM7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNDQUFzQyxTQUFTO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixRQUFRO0FBQzlCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSDs7QUFFQSx5QkFBeUIsR0FBRzs7QUFFNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyxTQUFTO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQSx5Q0FBeUMsU0FBUztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQSxtRkFBbUYsRUFBRTtBQUNyRjtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBLFFBQVE7QUFDUjtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBOztBQUVBLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vaXBhZGRyLmpzQDEuOS4xL25vZGVfbW9kdWxlcy9pcGFkZHIuanMvbGliL2lwYWRkci5qcz81ZjQ2Il0sInNvdXJjZXNDb250ZW50IjpbIihmdW5jdGlvbigpIHtcbiAgdmFyIGV4cGFuZElQdjYsIGlwYWRkciwgaXB2NFBhcnQsIGlwdjRSZWdleGVzLCBpcHY2UGFydCwgaXB2NlJlZ2V4ZXMsIG1hdGNoQ0lEUiwgcm9vdCwgem9uZUluZGV4O1xuXG4gIGlwYWRkciA9IHt9O1xuXG4gIHJvb3QgPSB0aGlzO1xuXG4gIGlmICgodHlwZW9mIG1vZHVsZSAhPT0gXCJ1bmRlZmluZWRcIiAmJiBtb2R1bGUgIT09IG51bGwpICYmIG1vZHVsZS5leHBvcnRzKSB7XG4gICAgbW9kdWxlLmV4cG9ydHMgPSBpcGFkZHI7XG4gIH0gZWxzZSB7XG4gICAgcm9vdFsnaXBhZGRyJ10gPSBpcGFkZHI7XG4gIH1cblxuICBtYXRjaENJRFIgPSBmdW5jdGlvbihmaXJzdCwgc2Vjb25kLCBwYXJ0U2l6ZSwgY2lkckJpdHMpIHtcbiAgICB2YXIgcGFydCwgc2hpZnQ7XG4gICAgaWYgKGZpcnN0Lmxlbmd0aCAhPT0gc2Vjb25kLmxlbmd0aCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaXBhZGRyOiBjYW5ub3QgbWF0Y2ggQ0lEUiBmb3Igb2JqZWN0cyB3aXRoIGRpZmZlcmVudCBsZW5ndGhzXCIpO1xuICAgIH1cbiAgICBwYXJ0ID0gMDtcbiAgICB3aGlsZSAoY2lkckJpdHMgPiAwKSB7XG4gICAgICBzaGlmdCA9IHBhcnRTaXplIC0gY2lkckJpdHM7XG4gICAgICBpZiAoc2hpZnQgPCAwKSB7XG4gICAgICAgIHNoaWZ0ID0gMDtcbiAgICAgIH1cbiAgICAgIGlmIChmaXJzdFtwYXJ0XSA+PiBzaGlmdCAhPT0gc2Vjb25kW3BhcnRdID4+IHNoaWZ0KSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGNpZHJCaXRzIC09IHBhcnRTaXplO1xuICAgICAgcGFydCArPSAxO1xuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfTtcblxuICBpcGFkZHIuc3VibmV0TWF0Y2ggPSBmdW5jdGlvbihhZGRyZXNzLCByYW5nZUxpc3QsIGRlZmF1bHROYW1lKSB7XG4gICAgdmFyIGssIGxlbiwgcmFuZ2VOYW1lLCByYW5nZVN1Ym5ldHMsIHN1Ym5ldDtcbiAgICBpZiAoZGVmYXVsdE5hbWUgPT0gbnVsbCkge1xuICAgICAgZGVmYXVsdE5hbWUgPSAndW5pY2FzdCc7XG4gICAgfVxuICAgIGZvciAocmFuZ2VOYW1lIGluIHJhbmdlTGlzdCkge1xuICAgICAgcmFuZ2VTdWJuZXRzID0gcmFuZ2VMaXN0W3JhbmdlTmFtZV07XG4gICAgICBpZiAocmFuZ2VTdWJuZXRzWzBdICYmICEocmFuZ2VTdWJuZXRzWzBdIGluc3RhbmNlb2YgQXJyYXkpKSB7XG4gICAgICAgIHJhbmdlU3VibmV0cyA9IFtyYW5nZVN1Ym5ldHNdO1xuICAgICAgfVxuICAgICAgZm9yIChrID0gMCwgbGVuID0gcmFuZ2VTdWJuZXRzLmxlbmd0aDsgayA8IGxlbjsgaysrKSB7XG4gICAgICAgIHN1Ym5ldCA9IHJhbmdlU3VibmV0c1trXTtcbiAgICAgICAgaWYgKGFkZHJlc3Mua2luZCgpID09PSBzdWJuZXRbMF0ua2luZCgpKSB7XG4gICAgICAgICAgaWYgKGFkZHJlc3MubWF0Y2guYXBwbHkoYWRkcmVzcywgc3VibmV0KSkge1xuICAgICAgICAgICAgcmV0dXJuIHJhbmdlTmFtZTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGRlZmF1bHROYW1lO1xuICB9O1xuXG4gIGlwYWRkci5JUHY0ID0gKGZ1bmN0aW9uKCkge1xuICAgIGZ1bmN0aW9uIElQdjQob2N0ZXRzKSB7XG4gICAgICB2YXIgaywgbGVuLCBvY3RldDtcbiAgICAgIGlmIChvY3RldHMubGVuZ3RoICE9PSA0KSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogaXB2NCBvY3RldCBjb3VudCBzaG91bGQgYmUgNFwiKTtcbiAgICAgIH1cbiAgICAgIGZvciAoayA9IDAsIGxlbiA9IG9jdGV0cy5sZW5ndGg7IGsgPCBsZW47IGsrKykge1xuICAgICAgICBvY3RldCA9IG9jdGV0c1trXTtcbiAgICAgICAgaWYgKCEoKDAgPD0gb2N0ZXQgJiYgb2N0ZXQgPD0gMjU1KSkpIHtcbiAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpcGFkZHI6IGlwdjQgb2N0ZXQgc2hvdWxkIGZpdCBpbiA4IGJpdHNcIik7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICAgIHRoaXMub2N0ZXRzID0gb2N0ZXRzO1xuICAgIH1cblxuICAgIElQdjQucHJvdG90eXBlLmtpbmQgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiAnaXB2NCc7XG4gICAgfTtcblxuICAgIElQdjQucHJvdG90eXBlLnRvU3RyaW5nID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gdGhpcy5vY3RldHMuam9pbihcIi5cIik7XG4gICAgfTtcblxuICAgIElQdjQucHJvdG90eXBlLnRvTm9ybWFsaXplZFN0cmluZyA9IGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIHRoaXMudG9TdHJpbmcoKTtcbiAgICB9O1xuXG4gICAgSVB2NC5wcm90b3R5cGUudG9CeXRlQXJyYXkgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiB0aGlzLm9jdGV0cy5zbGljZSgwKTtcbiAgICB9O1xuXG4gICAgSVB2NC5wcm90b3R5cGUubWF0Y2ggPSBmdW5jdGlvbihvdGhlciwgY2lkclJhbmdlKSB7XG4gICAgICB2YXIgcmVmO1xuICAgICAgaWYgKGNpZHJSYW5nZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgIHJlZiA9IG90aGVyLCBvdGhlciA9IHJlZlswXSwgY2lkclJhbmdlID0gcmVmWzFdO1xuICAgICAgfVxuICAgICAgaWYgKG90aGVyLmtpbmQoKSAhPT0gJ2lwdjQnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogY2Fubm90IG1hdGNoIGlwdjQgYWRkcmVzcyB3aXRoIG5vbi1pcHY0IG9uZVwiKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBtYXRjaENJRFIodGhpcy5vY3RldHMsIG90aGVyLm9jdGV0cywgOCwgY2lkclJhbmdlKTtcbiAgICB9O1xuXG4gICAgSVB2NC5wcm90b3R5cGUuU3BlY2lhbFJhbmdlcyA9IHtcbiAgICAgIHVuc3BlY2lmaWVkOiBbW25ldyBJUHY0KFswLCAwLCAwLCAwXSksIDhdXSxcbiAgICAgIGJyb2FkY2FzdDogW1tuZXcgSVB2NChbMjU1LCAyNTUsIDI1NSwgMjU1XSksIDMyXV0sXG4gICAgICBtdWx0aWNhc3Q6IFtbbmV3IElQdjQoWzIyNCwgMCwgMCwgMF0pLCA0XV0sXG4gICAgICBsaW5rTG9jYWw6IFtbbmV3IElQdjQoWzE2OSwgMjU0LCAwLCAwXSksIDE2XV0sXG4gICAgICBsb29wYmFjazogW1tuZXcgSVB2NChbMTI3LCAwLCAwLCAwXSksIDhdXSxcbiAgICAgIGNhcnJpZXJHcmFkZU5hdDogW1tuZXcgSVB2NChbMTAwLCA2NCwgMCwgMF0pLCAxMF1dLFxuICAgICAgXCJwcml2YXRlXCI6IFtbbmV3IElQdjQoWzEwLCAwLCAwLCAwXSksIDhdLCBbbmV3IElQdjQoWzE3MiwgMTYsIDAsIDBdKSwgMTJdLCBbbmV3IElQdjQoWzE5MiwgMTY4LCAwLCAwXSksIDE2XV0sXG4gICAgICByZXNlcnZlZDogW1tuZXcgSVB2NChbMTkyLCAwLCAwLCAwXSksIDI0XSwgW25ldyBJUHY0KFsxOTIsIDAsIDIsIDBdKSwgMjRdLCBbbmV3IElQdjQoWzE5MiwgODgsIDk5LCAwXSksIDI0XSwgW25ldyBJUHY0KFsxOTgsIDUxLCAxMDAsIDBdKSwgMjRdLCBbbmV3IElQdjQoWzIwMywgMCwgMTEzLCAwXSksIDI0XSwgW25ldyBJUHY0KFsyNDAsIDAsIDAsIDBdKSwgNF1dXG4gICAgfTtcblxuICAgIElQdjQucHJvdG90eXBlLnJhbmdlID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gaXBhZGRyLnN1Ym5ldE1hdGNoKHRoaXMsIHRoaXMuU3BlY2lhbFJhbmdlcyk7XG4gICAgfTtcblxuICAgIElQdjQucHJvdG90eXBlLnRvSVB2NE1hcHBlZEFkZHJlc3MgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiBpcGFkZHIuSVB2Ni5wYXJzZShcIjo6ZmZmZjpcIiArICh0aGlzLnRvU3RyaW5nKCkpKTtcbiAgICB9O1xuXG4gICAgSVB2NC5wcm90b3R5cGUucHJlZml4TGVuZ3RoRnJvbVN1Ym5ldE1hc2sgPSBmdW5jdGlvbigpIHtcbiAgICAgIHZhciBjaWRyLCBpLCBrLCBvY3RldCwgc3RvcCwgemVyb3MsIHplcm90YWJsZTtcbiAgICAgIHplcm90YWJsZSA9IHtcbiAgICAgICAgMDogOCxcbiAgICAgICAgMTI4OiA3LFxuICAgICAgICAxOTI6IDYsXG4gICAgICAgIDIyNDogNSxcbiAgICAgICAgMjQwOiA0LFxuICAgICAgICAyNDg6IDMsXG4gICAgICAgIDI1MjogMixcbiAgICAgICAgMjU0OiAxLFxuICAgICAgICAyNTU6IDBcbiAgICAgIH07XG4gICAgICBjaWRyID0gMDtcbiAgICAgIHN0b3AgPSBmYWxzZTtcbiAgICAgIGZvciAoaSA9IGsgPSAzOyBrID49IDA7IGkgPSBrICs9IC0xKSB7XG4gICAgICAgIG9jdGV0ID0gdGhpcy5vY3RldHNbaV07XG4gICAgICAgIGlmIChvY3RldCBpbiB6ZXJvdGFibGUpIHtcbiAgICAgICAgICB6ZXJvcyA9IHplcm90YWJsZVtvY3RldF07XG4gICAgICAgICAgaWYgKHN0b3AgJiYgemVyb3MgIT09IDApIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoemVyb3MgIT09IDgpIHtcbiAgICAgICAgICAgIHN0b3AgPSB0cnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgICBjaWRyICs9IHplcm9zO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICByZXR1cm4gMzIgLSBjaWRyO1xuICAgIH07XG5cbiAgICByZXR1cm4gSVB2NDtcblxuICB9KSgpO1xuXG4gIGlwdjRQYXJ0ID0gXCIoMD9cXFxcZCt8MHhbYS1mMC05XSspXCI7XG5cbiAgaXB2NFJlZ2V4ZXMgPSB7XG4gICAgZm91ck9jdGV0OiBuZXcgUmVnRXhwKFwiXlwiICsgaXB2NFBhcnQgKyBcIlxcXFwuXCIgKyBpcHY0UGFydCArIFwiXFxcXC5cIiArIGlwdjRQYXJ0ICsgXCJcXFxcLlwiICsgaXB2NFBhcnQgKyBcIiRcIiwgJ2knKSxcbiAgICBsb25nVmFsdWU6IG5ldyBSZWdFeHAoXCJeXCIgKyBpcHY0UGFydCArIFwiJFwiLCAnaScpXG4gIH07XG5cbiAgaXBhZGRyLklQdjQucGFyc2VyID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIG1hdGNoLCBwYXJzZUludEF1dG8sIHBhcnQsIHNoaWZ0LCB2YWx1ZTtcbiAgICBwYXJzZUludEF1dG8gPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICAgIGlmIChzdHJpbmdbMF0gPT09IFwiMFwiICYmIHN0cmluZ1sxXSAhPT0gXCJ4XCIpIHtcbiAgICAgICAgcmV0dXJuIHBhcnNlSW50KHN0cmluZywgOCk7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByZXR1cm4gcGFyc2VJbnQoc3RyaW5nKTtcbiAgICAgIH1cbiAgICB9O1xuICAgIGlmIChtYXRjaCA9IHN0cmluZy5tYXRjaChpcHY0UmVnZXhlcy5mb3VyT2N0ZXQpKSB7XG4gICAgICByZXR1cm4gKGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgaywgbGVuLCByZWYsIHJlc3VsdHM7XG4gICAgICAgIHJlZiA9IG1hdGNoLnNsaWNlKDEsIDYpO1xuICAgICAgICByZXN1bHRzID0gW107XG4gICAgICAgIGZvciAoayA9IDAsIGxlbiA9IHJlZi5sZW5ndGg7IGsgPCBsZW47IGsrKykge1xuICAgICAgICAgIHBhcnQgPSByZWZba107XG4gICAgICAgICAgcmVzdWx0cy5wdXNoKHBhcnNlSW50QXV0byhwYXJ0KSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHJlc3VsdHM7XG4gICAgICB9KSgpO1xuICAgIH0gZWxzZSBpZiAobWF0Y2ggPSBzdHJpbmcubWF0Y2goaXB2NFJlZ2V4ZXMubG9uZ1ZhbHVlKSkge1xuICAgICAgdmFsdWUgPSBwYXJzZUludEF1dG8obWF0Y2hbMV0pO1xuICAgICAgaWYgKHZhbHVlID4gMHhmZmZmZmZmZiB8fCB2YWx1ZSA8IDApIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaXBhZGRyOiBhZGRyZXNzIG91dHNpZGUgZGVmaW5lZCByYW5nZVwiKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiAoKGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgaywgcmVzdWx0cztcbiAgICAgICAgcmVzdWx0cyA9IFtdO1xuICAgICAgICBmb3IgKHNoaWZ0ID0gayA9IDA7IGsgPD0gMjQ7IHNoaWZ0ID0gayArPSA4KSB7XG4gICAgICAgICAgcmVzdWx0cy5wdXNoKCh2YWx1ZSA+PiBzaGlmdCkgJiAweGZmKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0cztcbiAgICAgIH0pKCkpLnJldmVyc2UoKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICB9O1xuXG4gIGlwYWRkci5JUHY2ID0gKGZ1bmN0aW9uKCkge1xuICAgIGZ1bmN0aW9uIElQdjYocGFydHMsIHpvbmVJZCkge1xuICAgICAgdmFyIGksIGssIGwsIGxlbiwgcGFydCwgcmVmO1xuICAgICAgaWYgKHBhcnRzLmxlbmd0aCA9PT0gMTYpIHtcbiAgICAgICAgdGhpcy5wYXJ0cyA9IFtdO1xuICAgICAgICBmb3IgKGkgPSBrID0gMDsgayA8PSAxNDsgaSA9IGsgKz0gMikge1xuICAgICAgICAgIHRoaXMucGFydHMucHVzaCgocGFydHNbaV0gPDwgOCkgfCBwYXJ0c1tpICsgMV0pO1xuICAgICAgICB9XG4gICAgICB9IGVsc2UgaWYgKHBhcnRzLmxlbmd0aCA9PT0gOCkge1xuICAgICAgICB0aGlzLnBhcnRzID0gcGFydHM7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpcGFkZHI6IGlwdjYgcGFydCBjb3VudCBzaG91bGQgYmUgOCBvciAxNlwiKTtcbiAgICAgIH1cbiAgICAgIHJlZiA9IHRoaXMucGFydHM7XG4gICAgICBmb3IgKGwgPSAwLCBsZW4gPSByZWYubGVuZ3RoOyBsIDwgbGVuOyBsKyspIHtcbiAgICAgICAgcGFydCA9IHJlZltsXTtcbiAgICAgICAgaWYgKCEoKDAgPD0gcGFydCAmJiBwYXJ0IDw9IDB4ZmZmZikpKSB7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiaXBhZGRyOiBpcHY2IHBhcnQgc2hvdWxkIGZpdCBpbiAxNiBiaXRzXCIpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoem9uZUlkKSB7XG4gICAgICAgIHRoaXMuem9uZUlkID0gem9uZUlkO1xuICAgICAgfVxuICAgIH1cblxuICAgIElQdjYucHJvdG90eXBlLmtpbmQgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiAnaXB2Nic7XG4gICAgfTtcblxuICAgIElQdjYucHJvdG90eXBlLnRvU3RyaW5nID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gdGhpcy50b05vcm1hbGl6ZWRTdHJpbmcoKS5yZXBsYWNlKC8oKF58OikoMCg6fCQpKSspLywgJzo6Jyk7XG4gICAgfTtcblxuICAgIElQdjYucHJvdG90eXBlLnRvUkZDNTk1MlN0cmluZyA9IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIGJlc3RNYXRjaEluZGV4LCBiZXN0TWF0Y2hMZW5ndGgsIG1hdGNoLCByZWdleCwgc3RyaW5nO1xuICAgICAgcmVnZXggPSAvKChefDopKDAoOnwkKSl7Mix9KS9nO1xuICAgICAgc3RyaW5nID0gdGhpcy50b05vcm1hbGl6ZWRTdHJpbmcoKTtcbiAgICAgIGJlc3RNYXRjaEluZGV4ID0gMDtcbiAgICAgIGJlc3RNYXRjaExlbmd0aCA9IC0xO1xuICAgICAgd2hpbGUgKChtYXRjaCA9IHJlZ2V4LmV4ZWMoc3RyaW5nKSkpIHtcbiAgICAgICAgaWYgKG1hdGNoWzBdLmxlbmd0aCA+IGJlc3RNYXRjaExlbmd0aCkge1xuICAgICAgICAgIGJlc3RNYXRjaEluZGV4ID0gbWF0Y2guaW5kZXg7XG4gICAgICAgICAgYmVzdE1hdGNoTGVuZ3RoID0gbWF0Y2hbMF0ubGVuZ3RoO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAoYmVzdE1hdGNoTGVuZ3RoIDwgMCkge1xuICAgICAgICByZXR1cm4gc3RyaW5nO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHN0cmluZy5zdWJzdHJpbmcoMCwgYmVzdE1hdGNoSW5kZXgpICsgJzo6JyArIHN0cmluZy5zdWJzdHJpbmcoYmVzdE1hdGNoSW5kZXggKyBiZXN0TWF0Y2hMZW5ndGgpO1xuICAgIH07XG5cbiAgICBJUHY2LnByb3RvdHlwZS50b0J5dGVBcnJheSA9IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIGJ5dGVzLCBrLCBsZW4sIHBhcnQsIHJlZjtcbiAgICAgIGJ5dGVzID0gW107XG4gICAgICByZWYgPSB0aGlzLnBhcnRzO1xuICAgICAgZm9yIChrID0gMCwgbGVuID0gcmVmLmxlbmd0aDsgayA8IGxlbjsgaysrKSB7XG4gICAgICAgIHBhcnQgPSByZWZba107XG4gICAgICAgIGJ5dGVzLnB1c2gocGFydCA+PiA4KTtcbiAgICAgICAgYnl0ZXMucHVzaChwYXJ0ICYgMHhmZik7XG4gICAgICB9XG4gICAgICByZXR1cm4gYnl0ZXM7XG4gICAgfTtcblxuICAgIElQdjYucHJvdG90eXBlLnRvTm9ybWFsaXplZFN0cmluZyA9IGZ1bmN0aW9uKCkge1xuICAgICAgdmFyIGFkZHIsIHBhcnQsIHN1ZmZpeDtcbiAgICAgIGFkZHIgPSAoKGZ1bmN0aW9uKCkge1xuICAgICAgICB2YXIgaywgbGVuLCByZWYsIHJlc3VsdHM7XG4gICAgICAgIHJlZiA9IHRoaXMucGFydHM7XG4gICAgICAgIHJlc3VsdHMgPSBbXTtcbiAgICAgICAgZm9yIChrID0gMCwgbGVuID0gcmVmLmxlbmd0aDsgayA8IGxlbjsgaysrKSB7XG4gICAgICAgICAgcGFydCA9IHJlZltrXTtcbiAgICAgICAgICByZXN1bHRzLnB1c2gocGFydC50b1N0cmluZygxNikpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHRzO1xuICAgICAgfSkuY2FsbCh0aGlzKSkuam9pbihcIjpcIik7XG4gICAgICBzdWZmaXggPSAnJztcbiAgICAgIGlmICh0aGlzLnpvbmVJZCkge1xuICAgICAgICBzdWZmaXggPSAnJScgKyB0aGlzLnpvbmVJZDtcbiAgICAgIH1cbiAgICAgIHJldHVybiBhZGRyICsgc3VmZml4O1xuICAgIH07XG5cbiAgICBJUHY2LnByb3RvdHlwZS50b0ZpeGVkTGVuZ3RoU3RyaW5nID0gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgYWRkciwgcGFydCwgc3VmZml4O1xuICAgICAgYWRkciA9ICgoZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBrLCBsZW4sIHJlZiwgcmVzdWx0cztcbiAgICAgICAgcmVmID0gdGhpcy5wYXJ0cztcbiAgICAgICAgcmVzdWx0cyA9IFtdO1xuICAgICAgICBmb3IgKGsgPSAwLCBsZW4gPSByZWYubGVuZ3RoOyBrIDwgbGVuOyBrKyspIHtcbiAgICAgICAgICBwYXJ0ID0gcmVmW2tdO1xuICAgICAgICAgIHJlc3VsdHMucHVzaChwYXJ0LnRvU3RyaW5nKDE2KS5wYWRTdGFydCg0LCAnMCcpKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0cztcbiAgICAgIH0pLmNhbGwodGhpcykpLmpvaW4oXCI6XCIpO1xuICAgICAgc3VmZml4ID0gJyc7XG4gICAgICBpZiAodGhpcy56b25lSWQpIHtcbiAgICAgICAgc3VmZml4ID0gJyUnICsgdGhpcy56b25lSWQ7XG4gICAgICB9XG4gICAgICByZXR1cm4gYWRkciArIHN1ZmZpeDtcbiAgICB9O1xuXG4gICAgSVB2Ni5wcm90b3R5cGUubWF0Y2ggPSBmdW5jdGlvbihvdGhlciwgY2lkclJhbmdlKSB7XG4gICAgICB2YXIgcmVmO1xuICAgICAgaWYgKGNpZHJSYW5nZSA9PT0gdm9pZCAwKSB7XG4gICAgICAgIHJlZiA9IG90aGVyLCBvdGhlciA9IHJlZlswXSwgY2lkclJhbmdlID0gcmVmWzFdO1xuICAgICAgfVxuICAgICAgaWYgKG90aGVyLmtpbmQoKSAhPT0gJ2lwdjYnKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogY2Fubm90IG1hdGNoIGlwdjYgYWRkcmVzcyB3aXRoIG5vbi1pcHY2IG9uZVwiKTtcbiAgICAgIH1cbiAgICAgIHJldHVybiBtYXRjaENJRFIodGhpcy5wYXJ0cywgb3RoZXIucGFydHMsIDE2LCBjaWRyUmFuZ2UpO1xuICAgIH07XG5cbiAgICBJUHY2LnByb3RvdHlwZS5TcGVjaWFsUmFuZ2VzID0ge1xuICAgICAgdW5zcGVjaWZpZWQ6IFtuZXcgSVB2NihbMCwgMCwgMCwgMCwgMCwgMCwgMCwgMF0pLCAxMjhdLFxuICAgICAgbGlua0xvY2FsOiBbbmV3IElQdjYoWzB4ZmU4MCwgMCwgMCwgMCwgMCwgMCwgMCwgMF0pLCAxMF0sXG4gICAgICBtdWx0aWNhc3Q6IFtuZXcgSVB2NihbMHhmZjAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwXSksIDhdLFxuICAgICAgbG9vcGJhY2s6IFtuZXcgSVB2NihbMCwgMCwgMCwgMCwgMCwgMCwgMCwgMV0pLCAxMjhdLFxuICAgICAgdW5pcXVlTG9jYWw6IFtuZXcgSVB2NihbMHhmYzAwLCAwLCAwLCAwLCAwLCAwLCAwLCAwXSksIDddLFxuICAgICAgaXB2NE1hcHBlZDogW25ldyBJUHY2KFswLCAwLCAwLCAwLCAwLCAweGZmZmYsIDAsIDBdKSwgOTZdLFxuICAgICAgcmZjNjE0NTogW25ldyBJUHY2KFswLCAwLCAwLCAwLCAweGZmZmYsIDAsIDAsIDBdKSwgOTZdLFxuICAgICAgcmZjNjA1MjogW25ldyBJUHY2KFsweDY0LCAweGZmOWIsIDAsIDAsIDAsIDAsIDAsIDBdKSwgOTZdLFxuICAgICAgJzZ0bzQnOiBbbmV3IElQdjYoWzB4MjAwMiwgMCwgMCwgMCwgMCwgMCwgMCwgMF0pLCAxNl0sXG4gICAgICB0ZXJlZG86IFtuZXcgSVB2NihbMHgyMDAxLCAwLCAwLCAwLCAwLCAwLCAwLCAwXSksIDMyXSxcbiAgICAgIHJlc2VydmVkOiBbW25ldyBJUHY2KFsweDIwMDEsIDB4ZGI4LCAwLCAwLCAwLCAwLCAwLCAwXSksIDMyXV1cbiAgICB9O1xuXG4gICAgSVB2Ni5wcm90b3R5cGUucmFuZ2UgPSBmdW5jdGlvbigpIHtcbiAgICAgIHJldHVybiBpcGFkZHIuc3VibmV0TWF0Y2godGhpcywgdGhpcy5TcGVjaWFsUmFuZ2VzKTtcbiAgICB9O1xuXG4gICAgSVB2Ni5wcm90b3R5cGUuaXNJUHY0TWFwcGVkQWRkcmVzcyA9IGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIHRoaXMucmFuZ2UoKSA9PT0gJ2lwdjRNYXBwZWQnO1xuICAgIH07XG5cbiAgICBJUHY2LnByb3RvdHlwZS50b0lQdjRBZGRyZXNzID0gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgaGlnaCwgbG93LCByZWY7XG4gICAgICBpZiAoIXRoaXMuaXNJUHY0TWFwcGVkQWRkcmVzcygpKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogdHJ5aW5nIHRvIGNvbnZlcnQgYSBnZW5lcmljIGlwdjYgYWRkcmVzcyB0byBpcHY0XCIpO1xuICAgICAgfVxuICAgICAgcmVmID0gdGhpcy5wYXJ0cy5zbGljZSgtMiksIGhpZ2ggPSByZWZbMF0sIGxvdyA9IHJlZlsxXTtcbiAgICAgIHJldHVybiBuZXcgaXBhZGRyLklQdjQoW2hpZ2ggPj4gOCwgaGlnaCAmIDB4ZmYsIGxvdyA+PiA4LCBsb3cgJiAweGZmXSk7XG4gICAgfTtcblxuICAgIElQdjYucHJvdG90eXBlLnByZWZpeExlbmd0aEZyb21TdWJuZXRNYXNrID0gZnVuY3Rpb24oKSB7XG4gICAgICB2YXIgY2lkciwgaSwgaywgcGFydCwgc3RvcCwgemVyb3MsIHplcm90YWJsZTtcbiAgICAgIHplcm90YWJsZSA9IHtcbiAgICAgICAgMDogMTYsXG4gICAgICAgIDMyNzY4OiAxNSxcbiAgICAgICAgNDkxNTI6IDE0LFxuICAgICAgICA1NzM0NDogMTMsXG4gICAgICAgIDYxNDQwOiAxMixcbiAgICAgICAgNjM0ODg6IDExLFxuICAgICAgICA2NDUxMjogMTAsXG4gICAgICAgIDY1MDI0OiA5LFxuICAgICAgICA2NTI4MDogOCxcbiAgICAgICAgNjU0MDg6IDcsXG4gICAgICAgIDY1NDcyOiA2LFxuICAgICAgICA2NTUwNDogNSxcbiAgICAgICAgNjU1MjA6IDQsXG4gICAgICAgIDY1NTI4OiAzLFxuICAgICAgICA2NTUzMjogMixcbiAgICAgICAgNjU1MzQ6IDEsXG4gICAgICAgIDY1NTM1OiAwXG4gICAgICB9O1xuICAgICAgY2lkciA9IDA7XG4gICAgICBzdG9wID0gZmFsc2U7XG4gICAgICBmb3IgKGkgPSBrID0gNzsgayA+PSAwOyBpID0gayArPSAtMSkge1xuICAgICAgICBwYXJ0ID0gdGhpcy5wYXJ0c1tpXTtcbiAgICAgICAgaWYgKHBhcnQgaW4gemVyb3RhYmxlKSB7XG4gICAgICAgICAgemVyb3MgPSB6ZXJvdGFibGVbcGFydF07XG4gICAgICAgICAgaWYgKHN0b3AgJiYgemVyb3MgIT09IDApIHtcbiAgICAgICAgICAgIHJldHVybiBudWxsO1xuICAgICAgICAgIH1cbiAgICAgICAgICBpZiAoemVyb3MgIT09IDE2KSB7XG4gICAgICAgICAgICBzdG9wID0gdHJ1ZTtcbiAgICAgICAgICB9XG4gICAgICAgICAgY2lkciArPSB6ZXJvcztcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIDEyOCAtIGNpZHI7XG4gICAgfTtcblxuICAgIHJldHVybiBJUHY2O1xuXG4gIH0pKCk7XG5cbiAgaXB2NlBhcnQgPSBcIig/OlswLTlhLWZdKzo6PykrXCI7XG5cbiAgem9uZUluZGV4ID0gXCIlWzAtOWEtel17MSx9XCI7XG5cbiAgaXB2NlJlZ2V4ZXMgPSB7XG4gICAgem9uZUluZGV4OiBuZXcgUmVnRXhwKHpvbmVJbmRleCwgJ2knKSxcbiAgICBcIm5hdGl2ZVwiOiBuZXcgUmVnRXhwKFwiXig6Oik/KFwiICsgaXB2NlBhcnQgKyBcIik/KFswLTlhLWZdKyk/KDo6KT8oXCIgKyB6b25lSW5kZXggKyBcIik/JFwiLCAnaScpLFxuICAgIHRyYW5zaXRpb25hbDogbmV3IFJlZ0V4cCgoXCJeKCg/OlwiICsgaXB2NlBhcnQgKyBcIil8KD86OjopKD86XCIgKyBpcHY2UGFydCArIFwiKT8pXCIpICsgKGlwdjRQYXJ0ICsgXCJcXFxcLlwiICsgaXB2NFBhcnQgKyBcIlxcXFwuXCIgKyBpcHY0UGFydCArIFwiXFxcXC5cIiArIGlwdjRQYXJ0KSArIChcIihcIiArIHpvbmVJbmRleCArIFwiKT8kXCIpLCAnaScpXG4gIH07XG5cbiAgZXhwYW5kSVB2NiA9IGZ1bmN0aW9uKHN0cmluZywgcGFydHMpIHtcbiAgICB2YXIgY29sb25Db3VudCwgbGFzdENvbG9uLCBwYXJ0LCByZXBsYWNlbWVudCwgcmVwbGFjZW1lbnRDb3VudCwgem9uZUlkO1xuICAgIGlmIChzdHJpbmcuaW5kZXhPZignOjonKSAhPT0gc3RyaW5nLmxhc3RJbmRleE9mKCc6OicpKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgem9uZUlkID0gKHN0cmluZy5tYXRjaChpcHY2UmVnZXhlc1snem9uZUluZGV4J10pIHx8IFtdKVswXTtcbiAgICBpZiAoem9uZUlkKSB7XG4gICAgICB6b25lSWQgPSB6b25lSWQuc3Vic3RyaW5nKDEpO1xuICAgICAgc3RyaW5nID0gc3RyaW5nLnJlcGxhY2UoLyUuKyQvLCAnJyk7XG4gICAgfVxuICAgIGNvbG9uQ291bnQgPSAwO1xuICAgIGxhc3RDb2xvbiA9IC0xO1xuICAgIHdoaWxlICgobGFzdENvbG9uID0gc3RyaW5nLmluZGV4T2YoJzonLCBsYXN0Q29sb24gKyAxKSkgPj0gMCkge1xuICAgICAgY29sb25Db3VudCsrO1xuICAgIH1cbiAgICBpZiAoc3RyaW5nLnN1YnN0cigwLCAyKSA9PT0gJzo6Jykge1xuICAgICAgY29sb25Db3VudC0tO1xuICAgIH1cbiAgICBpZiAoc3RyaW5nLnN1YnN0cigtMiwgMikgPT09ICc6OicpIHtcbiAgICAgIGNvbG9uQ291bnQtLTtcbiAgICB9XG4gICAgaWYgKGNvbG9uQ291bnQgPiBwYXJ0cykge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIHJlcGxhY2VtZW50Q291bnQgPSBwYXJ0cyAtIGNvbG9uQ291bnQ7XG4gICAgcmVwbGFjZW1lbnQgPSAnOic7XG4gICAgd2hpbGUgKHJlcGxhY2VtZW50Q291bnQtLSkge1xuICAgICAgcmVwbGFjZW1lbnQgKz0gJzA6JztcbiAgICB9XG4gICAgc3RyaW5nID0gc3RyaW5nLnJlcGxhY2UoJzo6JywgcmVwbGFjZW1lbnQpO1xuICAgIGlmIChzdHJpbmdbMF0gPT09ICc6Jykge1xuICAgICAgc3RyaW5nID0gc3RyaW5nLnNsaWNlKDEpO1xuICAgIH1cbiAgICBpZiAoc3RyaW5nW3N0cmluZy5sZW5ndGggLSAxXSA9PT0gJzonKSB7XG4gICAgICBzdHJpbmcgPSBzdHJpbmcuc2xpY2UoMCwgLTEpO1xuICAgIH1cbiAgICBwYXJ0cyA9IChmdW5jdGlvbigpIHtcbiAgICAgIHZhciBrLCBsZW4sIHJlZiwgcmVzdWx0cztcbiAgICAgIHJlZiA9IHN0cmluZy5zcGxpdChcIjpcIik7XG4gICAgICByZXN1bHRzID0gW107XG4gICAgICBmb3IgKGsgPSAwLCBsZW4gPSByZWYubGVuZ3RoOyBrIDwgbGVuOyBrKyspIHtcbiAgICAgICAgcGFydCA9IHJlZltrXTtcbiAgICAgICAgcmVzdWx0cy5wdXNoKHBhcnNlSW50KHBhcnQsIDE2KSk7XG4gICAgICB9XG4gICAgICByZXR1cm4gcmVzdWx0cztcbiAgICB9KSgpO1xuICAgIHJldHVybiB7XG4gICAgICBwYXJ0czogcGFydHMsXG4gICAgICB6b25lSWQ6IHpvbmVJZFxuICAgIH07XG4gIH07XG5cbiAgaXBhZGRyLklQdjYucGFyc2VyID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIGFkZHIsIGssIGxlbiwgbWF0Y2gsIG9jdGV0LCBvY3RldHMsIHpvbmVJZDtcbiAgICBpZiAoaXB2NlJlZ2V4ZXNbJ25hdGl2ZSddLnRlc3Qoc3RyaW5nKSkge1xuICAgICAgcmV0dXJuIGV4cGFuZElQdjYoc3RyaW5nLCA4KTtcbiAgICB9IGVsc2UgaWYgKG1hdGNoID0gc3RyaW5nLm1hdGNoKGlwdjZSZWdleGVzWyd0cmFuc2l0aW9uYWwnXSkpIHtcbiAgICAgIHpvbmVJZCA9IG1hdGNoWzZdIHx8ICcnO1xuICAgICAgYWRkciA9IGV4cGFuZElQdjYobWF0Y2hbMV0uc2xpY2UoMCwgLTEpICsgem9uZUlkLCA2KTtcbiAgICAgIGlmIChhZGRyLnBhcnRzKSB7XG4gICAgICAgIG9jdGV0cyA9IFtwYXJzZUludChtYXRjaFsyXSksIHBhcnNlSW50KG1hdGNoWzNdKSwgcGFyc2VJbnQobWF0Y2hbNF0pLCBwYXJzZUludChtYXRjaFs1XSldO1xuICAgICAgICBmb3IgKGsgPSAwLCBsZW4gPSBvY3RldHMubGVuZ3RoOyBrIDwgbGVuOyBrKyspIHtcbiAgICAgICAgICBvY3RldCA9IG9jdGV0c1trXTtcbiAgICAgICAgICBpZiAoISgoMCA8PSBvY3RldCAmJiBvY3RldCA8PSAyNTUpKSkge1xuICAgICAgICAgICAgcmV0dXJuIG51bGw7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGFkZHIucGFydHMucHVzaChvY3RldHNbMF0gPDwgOCB8IG9jdGV0c1sxXSk7XG4gICAgICAgIGFkZHIucGFydHMucHVzaChvY3RldHNbMl0gPDwgOCB8IG9jdGV0c1szXSk7XG4gICAgICAgIHJldHVybiB7XG4gICAgICAgICAgcGFydHM6IGFkZHIucGFydHMsXG4gICAgICAgICAgem9uZUlkOiBhZGRyLnpvbmVJZFxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gbnVsbDtcbiAgfTtcblxuICBpcGFkZHIuSVB2NC5pc0lQdjQgPSBpcGFkZHIuSVB2Ni5pc0lQdjYgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5wYXJzZXIoc3RyaW5nKSAhPT0gbnVsbDtcbiAgfTtcblxuICBpcGFkZHIuSVB2NC5pc1ZhbGlkID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIGU7XG4gICAgdHJ5IHtcbiAgICAgIG5ldyB0aGlzKHRoaXMucGFyc2VyKHN0cmluZykpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfSBjYXRjaCAoZXJyb3IxKSB7XG4gICAgICBlID0gZXJyb3IxO1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICBpcGFkZHIuSVB2NC5pc1ZhbGlkRm91clBhcnREZWNpbWFsID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgaWYgKGlwYWRkci5JUHY0LmlzVmFsaWQoc3RyaW5nKSAmJiBzdHJpbmcubWF0Y2goL14oMHxbMS05XVxcZCopKFxcLigwfFsxLTldXFxkKikpezN9JC8pKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgfTtcblxuICBpcGFkZHIuSVB2Ni5pc1ZhbGlkID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIGFkZHIsIGU7XG4gICAgaWYgKHR5cGVvZiBzdHJpbmcgPT09IFwic3RyaW5nXCIgJiYgc3RyaW5nLmluZGV4T2YoXCI6XCIpID09PSAtMSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICB0cnkge1xuICAgICAgYWRkciA9IHRoaXMucGFyc2VyKHN0cmluZyk7XG4gICAgICBuZXcgdGhpcyhhZGRyLnBhcnRzLCBhZGRyLnpvbmVJZCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGNhdGNoIChlcnJvcjEpIHtcbiAgICAgIGUgPSBlcnJvcjE7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICB9O1xuXG4gIGlwYWRkci5JUHY0LnBhcnNlID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIHBhcnRzO1xuICAgIHBhcnRzID0gdGhpcy5wYXJzZXIoc3RyaW5nKTtcbiAgICBpZiAocGFydHMgPT09IG51bGwpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogc3RyaW5nIGlzIG5vdCBmb3JtYXR0ZWQgbGlrZSBpcCBhZGRyZXNzXCIpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IHRoaXMocGFydHMpO1xuICB9O1xuXG4gIGlwYWRkci5JUHY2LnBhcnNlID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIGFkZHI7XG4gICAgYWRkciA9IHRoaXMucGFyc2VyKHN0cmluZyk7XG4gICAgaWYgKGFkZHIucGFydHMgPT09IG51bGwpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogc3RyaW5nIGlzIG5vdCBmb3JtYXR0ZWQgbGlrZSBpcCBhZGRyZXNzXCIpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IHRoaXMoYWRkci5wYXJ0cywgYWRkci56b25lSWQpO1xuICB9O1xuXG4gIGlwYWRkci5JUHY0LnBhcnNlQ0lEUiA9IGZ1bmN0aW9uKHN0cmluZykge1xuICAgIHZhciBtYXNrTGVuZ3RoLCBtYXRjaCwgcGFyc2VkO1xuICAgIGlmIChtYXRjaCA9IHN0cmluZy5tYXRjaCgvXiguKylcXC8oXFxkKykkLykpIHtcbiAgICAgIG1hc2tMZW5ndGggPSBwYXJzZUludChtYXRjaFsyXSk7XG4gICAgICBpZiAobWFza0xlbmd0aCA+PSAwICYmIG1hc2tMZW5ndGggPD0gMzIpIHtcbiAgICAgICAgcGFyc2VkID0gW3RoaXMucGFyc2UobWF0Y2hbMV0pLCBtYXNrTGVuZ3RoXTtcbiAgICAgICAgT2JqZWN0LmRlZmluZVByb3BlcnR5KHBhcnNlZCwgJ3RvU3RyaW5nJywge1xuICAgICAgICAgIHZhbHVlOiBmdW5jdGlvbigpIHtcbiAgICAgICAgICAgIHJldHVybiB0aGlzLmpvaW4oJy8nKTtcbiAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICByZXR1cm4gcGFyc2VkO1xuICAgICAgfVxuICAgIH1cbiAgICB0aHJvdyBuZXcgRXJyb3IoXCJpcGFkZHI6IHN0cmluZyBpcyBub3QgZm9ybWF0dGVkIGxpa2UgYW4gSVB2NCBDSURSIHJhbmdlXCIpO1xuICB9O1xuXG4gIGlwYWRkci5JUHY0LnN1Ym5ldE1hc2tGcm9tUHJlZml4TGVuZ3RoID0gZnVuY3Rpb24ocHJlZml4KSB7XG4gICAgdmFyIGZpbGxlZE9jdGV0Q291bnQsIGosIG9jdGV0cztcbiAgICBwcmVmaXggPSBwYXJzZUludChwcmVmaXgpO1xuICAgIGlmIChwcmVmaXggPCAwIHx8IHByZWZpeCA+IDMyKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoJ2lwYWRkcjogaW52YWxpZCBJUHY0IHByZWZpeCBsZW5ndGgnKTtcbiAgICB9XG4gICAgb2N0ZXRzID0gWzAsIDAsIDAsIDBdO1xuICAgIGogPSAwO1xuICAgIGZpbGxlZE9jdGV0Q291bnQgPSBNYXRoLmZsb29yKHByZWZpeCAvIDgpO1xuICAgIHdoaWxlIChqIDwgZmlsbGVkT2N0ZXRDb3VudCkge1xuICAgICAgb2N0ZXRzW2pdID0gMjU1O1xuICAgICAgaisrO1xuICAgIH1cbiAgICBpZiAoZmlsbGVkT2N0ZXRDb3VudCA8IDQpIHtcbiAgICAgIG9jdGV0c1tmaWxsZWRPY3RldENvdW50XSA9IE1hdGgucG93KDIsIHByZWZpeCAlIDgpIC0gMSA8PCA4IC0gKHByZWZpeCAlIDgpO1xuICAgIH1cbiAgICByZXR1cm4gbmV3IHRoaXMob2N0ZXRzKTtcbiAgfTtcblxuICBpcGFkZHIuSVB2NC5icm9hZGNhc3RBZGRyZXNzRnJvbUNJRFIgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICB2YXIgY2lkciwgZXJyb3IsIGksIGlwSW50ZXJmYWNlT2N0ZXRzLCBvY3RldHMsIHN1Ym5ldE1hc2tPY3RldHM7XG4gICAgdHJ5IHtcbiAgICAgIGNpZHIgPSB0aGlzLnBhcnNlQ0lEUihzdHJpbmcpO1xuICAgICAgaXBJbnRlcmZhY2VPY3RldHMgPSBjaWRyWzBdLnRvQnl0ZUFycmF5KCk7XG4gICAgICBzdWJuZXRNYXNrT2N0ZXRzID0gdGhpcy5zdWJuZXRNYXNrRnJvbVByZWZpeExlbmd0aChjaWRyWzFdKS50b0J5dGVBcnJheSgpO1xuICAgICAgb2N0ZXRzID0gW107XG4gICAgICBpID0gMDtcbiAgICAgIHdoaWxlIChpIDwgNCkge1xuICAgICAgICBvY3RldHMucHVzaChwYXJzZUludChpcEludGVyZmFjZU9jdGV0c1tpXSwgMTApIHwgcGFyc2VJbnQoc3VibmV0TWFza09jdGV0c1tpXSwgMTApIF4gMjU1KTtcbiAgICAgICAgaSsrO1xuICAgICAgfVxuICAgICAgcmV0dXJuIG5ldyB0aGlzKG9jdGV0cyk7XG4gICAgfSBjYXRjaCAoZXJyb3IxKSB7XG4gICAgICBlcnJvciA9IGVycm9yMTtcbiAgICAgIHRocm93IG5ldyBFcnJvcignaXBhZGRyOiB0aGUgYWRkcmVzcyBkb2VzIG5vdCBoYXZlIElQdjQgQ0lEUiBmb3JtYXQnKTtcbiAgICB9XG4gIH07XG5cbiAgaXBhZGRyLklQdjQubmV0d29ya0FkZHJlc3NGcm9tQ0lEUiA9IGZ1bmN0aW9uKHN0cmluZykge1xuICAgIHZhciBjaWRyLCBlcnJvciwgaSwgaXBJbnRlcmZhY2VPY3RldHMsIG9jdGV0cywgc3VibmV0TWFza09jdGV0cztcbiAgICB0cnkge1xuICAgICAgY2lkciA9IHRoaXMucGFyc2VDSURSKHN0cmluZyk7XG4gICAgICBpcEludGVyZmFjZU9jdGV0cyA9IGNpZHJbMF0udG9CeXRlQXJyYXkoKTtcbiAgICAgIHN1Ym5ldE1hc2tPY3RldHMgPSB0aGlzLnN1Ym5ldE1hc2tGcm9tUHJlZml4TGVuZ3RoKGNpZHJbMV0pLnRvQnl0ZUFycmF5KCk7XG4gICAgICBvY3RldHMgPSBbXTtcbiAgICAgIGkgPSAwO1xuICAgICAgd2hpbGUgKGkgPCA0KSB7XG4gICAgICAgIG9jdGV0cy5wdXNoKHBhcnNlSW50KGlwSW50ZXJmYWNlT2N0ZXRzW2ldLCAxMCkgJiBwYXJzZUludChzdWJuZXRNYXNrT2N0ZXRzW2ldLCAxMCkpO1xuICAgICAgICBpKys7XG4gICAgICB9XG4gICAgICByZXR1cm4gbmV3IHRoaXMob2N0ZXRzKTtcbiAgICB9IGNhdGNoIChlcnJvcjEpIHtcbiAgICAgIGVycm9yID0gZXJyb3IxO1xuICAgICAgdGhyb3cgbmV3IEVycm9yKCdpcGFkZHI6IHRoZSBhZGRyZXNzIGRvZXMgbm90IGhhdmUgSVB2NCBDSURSIGZvcm1hdCcpO1xuICAgIH1cbiAgfTtcblxuICBpcGFkZHIuSVB2Ni5wYXJzZUNJRFIgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICB2YXIgbWFza0xlbmd0aCwgbWF0Y2gsIHBhcnNlZDtcbiAgICBpZiAobWF0Y2ggPSBzdHJpbmcubWF0Y2goL14oLispXFwvKFxcZCspJC8pKSB7XG4gICAgICBtYXNrTGVuZ3RoID0gcGFyc2VJbnQobWF0Y2hbMl0pO1xuICAgICAgaWYgKG1hc2tMZW5ndGggPj0gMCAmJiBtYXNrTGVuZ3RoIDw9IDEyOCkge1xuICAgICAgICBwYXJzZWQgPSBbdGhpcy5wYXJzZShtYXRjaFsxXSksIG1hc2tMZW5ndGhdO1xuICAgICAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkocGFyc2VkLCAndG9TdHJpbmcnLCB7XG4gICAgICAgICAgdmFsdWU6IGZ1bmN0aW9uKCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuam9pbignLycpO1xuICAgICAgICAgIH1cbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBwYXJzZWQ7XG4gICAgICB9XG4gICAgfVxuICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogc3RyaW5nIGlzIG5vdCBmb3JtYXR0ZWQgbGlrZSBhbiBJUHY2IENJRFIgcmFuZ2VcIik7XG4gIH07XG5cbiAgaXBhZGRyLmlzVmFsaWQgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICByZXR1cm4gaXBhZGRyLklQdjYuaXNWYWxpZChzdHJpbmcpIHx8IGlwYWRkci5JUHY0LmlzVmFsaWQoc3RyaW5nKTtcbiAgfTtcblxuICBpcGFkZHIucGFyc2UgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICBpZiAoaXBhZGRyLklQdjYuaXNWYWxpZChzdHJpbmcpKSB7XG4gICAgICByZXR1cm4gaXBhZGRyLklQdjYucGFyc2Uoc3RyaW5nKTtcbiAgICB9IGVsc2UgaWYgKGlwYWRkci5JUHY0LmlzVmFsaWQoc3RyaW5nKSkge1xuICAgICAgcmV0dXJuIGlwYWRkci5JUHY0LnBhcnNlKHN0cmluZyk7XG4gICAgfSBlbHNlIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcihcImlwYWRkcjogdGhlIGFkZHJlc3MgaGFzIG5laXRoZXIgSVB2NiBub3IgSVB2NCBmb3JtYXRcIik7XG4gICAgfVxuICB9O1xuXG4gIGlwYWRkci5wYXJzZUNJRFIgPSBmdW5jdGlvbihzdHJpbmcpIHtcbiAgICB2YXIgZTtcbiAgICB0cnkge1xuICAgICAgcmV0dXJuIGlwYWRkci5JUHY2LnBhcnNlQ0lEUihzdHJpbmcpO1xuICAgIH0gY2F0Y2ggKGVycm9yMSkge1xuICAgICAgZSA9IGVycm9yMTtcbiAgICAgIHRyeSB7XG4gICAgICAgIHJldHVybiBpcGFkZHIuSVB2NC5wYXJzZUNJRFIoc3RyaW5nKTtcbiAgICAgIH0gY2F0Y2ggKGVycm9yMSkge1xuICAgICAgICBlID0gZXJyb3IxO1xuICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpcGFkZHI6IHRoZSBhZGRyZXNzIGhhcyBuZWl0aGVyIElQdjYgbm9yIElQdjQgQ0lEUiBmb3JtYXRcIik7XG4gICAgICB9XG4gICAgfVxuICB9O1xuXG4gIGlwYWRkci5mcm9tQnl0ZUFycmF5ID0gZnVuY3Rpb24oYnl0ZXMpIHtcbiAgICB2YXIgbGVuZ3RoO1xuICAgIGxlbmd0aCA9IGJ5dGVzLmxlbmd0aDtcbiAgICBpZiAobGVuZ3RoID09PSA0KSB7XG4gICAgICByZXR1cm4gbmV3IGlwYWRkci5JUHY0KGJ5dGVzKTtcbiAgICB9IGVsc2UgaWYgKGxlbmd0aCA9PT0gMTYpIHtcbiAgICAgIHJldHVybiBuZXcgaXBhZGRyLklQdjYoYnl0ZXMpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXCJpcGFkZHI6IHRoZSBiaW5hcnkgaW5wdXQgaXMgbmVpdGhlciBhbiBJUHY2IG5vciBJUHY0IGFkZHJlc3NcIik7XG4gICAgfVxuICB9O1xuXG4gIGlwYWRkci5wcm9jZXNzID0gZnVuY3Rpb24oc3RyaW5nKSB7XG4gICAgdmFyIGFkZHI7XG4gICAgYWRkciA9IHRoaXMucGFyc2Uoc3RyaW5nKTtcbiAgICBpZiAoYWRkci5raW5kKCkgPT09ICdpcHY2JyAmJiBhZGRyLmlzSVB2NE1hcHBlZEFkZHJlc3MoKSkge1xuICAgICAgcmV0dXJuIGFkZHIudG9JUHY0QWRkcmVzcygpO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXR1cm4gYWRkcjtcbiAgICB9XG4gIH07XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/ipaddr.js@1.9.1/node_modules/ipaddr.js/lib/ipaddr.js\n");

/***/ })

};
;