"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/find-my-way-ts@0.1.5";
exports.ids = ["vendor-chunks/find-my-way-ts@0.1.5"];
exports.modules = {

/***/ "(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/QueryString.js":
/*!*****************************************************************************************************!*\
  !*** ./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/QueryString.js ***!
  \*****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse),\n/* harmony export */   stringify: () => (/* binding */ stringify)\n/* harmony export */ });\n/**\n * @since 1.0.0\n */\n// Taken from https://github.com/anonrig/fast-querystring under MIT License\nconst plusRegex = /\\+/g;\nconst Empty = function () {};\nEmpty.prototype = /*#__PURE__*/Object.create(null);\n/**\n * @category parsing\n * @since 1.0.0\n */\nfunction parse(input) {\n  // Optimization: Use new Empty() instead of Object.create(null) for performance\n  // v8 has a better optimization for initializing functions compared to Object\n  const result = new Empty();\n  if (typeof input !== \"string\") {\n    return result;\n  }\n  const inputLength = input.length;\n  let key = \"\";\n  let value = \"\";\n  let startingIndex = -1;\n  let equalityIndex = -1;\n  let shouldDecodeKey = false;\n  let shouldDecodeValue = false;\n  let keyHasPlus = false;\n  let valueHasPlus = false;\n  let hasBothKeyValuePair = false;\n  let c = 0;\n  // Have a boundary of input.length + 1 to access last pair inside the loop.\n  for (let i = 0; i < inputLength + 1; i++) {\n    c = i !== inputLength ? input.charCodeAt(i) : 38;\n    // Handle '&' and end of line to pass the current values to result\n    if (c === 38) {\n      hasBothKeyValuePair = equalityIndex > startingIndex;\n      // Optimization: Reuse equality index to store the end of key\n      if (!hasBothKeyValuePair) {\n        equalityIndex = i;\n      }\n      key = input.slice(startingIndex + 1, equalityIndex);\n      // Add key/value pair only if the range size is greater than 1; a.k.a. contains at least \"=\"\n      if (hasBothKeyValuePair || key.length > 0) {\n        // Optimization: Replace '+' with space\n        if (keyHasPlus) {\n          key = key.replace(plusRegex, \" \");\n        }\n        // Optimization: Do not decode if it's not necessary.\n        if (shouldDecodeKey) {\n          key = decodeURIComponent(key) || key;\n        }\n        if (hasBothKeyValuePair) {\n          value = input.slice(equalityIndex + 1, i);\n          if (valueHasPlus) {\n            value = value.replace(plusRegex, \" \");\n          }\n          if (shouldDecodeValue) {\n            value = decodeURIComponent(value) || value;\n          }\n        }\n        const currentValue = result[key];\n        if (currentValue === undefined) {\n          result[key] = value;\n        } else {\n          // Optimization: value.pop is faster than Array.isArray(value)\n          if (currentValue.pop) {\n            currentValue.push(value);\n          } else {\n            result[key] = [currentValue, value];\n          }\n        }\n      }\n      // Reset reading key value pairs\n      value = \"\";\n      startingIndex = i;\n      equalityIndex = i;\n      shouldDecodeKey = false;\n      shouldDecodeValue = false;\n      keyHasPlus = false;\n      valueHasPlus = false;\n    }\n    // Check '='\n    else if (c === 61) {\n      if (equalityIndex <= startingIndex) {\n        equalityIndex = i;\n      }\n      // If '=' character occurs again, we should decode the input.\n      else {\n        shouldDecodeValue = true;\n      }\n    }\n    // Check '+', and remember to replace it with empty space.\n    else if (c === 43) {\n      if (equalityIndex > startingIndex) {\n        valueHasPlus = true;\n      } else {\n        keyHasPlus = true;\n      }\n    }\n    // Check '%' character for encoding\n    else if (c === 37) {\n      if (equalityIndex > startingIndex) {\n        shouldDecodeValue = true;\n      } else {\n        shouldDecodeKey = true;\n      }\n    }\n  }\n  return result;\n}\nfunction getAsPrimitive(value) {\n  const type = typeof value;\n  if (type === \"string\") {\n    // Length check is handled inside encodeString function\n    return encodeString(value);\n  } else if (type === \"bigint\" || type === \"boolean\") {\n    return \"\" + value;\n  } else if (type === \"number\" && Number.isFinite(value)) {\n    return value < 1e21 ? \"\" + value : encodeString(\"\" + value);\n  }\n  return \"\";\n}\n/**\n * @category encoding\n * @since 1.0.0\n */\nfunction stringify(input) {\n  let result = \"\";\n  if (input === null || typeof input !== \"object\") {\n    return result;\n  }\n  const separator = \"&\";\n  const keys = Object.keys(input);\n  const keyLength = keys.length;\n  let valueLength = 0;\n  for (let i = 0; i < keyLength; i++) {\n    const key = keys[i];\n    const value = input[key];\n    const encodedKey = encodeString(key) + \"=\";\n    if (i) {\n      result += separator;\n    }\n    if (Array.isArray(value)) {\n      valueLength = value.length;\n      for (let j = 0; j < valueLength; j++) {\n        if (j) {\n          result += separator;\n        }\n        // Optimization: Dividing into multiple lines improves the performance.\n        // Since v8 does not need to care about the '+' character if it was one-liner.\n        result += encodedKey;\n        result += getAsPrimitive(value[j]);\n      }\n    } else {\n      result += encodedKey;\n      result += getAsPrimitive(value);\n    }\n  }\n  return result;\n}\n// -----------------------------------------------------------------------------\n// This has been taken from Node.js project.\n// Full implementation can be found from https://github.com/nodejs/node/blob/main/lib/internal/querystring.js\nconst hexTable = /*#__PURE__*/Array.from({\n  length: 256\n}, (_, i) => \"%\" + ((i < 16 ? \"0\" : \"\") + i.toString(16)).toUpperCase());\n// These characters do not need escaping when generating query strings:\n// ! - . _ ~\n// ' ( ) *\n// digits\n// alpha (uppercase)\n// alpha (lowercase)\n// biome-ignore format: the array should not be formatted\nconst noEscape = /*#__PURE__*/new Int8Array([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n// 0 - 15\n0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,\n// 16 - 31\n0, 1, 0, 0, 0, 0, 0, 1, 1, 1, 1, 0, 0, 1, 1, 0,\n// 32 - 47\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 0, 0,\n// 48 - 63\n0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n// 64 - 79\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 0, 1,\n// 80 - 95\n0, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1,\n// 96 - 111\n1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 0, 0, 0, 1, 0 // 112 - 127\n]);\nfunction encodeString(str) {\n  const len = str.length;\n  if (len === 0) return \"\";\n  let out = \"\";\n  let lastPos = 0;\n  let i = 0;\n  outer: for (; i < len; i++) {\n    let c = str.charCodeAt(i);\n    // ASCII\n    while (c < 0x80) {\n      if (noEscape[c] !== 1) {\n        if (lastPos < i) out += str.slice(lastPos, i);\n        lastPos = i + 1;\n        out += hexTable[c];\n      }\n      if (++i === len) break outer;\n      c = str.charCodeAt(i);\n    }\n    if (lastPos < i) out += str.slice(lastPos, i);\n    // Multi-byte characters ...\n    if (c < 0x800) {\n      lastPos = i + 1;\n      out += hexTable[0xc0 | c >> 6] + hexTable[0x80 | c & 0x3f];\n      continue;\n    }\n    if (c < 0xd800 || c >= 0xe000) {\n      lastPos = i + 1;\n      out += hexTable[0xe0 | c >> 12] + hexTable[0x80 | c >> 6 & 0x3f] + hexTable[0x80 | c & 0x3f];\n      continue;\n    }\n    // Surrogate pair\n    ++i;\n    // This branch should never happen because all URLSearchParams entries\n    // should already be converted to USVString. But, included for\n    // completion's sake anyway.\n    if (i >= len) {\n      throw new Error(\"URI malformed\");\n    }\n    const c2 = str.charCodeAt(i) & 0x3ff;\n    lastPos = i + 1;\n    c = 0x10000 + ((c & 0x3ff) << 10 | c2);\n    out += hexTable[0xf0 | c >> 18] + hexTable[0x80 | c >> 12 & 0x3f] + hexTable[0x80 | c >> 6 & 0x3f] + hexTable[0x80 | c & 0x3f];\n  }\n  if (lastPos === 0) return str;\n  if (lastPos < len) return out + str.slice(lastPos);\n  return out;\n}\n//# sourceMappingURL=QueryString.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/QueryString.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/index.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\n/* harmony import */ var _internal_router_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internal/router.js */ \"(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/internal/router.js\");\n/**\n * @since 1.0.0\n */\n\n/**\n * @since 1.0.0\n * @category constructors\n */\nconst make = _internal_router_js__WEBPACK_IMPORTED_MODULE_0__.make;\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9maW5kLW15LXdheS10c0AwLjEuNS9ub2RlX21vZHVsZXMvZmluZC1teS13YXktdHMvZGlzdC9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDaUQ7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDTyxhQUFhLHFEQUFhO0FBQ2pDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2ZpbmQtbXktd2F5LXRzQDAuMS41L25vZGVfbW9kdWxlcy9maW5kLW15LXdheS10cy9kaXN0L2VzbS9pbmRleC5qcz9hMDQ4Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQHNpbmNlIDEuMC4wXG4gKi9cbmltcG9ydCAqIGFzIGludGVybmFsIGZyb20gXCIuL2ludGVybmFsL3JvdXRlci5qc1wiO1xuLyoqXG4gKiBAc2luY2UgMS4wLjBcbiAqIEBjYXRlZ29yeSBjb25zdHJ1Y3RvcnNcbiAqL1xuZXhwb3J0IGNvbnN0IG1ha2UgPSBpbnRlcm5hbC5tYWtlO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/internal/router.js":
/*!*********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/internal/router.js ***!
  \*********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   make: () => (/* binding */ make)\n/* harmony export */ });\n/* harmony import */ var _QueryString_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../QueryString.js */ \"(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/QueryString.js\");\n\nconst FULL_PATH_REGEXP = /^https?:\\/\\/.*?\\//;\nconst OPTIONAL_PARAM_REGEXP = /(\\/:[^/()]*?)\\?(\\/?)/;\n/** @internal */\nconst make = (options = {}) => new RouterImpl(options);\nclass RouterImpl {\n  constructor(options = {}) {\n    this.options = {\n      ignoreTrailingSlash: true,\n      ignoreDuplicateSlashes: true,\n      caseSensitive: false,\n      maxParamLength: 100,\n      ...options\n    };\n  }\n  options;\n  routes = [];\n  trees = {};\n  on(method, path, handler) {\n    const optionalParamMatch = path.match(OPTIONAL_PARAM_REGEXP);\n    if (optionalParamMatch && optionalParamMatch.index !== undefined) {\n      assert(path.length === optionalParamMatch.index + optionalParamMatch[0].length, \"Optional Parameter needs to be the last parameter of the path\");\n      const pathFull = path.replace(OPTIONAL_PARAM_REGEXP, \"$1$2\");\n      const pathOptional = path.replace(OPTIONAL_PARAM_REGEXP, \"$2\");\n      this.on(method, pathFull, handler);\n      this.on(method, pathOptional, handler);\n      return;\n    }\n    if (this.options.ignoreDuplicateSlashes) {\n      path = removeDuplicateSlashes(path);\n    }\n    if (this.options.ignoreTrailingSlash) {\n      path = trimLastSlash(path);\n    }\n    const methods = typeof method === \"string\" ? [method] : method;\n    for (const method of methods) {\n      this._on(method, path, handler);\n    }\n  }\n  all(path, handler) {\n    this.on(httpMethods, path, handler);\n  }\n  _on(method, path, handler) {\n    if (this.trees[method] === undefined) {\n      this.trees[method] = new StaticNode(\"/\");\n    }\n    let pattern = path;\n    if (pattern === \"*\" && this.trees[method].prefix.length !== 0) {\n      const currentRoot = this.trees[method];\n      this.trees[method] = new StaticNode(\"\");\n      this.trees[method].staticChildren[\"/\"] = currentRoot;\n    }\n    let parentNodePathIndex = this.trees[method].prefix.length;\n    let currentNode = this.trees[method];\n    const params = [];\n    for (let i = 0; i <= pattern.length; i++) {\n      if (pattern.charCodeAt(i) === 58 && pattern.charCodeAt(i + 1) === 58) {\n        // It's a double colon\n        i++;\n        continue;\n      }\n      const isParametricNode = pattern.charCodeAt(i) === 58 && pattern.charCodeAt(i + 1) !== 58;\n      const isWildcardNode = pattern.charCodeAt(i) === 42;\n      if (isParametricNode || isWildcardNode || i === pattern.length && i !== parentNodePathIndex) {\n        let staticNodePath = pattern.slice(parentNodePathIndex, i);\n        if (!this.options.caseSensitive) {\n          staticNodePath = staticNodePath.toLowerCase();\n        }\n        staticNodePath = staticNodePath.split(\"::\").join(\":\");\n        staticNodePath = staticNodePath.split(\"%\").join(\"%25\");\n        // add the static part of the route to the tree\n        currentNode = currentNode.createStaticChild(staticNodePath);\n      }\n      if (isParametricNode) {\n        let isRegexNode = false;\n        const regexps = [];\n        let lastParamStartIndex = i + 1;\n        for (let j = lastParamStartIndex;; j++) {\n          const charCode = pattern.charCodeAt(j);\n          const isRegexParam = charCode === 40;\n          const isStaticPart = charCode === 45 || charCode === 46;\n          const isEndOfNode = charCode === 47 || j === pattern.length;\n          if (isRegexParam || isStaticPart || isEndOfNode) {\n            const paramName = pattern.slice(lastParamStartIndex, j);\n            params.push(paramName);\n            isRegexNode = isRegexNode || isRegexParam || isStaticPart;\n            if (isRegexParam) {\n              const endOfRegexIndex = getClosingParenthensePosition(pattern, j);\n              const regexString = pattern.slice(j, endOfRegexIndex + 1);\n              regexps.push(trimRegExpStartAndEnd(regexString));\n              j = endOfRegexIndex + 1;\n            } else {\n              regexps.push(\"(.*?)\");\n            }\n            const staticPartStartIndex = j;\n            for (; j < pattern.length; j++) {\n              const charCode = pattern.charCodeAt(j);\n              if (charCode === 47) break;\n              if (charCode === 58) {\n                const nextCharCode = pattern.charCodeAt(j + 1);\n                if (nextCharCode === 58) j++;else break;\n              }\n            }\n            let staticPart = pattern.slice(staticPartStartIndex, j);\n            if (staticPart) {\n              staticPart = staticPart.split(\"::\").join(\":\");\n              staticPart = staticPart.split(\"%\").join(\"%25\");\n              regexps.push(escapeRegExp(staticPart));\n            }\n            lastParamStartIndex = j + 1;\n            if (isEndOfNode || pattern.charCodeAt(j) === 47 || j === pattern.length) {\n              const nodePattern = isRegexNode ? \"()\" + staticPart : staticPart;\n              const nodePath = pattern.slice(i, j);\n              pattern = pattern.slice(0, i + 1) + nodePattern + pattern.slice(j);\n              i += nodePattern.length;\n              const regex = isRegexNode ? new RegExp(\"^\" + regexps.join(\"\") + \"$\") : undefined;\n              currentNode = currentNode.createParametricChild(regex, staticPart, nodePath);\n              parentNodePathIndex = i + 1;\n              break;\n            }\n          }\n        }\n      } else if (isWildcardNode) {\n        // add the wildcard parameter\n        params.push(\"*\");\n        currentNode = currentNode.createWildcardChild();\n        parentNodePathIndex = i + 1;\n        if (i !== pattern.length - 1) {\n          throw new Error(\"Wildcard must be the last character in the route\");\n        }\n      }\n    }\n    if (!this.options.caseSensitive) {\n      pattern = pattern.toLowerCase();\n    }\n    if (pattern === \"*\") {\n      pattern = \"/*\";\n    }\n    for (const existRoute of this.routes) {\n      if (existRoute.method === method && existRoute.pattern === pattern) {\n        throw new Error(`Method '${method}' already declared for route '${pattern}'`);\n      }\n    }\n    const route = {\n      method,\n      path,\n      pattern,\n      params,\n      handler\n    };\n    this.routes.push(route);\n    currentNode.addRoute(route);\n  }\n  has(method, path) {\n    const node = this.trees[method];\n    if (node === undefined) {\n      return false;\n    }\n    const staticNode = node.getStaticChild(path);\n    if (staticNode === undefined) {\n      return false;\n    }\n    return staticNode.isLeafNode;\n  }\n  find(method, path) {\n    let currentNode = this.trees[method];\n    if (currentNode === undefined) return undefined;\n    if (path.charCodeAt(0) !== 47) {\n      // 47 is '/'\n      path = path.replace(FULL_PATH_REGEXP, \"/\");\n    }\n    // This must be run before sanitizeUrl as the resulting function\n    // .sliceParameter must be constructed with same URL string used\n    // throughout the rest of this function.\n    if (this.options.ignoreDuplicateSlashes) {\n      path = removeDuplicateSlashes(path);\n    }\n    let sanitizedUrl;\n    let querystring;\n    let shouldDecodeParam;\n    try {\n      sanitizedUrl = safeDecodeURI(path);\n      path = sanitizedUrl.path;\n      querystring = sanitizedUrl.querystring;\n      shouldDecodeParam = sanitizedUrl.shouldDecodeParam;\n    } catch (error) {\n      return undefined;\n    }\n    if (this.options.ignoreTrailingSlash) {\n      path = trimLastSlash(path);\n    }\n    const originPath = path;\n    if (this.options.caseSensitive === false) {\n      path = path.toLowerCase();\n    }\n    const maxParamLength = this.options.maxParamLength;\n    let pathIndex = currentNode.prefix.length;\n    const params = [];\n    const pathLen = path.length;\n    const brothersNodesStack = [];\n    while (true) {\n      if (pathIndex === pathLen && currentNode.isLeafNode) {\n        const handle = currentNode.handlerStorage?.find();\n        if (handle !== undefined) {\n          return {\n            handler: handle.handler,\n            params: handle.createParams(params),\n            searchParams: _QueryString_js__WEBPACK_IMPORTED_MODULE_0__.parse(querystring)\n          };\n        }\n      }\n      let node = currentNode.getNextNode(path, pathIndex, brothersNodesStack, params.length);\n      if (node === undefined) {\n        if (brothersNodesStack.length === 0) {\n          return undefined;\n        }\n        const brotherNodeState = brothersNodesStack.pop();\n        pathIndex = brotherNodeState.brotherPathIndex;\n        params.splice(brotherNodeState.paramsCount);\n        node = brotherNodeState.brotherNode;\n      }\n      currentNode = node;\n      // static route\n      if (currentNode._tag === \"StaticNode\") {\n        pathIndex += currentNode.prefix.length;\n        continue;\n      }\n      if (currentNode._tag === \"WildcardNode\") {\n        let param = originPath.slice(pathIndex);\n        if (shouldDecodeParam) {\n          param = safeDecodeURIComponent(param);\n        }\n        params.push(param);\n        pathIndex = pathLen;\n        continue;\n      }\n      if (currentNode._tag === \"ParametricNode\") {\n        let paramEndIndex = originPath.indexOf(\"/\", pathIndex);\n        if (paramEndIndex === -1) {\n          paramEndIndex = pathLen;\n        }\n        let param = originPath.slice(pathIndex, paramEndIndex);\n        if (shouldDecodeParam) {\n          param = safeDecodeURIComponent(param);\n        }\n        if (currentNode.regex !== undefined) {\n          const matchedParameters = currentNode.regex.exec(param);\n          if (matchedParameters === null) continue;\n          for (let i = 1; i < matchedParameters.length; i++) {\n            const matchedParam = matchedParameters[i];\n            if (matchedParam.length > maxParamLength) {\n              return undefined;\n            }\n            params.push(matchedParam);\n          }\n        } else {\n          if (param.length > maxParamLength) {\n            return undefined;\n          }\n          params.push(param);\n        }\n        pathIndex = paramEndIndex;\n      }\n    }\n  }\n}\nclass HandlerStorage {\n  handlers = [];\n  unconstrainedHandler;\n  find() {\n    return this.unconstrainedHandler;\n  }\n  add(route) {\n    const handler = {\n      params: route.params,\n      handler: route.handler,\n      createParams: compileCreateParams(route.params)\n    };\n    this.handlers.push(handler);\n    this.unconstrainedHandler = this.handlers[0];\n  }\n}\nclass NodeBase {\n  isLeafNode = false;\n  routes;\n  handlerStorage;\n  addRoute(route) {\n    if (this.routes === undefined) {\n      this.routes = [route];\n    } else {\n      this.routes.push(route);\n    }\n    if (this.handlerStorage === undefined) {\n      this.handlerStorage = new HandlerStorage();\n    }\n    this.isLeafNode = true;\n    this.handlerStorage.add(route);\n  }\n}\nclass ParentNode extends NodeBase {\n  staticChildren = {};\n  findStaticMatchingChild(path, pathIndex) {\n    const staticChild = this.staticChildren[path.charAt(pathIndex)];\n    if (staticChild === undefined || !staticChild.matchPrefix(path, pathIndex)) {\n      return undefined;\n    }\n    return staticChild;\n  }\n  getStaticChild(path, pathIndex = 0) {\n    if (path.length === pathIndex) {\n      return this;\n    }\n    const staticChild = this.findStaticMatchingChild(path, pathIndex);\n    if (staticChild === undefined) {\n      return undefined;\n    }\n    return staticChild.getStaticChild(path, pathIndex + staticChild.prefix.length);\n  }\n  createStaticChild(path) {\n    if (path.length === 0) {\n      return this;\n    }\n    let staticChild = this.staticChildren[path.charAt(0)];\n    if (staticChild) {\n      let i = 1;\n      for (; i < staticChild.prefix.length; i++) {\n        if (path.charCodeAt(i) !== staticChild.prefix.charCodeAt(i)) {\n          staticChild = staticChild.split(this, i);\n          break;\n        }\n      }\n      return staticChild.createStaticChild(path.slice(i));\n    }\n    const label = path.charAt(0);\n    this.staticChildren[label] = new StaticNode(path);\n    return this.staticChildren[label];\n  }\n}\nclass StaticNode extends ParentNode {\n  _tag = \"StaticNode\";\n  constructor(prefix) {\n    super();\n    this.setPrefix(prefix);\n  }\n  prefix;\n  matchPrefix;\n  parametricChildren = [];\n  wildcardChild;\n  setPrefix(prefix) {\n    this.prefix = prefix;\n    if (prefix.length === 1) {\n      this.matchPrefix = (_path, _pathIndex) => true;\n    } else {\n      const len = prefix.length;\n      this.matchPrefix = function (path, pathIndex) {\n        for (let i = 1; i < len; i++) {\n          if (path.charCodeAt(pathIndex + i) !== this.prefix.charCodeAt(i)) {\n            return false;\n          }\n        }\n        return true;\n      };\n    }\n  }\n  getParametricChild(regex) {\n    if (regex === undefined) {\n      return this.parametricChildren.find(child => child.isRegex === false);\n    }\n    const source = regex.source;\n    return this.parametricChildren.find(child => {\n      if (child.regex === undefined) {\n        return false;\n      }\n      return child.regex.source === source;\n    });\n  }\n  createParametricChild(regex, staticSuffix, nodePath) {\n    let child = this.getParametricChild(regex);\n    if (child !== undefined) {\n      child.nodePaths.add(nodePath);\n      return child;\n    }\n    child = new ParametricNode(regex, staticSuffix, nodePath);\n    this.parametricChildren.push(child);\n    this.parametricChildren.sort((child1, child2) => {\n      if (!child1.isRegex) return 1;\n      if (!child2.isRegex) return -1;\n      if (child1.staticSuffix === undefined) return 1;\n      if (child2.staticSuffix === undefined) return -1;\n      if (child2.staticSuffix.endsWith(child1.staticSuffix)) return 1;\n      if (child1.staticSuffix.endsWith(child2.staticSuffix)) return -1;\n      return 0;\n    });\n    return child;\n  }\n  createWildcardChild() {\n    if (this.wildcardChild === undefined) {\n      this.wildcardChild = new WildcardNode();\n    }\n    return this.wildcardChild;\n  }\n  split(parentNode, length) {\n    const parentPrefix = this.prefix.slice(0, length);\n    const childPrefix = this.prefix.slice(length);\n    this.setPrefix(childPrefix);\n    const staticNode = new StaticNode(parentPrefix);\n    staticNode.staticChildren[childPrefix.charAt(0)] = this;\n    parentNode.staticChildren[parentPrefix.charAt(0)] = staticNode;\n    return staticNode;\n  }\n  getNextNode(path, pathIndex, nodeStack, paramsCount) {\n    let node = this.findStaticMatchingChild(path, pathIndex);\n    let parametricBrotherNodeIndex = 0;\n    if (node === undefined) {\n      if (this.parametricChildren.length === 0) {\n        return this.wildcardChild;\n      }\n      node = this.parametricChildren[0];\n      parametricBrotherNodeIndex = 1;\n    }\n    if (this.wildcardChild !== undefined) {\n      nodeStack.push({\n        paramsCount,\n        brotherPathIndex: pathIndex,\n        brotherNode: this.wildcardChild\n      });\n    }\n    for (let i = this.parametricChildren.length - 1; i >= parametricBrotherNodeIndex; i--) {\n      nodeStack.push({\n        paramsCount,\n        brotherPathIndex: pathIndex,\n        brotherNode: this.parametricChildren[i]\n      });\n    }\n    return node;\n  }\n}\nclass ParametricNode extends ParentNode {\n  regex;\n  staticSuffix;\n  _tag = \"ParametricNode\";\n  constructor(regex, staticSuffix, nodePath) {\n    super();\n    this.regex = regex;\n    this.staticSuffix = staticSuffix;\n    this.isRegex = !!regex;\n    this.nodePaths = new Set([nodePath]);\n  }\n  isRegex;\n  nodePaths;\n  getNextNode(path, pathIndex) {\n    return this.findStaticMatchingChild(path, pathIndex);\n  }\n}\nclass WildcardNode extends NodeBase {\n  _tag = \"WildcardNode\";\n  getNextNode(_path, _pathIndex, _nodeStack, _paramsCount) {\n    return undefined;\n  }\n}\nconst assert = (condition, message) => {\n  if (!condition) {\n    throw new Error(message);\n  }\n};\nfunction removeDuplicateSlashes(path) {\n  return path.replace(/\\/\\/+/g, \"/\");\n}\nfunction trimLastSlash(path) {\n  if (path.length > 1 && path.charCodeAt(path.length - 1) === 47) {\n    return path.slice(0, -1);\n  }\n  return path;\n}\nfunction compileCreateParams(params) {\n  const len = params.length;\n  return function (paramsArray) {\n    const paramsObject = {};\n    for (let i = 0; i < len; i++) {\n      paramsObject[params[i]] = paramsArray[i];\n    }\n    return paramsObject;\n  };\n}\nfunction getClosingParenthensePosition(path, idx) {\n  // `path.indexOf()` will always return the first position of the closing parenthese,\n  // but it's inefficient for grouped or wrong regexp expressions.\n  // see issues #62 and #63 for more info\n  let parentheses = 1;\n  while (idx < path.length) {\n    idx++;\n    // ignore skipped chars\n    if (path[idx] === \"\\\\\") {\n      idx++;\n      continue;\n    }\n    if (path[idx] === \")\") {\n      parentheses--;\n    } else if (path[idx] === \"(\") {\n      parentheses++;\n    }\n    if (!parentheses) return idx;\n  }\n  throw new TypeError('Invalid regexp expression in \"' + path + '\"');\n}\nfunction trimRegExpStartAndEnd(regexString) {\n  // removes chars that marks start \"^\" and end \"$\" of regexp\n  if (regexString.charCodeAt(1) === 94) {\n    regexString = regexString.slice(0, 1) + regexString.slice(2);\n  }\n  if (regexString.charCodeAt(regexString.length - 2) === 36) {\n    regexString = regexString.slice(0, regexString.length - 2) + regexString.slice(regexString.length - 1);\n  }\n  return regexString;\n}\nfunction escapeRegExp(string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\n// It must spot all the chars where decodeURIComponent(x) !== decodeURI(x)\n// The chars are: # $ & + , / : ; = ? @\nfunction decodeComponentChar(highCharCode, lowCharCode) {\n  if (highCharCode === 50) {\n    if (lowCharCode === 53) return \"%\";\n    if (lowCharCode === 51) return \"#\";\n    if (lowCharCode === 52) return \"$\";\n    if (lowCharCode === 54) return \"&\";\n    if (lowCharCode === 66) return \"+\";\n    if (lowCharCode === 98) return \"+\";\n    if (lowCharCode === 67) return \",\";\n    if (lowCharCode === 99) return \",\";\n    if (lowCharCode === 70) return \"/\";\n    if (lowCharCode === 102) return \"/\";\n    return undefined;\n  }\n  if (highCharCode === 51) {\n    if (lowCharCode === 65) return \":\";\n    if (lowCharCode === 97) return \":\";\n    if (lowCharCode === 66) return \";\";\n    if (lowCharCode === 98) return \";\";\n    if (lowCharCode === 68) return \"=\";\n    if (lowCharCode === 100) return \"=\";\n    if (lowCharCode === 70) return \"?\";\n    if (lowCharCode === 102) return \"?\";\n    return undefined;\n  }\n  if (highCharCode === 52 && lowCharCode === 48) {\n    return \"@\";\n  }\n  return undefined;\n}\nfunction safeDecodeURI(path) {\n  let shouldDecode = false;\n  let shouldDecodeParam = false;\n  let querystring = \"\";\n  for (let i = 1; i < path.length; i++) {\n    const charCode = path.charCodeAt(i);\n    if (charCode === 37) {\n      const highCharCode = path.charCodeAt(i + 1);\n      const lowCharCode = path.charCodeAt(i + 2);\n      if (decodeComponentChar(highCharCode, lowCharCode) === undefined) {\n        shouldDecode = true;\n      } else {\n        shouldDecodeParam = true;\n        // %25 - encoded % char. We need to encode one more time to prevent double decoding\n        if (highCharCode === 50 && lowCharCode === 53) {\n          shouldDecode = true;\n          path = path.slice(0, i + 1) + \"25\" + path.slice(i + 1);\n          i += 2;\n        }\n        i += 2;\n      }\n      // Some systems do not follow RFC and separate the path and query\n      // string with a `;` character (code 59), e.g. `/foo;jsessionid=123456`.\n      // Thus, we need to split on `;` as well as `?` and `#`.\n    } else if (charCode === 63 || charCode === 59 || charCode === 35) {\n      querystring = path.slice(i + 1);\n      path = path.slice(0, i);\n      break;\n    }\n  }\n  const decodedPath = shouldDecode ? decodeURI(path) : path;\n  return {\n    path: decodedPath,\n    querystring,\n    shouldDecodeParam\n  };\n}\nfunction safeDecodeURIComponent(uriComponent) {\n  const startIndex = uriComponent.indexOf(\"%\");\n  if (startIndex === -1) return uriComponent;\n  let decoded = \"\";\n  let lastIndex = startIndex;\n  for (let i = startIndex; i < uriComponent.length; i++) {\n    if (uriComponent.charCodeAt(i) === 37) {\n      const highCharCode = uriComponent.charCodeAt(i + 1);\n      const lowCharCode = uriComponent.charCodeAt(i + 2);\n      const decodedChar = decodeComponentChar(highCharCode, lowCharCode);\n      decoded += uriComponent.slice(lastIndex, i) + decodedChar;\n      lastIndex = i + 3;\n    }\n  }\n  return uriComponent.slice(0, startIndex) + decoded + uriComponent.slice(lastIndex);\n}\nconst httpMethods = [\"ACL\", \"BIND\", \"CHECKOUT\", \"CONNECT\", \"COPY\", \"DELETE\", \"GET\", \"HEAD\", \"LINK\", \"LOCK\", \"M-SEARCH\", \"MERGE\", \"MKACTIVITY\", \"MKCALENDAR\", \"MKCOL\", \"MOVE\", \"NOTIFY\", \"OPTIONS\", \"PATCH\", \"POST\", \"PROPFIND\", \"PROPPATCH\", \"PURGE\", \"PUT\", \"REBIND\", \"REPORT\", \"SEARCH\", \"SOURCE\", \"SUBSCRIBE\", \"TRACE\", \"UNBIND\", \"UNLINK\", \"UNLOCK\", \"UNSUBSCRIBE\"];\n//# sourceMappingURL=router.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/find-my-way-ts@0.1.5/node_modules/find-my-way-ts/dist/esm/internal/router.js\n");

/***/ })

};
;