"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/finalhandler@1.3.1";
exports.ids = ["vendor-chunks/finalhandler@1.3.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/finalhandler@1.3.1/node_modules/finalhandler/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/finalhandler@1.3.1/node_modules/finalhandler/index.js ***!
  \**********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * finalhandler\n * Copyright(c) 2014-2022 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('finalhandler')\nvar encodeUrl = __webpack_require__(/*! encodeurl */ \"(rsc)/./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js\")\nvar escapeHtml = __webpack_require__(/*! escape-html */ \"(rsc)/./node_modules/.pnpm/escape-html@1.0.3/node_modules/escape-html/index.js\")\nvar onFinished = __webpack_require__(/*! on-finished */ \"(rsc)/./node_modules/.pnpm/on-finished@2.4.1/node_modules/on-finished/index.js\")\nvar parseUrl = __webpack_require__(/*! parseurl */ \"(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\")\nvar statuses = __webpack_require__(/*! statuses */ \"(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js\")\nvar unpipe = __webpack_require__(/*! unpipe */ \"(rsc)/./node_modules/.pnpm/unpipe@1.0.0/node_modules/unpipe/index.js\")\n\n/**\n * Module variables.\n * @private\n */\n\nvar DOUBLE_SPACE_REGEXP = /\\x20{2}/g\nvar NEWLINE_REGEXP = /\\n/g\n\n/* istanbul ignore next */\nvar defer = typeof setImmediate === 'function'\n  ? setImmediate\n  : function (fn) { process.nextTick(fn.bind.apply(fn, arguments)) }\nvar isFinished = onFinished.isFinished\n\n/**\n * Create a minimal HTML document.\n *\n * @param {string} message\n * @private\n */\n\nfunction createHtmlDocument (message) {\n  var body = escapeHtml(message)\n    .replace(NEWLINE_REGEXP, '<br>')\n    .replace(DOUBLE_SPACE_REGEXP, ' &nbsp;')\n\n  return '<!DOCTYPE html>\\n' +\n    '<html lang=\"en\">\\n' +\n    '<head>\\n' +\n    '<meta charset=\"utf-8\">\\n' +\n    '<title>Error</title>\\n' +\n    '</head>\\n' +\n    '<body>\\n' +\n    '<pre>' + body + '</pre>\\n' +\n    '</body>\\n' +\n    '</html>\\n'\n}\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = finalhandler\n\n/**\n * Create a function to handle the final response.\n *\n * @param {Request} req\n * @param {Response} res\n * @param {Object} [options]\n * @return {Function}\n * @public\n */\n\nfunction finalhandler (req, res, options) {\n  var opts = options || {}\n\n  // get environment\n  var env = opts.env || \"development\" || 0\n\n  // get error callback\n  var onerror = opts.onerror\n\n  return function (err) {\n    var headers\n    var msg\n    var status\n\n    // ignore 404 on in-flight response\n    if (!err && headersSent(res)) {\n      debug('cannot 404 after headers sent')\n      return\n    }\n\n    // unhandled error\n    if (err) {\n      // respect status code from error\n      status = getErrorStatusCode(err)\n\n      if (status === undefined) {\n        // fallback to status code on response\n        status = getResponseStatusCode(res)\n      } else {\n        // respect headers from error\n        headers = getErrorHeaders(err)\n      }\n\n      // get error message\n      msg = getErrorMessage(err, status, env)\n    } else {\n      // not found\n      status = 404\n      msg = 'Cannot ' + req.method + ' ' + encodeUrl(getResourceName(req))\n    }\n\n    debug('default %s', status)\n\n    // schedule onerror callback\n    if (err && onerror) {\n      defer(onerror, err, req, res)\n    }\n\n    // cannot actually respond\n    if (headersSent(res)) {\n      debug('cannot %d after headers sent', status)\n      if (req.socket) {\n        req.socket.destroy()\n      }\n      return\n    }\n\n    // send response\n    send(req, res, status, headers, msg)\n  }\n}\n\n/**\n * Get headers from Error object.\n *\n * @param {Error} err\n * @return {object}\n * @private\n */\n\nfunction getErrorHeaders (err) {\n  if (!err.headers || typeof err.headers !== 'object') {\n    return undefined\n  }\n\n  var headers = Object.create(null)\n  var keys = Object.keys(err.headers)\n\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i]\n    headers[key] = err.headers[key]\n  }\n\n  return headers\n}\n\n/**\n * Get message from Error object, fallback to status message.\n *\n * @param {Error} err\n * @param {number} status\n * @param {string} env\n * @return {string}\n * @private\n */\n\nfunction getErrorMessage (err, status, env) {\n  var msg\n\n  if (env !== 'production') {\n    // use err.stack, which typically includes err.message\n    msg = err.stack\n\n    // fallback to err.toString() when possible\n    if (!msg && typeof err.toString === 'function') {\n      msg = err.toString()\n    }\n  }\n\n  return msg || statuses.message[status]\n}\n\n/**\n * Get status code from Error object.\n *\n * @param {Error} err\n * @return {number}\n * @private\n */\n\nfunction getErrorStatusCode (err) {\n  // check err.status\n  if (typeof err.status === 'number' && err.status >= 400 && err.status < 600) {\n    return err.status\n  }\n\n  // check err.statusCode\n  if (typeof err.statusCode === 'number' && err.statusCode >= 400 && err.statusCode < 600) {\n    return err.statusCode\n  }\n\n  return undefined\n}\n\n/**\n * Get resource name for the request.\n *\n * This is typically just the original pathname of the request\n * but will fallback to \"resource\" is that cannot be determined.\n *\n * @param {IncomingMessage} req\n * @return {string}\n * @private\n */\n\nfunction getResourceName (req) {\n  try {\n    return parseUrl.original(req).pathname\n  } catch (e) {\n    return 'resource'\n  }\n}\n\n/**\n * Get status code from response.\n *\n * @param {OutgoingMessage} res\n * @return {number}\n * @private\n */\n\nfunction getResponseStatusCode (res) {\n  var status = res.statusCode\n\n  // default status code to 500 if outside valid range\n  if (typeof status !== 'number' || status < 400 || status > 599) {\n    status = 500\n  }\n\n  return status\n}\n\n/**\n * Determine if the response headers have been sent.\n *\n * @param {object} res\n * @returns {boolean}\n * @private\n */\n\nfunction headersSent (res) {\n  return typeof res.headersSent !== 'boolean'\n    ? Boolean(res._header)\n    : res.headersSent\n}\n\n/**\n * Send response.\n *\n * @param {IncomingMessage} req\n * @param {OutgoingMessage} res\n * @param {number} status\n * @param {object} headers\n * @param {string} message\n * @private\n */\n\nfunction send (req, res, status, headers, message) {\n  function write () {\n    // response body\n    var body = createHtmlDocument(message)\n\n    // response status\n    res.statusCode = status\n\n    if (req.httpVersionMajor < 2) {\n      res.statusMessage = statuses.message[status]\n    }\n\n    // remove any content headers\n    res.removeHeader('Content-Encoding')\n    res.removeHeader('Content-Language')\n    res.removeHeader('Content-Range')\n\n    // response headers\n    setHeaders(res, headers)\n\n    // security headers\n    res.setHeader('Content-Security-Policy', \"default-src 'none'\")\n    res.setHeader('X-Content-Type-Options', 'nosniff')\n\n    // standard headers\n    res.setHeader('Content-Type', 'text/html; charset=utf-8')\n    res.setHeader('Content-Length', Buffer.byteLength(body, 'utf8'))\n\n    if (req.method === 'HEAD') {\n      res.end()\n      return\n    }\n\n    res.end(body, 'utf8')\n  }\n\n  if (isFinished(req)) {\n    write()\n    return\n  }\n\n  // unpipe everything from the request\n  unpipe(req)\n\n  // flush the request\n  onFinished(req, write)\n  req.resume()\n}\n\n/**\n * Set response headers from an object.\n *\n * @param {OutgoingMessage} res\n * @param {object} headers\n * @private\n */\n\nfunction setHeaders (res, headers) {\n  if (!headers) {\n    return\n  }\n\n  var keys = Object.keys(headers)\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i]\n    res.setHeader(key, headers[key])\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/finalhandler@1.3.1/node_modules/finalhandler/index.js\n");

/***/ })

};
;