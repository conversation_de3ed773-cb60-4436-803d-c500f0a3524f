"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f";
exports.ids = ["vendor-chunks/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f/node_modules/@udecode/slate-utils/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f/node_modules/@udecode/slate-utils/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDocumentNode: () => (/* binding */ createDocumentNode),\n/* harmony export */   createNode: () => (/* binding */ createNode),\n/* harmony export */   duplicateBlocks: () => (/* binding */ duplicateBlocks),\n/* harmony export */   findDescendant: () => (/* binding */ findDescendant),\n/* harmony export */   getAncestorNode: () => (/* binding */ getAncestorNode),\n/* harmony export */   getBlockAbove: () => (/* binding */ getBlockAbove),\n/* harmony export */   getBlocks: () => (/* binding */ getBlocks),\n/* harmony export */   getChildren: () => (/* binding */ getChildren),\n/* harmony export */   getEdgeBlocksAbove: () => (/* binding */ getEdgeBlocksAbove),\n/* harmony export */   getFirstNodeText: () => (/* binding */ getFirstNodeText),\n/* harmony export */   getFragmentProp: () => (/* binding */ getFragmentProp),\n/* harmony export */   getLastChild: () => (/* binding */ getLastChild),\n/* harmony export */   getLastChildPath: () => (/* binding */ getLastChildPath),\n/* harmony export */   getLastNodeByLevel: () => (/* binding */ getLastNodeByLevel),\n/* harmony export */   getMark: () => (/* binding */ getMark),\n/* harmony export */   getNextNodeStartPoint: () => (/* binding */ getNextNodeStartPoint),\n/* harmony export */   getNextSiblingNodes: () => (/* binding */ getNextSiblingNodes),\n/* harmony export */   getNodesRange: () => (/* binding */ getNodesRange),\n/* harmony export */   getOperations: () => (/* binding */ getOperations),\n/* harmony export */   getPointBeforeLocation: () => (/* binding */ getPointBeforeLocation),\n/* harmony export */   getPointFromLocation: () => (/* binding */ getPointFromLocation),\n/* harmony export */   getPointNextToVoid: () => (/* binding */ getPointNextToVoid),\n/* harmony export */   getPreviousBlockById: () => (/* binding */ getPreviousBlockById),\n/* harmony export */   getPreviousNodeEndPoint: () => (/* binding */ getPreviousNodeEndPoint),\n/* harmony export */   getPreviousPath: () => (/* binding */ getPreviousPath),\n/* harmony export */   getPreviousSiblingNode: () => (/* binding */ getPreviousSiblingNode),\n/* harmony export */   getRangeBefore: () => (/* binding */ getRangeBefore),\n/* harmony export */   getRangeFromBlockStart: () => (/* binding */ getRangeFromBlockStart),\n/* harmony export */   getSelectionFragment: () => (/* binding */ getSelectionFragment),\n/* harmony export */   getSelectionText: () => (/* binding */ getSelectionText),\n/* harmony export */   insertElements: () => (/* binding */ insertElements),\n/* harmony export */   insertEmptyElement: () => (/* binding */ insertEmptyElement),\n/* harmony export */   isAncestorEmpty: () => (/* binding */ isAncestorEmpty),\n/* harmony export */   isBlockAboveEmpty: () => (/* binding */ isBlockAboveEmpty),\n/* harmony export */   isBlockTextEmptyAfterSelection: () => (/* binding */ isBlockTextEmptyAfterSelection),\n/* harmony export */   isDocumentEnd: () => (/* binding */ isDocumentEnd),\n/* harmony export */   isEditorEmpty: () => (/* binding */ isEditorEmpty),\n/* harmony export */   isFirstChild: () => (/* binding */ isFirstChild),\n/* harmony export */   isLastChild: () => (/* binding */ isLastChild),\n/* harmony export */   isMarkActive: () => (/* binding */ isMarkActive),\n/* harmony export */   isPointAtWordEnd: () => (/* binding */ isPointAtWordEnd),\n/* harmony export */   isRangeAcrossBlocks: () => (/* binding */ isRangeAcrossBlocks),\n/* harmony export */   isRangeInSameBlock: () => (/* binding */ isRangeInSameBlock),\n/* harmony export */   isRangeInSingleText: () => (/* binding */ isRangeInSingleText),\n/* harmony export */   isSelectionAtBlockEnd: () => (/* binding */ isSelectionAtBlockEnd),\n/* harmony export */   isSelectionAtBlockStart: () => (/* binding */ isSelectionAtBlockStart),\n/* harmony export */   isSelectionCoverBlock: () => (/* binding */ isSelectionCoverBlock),\n/* harmony export */   isSelectionExpanded: () => (/* binding */ isSelectionExpanded),\n/* harmony export */   isTextByPath: () => (/* binding */ isTextByPath),\n/* harmony export */   isWordAfterTrigger: () => (/* binding */ isWordAfterTrigger),\n/* harmony export */   moveChildren: () => (/* binding */ moveChildren),\n/* harmony export */   queryEditor: () => (/* binding */ queryEditor),\n/* harmony export */   removeEditorText: () => (/* binding */ removeEditorText),\n/* harmony export */   removeEmptyPreviousBlock: () => (/* binding */ removeEmptyPreviousBlock),\n/* harmony export */   removeMark: () => (/* binding */ removeMark),\n/* harmony export */   removeNodeChildren: () => (/* binding */ removeNodeChildren),\n/* harmony export */   removeSelectionMark: () => (/* binding */ removeSelectionMark),\n/* harmony export */   replaceNode: () => (/* binding */ replaceNode),\n/* harmony export */   replaceNodeChildren: () => (/* binding */ replaceNodeChildren),\n/* harmony export */   selectEndOfBlockAboveSelection: () => (/* binding */ selectEndOfBlockAboveSelection),\n/* harmony export */   selectNodes: () => (/* binding */ selectNodes),\n/* harmony export */   setBlockAboveNode: () => (/* binding */ setBlockAboveNode),\n/* harmony export */   setBlockAboveTexts: () => (/* binding */ setBlockAboveTexts),\n/* harmony export */   setBlockNodes: () => (/* binding */ setBlockNodes),\n/* harmony export */   setMarks: () => (/* binding */ setMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   toggleWrapNodes: () => (/* binding */ toggleWrapNodes),\n/* harmony export */   unwrapStructuralNodes: () => (/* binding */ unwrapStructuralNodes),\n/* harmony export */   wrapNodeChildren: () => (/* binding */ wrapNodeChildren)\n/* harmony export */ });\n/* harmony import */ var _udecode_slate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @udecode/slate */ \"(ssr)/./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs\");\n/* harmony import */ var slate__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! slate */ \"(ssr)/./node_modules/.pnpm/slate@0.103.0/node_modules/slate/dist/index.es.js\");\n/* harmony import */ var lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lodash/castArray.js */ \"(ssr)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/castArray.js\");\n/* harmony import */ var lodash_map_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lodash/map.js */ \"(ssr)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/map.js\");\n/* harmony import */ var lodash_last_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash/last.js */ \"(ssr)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/last.js\");\n/* harmony import */ var _udecode_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @udecode/utils */ \"(ssr)/./node_modules/.pnpm/@udecode+utils@37.0.0/node_modules/@udecode/utils/dist/index.mjs\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/queries/findDescendant.ts\n\n\nvar findDescendant = (editor, options) => {\n  try {\n    const {\n      at = editor.selection,\n      match: _match,\n      reverse = false,\n      voids = false\n    } = options;\n    if (!at) return;\n    let from;\n    let to;\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Span.isSpan(at)) {\n      [from, to] = at;\n    } else if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(at)) {\n      const first = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPath)(editor, at, { edge: \"start\" });\n      const last2 = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPath)(editor, at, { edge: \"end\" });\n      from = reverse ? last2 : first;\n      to = reverse ? first : last2;\n    }\n    let root = [editor, []];\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at)) {\n      root = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeEntry)(editor, at);\n    }\n    const nodeEntries = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeDescendants)(root[0], {\n      from,\n      pass: ([n]) => voids ? false : (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isVoid)(editor, n),\n      reverse,\n      to\n    });\n    for (const [node, path] of nodeEntries) {\n      if ((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.match)(node, path, _match)) {\n        return [node, at.concat(path)];\n      }\n    }\n  } catch (error) {\n    return void 0;\n  }\n};\n\n// src/queries/getAncestorNode.ts\n\nvar getAncestorNode = (editor, path) => {\n  var _a;\n  const { selection } = editor;\n  const at = path ? path[0] : (_a = selection == null ? void 0 : selection.focus) == null ? void 0 : _a.path[0];\n  if (typeof at !== \"number\") return;\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeEntry)(editor, [at]);\n};\n\n// src/queries/getBlockAbove.ts\n\nvar getBlockAbove = (editor, options = {}) => (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getAboveNode)(editor, __spreadProps(__spreadValues({}, options), {\n  block: true\n}));\n\n// src/queries/getBlocks.ts\n\nvar getBlocks = (editor, options) => {\n  return [\n    ...(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeEntries)(editor, __spreadProps(__spreadValues({}, options), {\n      block: true\n    }))\n  ];\n};\n\n// src/queries/getChildren.ts\n\nvar getChildren = (nodeEntry) => {\n  const [node, path] = nodeEntry;\n  if ((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isAncestor)(node)) {\n    const { children } = node;\n    return children.map((child, index) => {\n      const childPath = path.concat([index]);\n      return [child, childPath];\n    });\n  }\n  return [];\n};\n\n// src/queries/getEdgeBlocksAbove.ts\n\nvar getEdgeBlocksAbove = (editor, _a = {}) => {\n  var _b = _a, { at: _at } = _b, options = __objRest(_b, [\"at\"]);\n  const at = _at != null ? _at : editor.selection;\n  if (!at) return null;\n  const [start, end] = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEdgePoints)(editor, at != null ? at : editor.selection);\n  const startBlock = getBlockAbove(editor, __spreadValues({\n    at: start\n  }, options));\n  if (!startBlock) return null;\n  const endBlock = getBlockAbove(editor, __spreadValues({\n    at: end\n  }, options));\n  if (!endBlock) return null;\n  return [startBlock, endBlock];\n};\n\n// src/queries/getFirstNodeText.ts\n\nvar getFirstNodeText = (root, options) => {\n  const texts = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeTexts)(root, options);\n  const firstTextEntry = texts.next().value;\n  return firstTextEntry != null ? firstTextEntry : void 0;\n};\n\n// src/queries/getFragmentProp.ts\n\nfunction getFragmentProp(fragment, { key, defaultValue, getProp, mode = \"block\" } = {}) {\n  if (fragment.length === 0) return defaultValue;\n  const getNodeValue = getProp != null ? getProp : (node) => {\n    return node[key];\n  };\n  let value;\n  for (const node of fragment) {\n    if (mode === \"block\" || mode === \"all\") {\n      const nodeValue = getNodeValue(node);\n      if (nodeValue !== void 0) {\n        if (value === void 0) {\n          value = nodeValue;\n        } else if (value !== nodeValue) {\n          return;\n        }\n        if (mode === \"block\") continue;\n      } else if (mode === \"block\") {\n        return defaultValue;\n      }\n    }\n    if (mode === \"text\" || mode === \"all\") {\n      const textEntries = Array.from((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeTexts)(node));\n      for (const [text] of textEntries) {\n        const textValue = getNodeValue(text);\n        if (textValue !== void 0) {\n          if (value === void 0) {\n            value = textValue;\n          } else if (value !== textValue) {\n            return;\n          }\n        } else if (mode === \"text\") {\n          return defaultValue;\n        }\n      }\n    }\n  }\n  return value;\n}\n\n// src/queries/getLastChild.ts\n\n\nvar getLastChild = (nodeEntry) => {\n  const [node, path] = nodeEntry;\n  if ((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isText)(node)) return null;\n  if (node.children.length === 0) return null;\n  const children = node.children;\n  return [children.at(-1), path.concat([children.length - 1])];\n};\nvar getLastChildPath = (nodeEntry) => {\n  const lastChild = getLastChild(nodeEntry);\n  if (!lastChild) return nodeEntry[1].concat([-1]);\n  return lastChild[1];\n};\nvar isLastChild = (parentEntry, childPath) => {\n  const lastChildPath = getLastChildPath(parentEntry);\n  return slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(lastChildPath, childPath);\n};\n\n// src/queries/getLastNodeByLevel.ts\n\nvar getLastChild2 = (node, level) => {\n  if (!(level + 1) || !(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isAncestor)(node)) return node;\n  const { children } = node;\n  const lastNode = children.at(-1);\n  return getLastChild2(lastNode, level - 1);\n};\nvar getLastNodeByLevel = (editor, level) => {\n  const { children } = editor;\n  const lastNode = children.at(-1);\n  if (!lastNode) return;\n  const [, lastPath] = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getLastNode)(editor, []);\n  return [getLastChild2(lastNode, level - 1), lastPath.slice(0, level + 1)];\n};\n\n// src/queries/getMark.ts\n\nvar getMark = (editor, key) => {\n  if (!editor) return;\n  const marks = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getMarks)(editor);\n  return marks == null ? void 0 : marks[key];\n};\n\n// src/queries/getNextNodeStartPoint.ts\n\nvar getNextNodeStartPoint = (editor, at) => {\n  const nextEntry = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNextNode)(editor, {\n    at\n  });\n  if (!nextEntry) return;\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getStartPoint)(editor, nextEntry[1]);\n};\n\n// src/queries/getNextSiblingNodes.ts\nvar getNextSiblingNodes = (ancestorEntry, path) => {\n  const [ancestor, ancestorPath] = ancestorEntry;\n  const leafIndex = path[ancestorPath.length];\n  const siblings = [];\n  const ancestorChildren = ancestor.children;\n  if (leafIndex + 1 < ancestor.children.length) {\n    for (let i = leafIndex + 1; i < ancestor.children.length; i++) {\n      siblings.push(ancestorChildren[i]);\n    }\n  }\n  return siblings;\n};\n\n// src/queries/getNodesRange.ts\n\nvar getNodesRange = (editor, nodeEntries) => {\n  if (nodeEntries.length === 0) return;\n  const firstBlockPath = nodeEntries[0][1];\n  const lastBlockPath = nodeEntries.at(-1)[1];\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getRange)(editor, firstBlockPath, lastBlockPath);\n};\n\n// src/queries/getOperations.ts\nvar getOperations = (editor) => editor.operations;\n\n// src/queries/getPointBeforeLocation.ts\n\n\n\n\n// src/queries/isRangeAcrossBlocks.ts\n\nvar isRangeAcrossBlocks = (editor, _a = {}) => {\n  var _b = _a, {\n    at\n  } = _b, options = __objRest(_b, [\n    \"at\"\n  ]);\n  if (!at) at = editor.selection;\n  if (!at) return;\n  const [start, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n  const startBlock = getBlockAbove(editor, __spreadValues({\n    at: start\n  }, options));\n  const endBlock = getBlockAbove(editor, __spreadValues({\n    at: end\n  }, options));\n  if (!startBlock && !endBlock) return;\n  if (!startBlock || !endBlock) return true;\n  return !slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(startBlock[1], endBlock[1]);\n};\n\n// src/queries/getPointBeforeLocation.ts\nvar getPointBeforeLocation = (editor, at, options) => {\n  var _a;\n  if (!options || !options.match && !options.matchString) {\n    return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointBefore)(editor, at, options);\n  }\n  const unitOffset = !options.unit || options.unit === \"offset\";\n  const matchStrings = options.matchString ? lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(options.matchString) : [\"\"];\n  const matchByRegex = (_a = options.matchByRegex) != null ? _a : false;\n  let point;\n  matchStrings.some((matchString) => {\n    var _a2, _b;\n    let beforeAt = at;\n    let previousBeforePoint = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPoint)(editor, at, { edge: \"end\" });\n    const stackLength = matchString.length + 1;\n    const stack = Array.from({ length: stackLength });\n    let count = 0;\n    while (true) {\n      const beforePoint = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointBefore)(editor, beforeAt, options);\n      if (!beforePoint) return;\n      if (isRangeAcrossBlocks(editor, {\n        at: {\n          anchor: beforePoint,\n          focus: previousBeforePoint\n        }\n      })) {\n        return;\n      }\n      const beforeString = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEditorString)(editor, {\n        anchor: beforePoint,\n        focus: previousBeforePoint\n      });\n      let beforeStringToMatch = beforeString;\n      if (unitOffset && stackLength) {\n        stack.unshift({\n          point: beforePoint,\n          text: beforeString\n        });\n        stack.pop();\n        beforeStringToMatch = lodash_map_js__WEBPACK_IMPORTED_MODULE_3__(stack.slice(0, -1), \"text\").join(\"\");\n      }\n      const isMatched = matchByRegex ? !!matchString.match(beforeStringToMatch) : beforeStringToMatch === matchString;\n      if (isMatched || ((_a2 = options.match) == null ? void 0 : _a2.call(options, { at, beforePoint, beforeString: beforeStringToMatch }))) {\n        if (options.afterMatch) {\n          if (stackLength && unitOffset) {\n            point = (_b = stack.at(-1)) == null ? void 0 : _b.point;\n            return !!point;\n          }\n          point = previousBeforePoint;\n          return true;\n        }\n        point = beforePoint;\n        return true;\n      }\n      previousBeforePoint = beforePoint;\n      beforeAt = beforePoint;\n      count += 1;\n      if (!options.skipInvalid && (!matchString || count >= matchString.length))\n        return;\n    }\n  });\n  return point;\n};\n\n// src/queries/getPointFromLocation.ts\n\nvar getPointFromLocation = (editor, {\n  at = editor.selection,\n  focus\n} = {}) => {\n  let point;\n  if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(at)) point = focus ? at.focus : at.anchor;\n  if (slate__WEBPACK_IMPORTED_MODULE_0__.Point.isPoint(at)) point = at;\n  if (slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at)) point = { offset: 0, path: at };\n  return point;\n};\n\n// src/queries/getPointNextToVoid.ts\n\n\nvar getPointNextToVoid = (editor, {\n  after,\n  at\n}) => {\n  const startVoid = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getVoidNode)(editor, { at, mode: \"highest\" });\n  if (startVoid) {\n    const blockAbove = getBlockAbove(editor, { at });\n    if (blockAbove) {\n      const nextPoint = after ? (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointAfter)(editor, at) : (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointBefore)(editor, at);\n      if (nextPoint && blockAbove && slate__WEBPACK_IMPORTED_MODULE_0__.Path.isAncestor(blockAbove[1], nextPoint.path)) {\n        at = nextPoint;\n      }\n    }\n  }\n  return at;\n};\n\n// src/queries/getPreviousBlockById.ts\n\nvar getPreviousBlockById = (editor, id, query) => {\n  const entry = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.findNode)(editor, {\n    match: { id }\n  });\n  if (entry) {\n    const prevEntry = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPreviousNode)(editor, { at: entry[1] });\n    if ((prevEntry == null ? void 0 : prevEntry[0].id) && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isBlock)(editor, prevEntry[0])) {\n      return prevEntry;\n    }\n  }\n  let found = false;\n  const _nodes = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeEntries)(editor, {\n    at: [],\n    match: (n) => {\n      if (!(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isBlock)(editor, n) || !n.id) return false;\n      if (n.id === id) {\n        found = true;\n        return false;\n      }\n      return found && n.id !== id && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.queryNode)([n, []], query);\n    },\n    mode: \"highest\",\n    reverse: true\n  });\n  const nodeEntries = Array.from(_nodes);\n  if (nodeEntries.length > 0) {\n    return nodeEntries[0];\n  }\n  if (!found) return;\n  const _entries = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeEntries)(editor, {\n    at: [],\n    match: (n) => {\n      return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isBlock)(editor, n) && !!n.id && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.queryNode)([n, []], query);\n    },\n    mode: \"highest\"\n  });\n  const firstNodeEntry = Array.from(_entries);\n  if (firstNodeEntry.length > 0) {\n    const [, path] = firstNodeEntry[0];\n    path[path.length - 1] = path.at(-1) - 1;\n    return [null, path];\n  }\n};\n\n// src/queries/getPreviousNodeEndPoint.ts\n\nvar getPreviousNodeEndPoint = (editor, at) => {\n  const prevEntry = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPreviousNode)(editor, {\n    at\n  });\n  if (!prevEntry) return;\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEndPoint)(editor, prevEntry[1]);\n};\n\n// src/queries/getPreviousPath.ts\nvar getPreviousPath = (path) => {\n  if (path.length === 0) return;\n  const last2 = path.at(-1);\n  if (last2 <= 0) return;\n  return path.slice(0, -1).concat(last2 - 1);\n};\n\n// src/queries/getPreviousSiblingNode.ts\n\n\nvar getPreviousSiblingNode = (editor, path) => {\n  const index = lodash_last_js__WEBPACK_IMPORTED_MODULE_4__(path);\n  if (index > 0) {\n    const previousSiblingIndex = index - 1;\n    const previousSiblingPath = path.slice(0, -1).concat([previousSiblingIndex]);\n    const previousSiblingNode = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNode)(editor, previousSiblingPath);\n    return previousSiblingNode ? [previousSiblingNode, previousSiblingPath] : void 0;\n  }\n};\n\n// src/queries/getRangeBefore.ts\n\nvar getRangeBefore = (editor, at, options) => {\n  const anchor = getPointBeforeLocation(editor, at, options);\n  if (!anchor) return;\n  const focus = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPoint)(editor, at, { edge: \"end\" });\n  return {\n    anchor,\n    focus\n  };\n};\n\n// src/queries/getRangeFromBlockStart.ts\n\nvar getRangeFromBlockStart = (editor, options = {}) => {\n  var _a;\n  const path = (_a = getBlockAbove(editor, options)) == null ? void 0 : _a[1];\n  if (!path) return;\n  const start = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getStartPoint)(editor, path);\n  const focus = getPointFromLocation(editor, options);\n  if (!focus) return;\n  return { anchor: start, focus };\n};\n\n// src/queries/getSelectionFragment.ts\n\n\n// src/utils/unwrapStructuralNodes.ts\nvar unwrapStructuralNodes = (nodes, { structuralTypes } = {}) => {\n  const unwrap = (nodes2, acc = []) => {\n    nodes2.forEach((node) => {\n      if (structuralTypes == null ? void 0 : structuralTypes.includes(node.type)) {\n        return unwrap(node.children, acc);\n      }\n      acc.push(node);\n    });\n    return acc;\n  };\n  return unwrap(nodes);\n};\n\n// src/queries/getSelectionFragment.ts\nvar getSelectionFragment = (editor, options) => {\n  if (!editor.selection) return [];\n  const fragment = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getFragment)(editor, editor.selection);\n  if (fragment.length === 0) return [];\n  return unwrapStructuralNodes(fragment, options);\n};\n\n// src/queries/getSelectionText.ts\n\nvar getSelectionText = (editor) => (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEditorString)(editor, editor.selection);\n\n// src/queries/isAncestorEmpty.ts\n\nvar isAncestorEmpty = (editor, node) => !(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeString)(node) && !node.children.some((n) => (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isInline)(editor, n));\n\n// src/queries/isBlockAboveEmpty.ts\nvar isBlockAboveEmpty = (editor) => {\n  var _a;\n  const block = (_a = getBlockAbove(editor)) == null ? void 0 : _a[0];\n  if (!block) return false;\n  return isAncestorEmpty(editor, block);\n};\n\n// src/queries/isBlockTextEmptyAfterSelection.ts\n\nvar isBlockTextEmptyAfterSelection = (editor) => {\n  if (!editor.selection) return false;\n  const blockAbove = getBlockAbove(editor);\n  if (!blockAbove) return false;\n  const cursor = editor.selection.focus;\n  const selectionParentEntry = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getParentNode)(editor, editor.selection);\n  if (!selectionParentEntry) return false;\n  const [, selectionParentPath] = selectionParentEntry;\n  if (!(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isEndPoint)(editor, cursor, selectionParentPath)) return false;\n  const siblingNodes = getNextSiblingNodes(blockAbove, cursor.path);\n  if (siblingNodes.length > 0) {\n    for (const siblingNode of siblingNodes) {\n      if ((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isText)(siblingNode) && siblingNode.text) {\n        return false;\n      }\n    }\n  } else {\n    return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isEndPoint)(editor, cursor, blockAbove[1]);\n  }\n  return true;\n};\n\n// src/queries/isDocumentEnd.ts\n\n\nvar isDocumentEnd = (editor) => {\n  if (editor.selection) {\n    const point = editor.selection.focus;\n    const endPoint = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEndPoint)(editor, []);\n    return endPoint.offset === 0 && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isEndPoint)(editor, point, point) && slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(slate__WEBPACK_IMPORTED_MODULE_0__.Path.next(slate__WEBPACK_IMPORTED_MODULE_0__.Path.parent(point.path)), endPoint.path);\n  }\n  return false;\n};\n\n// src/queries/isEditorEmpty.ts\n\nvar isEditorEmpty = (editor) => {\n  return editor.children.length === 1 && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isElementEmpty)(editor, editor.children[0]);\n};\n\n// src/queries/isFirstChild.ts\nvar isFirstChild = (path) => path.at(-1) === 0;\n\n// src/queries/isMarkActive.ts\n\nvar isMarkActive = (editor, type) => {\n  return (0,_udecode_utils__WEBPACK_IMPORTED_MODULE_5__.isDefined)(getMark(editor, type));\n};\n\n// src/queries/isPointAtWordEnd.ts\n\nvar AFTER_MATCH_REGEX = /^(?:\\s|$)/;\nvar isPointAtWordEnd = (editor, { at }) => {\n  const after = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointAfter)(editor, at);\n  const afterRange = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getRange)(editor, at, after);\n  const afterText = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEditorString)(editor, afterRange);\n  return !!AFTER_MATCH_REGEX.exec(afterText);\n};\n\n// src/queries/isRangeInSameBlock.ts\n\nvar isRangeInSameBlock = (editor, _a = {}) => {\n  var _b = _a, {\n    at\n  } = _b, options = __objRest(_b, [\n    \"at\"\n  ]);\n  if (!at) at = editor.selection;\n  if (!at) return;\n  const [start, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n  const startBlock = getBlockAbove(editor, __spreadValues({\n    at: start\n  }, options));\n  const endBlock = getBlockAbove(editor, __spreadValues({\n    at: end\n  }, options));\n  if (!startBlock || !endBlock) return;\n  return slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(startBlock[1], endBlock[1]);\n};\n\n// src/queries/isRangeInSingleText.ts\n\nvar isRangeInSingleText = (at) => {\n  const [start, end] = slate__WEBPACK_IMPORTED_MODULE_0__.Range.edges(at);\n  return slate__WEBPACK_IMPORTED_MODULE_0__.Path.equals(start.path, end.path);\n};\n\n// src/queries/isSelectionAtBlockEnd.ts\n\nvar isSelectionAtBlockEnd = (editor, options) => {\n  var _a, _b;\n  const path = (_a = getBlockAbove(editor, options)) == null ? void 0 : _a[1];\n  return !!path && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isEndPoint)(editor, (_b = editor.selection) == null ? void 0 : _b.focus, path);\n};\n\n// src/queries/isSelectionAtBlockStart.ts\n\nvar isSelectionAtBlockStart = (editor, options) => {\n  var _a;\n  const { selection } = editor;\n  if (!selection) return false;\n  const path = (_a = getBlockAbove(editor, options)) == null ? void 0 : _a[1];\n  if (!path) return false;\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isStartPoint)(editor, selection.focus, path) || (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isExpanded)(editor.selection) && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isStartPoint)(editor, selection.anchor, path);\n};\n\n// src/queries/isSelectionCoverBlock.ts\nvar isSelectionCoverBlock = (editor, _a = {}) => {\n  var _b = _a, {\n    at\n  } = _b, options = __objRest(_b, [\n    \"at\"\n  ]);\n  return isSelectionAtBlockEnd(editor, options) && isSelectionAtBlockStart(editor, options) && isRangeInSameBlock(editor, options);\n};\n\n// src/queries/isSelectionExpanded.ts\n\nvar isSelectionExpanded = (editor) => (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isExpanded)(editor.selection);\n\n// src/queries/isTextByPath.ts\n\nvar isTextByPath = (editor, path) => {\n  const node = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNode)(editor, path);\n  return (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isText)(node);\n};\n\n// src/queries/isWordAfterTrigger.ts\n\n\nvar isWordAfterTrigger = (editor, { at, trigger }) => {\n  const wordBefore = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointBefore)(editor, at, { unit: \"word\" });\n  const before = wordBefore && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getPointBefore)(editor, wordBefore);\n  const beforeRange = before && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getRange)(editor, before, at);\n  const beforeText = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEditorString)(editor, beforeRange);\n  const escapedTrigger = (0,_udecode_utils__WEBPACK_IMPORTED_MODULE_5__.escapeRegExp)(trigger);\n  const beforeRegex = new RegExp(\n    `^${escapedTrigger}([\\\\w|\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\xFF|\\u0430-\\u044F\\u0410-\\u042F\\u0451\\u0401]+)$`\n  );\n  const match2 = beforeText ? beforeText.match(beforeRegex) : null;\n  return {\n    match: match2,\n    range: beforeRange\n  };\n};\n\n// src/queries/queryEditor.ts\n\n\nvar queryEditor = (editor, {\n  allow,\n  at = editor.selection || [],\n  exclude,\n  filter,\n  selectionAtBlockEnd,\n  selectionAtBlockStart\n} = {}) => {\n  if (filter && !filter(editor) || selectionAtBlockStart && !isSelectionAtBlockStart(editor) || selectionAtBlockEnd && !isSelectionAtBlockEnd(editor)) {\n    return false;\n  }\n  const allows = lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(allow);\n  if (allows.length > 0 && !(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.someNode)(editor, { at, match: { type: allows } })) {\n    return false;\n  }\n  const excludes = lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(exclude);\n  if (excludes.length > 0 && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.someNode)(editor, { at, match: { type: excludes } })) {\n    return false;\n  }\n  return true;\n};\n\n// src/transforms/duplicateBlocks.ts\n\nvar duplicateBlocks = (editor, blocks) => {\n  const lastBlock = blocks.at(-1);\n  if (!lastBlock) return;\n  editor.insertNodes(blocks.map((item) => item[0]), {\n    at: slate__WEBPACK_IMPORTED_MODULE_0__.Path.next(lastBlock[1])\n  });\n};\n\n// src/transforms/insertElements.ts\n\nvar insertElements = (editor, nodes, options) => {\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.insertNodes)(editor, nodes, options);\n};\n\n// src/transforms/insertEmptyElement.ts\n\nvar insertEmptyElement = (editor, type, options) => {\n  insertElements(\n    editor,\n    {\n      children: [{ text: \"\" }],\n      type\n    },\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getQueryOptions)(editor, options)\n  );\n};\n\n// src/transforms/moveChildren.ts\n\n\nvar moveChildren = (editor, { at, fromStartIndex = 0, match: match2, to }) => {\n  let moved = 0;\n  const parentPath = slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at) ? at : at[1];\n  const parentNode = slate__WEBPACK_IMPORTED_MODULE_0__.Path.isPath(at) ? (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNode)(editor, parentPath) : at[0];\n  if (!parentNode) return moved;\n  if (!(0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isBlock)(editor, parentNode)) return moved;\n  for (let i = parentNode.children.length - 1; i >= fromStartIndex; i--) {\n    const childPath = [...parentPath, i];\n    const childNode = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNode)(editor, childPath);\n    if (!match2 || childNode && match2([childNode, childPath])) {\n      (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.moveNodes)(editor, { at: childPath, to });\n      moved++;\n    }\n  }\n  return moved;\n};\n\n// src/transforms/removeEditorText.ts\n\nvar removeEditorText = (editor, _a = {}) => {\n  var _b = _a, { match: match2 } = _b, options = __objRest(_b, [\"match\"]);\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.removeNodes)(editor, __spreadValues({\n    at: [],\n    match: (n, p) => {\n      var _a2;\n      return ((_a2 = n.text) == null ? void 0 : _a2.length) > 0 && (!match2 || match2(n, p));\n    }\n  }, options));\n};\n\n// src/transforms/removeEmptyPreviousBlock.ts\n\nvar removeEmptyPreviousBlock = (editor, options = {}) => {\n  const entry = getBlockAbove(editor, options);\n  if (!entry) return;\n  const prevEntry = getPreviousSiblingNode(editor, entry[1]);\n  if (prevEntry && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isElementEmpty)(editor, prevEntry[0])) {\n    editor.removeNodes({ at: prevEntry[1] });\n  }\n};\n\n// src/transforms/removeMark.ts\n\n\n\nvar removeMark = (editor, _a) => {\n  var _b = _a, { key, at, shouldChange = true } = _b, rest = __objRest(_b, [\"key\", \"at\", \"shouldChange\"]);\n  var _a2;\n  const selection = at != null ? at : editor.selection;\n  key = lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(key);\n  if (selection) {\n    if (slate__WEBPACK_IMPORTED_MODULE_0__.Range.isRange(selection) && slate__WEBPACK_IMPORTED_MODULE_0__.Range.isExpanded(selection)) {\n      (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.unsetNodes)(editor, key, __spreadValues({\n        at: selection,\n        match: _udecode_slate__WEBPACK_IMPORTED_MODULE_1__.isText,\n        split: true\n      }, rest));\n    } else if (editor.selection) {\n      const marks = (_a2 = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getMarks)(editor)) != null ? _a2 : {};\n      key.forEach((k) => {\n        delete marks[k];\n      });\n      editor.marks = marks;\n      shouldChange && editor.onChange();\n    }\n  }\n};\n\n// src/transforms/removeNodeChildren.ts\n\nvar removeNodeChildren = (editor, path, options) => {\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    for (const [, childPath] of (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNodeChildren)(editor, path, {\n      reverse: true\n    })) {\n      (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.removeNodes)(editor, __spreadProps(__spreadValues({}, options), { at: childPath }));\n    }\n  });\n};\n\n// src/transforms/removeSelectionMark.ts\n\nvar removeSelectionMark = (editor) => {\n  const marks = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getMarks)(editor);\n  if (!marks) return;\n  Object.keys(marks).forEach((key) => {\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.removeEditorMark)(editor, key);\n  });\n};\n\n// src/transforms/replaceNode.ts\n\nvar replaceNode = (editor, { at, insertOptions, nodes, removeOptions }) => {\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.removeNodes)(editor, __spreadProps(__spreadValues({}, removeOptions), { at }));\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.insertNodes)(editor, nodes, __spreadProps(__spreadValues({}, insertOptions), {\n      at\n    }));\n  });\n};\n\n// src/transforms/replaceNodeChildren.ts\n\nvar replaceNodeChildren = (editor, { at, insertOptions, nodes, removeOptions }) => {\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    removeNodeChildren(editor, at, removeOptions);\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.insertNodes)(editor, nodes, __spreadProps(__spreadValues({}, insertOptions), {\n      at: at.concat([0])\n    }));\n  });\n};\n\n// src/transforms/selectEndOfBlockAboveSelection.ts\n\nvar selectEndOfBlockAboveSelection = (editor) => {\n  var _a;\n  const path = (_a = getBlockAbove(editor)) == null ? void 0 : _a[1];\n  path && (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.select)(editor, (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getEndPoint)(editor, path));\n};\n\n// src/transforms/selectNodes.ts\n\nvar selectNodes = (editor, nodes) => {\n  const range = getNodesRange(editor, nodes);\n  if (!range) return;\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.setSelection)(editor, range);\n};\n\n// src/transforms/setBlockAboveNode.ts\nvar setBlockAboveNode = (editor, props, options) => {\n  var _a;\n  const at = (_a = getBlockAbove(editor)) == null ? void 0 : _a[1];\n  if (!at) return;\n  editor.setNodes(props, __spreadProps(__spreadValues({}, options), {\n    at: getBlockAbove(editor)[1]\n  }));\n};\nvar setBlockAboveTexts = (editor, props, options) => {\n  setBlockAboveNode(editor, props, __spreadProps(__spreadValues({}, options), { mode: \"lowest\" }));\n};\n\n// src/transforms/setBlockNodes.ts\nvar setBlockNodes = (editor, props, options) => {\n  editor.withoutNormalizing(() => {\n    const blocks = getBlocks(editor, options);\n    blocks.forEach(([, path]) => {\n      editor.setNodes(props, {\n        at: path\n      });\n    });\n  });\n};\n\n// src/transforms/setMarks.ts\n\n\nvar setMarks = (editor, marks, clear = []) => {\n  if (!editor.selection) return;\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    const clears = lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(clear);\n    removeMark(editor, { key: clears });\n    removeMark(editor, { key: Object.keys(marks) });\n    Object.keys(marks).forEach((key) => {\n      editor.addMark(key, marks[key]);\n    });\n  });\n};\n\n// src/transforms/toggleMark.ts\n\n\nvar toggleMark = (editor, { key, clear }) => {\n  if (!editor.selection) return;\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    const isActive = isMarkActive(editor, key);\n    if (isActive) {\n      editor.removeMark(key);\n      return;\n    }\n    if (clear) {\n      const clears = lodash_castArray_js__WEBPACK_IMPORTED_MODULE_2__(clear);\n      removeMark(editor, { key: clears });\n    }\n    editor.addMark(key, true);\n  });\n};\n\n// src/transforms/toggleWrapNodes.ts\n\nvar toggleWrapNodes = (editor, type) => {\n  if ((0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.someNode)(editor, { match: { type } })) {\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.unwrapNodes)(editor, { match: { type } });\n  } else {\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.wrapNodes)(editor, {\n      children: [],\n      type\n    });\n  }\n};\n\n// src/transforms/wrapNodeChildren.ts\n\nvar wrapNodeChildren = (editor, element, options) => {\n  const path = options == null ? void 0 : options.at;\n  const node = (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.getNode)(editor, path);\n  if (!(node == null ? void 0 : node.children)) return;\n  (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.withoutNormalizing)(editor, () => {\n    const firstChildPath = path.concat([0]);\n    (0,_udecode_slate__WEBPACK_IMPORTED_MODULE_1__.wrapNodes)(editor, element, __spreadProps(__spreadValues({}, options), {\n      at: firstChildPath\n    }));\n    if (node.children.length < 2) return;\n    moveChildren(editor, {\n      at: path,\n      fromStartIndex: 1,\n      to: firstChildPath.concat([1])\n    });\n  });\n};\n\n// src/utils/createDocumentNode.ts\nvar createDocumentNode = (type = \"p\", text = \"\", remaining = []) => [\n  {\n    children: [\n      {\n        children: [{ text }],\n        type\n      },\n      ...remaining\n    ]\n  }\n];\n\n// src/utils/createNode.ts\nvar createNode = (type = \"p\", text = \"\") => ({\n  children: [{ text }],\n  type\n});\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f/node_modules/@udecode/slate-utils/dist/index.mjs\n");

/***/ })

};
;