"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/is-hotkey@0.2.0";
exports.ids = ["vendor-chunks/is-hotkey@0.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/is-hotkey@0.2.0/node_modules/is-hotkey/lib/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/is-hotkey@0.2.0/node_modules/is-hotkey/lib/index.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\n\n/**\n * Constants.\n */\n\nvar IS_MAC = typeof window != 'undefined' && /Mac|iPod|iPhone|iPad/.test(window.navigator.platform);\n\nvar MODIFIERS = {\n  alt: 'altKey',\n  control: 'ctrlKey',\n  meta: 'metaKey',\n  shift: 'shiftKey'\n};\n\nvar ALIASES = {\n  add: '+',\n  break: 'pause',\n  cmd: 'meta',\n  command: 'meta',\n  ctl: 'control',\n  ctrl: 'control',\n  del: 'delete',\n  down: 'arrowdown',\n  esc: 'escape',\n  ins: 'insert',\n  left: 'arrowleft',\n  mod: IS_MAC ? 'meta' : 'control',\n  opt: 'alt',\n  option: 'alt',\n  return: 'enter',\n  right: 'arrowright',\n  space: ' ',\n  spacebar: ' ',\n  up: 'arrowup',\n  win: 'meta',\n  windows: 'meta'\n};\n\nvar CODES = {\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  control: 17,\n  alt: 18,\n  pause: 19,\n  capslock: 20,\n  escape: 27,\n  ' ': 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  arrowleft: 37,\n  arrowup: 38,\n  arrowright: 39,\n  arrowdown: 40,\n  insert: 45,\n  delete: 46,\n  meta: 91,\n  numlock: 144,\n  scrolllock: 145,\n  ';': 186,\n  '=': 187,\n  ',': 188,\n  '-': 189,\n  '.': 190,\n  '/': 191,\n  '`': 192,\n  '[': 219,\n  '\\\\': 220,\n  ']': 221,\n  '\\'': 222\n};\n\nfor (var f = 1; f < 20; f++) {\n  CODES['f' + f] = 111 + f;\n}\n\n/**\n * Is hotkey?\n */\n\nfunction isHotkey(hotkey, options, event) {\n  if (options && !('byKey' in options)) {\n    event = options;\n    options = null;\n  }\n\n  if (!Array.isArray(hotkey)) {\n    hotkey = [hotkey];\n  }\n\n  var array = hotkey.map(function (string) {\n    return parseHotkey(string, options);\n  });\n  var check = function check(e) {\n    return array.some(function (object) {\n      return compareHotkey(object, e);\n    });\n  };\n  var ret = event == null ? check : check(event);\n  return ret;\n}\n\nfunction isCodeHotkey(hotkey, event) {\n  return isHotkey(hotkey, event);\n}\n\nfunction isKeyHotkey(hotkey, event) {\n  return isHotkey(hotkey, { byKey: true }, event);\n}\n\n/**\n * Parse.\n */\n\nfunction parseHotkey(hotkey, options) {\n  var byKey = options && options.byKey;\n  var ret = {};\n\n  // Special case to handle the `+` key since we use it as a separator.\n  hotkey = hotkey.replace('++', '+add');\n  var values = hotkey.split('+');\n  var length = values.length;\n\n  // Ensure that all the modifiers are set to false unless the hotkey has them.\n\n  for (var k in MODIFIERS) {\n    ret[MODIFIERS[k]] = false;\n  }\n\n  var _iteratorNormalCompletion = true;\n  var _didIteratorError = false;\n  var _iteratorError = undefined;\n\n  try {\n    for (var _iterator = values[Symbol.iterator](), _step; !(_iteratorNormalCompletion = (_step = _iterator.next()).done); _iteratorNormalCompletion = true) {\n      var value = _step.value;\n\n      var optional = value.endsWith('?') && value.length > 1;\n\n      if (optional) {\n        value = value.slice(0, -1);\n      }\n\n      var name = toKeyName(value);\n      var modifier = MODIFIERS[name];\n\n      if (value.length > 1 && !modifier && !ALIASES[value] && !CODES[name]) {\n        throw new TypeError('Unknown modifier: \"' + value + '\"');\n      }\n\n      if (length === 1 || !modifier) {\n        if (byKey) {\n          ret.key = name;\n        } else {\n          ret.which = toKeyCode(value);\n        }\n      }\n\n      if (modifier) {\n        ret[modifier] = optional ? null : true;\n      }\n    }\n  } catch (err) {\n    _didIteratorError = true;\n    _iteratorError = err;\n  } finally {\n    try {\n      if (!_iteratorNormalCompletion && _iterator.return) {\n        _iterator.return();\n      }\n    } finally {\n      if (_didIteratorError) {\n        throw _iteratorError;\n      }\n    }\n  }\n\n  return ret;\n}\n\n/**\n * Compare.\n */\n\nfunction compareHotkey(object, event) {\n  for (var key in object) {\n    var expected = object[key];\n    var actual = void 0;\n\n    if (expected == null) {\n      continue;\n    }\n\n    if (key === 'key' && event.key != null) {\n      actual = event.key.toLowerCase();\n    } else if (key === 'which') {\n      actual = expected === 91 && event.which === 93 ? 91 : event.which;\n    } else {\n      actual = event[key];\n    }\n\n    if (actual == null && expected === false) {\n      continue;\n    }\n\n    if (actual !== expected) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Utils.\n */\n\nfunction toKeyCode(name) {\n  name = toKeyName(name);\n  var code = CODES[name] || name.toUpperCase().charCodeAt(0);\n  return code;\n}\n\nfunction toKeyName(name) {\n  name = name.toLowerCase();\n  name = ALIASES[name] || name;\n  return name;\n}\n\n/**\n * Export.\n */\n\nexports[\"default\"] = isHotkey;\nexports.isHotkey = isHotkey;\nexports.isCodeHotkey = isCodeHotkey;\nexports.isKeyHotkey = isKeyHotkey;\nexports.parseHotkey = parseHotkey;\nexports.compareHotkey = compareHotkey;\nexports.toKeyCode = toKeyCode;\nexports.toKeyName = toKeyName;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/is-hotkey@0.2.0/node_modules/is-hotkey/lib/index.js\n");

/***/ })

};
;