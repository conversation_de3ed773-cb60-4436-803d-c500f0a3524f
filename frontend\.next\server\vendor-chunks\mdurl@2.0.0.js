"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/mdurl@2.0.0";
exports.ids = ["vendor-chunks/mdurl@2.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/index.mjs":
/*!*********************************************************************!*\
  !*** ./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/index.mjs ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decode: () => (/* reexport safe */ _lib_decode_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   encode: () => (/* reexport safe */ _lib_encode_mjs__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   format: () => (/* reexport safe */ _lib_format_mjs__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   parse: () => (/* reexport safe */ _lib_parse_mjs__WEBPACK_IMPORTED_MODULE_3__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _lib_decode_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/decode.mjs */ \"(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/decode.mjs\");\n/* harmony import */ var _lib_encode_mjs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./lib/encode.mjs */ \"(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/encode.mjs\");\n/* harmony import */ var _lib_format_mjs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lib/format.mjs */ \"(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/format.mjs\");\n/* harmony import */ var _lib_parse_mjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./lib/parse.mjs */ \"(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/parse.mjs\");\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWR1cmxAMi4wLjAvbm9kZV9tb2R1bGVzL21kdXJsL2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFxQztBQUNBO0FBQ0E7QUFDRjs7QUFPbEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vbWR1cmxAMi4wLjAvbm9kZV9tb2R1bGVzL21kdXJsL2luZGV4Lm1qcz9kMWVjIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBkZWNvZGUgZnJvbSAnLi9saWIvZGVjb2RlLm1qcydcbmltcG9ydCBlbmNvZGUgZnJvbSAnLi9saWIvZW5jb2RlLm1qcydcbmltcG9ydCBmb3JtYXQgZnJvbSAnLi9saWIvZm9ybWF0Lm1qcydcbmltcG9ydCBwYXJzZSBmcm9tICcuL2xpYi9wYXJzZS5tanMnXG5cbmV4cG9ydCB7XG4gIGRlY29kZSxcbiAgZW5jb2RlLFxuICBmb3JtYXQsXG4gIHBhcnNlXG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/decode.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/decode.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* eslint-disable no-bitwise */\n\nconst decodeCache = {}\n\nfunction getDecodeCache (exclude) {\n  let cache = decodeCache[exclude]\n  if (cache) { return cache }\n\n  cache = decodeCache[exclude] = []\n\n  for (let i = 0; i < 128; i++) {\n    const ch = String.fromCharCode(i)\n    cache.push(ch)\n  }\n\n  for (let i = 0; i < exclude.length; i++) {\n    const ch = exclude.charCodeAt(i)\n    cache[ch] = '%' + ('0' + ch.toString(16).toUpperCase()).slice(-2)\n  }\n\n  return cache\n}\n\n// Decode percent-encoded string.\n//\nfunction decode (string, exclude) {\n  if (typeof exclude !== 'string') {\n    exclude = decode.defaultChars\n  }\n\n  const cache = getDecodeCache(exclude)\n\n  return string.replace(/(%[a-f0-9]{2})+/gi, function (seq) {\n    let result = ''\n\n    for (let i = 0, l = seq.length; i < l; i += 3) {\n      const b1 = parseInt(seq.slice(i + 1, i + 3), 16)\n\n      if (b1 < 0x80) {\n        result += cache[b1]\n        continue\n      }\n\n      if ((b1 & 0xE0) === 0xC0 && (i + 3 < l)) {\n        // 110xxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n\n        if ((b2 & 0xC0) === 0x80) {\n          const chr = ((b1 << 6) & 0x7C0) | (b2 & 0x3F)\n\n          if (chr < 0x80) {\n            result += '\\ufffd\\ufffd'\n          } else {\n            result += String.fromCharCode(chr)\n          }\n\n          i += 3\n          continue\n        }\n      }\n\n      if ((b1 & 0xF0) === 0xE0 && (i + 6 < l)) {\n        // 1110xxxx 10xxxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n        const b3 = parseInt(seq.slice(i + 7, i + 9), 16)\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80) {\n          const chr = ((b1 << 12) & 0xF000) | ((b2 << 6) & 0xFC0) | (b3 & 0x3F)\n\n          if (chr < 0x800 || (chr >= 0xD800 && chr <= 0xDFFF)) {\n            result += '\\ufffd\\ufffd\\ufffd'\n          } else {\n            result += String.fromCharCode(chr)\n          }\n\n          i += 6\n          continue\n        }\n      }\n\n      if ((b1 & 0xF8) === 0xF0 && (i + 9 < l)) {\n        // 111110xx 10xxxxxx 10xxxxxx 10xxxxxx\n        const b2 = parseInt(seq.slice(i + 4, i + 6), 16)\n        const b3 = parseInt(seq.slice(i + 7, i + 9), 16)\n        const b4 = parseInt(seq.slice(i + 10, i + 12), 16)\n\n        if ((b2 & 0xC0) === 0x80 && (b3 & 0xC0) === 0x80 && (b4 & 0xC0) === 0x80) {\n          let chr = ((b1 << 18) & 0x1C0000) | ((b2 << 12) & 0x3F000) | ((b3 << 6) & 0xFC0) | (b4 & 0x3F)\n\n          if (chr < 0x10000 || chr > 0x10FFFF) {\n            result += '\\ufffd\\ufffd\\ufffd\\ufffd'\n          } else {\n            chr -= 0x10000\n            result += String.fromCharCode(0xD800 + (chr >> 10), 0xDC00 + (chr & 0x3FF))\n          }\n\n          i += 9\n          continue\n        }\n      }\n\n      result += '\\ufffd'\n    }\n\n    return result\n  })\n}\n\ndecode.defaultChars = ';/?:@&=+$,#'\ndecode.componentChars = ''\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (decode);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/decode.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/encode.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/encode.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\nconst encodeCache = {}\n\n// Create a lookup array where anything but characters in `chars` string\n// and alphanumeric chars is percent-encoded.\n//\nfunction getEncodeCache (exclude) {\n  let cache = encodeCache[exclude]\n  if (cache) { return cache }\n\n  cache = encodeCache[exclude] = []\n\n  for (let i = 0; i < 128; i++) {\n    const ch = String.fromCharCode(i)\n\n    if (/^[0-9a-z]$/i.test(ch)) {\n      // always allow unencoded alphanumeric characters\n      cache.push(ch)\n    } else {\n      cache.push('%' + ('0' + i.toString(16).toUpperCase()).slice(-2))\n    }\n  }\n\n  for (let i = 0; i < exclude.length; i++) {\n    cache[exclude.charCodeAt(i)] = exclude[i]\n  }\n\n  return cache\n}\n\n// Encode unsafe characters with percent-encoding, skipping already\n// encoded sequences.\n//\n//  - string       - string to encode\n//  - exclude      - list of characters to ignore (in addition to a-zA-Z0-9)\n//  - keepEscaped  - don't encode '%' in a correct escape sequence (default: true)\n//\nfunction encode (string, exclude, keepEscaped) {\n  if (typeof exclude !== 'string') {\n    // encode(string, keepEscaped)\n    keepEscaped = exclude\n    exclude = encode.defaultChars\n  }\n\n  if (typeof keepEscaped === 'undefined') {\n    keepEscaped = true\n  }\n\n  const cache = getEncodeCache(exclude)\n  let result = ''\n\n  for (let i = 0, l = string.length; i < l; i++) {\n    const code = string.charCodeAt(i)\n\n    if (keepEscaped && code === 0x25 /* % */ && i + 2 < l) {\n      if (/^[0-9a-f]{2}$/i.test(string.slice(i + 1, i + 3))) {\n        result += string.slice(i, i + 3)\n        i += 2\n        continue\n      }\n    }\n\n    if (code < 128) {\n      result += cache[code]\n      continue\n    }\n\n    if (code >= 0xD800 && code <= 0xDFFF) {\n      if (code >= 0xD800 && code <= 0xDBFF && i + 1 < l) {\n        const nextCode = string.charCodeAt(i + 1)\n        if (nextCode >= 0xDC00 && nextCode <= 0xDFFF) {\n          result += encodeURIComponent(string[i] + string[i + 1])\n          i++\n          continue\n        }\n      }\n      result += '%EF%BF%BD'\n      continue\n    }\n\n    result += encodeURIComponent(string[i])\n  }\n\n  return result\n}\n\nencode.defaultChars = \";/?:@&=+$,-_.!~*'()#\"\nencode.componentChars = \"-_.!~*'()\"\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (encode);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/encode.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/format.mjs":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/format.mjs ***!
  \**************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ format)\n/* harmony export */ });\nfunction format (url) {\n  let result = ''\n\n  result += url.protocol || ''\n  result += url.slashes ? '//' : ''\n  result += url.auth ? url.auth + '@' : ''\n\n  if (url.hostname && url.hostname.indexOf(':') !== -1) {\n    // ipv6 address\n    result += '[' + url.hostname + ']'\n  } else {\n    result += url.hostname || ''\n  }\n\n  result += url.port ? ':' + url.port : ''\n  result += url.pathname || ''\n  result += url.search || ''\n  result += url.hash || ''\n\n  return result\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWR1cmxAMi4wLjAvbm9kZV9tb2R1bGVzL21kdXJsL2xpYi9mb3JtYXQubWpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBZTtBQUNmOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL21kdXJsQDIuMC4wL25vZGVfbW9kdWxlcy9tZHVybC9saWIvZm9ybWF0Lm1qcz9jZGU4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIGZvcm1hdCAodXJsKSB7XG4gIGxldCByZXN1bHQgPSAnJ1xuXG4gIHJlc3VsdCArPSB1cmwucHJvdG9jb2wgfHwgJydcbiAgcmVzdWx0ICs9IHVybC5zbGFzaGVzID8gJy8vJyA6ICcnXG4gIHJlc3VsdCArPSB1cmwuYXV0aCA/IHVybC5hdXRoICsgJ0AnIDogJydcblxuICBpZiAodXJsLmhvc3RuYW1lICYmIHVybC5ob3N0bmFtZS5pbmRleE9mKCc6JykgIT09IC0xKSB7XG4gICAgLy8gaXB2NiBhZGRyZXNzXG4gICAgcmVzdWx0ICs9ICdbJyArIHVybC5ob3N0bmFtZSArICddJ1xuICB9IGVsc2Uge1xuICAgIHJlc3VsdCArPSB1cmwuaG9zdG5hbWUgfHwgJydcbiAgfVxuXG4gIHJlc3VsdCArPSB1cmwucG9ydCA/ICc6JyArIHVybC5wb3J0IDogJydcbiAgcmVzdWx0ICs9IHVybC5wYXRobmFtZSB8fCAnJ1xuICByZXN1bHQgKz0gdXJsLnNlYXJjaCB8fCAnJ1xuICByZXN1bHQgKz0gdXJsLmhhc2ggfHwgJydcblxuICByZXR1cm4gcmVzdWx0XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/format.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/parse.mjs":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/parse.mjs ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n//\n// Changes from joyent/node:\n//\n// 1. No leading slash in paths,\n//    e.g. in `url.parse('http://foo?bar')` pathname is ``, not `/`\n//\n// 2. Backslashes are not replaced with slashes,\n//    so `http:\\\\example.org\\` is treated like a relative path\n//\n// 3. Trailing colon is treated like a part of the path,\n//    i.e. in `http://example.org:foo` pathname is `:foo`\n//\n// 4. Nothing is URL-encoded in the resulting object,\n//    (in joyent/node some chars in auth and paths are encoded)\n//\n// 5. `url.parse()` does not have `parseQueryString` argument\n//\n// 6. Removed extraneous result properties: `host`, `path`, `query`, etc.,\n//    which can be constructed using other parts of the url.\n//\n\nfunction Url () {\n  this.protocol = null\n  this.slashes = null\n  this.auth = null\n  this.port = null\n  this.hostname = null\n  this.hash = null\n  this.search = null\n  this.pathname = null\n}\n\n// Reference: RFC 3986, RFC 1808, RFC 2396\n\n// define these here so at least they only have to be\n// compiled once on the first module load.\nconst protocolPattern = /^([a-z0-9.+-]+:)/i\nconst portPattern = /:[0-9]*$/\n\n// Special case for a simple path URL\n/* eslint-disable-next-line no-useless-escape */\nconst simplePathPattern = /^(\\/\\/?(?!\\/)[^\\?\\s]*)(\\?[^\\s]*)?$/\n\n// RFC 2396: characters reserved for delimiting URLs.\n// We actually just auto-escape these.\nconst delims = ['<', '>', '\"', '`', ' ', '\\r', '\\n', '\\t']\n\n// RFC 2396: characters not allowed for various reasons.\nconst unwise = ['{', '}', '|', '\\\\', '^', '`'].concat(delims)\n\n// Allowed by RFCs, but cause of XSS attacks.  Always escape these.\nconst autoEscape = ['\\''].concat(unwise)\n// Characters that are never ever allowed in a hostname.\n// Note that any invalid chars are also handled, but these\n// are the ones that are *expected* to be seen, so we fast-path\n// them.\nconst nonHostChars = ['%', '/', '?', ';', '#'].concat(autoEscape)\nconst hostEndingChars = ['/', '?', '#']\nconst hostnameMaxLen = 255\nconst hostnamePartPattern = /^[+a-z0-9A-Z_-]{0,63}$/\nconst hostnamePartStart = /^([+a-z0-9A-Z_-]{0,63})(.*)$/\n// protocols that can allow \"unsafe\" and \"unwise\" chars.\n// protocols that never have a hostname.\nconst hostlessProtocol = {\n  javascript: true,\n  'javascript:': true\n}\n// protocols that always contain a // bit.\nconst slashedProtocol = {\n  http: true,\n  https: true,\n  ftp: true,\n  gopher: true,\n  file: true,\n  'http:': true,\n  'https:': true,\n  'ftp:': true,\n  'gopher:': true,\n  'file:': true\n}\n\nfunction urlParse (url, slashesDenoteHost) {\n  if (url && url instanceof Url) return url\n\n  const u = new Url()\n  u.parse(url, slashesDenoteHost)\n  return u\n}\n\nUrl.prototype.parse = function (url, slashesDenoteHost) {\n  let lowerProto, hec, slashes\n  let rest = url\n\n  // trim before proceeding.\n  // This is to support parse stuff like \"  http://foo.com  \\n\"\n  rest = rest.trim()\n\n  if (!slashesDenoteHost && url.split('#').length === 1) {\n    // Try fast path regexp\n    const simplePath = simplePathPattern.exec(rest)\n    if (simplePath) {\n      this.pathname = simplePath[1]\n      if (simplePath[2]) {\n        this.search = simplePath[2]\n      }\n      return this\n    }\n  }\n\n  let proto = protocolPattern.exec(rest)\n  if (proto) {\n    proto = proto[0]\n    lowerProto = proto.toLowerCase()\n    this.protocol = proto\n    rest = rest.substr(proto.length)\n  }\n\n  // figure out if it's got a host\n  // user@server is *always* interpreted as a hostname, and url\n  // resolution will treat //foo/bar as host=foo,path=bar because that's\n  // how the browser resolves relative URLs.\n  /* eslint-disable-next-line no-useless-escape */\n  if (slashesDenoteHost || proto || rest.match(/^\\/\\/[^@\\/]+@[^@\\/]+/)) {\n    slashes = rest.substr(0, 2) === '//'\n    if (slashes && !(proto && hostlessProtocol[proto])) {\n      rest = rest.substr(2)\n      this.slashes = true\n    }\n  }\n\n  if (!hostlessProtocol[proto] &&\n      (slashes || (proto && !slashedProtocol[proto]))) {\n    // there's a hostname.\n    // the first instance of /, ?, ;, or # ends the host.\n    //\n    // If there is an @ in the hostname, then non-host chars *are* allowed\n    // to the left of the last @ sign, unless some host-ending character\n    // comes *before* the @-sign.\n    // URLs are obnoxious.\n    //\n    // ex:\n    // http://a@b@c/ => user:a@b host:c\n    // http://a@b?@c => user:a host:c path:/?@c\n\n    // v0.12 TODO(isaacs): This is not quite how Chrome does things.\n    // Review our test case against browsers more comprehensively.\n\n    // find the first instance of any hostEndingChars\n    let hostEnd = -1\n    for (let i = 0; i < hostEndingChars.length; i++) {\n      hec = rest.indexOf(hostEndingChars[i])\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec\n      }\n    }\n\n    // at this point, either we have an explicit point where the\n    // auth portion cannot go past, or the last @ char is the decider.\n    let auth, atSign\n    if (hostEnd === -1) {\n      // atSign can be anywhere.\n      atSign = rest.lastIndexOf('@')\n    } else {\n      // atSign must be in auth portion.\n      // http://a@b/c@d => host:b auth:a path:/c@d\n      atSign = rest.lastIndexOf('@', hostEnd)\n    }\n\n    // Now we have a portion which is definitely the auth.\n    // Pull that off.\n    if (atSign !== -1) {\n      auth = rest.slice(0, atSign)\n      rest = rest.slice(atSign + 1)\n      this.auth = auth\n    }\n\n    // the host is the remaining to the left of the first non-host char\n    hostEnd = -1\n    for (let i = 0; i < nonHostChars.length; i++) {\n      hec = rest.indexOf(nonHostChars[i])\n      if (hec !== -1 && (hostEnd === -1 || hec < hostEnd)) {\n        hostEnd = hec\n      }\n    }\n    // if we still have not hit it, then the entire thing is a host.\n    if (hostEnd === -1) {\n      hostEnd = rest.length\n    }\n\n    if (rest[hostEnd - 1] === ':') { hostEnd-- }\n    const host = rest.slice(0, hostEnd)\n    rest = rest.slice(hostEnd)\n\n    // pull out port.\n    this.parseHost(host)\n\n    // we've indicated that there is a hostname,\n    // so even if it's empty, it has to be present.\n    this.hostname = this.hostname || ''\n\n    // if hostname begins with [ and ends with ]\n    // assume that it's an IPv6 address.\n    const ipv6Hostname = this.hostname[0] === '[' &&\n        this.hostname[this.hostname.length - 1] === ']'\n\n    // validate a little.\n    if (!ipv6Hostname) {\n      const hostparts = this.hostname.split(/\\./)\n      for (let i = 0, l = hostparts.length; i < l; i++) {\n        const part = hostparts[i]\n        if (!part) { continue }\n        if (!part.match(hostnamePartPattern)) {\n          let newpart = ''\n          for (let j = 0, k = part.length; j < k; j++) {\n            if (part.charCodeAt(j) > 127) {\n              // we replace non-ASCII char with a temporary placeholder\n              // we need this to make sure size of hostname is not\n              // broken by replacing non-ASCII by nothing\n              newpart += 'x'\n            } else {\n              newpart += part[j]\n            }\n          }\n          // we test again with ASCII char only\n          if (!newpart.match(hostnamePartPattern)) {\n            const validParts = hostparts.slice(0, i)\n            const notHost = hostparts.slice(i + 1)\n            const bit = part.match(hostnamePartStart)\n            if (bit) {\n              validParts.push(bit[1])\n              notHost.unshift(bit[2])\n            }\n            if (notHost.length) {\n              rest = notHost.join('.') + rest\n            }\n            this.hostname = validParts.join('.')\n            break\n          }\n        }\n      }\n    }\n\n    if (this.hostname.length > hostnameMaxLen) {\n      this.hostname = ''\n    }\n\n    // strip [ and ] from the hostname\n    // the host field still retains them, though\n    if (ipv6Hostname) {\n      this.hostname = this.hostname.substr(1, this.hostname.length - 2)\n    }\n  }\n\n  // chop off from the tail first.\n  const hash = rest.indexOf('#')\n  if (hash !== -1) {\n    // got a fragment string.\n    this.hash = rest.substr(hash)\n    rest = rest.slice(0, hash)\n  }\n  const qm = rest.indexOf('?')\n  if (qm !== -1) {\n    this.search = rest.substr(qm)\n    rest = rest.slice(0, qm)\n  }\n  if (rest) { this.pathname = rest }\n  if (slashedProtocol[lowerProto] &&\n      this.hostname && !this.pathname) {\n    this.pathname = ''\n  }\n\n  return this\n}\n\nUrl.prototype.parseHost = function (host) {\n  let port = portPattern.exec(host)\n  if (port) {\n    port = port[0]\n    if (port !== ':') {\n      this.port = port.substr(1)\n    }\n    host = host.substr(0, host.length - port.length)\n  }\n  if (host) { this.hostname = host }\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (urlParse);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vbWR1cmxAMi4wLjAvbm9kZV9tb2R1bGVzL21kdXJsL2xpYi9wYXJzZS5tanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0Esa0JBQWtCLEtBQUs7O0FBRXZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0EsNkNBQTZDLEtBQUs7QUFDbEQsNENBQTRDLEtBQUs7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQztBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0JBQW9CLDRCQUE0QjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esb0JBQW9CLHlCQUF5QjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEscUNBQXFDO0FBQ3JDO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsNENBQTRDLE9BQU87QUFDbkQ7QUFDQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBLDJDQUEyQyxPQUFPO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxjQUFjO0FBQ2Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsY0FBYztBQUNkOztBQUVBLGlFQUFlLFFBQVEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vbWR1cmxAMi4wLjAvbm9kZV9tb2R1bGVzL21kdXJsL2xpYi9wYXJzZS5tanM/M2ZjYyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBDb3B5cmlnaHQgSm95ZW50LCBJbmMuIGFuZCBvdGhlciBOb2RlIGNvbnRyaWJ1dG9ycy5cbi8vXG4vLyBQZXJtaXNzaW9uIGlzIGhlcmVieSBncmFudGVkLCBmcmVlIG9mIGNoYXJnZSwgdG8gYW55IHBlcnNvbiBvYnRhaW5pbmcgYVxuLy8gY29weSBvZiB0aGlzIHNvZnR3YXJlIGFuZCBhc3NvY2lhdGVkIGRvY3VtZW50YXRpb24gZmlsZXMgKHRoZVxuLy8gXCJTb2Z0d2FyZVwiKSwgdG8gZGVhbCBpbiB0aGUgU29mdHdhcmUgd2l0aG91dCByZXN0cmljdGlvbiwgaW5jbHVkaW5nXG4vLyB3aXRob3V0IGxpbWl0YXRpb24gdGhlIHJpZ2h0cyB0byB1c2UsIGNvcHksIG1vZGlmeSwgbWVyZ2UsIHB1Ymxpc2gsXG4vLyBkaXN0cmlidXRlLCBzdWJsaWNlbnNlLCBhbmQvb3Igc2VsbCBjb3BpZXMgb2YgdGhlIFNvZnR3YXJlLCBhbmQgdG8gcGVybWl0XG4vLyBwZXJzb25zIHRvIHdob20gdGhlIFNvZnR3YXJlIGlzIGZ1cm5pc2hlZCB0byBkbyBzbywgc3ViamVjdCB0byB0aGVcbi8vIGZvbGxvd2luZyBjb25kaXRpb25zOlxuLy9cbi8vIFRoZSBhYm92ZSBjb3B5cmlnaHQgbm90aWNlIGFuZCB0aGlzIHBlcm1pc3Npb24gbm90aWNlIHNoYWxsIGJlIGluY2x1ZGVkXG4vLyBpbiBhbGwgY29waWVzIG9yIHN1YnN0YW50aWFsIHBvcnRpb25zIG9mIHRoZSBTb2Z0d2FyZS5cbi8vXG4vLyBUSEUgU09GVFdBUkUgSVMgUFJPVklERUQgXCJBUyBJU1wiLCBXSVRIT1VUIFdBUlJBTlRZIE9GIEFOWSBLSU5ELCBFWFBSRVNTXG4vLyBPUiBJTVBMSUVELCBJTkNMVURJTkcgQlVUIE5PVCBMSU1JVEVEIFRPIFRIRSBXQVJSQU5USUVTIE9GXG4vLyBNRVJDSEFOVEFCSUxJVFksIEZJVE5FU1MgRk9SIEEgUEFSVElDVUxBUiBQVVJQT1NFIEFORCBOT05JTkZSSU5HRU1FTlQuIElOXG4vLyBOTyBFVkVOVCBTSEFMTCBUSEUgQVVUSE9SUyBPUiBDT1BZUklHSFQgSE9MREVSUyBCRSBMSUFCTEUgRk9SIEFOWSBDTEFJTSxcbi8vIERBTUFHRVMgT1IgT1RIRVIgTElBQklMSVRZLCBXSEVUSEVSIElOIEFOIEFDVElPTiBPRiBDT05UUkFDVCwgVE9SVCBPUlxuLy8gT1RIRVJXSVNFLCBBUklTSU5HIEZST00sIE9VVCBPRiBPUiBJTiBDT05ORUNUSU9OIFdJVEggVEhFIFNPRlRXQVJFIE9SIFRIRVxuLy8gVVNFIE9SIE9USEVSIERFQUxJTkdTIElOIFRIRSBTT0ZUV0FSRS5cblxuLy9cbi8vIENoYW5nZXMgZnJvbSBqb3llbnQvbm9kZTpcbi8vXG4vLyAxLiBObyBsZWFkaW5nIHNsYXNoIGluIHBhdGhzLFxuLy8gICAgZS5nLiBpbiBgdXJsLnBhcnNlKCdodHRwOi8vZm9vP2JhcicpYCBwYXRobmFtZSBpcyBgYCwgbm90IGAvYFxuLy9cbi8vIDIuIEJhY2tzbGFzaGVzIGFyZSBub3QgcmVwbGFjZWQgd2l0aCBzbGFzaGVzLFxuLy8gICAgc28gYGh0dHA6XFxcXGV4YW1wbGUub3JnXFxgIGlzIHRyZWF0ZWQgbGlrZSBhIHJlbGF0aXZlIHBhdGhcbi8vXG4vLyAzLiBUcmFpbGluZyBjb2xvbiBpcyB0cmVhdGVkIGxpa2UgYSBwYXJ0IG9mIHRoZSBwYXRoLFxuLy8gICAgaS5lLiBpbiBgaHR0cDovL2V4YW1wbGUub3JnOmZvb2AgcGF0aG5hbWUgaXMgYDpmb29gXG4vL1xuLy8gNC4gTm90aGluZyBpcyBVUkwtZW5jb2RlZCBpbiB0aGUgcmVzdWx0aW5nIG9iamVjdCxcbi8vICAgIChpbiBqb3llbnQvbm9kZSBzb21lIGNoYXJzIGluIGF1dGggYW5kIHBhdGhzIGFyZSBlbmNvZGVkKVxuLy9cbi8vIDUuIGB1cmwucGFyc2UoKWAgZG9lcyBub3QgaGF2ZSBgcGFyc2VRdWVyeVN0cmluZ2AgYXJndW1lbnRcbi8vXG4vLyA2LiBSZW1vdmVkIGV4dHJhbmVvdXMgcmVzdWx0IHByb3BlcnRpZXM6IGBob3N0YCwgYHBhdGhgLCBgcXVlcnlgLCBldGMuLFxuLy8gICAgd2hpY2ggY2FuIGJlIGNvbnN0cnVjdGVkIHVzaW5nIG90aGVyIHBhcnRzIG9mIHRoZSB1cmwuXG4vL1xuXG5mdW5jdGlvbiBVcmwgKCkge1xuICB0aGlzLnByb3RvY29sID0gbnVsbFxuICB0aGlzLnNsYXNoZXMgPSBudWxsXG4gIHRoaXMuYXV0aCA9IG51bGxcbiAgdGhpcy5wb3J0ID0gbnVsbFxuICB0aGlzLmhvc3RuYW1lID0gbnVsbFxuICB0aGlzLmhhc2ggPSBudWxsXG4gIHRoaXMuc2VhcmNoID0gbnVsbFxuICB0aGlzLnBhdGhuYW1lID0gbnVsbFxufVxuXG4vLyBSZWZlcmVuY2U6IFJGQyAzOTg2LCBSRkMgMTgwOCwgUkZDIDIzOTZcblxuLy8gZGVmaW5lIHRoZXNlIGhlcmUgc28gYXQgbGVhc3QgdGhleSBvbmx5IGhhdmUgdG8gYmVcbi8vIGNvbXBpbGVkIG9uY2Ugb24gdGhlIGZpcnN0IG1vZHVsZSBsb2FkLlxuY29uc3QgcHJvdG9jb2xQYXR0ZXJuID0gL14oW2EtejAtOS4rLV0rOikvaVxuY29uc3QgcG9ydFBhdHRlcm4gPSAvOlswLTldKiQvXG5cbi8vIFNwZWNpYWwgY2FzZSBmb3IgYSBzaW1wbGUgcGF0aCBVUkxcbi8qIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11c2VsZXNzLWVzY2FwZSAqL1xuY29uc3Qgc2ltcGxlUGF0aFBhdHRlcm4gPSAvXihcXC9cXC8/KD8hXFwvKVteXFw/XFxzXSopKFxcP1teXFxzXSopPyQvXG5cbi8vIFJGQyAyMzk2OiBjaGFyYWN0ZXJzIHJlc2VydmVkIGZvciBkZWxpbWl0aW5nIFVSTHMuXG4vLyBXZSBhY3R1YWxseSBqdXN0IGF1dG8tZXNjYXBlIHRoZXNlLlxuY29uc3QgZGVsaW1zID0gWyc8JywgJz4nLCAnXCInLCAnYCcsICcgJywgJ1xccicsICdcXG4nLCAnXFx0J11cblxuLy8gUkZDIDIzOTY6IGNoYXJhY3RlcnMgbm90IGFsbG93ZWQgZm9yIHZhcmlvdXMgcmVhc29ucy5cbmNvbnN0IHVud2lzZSA9IFsneycsICd9JywgJ3wnLCAnXFxcXCcsICdeJywgJ2AnXS5jb25jYXQoZGVsaW1zKVxuXG4vLyBBbGxvd2VkIGJ5IFJGQ3MsIGJ1dCBjYXVzZSBvZiBYU1MgYXR0YWNrcy4gIEFsd2F5cyBlc2NhcGUgdGhlc2UuXG5jb25zdCBhdXRvRXNjYXBlID0gWydcXCcnXS5jb25jYXQodW53aXNlKVxuLy8gQ2hhcmFjdGVycyB0aGF0IGFyZSBuZXZlciBldmVyIGFsbG93ZWQgaW4gYSBob3N0bmFtZS5cbi8vIE5vdGUgdGhhdCBhbnkgaW52YWxpZCBjaGFycyBhcmUgYWxzbyBoYW5kbGVkLCBidXQgdGhlc2Vcbi8vIGFyZSB0aGUgb25lcyB0aGF0IGFyZSAqZXhwZWN0ZWQqIHRvIGJlIHNlZW4sIHNvIHdlIGZhc3QtcGF0aFxuLy8gdGhlbS5cbmNvbnN0IG5vbkhvc3RDaGFycyA9IFsnJScsICcvJywgJz8nLCAnOycsICcjJ10uY29uY2F0KGF1dG9Fc2NhcGUpXG5jb25zdCBob3N0RW5kaW5nQ2hhcnMgPSBbJy8nLCAnPycsICcjJ11cbmNvbnN0IGhvc3RuYW1lTWF4TGVuID0gMjU1XG5jb25zdCBob3N0bmFtZVBhcnRQYXR0ZXJuID0gL15bK2EtejAtOUEtWl8tXXswLDYzfSQvXG5jb25zdCBob3N0bmFtZVBhcnRTdGFydCA9IC9eKFsrYS16MC05QS1aXy1dezAsNjN9KSguKikkL1xuLy8gcHJvdG9jb2xzIHRoYXQgY2FuIGFsbG93IFwidW5zYWZlXCIgYW5kIFwidW53aXNlXCIgY2hhcnMuXG4vLyBwcm90b2NvbHMgdGhhdCBuZXZlciBoYXZlIGEgaG9zdG5hbWUuXG5jb25zdCBob3N0bGVzc1Byb3RvY29sID0ge1xuICBqYXZhc2NyaXB0OiB0cnVlLFxuICAnamF2YXNjcmlwdDonOiB0cnVlXG59XG4vLyBwcm90b2NvbHMgdGhhdCBhbHdheXMgY29udGFpbiBhIC8vIGJpdC5cbmNvbnN0IHNsYXNoZWRQcm90b2NvbCA9IHtcbiAgaHR0cDogdHJ1ZSxcbiAgaHR0cHM6IHRydWUsXG4gIGZ0cDogdHJ1ZSxcbiAgZ29waGVyOiB0cnVlLFxuICBmaWxlOiB0cnVlLFxuICAnaHR0cDonOiB0cnVlLFxuICAnaHR0cHM6JzogdHJ1ZSxcbiAgJ2Z0cDonOiB0cnVlLFxuICAnZ29waGVyOic6IHRydWUsXG4gICdmaWxlOic6IHRydWVcbn1cblxuZnVuY3Rpb24gdXJsUGFyc2UgKHVybCwgc2xhc2hlc0Rlbm90ZUhvc3QpIHtcbiAgaWYgKHVybCAmJiB1cmwgaW5zdGFuY2VvZiBVcmwpIHJldHVybiB1cmxcblxuICBjb25zdCB1ID0gbmV3IFVybCgpXG4gIHUucGFyc2UodXJsLCBzbGFzaGVzRGVub3RlSG9zdClcbiAgcmV0dXJuIHVcbn1cblxuVXJsLnByb3RvdHlwZS5wYXJzZSA9IGZ1bmN0aW9uICh1cmwsIHNsYXNoZXNEZW5vdGVIb3N0KSB7XG4gIGxldCBsb3dlclByb3RvLCBoZWMsIHNsYXNoZXNcbiAgbGV0IHJlc3QgPSB1cmxcblxuICAvLyB0cmltIGJlZm9yZSBwcm9jZWVkaW5nLlxuICAvLyBUaGlzIGlzIHRvIHN1cHBvcnQgcGFyc2Ugc3R1ZmYgbGlrZSBcIiAgaHR0cDovL2Zvby5jb20gIFxcblwiXG4gIHJlc3QgPSByZXN0LnRyaW0oKVxuXG4gIGlmICghc2xhc2hlc0Rlbm90ZUhvc3QgJiYgdXJsLnNwbGl0KCcjJykubGVuZ3RoID09PSAxKSB7XG4gICAgLy8gVHJ5IGZhc3QgcGF0aCByZWdleHBcbiAgICBjb25zdCBzaW1wbGVQYXRoID0gc2ltcGxlUGF0aFBhdHRlcm4uZXhlYyhyZXN0KVxuICAgIGlmIChzaW1wbGVQYXRoKSB7XG4gICAgICB0aGlzLnBhdGhuYW1lID0gc2ltcGxlUGF0aFsxXVxuICAgICAgaWYgKHNpbXBsZVBhdGhbMl0pIHtcbiAgICAgICAgdGhpcy5zZWFyY2ggPSBzaW1wbGVQYXRoWzJdXG4gICAgICB9XG4gICAgICByZXR1cm4gdGhpc1xuICAgIH1cbiAgfVxuXG4gIGxldCBwcm90byA9IHByb3RvY29sUGF0dGVybi5leGVjKHJlc3QpXG4gIGlmIChwcm90bykge1xuICAgIHByb3RvID0gcHJvdG9bMF1cbiAgICBsb3dlclByb3RvID0gcHJvdG8udG9Mb3dlckNhc2UoKVxuICAgIHRoaXMucHJvdG9jb2wgPSBwcm90b1xuICAgIHJlc3QgPSByZXN0LnN1YnN0cihwcm90by5sZW5ndGgpXG4gIH1cblxuICAvLyBmaWd1cmUgb3V0IGlmIGl0J3MgZ290IGEgaG9zdFxuICAvLyB1c2VyQHNlcnZlciBpcyAqYWx3YXlzKiBpbnRlcnByZXRlZCBhcyBhIGhvc3RuYW1lLCBhbmQgdXJsXG4gIC8vIHJlc29sdXRpb24gd2lsbCB0cmVhdCAvL2Zvby9iYXIgYXMgaG9zdD1mb28scGF0aD1iYXIgYmVjYXVzZSB0aGF0J3NcbiAgLy8gaG93IHRoZSBicm93c2VyIHJlc29sdmVzIHJlbGF0aXZlIFVSTHMuXG4gIC8qIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby11c2VsZXNzLWVzY2FwZSAqL1xuICBpZiAoc2xhc2hlc0Rlbm90ZUhvc3QgfHwgcHJvdG8gfHwgcmVzdC5tYXRjaCgvXlxcL1xcL1teQFxcL10rQFteQFxcL10rLykpIHtcbiAgICBzbGFzaGVzID0gcmVzdC5zdWJzdHIoMCwgMikgPT09ICcvLydcbiAgICBpZiAoc2xhc2hlcyAmJiAhKHByb3RvICYmIGhvc3RsZXNzUHJvdG9jb2xbcHJvdG9dKSkge1xuICAgICAgcmVzdCA9IHJlc3Quc3Vic3RyKDIpXG4gICAgICB0aGlzLnNsYXNoZXMgPSB0cnVlXG4gICAgfVxuICB9XG5cbiAgaWYgKCFob3N0bGVzc1Byb3RvY29sW3Byb3RvXSAmJlxuICAgICAgKHNsYXNoZXMgfHwgKHByb3RvICYmICFzbGFzaGVkUHJvdG9jb2xbcHJvdG9dKSkpIHtcbiAgICAvLyB0aGVyZSdzIGEgaG9zdG5hbWUuXG4gICAgLy8gdGhlIGZpcnN0IGluc3RhbmNlIG9mIC8sID8sIDssIG9yICMgZW5kcyB0aGUgaG9zdC5cbiAgICAvL1xuICAgIC8vIElmIHRoZXJlIGlzIGFuIEAgaW4gdGhlIGhvc3RuYW1lLCB0aGVuIG5vbi1ob3N0IGNoYXJzICphcmUqIGFsbG93ZWRcbiAgICAvLyB0byB0aGUgbGVmdCBvZiB0aGUgbGFzdCBAIHNpZ24sIHVubGVzcyBzb21lIGhvc3QtZW5kaW5nIGNoYXJhY3RlclxuICAgIC8vIGNvbWVzICpiZWZvcmUqIHRoZSBALXNpZ24uXG4gICAgLy8gVVJMcyBhcmUgb2Jub3hpb3VzLlxuICAgIC8vXG4gICAgLy8gZXg6XG4gICAgLy8gaHR0cDovL2FAYkBjLyA9PiB1c2VyOmFAYiBob3N0OmNcbiAgICAvLyBodHRwOi8vYUBiP0BjID0+IHVzZXI6YSBob3N0OmMgcGF0aDovP0BjXG5cbiAgICAvLyB2MC4xMiBUT0RPKGlzYWFjcyk6IFRoaXMgaXMgbm90IHF1aXRlIGhvdyBDaHJvbWUgZG9lcyB0aGluZ3MuXG4gICAgLy8gUmV2aWV3IG91ciB0ZXN0IGNhc2UgYWdhaW5zdCBicm93c2VycyBtb3JlIGNvbXByZWhlbnNpdmVseS5cblxuICAgIC8vIGZpbmQgdGhlIGZpcnN0IGluc3RhbmNlIG9mIGFueSBob3N0RW5kaW5nQ2hhcnNcbiAgICBsZXQgaG9zdEVuZCA9IC0xXG4gICAgZm9yIChsZXQgaSA9IDA7IGkgPCBob3N0RW5kaW5nQ2hhcnMubGVuZ3RoOyBpKyspIHtcbiAgICAgIGhlYyA9IHJlc3QuaW5kZXhPZihob3N0RW5kaW5nQ2hhcnNbaV0pXG4gICAgICBpZiAoaGVjICE9PSAtMSAmJiAoaG9zdEVuZCA9PT0gLTEgfHwgaGVjIDwgaG9zdEVuZCkpIHtcbiAgICAgICAgaG9zdEVuZCA9IGhlY1xuICAgICAgfVxuICAgIH1cblxuICAgIC8vIGF0IHRoaXMgcG9pbnQsIGVpdGhlciB3ZSBoYXZlIGFuIGV4cGxpY2l0IHBvaW50IHdoZXJlIHRoZVxuICAgIC8vIGF1dGggcG9ydGlvbiBjYW5ub3QgZ28gcGFzdCwgb3IgdGhlIGxhc3QgQCBjaGFyIGlzIHRoZSBkZWNpZGVyLlxuICAgIGxldCBhdXRoLCBhdFNpZ25cbiAgICBpZiAoaG9zdEVuZCA9PT0gLTEpIHtcbiAgICAgIC8vIGF0U2lnbiBjYW4gYmUgYW55d2hlcmUuXG4gICAgICBhdFNpZ24gPSByZXN0Lmxhc3RJbmRleE9mKCdAJylcbiAgICB9IGVsc2Uge1xuICAgICAgLy8gYXRTaWduIG11c3QgYmUgaW4gYXV0aCBwb3J0aW9uLlxuICAgICAgLy8gaHR0cDovL2FAYi9jQGQgPT4gaG9zdDpiIGF1dGg6YSBwYXRoOi9jQGRcbiAgICAgIGF0U2lnbiA9IHJlc3QubGFzdEluZGV4T2YoJ0AnLCBob3N0RW5kKVxuICAgIH1cblxuICAgIC8vIE5vdyB3ZSBoYXZlIGEgcG9ydGlvbiB3aGljaCBpcyBkZWZpbml0ZWx5IHRoZSBhdXRoLlxuICAgIC8vIFB1bGwgdGhhdCBvZmYuXG4gICAgaWYgKGF0U2lnbiAhPT0gLTEpIHtcbiAgICAgIGF1dGggPSByZXN0LnNsaWNlKDAsIGF0U2lnbilcbiAgICAgIHJlc3QgPSByZXN0LnNsaWNlKGF0U2lnbiArIDEpXG4gICAgICB0aGlzLmF1dGggPSBhdXRoXG4gICAgfVxuXG4gICAgLy8gdGhlIGhvc3QgaXMgdGhlIHJlbWFpbmluZyB0byB0aGUgbGVmdCBvZiB0aGUgZmlyc3Qgbm9uLWhvc3QgY2hhclxuICAgIGhvc3RFbmQgPSAtMVxuICAgIGZvciAobGV0IGkgPSAwOyBpIDwgbm9uSG9zdENoYXJzLmxlbmd0aDsgaSsrKSB7XG4gICAgICBoZWMgPSByZXN0LmluZGV4T2Yobm9uSG9zdENoYXJzW2ldKVxuICAgICAgaWYgKGhlYyAhPT0gLTEgJiYgKGhvc3RFbmQgPT09IC0xIHx8IGhlYyA8IGhvc3RFbmQpKSB7XG4gICAgICAgIGhvc3RFbmQgPSBoZWNcbiAgICAgIH1cbiAgICB9XG4gICAgLy8gaWYgd2Ugc3RpbGwgaGF2ZSBub3QgaGl0IGl0LCB0aGVuIHRoZSBlbnRpcmUgdGhpbmcgaXMgYSBob3N0LlxuICAgIGlmIChob3N0RW5kID09PSAtMSkge1xuICAgICAgaG9zdEVuZCA9IHJlc3QubGVuZ3RoXG4gICAgfVxuXG4gICAgaWYgKHJlc3RbaG9zdEVuZCAtIDFdID09PSAnOicpIHsgaG9zdEVuZC0tIH1cbiAgICBjb25zdCBob3N0ID0gcmVzdC5zbGljZSgwLCBob3N0RW5kKVxuICAgIHJlc3QgPSByZXN0LnNsaWNlKGhvc3RFbmQpXG5cbiAgICAvLyBwdWxsIG91dCBwb3J0LlxuICAgIHRoaXMucGFyc2VIb3N0KGhvc3QpXG5cbiAgICAvLyB3ZSd2ZSBpbmRpY2F0ZWQgdGhhdCB0aGVyZSBpcyBhIGhvc3RuYW1lLFxuICAgIC8vIHNvIGV2ZW4gaWYgaXQncyBlbXB0eSwgaXQgaGFzIHRvIGJlIHByZXNlbnQuXG4gICAgdGhpcy5ob3N0bmFtZSA9IHRoaXMuaG9zdG5hbWUgfHwgJydcblxuICAgIC8vIGlmIGhvc3RuYW1lIGJlZ2lucyB3aXRoIFsgYW5kIGVuZHMgd2l0aCBdXG4gICAgLy8gYXNzdW1lIHRoYXQgaXQncyBhbiBJUHY2IGFkZHJlc3MuXG4gICAgY29uc3QgaXB2Nkhvc3RuYW1lID0gdGhpcy5ob3N0bmFtZVswXSA9PT0gJ1snICYmXG4gICAgICAgIHRoaXMuaG9zdG5hbWVbdGhpcy5ob3N0bmFtZS5sZW5ndGggLSAxXSA9PT0gJ10nXG5cbiAgICAvLyB2YWxpZGF0ZSBhIGxpdHRsZS5cbiAgICBpZiAoIWlwdjZIb3N0bmFtZSkge1xuICAgICAgY29uc3QgaG9zdHBhcnRzID0gdGhpcy5ob3N0bmFtZS5zcGxpdCgvXFwuLylcbiAgICAgIGZvciAobGV0IGkgPSAwLCBsID0gaG9zdHBhcnRzLmxlbmd0aDsgaSA8IGw7IGkrKykge1xuICAgICAgICBjb25zdCBwYXJ0ID0gaG9zdHBhcnRzW2ldXG4gICAgICAgIGlmICghcGFydCkgeyBjb250aW51ZSB9XG4gICAgICAgIGlmICghcGFydC5tYXRjaChob3N0bmFtZVBhcnRQYXR0ZXJuKSkge1xuICAgICAgICAgIGxldCBuZXdwYXJ0ID0gJydcbiAgICAgICAgICBmb3IgKGxldCBqID0gMCwgayA9IHBhcnQubGVuZ3RoOyBqIDwgazsgaisrKSB7XG4gICAgICAgICAgICBpZiAocGFydC5jaGFyQ29kZUF0KGopID4gMTI3KSB7XG4gICAgICAgICAgICAgIC8vIHdlIHJlcGxhY2Ugbm9uLUFTQ0lJIGNoYXIgd2l0aCBhIHRlbXBvcmFyeSBwbGFjZWhvbGRlclxuICAgICAgICAgICAgICAvLyB3ZSBuZWVkIHRoaXMgdG8gbWFrZSBzdXJlIHNpemUgb2YgaG9zdG5hbWUgaXMgbm90XG4gICAgICAgICAgICAgIC8vIGJyb2tlbiBieSByZXBsYWNpbmcgbm9uLUFTQ0lJIGJ5IG5vdGhpbmdcbiAgICAgICAgICAgICAgbmV3cGFydCArPSAneCdcbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgIG5ld3BhcnQgKz0gcGFydFtqXVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgICAvLyB3ZSB0ZXN0IGFnYWluIHdpdGggQVNDSUkgY2hhciBvbmx5XG4gICAgICAgICAgaWYgKCFuZXdwYXJ0Lm1hdGNoKGhvc3RuYW1lUGFydFBhdHRlcm4pKSB7XG4gICAgICAgICAgICBjb25zdCB2YWxpZFBhcnRzID0gaG9zdHBhcnRzLnNsaWNlKDAsIGkpXG4gICAgICAgICAgICBjb25zdCBub3RIb3N0ID0gaG9zdHBhcnRzLnNsaWNlKGkgKyAxKVxuICAgICAgICAgICAgY29uc3QgYml0ID0gcGFydC5tYXRjaChob3N0bmFtZVBhcnRTdGFydClcbiAgICAgICAgICAgIGlmIChiaXQpIHtcbiAgICAgICAgICAgICAgdmFsaWRQYXJ0cy5wdXNoKGJpdFsxXSlcbiAgICAgICAgICAgICAgbm90SG9zdC51bnNoaWZ0KGJpdFsyXSlcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGlmIChub3RIb3N0Lmxlbmd0aCkge1xuICAgICAgICAgICAgICByZXN0ID0gbm90SG9zdC5qb2luKCcuJykgKyByZXN0XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aGlzLmhvc3RuYW1lID0gdmFsaWRQYXJ0cy5qb2luKCcuJylcbiAgICAgICAgICAgIGJyZWFrXG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuXG4gICAgaWYgKHRoaXMuaG9zdG5hbWUubGVuZ3RoID4gaG9zdG5hbWVNYXhMZW4pIHtcbiAgICAgIHRoaXMuaG9zdG5hbWUgPSAnJ1xuICAgIH1cblxuICAgIC8vIHN0cmlwIFsgYW5kIF0gZnJvbSB0aGUgaG9zdG5hbWVcbiAgICAvLyB0aGUgaG9zdCBmaWVsZCBzdGlsbCByZXRhaW5zIHRoZW0sIHRob3VnaFxuICAgIGlmIChpcHY2SG9zdG5hbWUpIHtcbiAgICAgIHRoaXMuaG9zdG5hbWUgPSB0aGlzLmhvc3RuYW1lLnN1YnN0cigxLCB0aGlzLmhvc3RuYW1lLmxlbmd0aCAtIDIpXG4gICAgfVxuICB9XG5cbiAgLy8gY2hvcCBvZmYgZnJvbSB0aGUgdGFpbCBmaXJzdC5cbiAgY29uc3QgaGFzaCA9IHJlc3QuaW5kZXhPZignIycpXG4gIGlmIChoYXNoICE9PSAtMSkge1xuICAgIC8vIGdvdCBhIGZyYWdtZW50IHN0cmluZy5cbiAgICB0aGlzLmhhc2ggPSByZXN0LnN1YnN0cihoYXNoKVxuICAgIHJlc3QgPSByZXN0LnNsaWNlKDAsIGhhc2gpXG4gIH1cbiAgY29uc3QgcW0gPSByZXN0LmluZGV4T2YoJz8nKVxuICBpZiAocW0gIT09IC0xKSB7XG4gICAgdGhpcy5zZWFyY2ggPSByZXN0LnN1YnN0cihxbSlcbiAgICByZXN0ID0gcmVzdC5zbGljZSgwLCBxbSlcbiAgfVxuICBpZiAocmVzdCkgeyB0aGlzLnBhdGhuYW1lID0gcmVzdCB9XG4gIGlmIChzbGFzaGVkUHJvdG9jb2xbbG93ZXJQcm90b10gJiZcbiAgICAgIHRoaXMuaG9zdG5hbWUgJiYgIXRoaXMucGF0aG5hbWUpIHtcbiAgICB0aGlzLnBhdGhuYW1lID0gJydcbiAgfVxuXG4gIHJldHVybiB0aGlzXG59XG5cblVybC5wcm90b3R5cGUucGFyc2VIb3N0ID0gZnVuY3Rpb24gKGhvc3QpIHtcbiAgbGV0IHBvcnQgPSBwb3J0UGF0dGVybi5leGVjKGhvc3QpXG4gIGlmIChwb3J0KSB7XG4gICAgcG9ydCA9IHBvcnRbMF1cbiAgICBpZiAocG9ydCAhPT0gJzonKSB7XG4gICAgICB0aGlzLnBvcnQgPSBwb3J0LnN1YnN0cigxKVxuICAgIH1cbiAgICBob3N0ID0gaG9zdC5zdWJzdHIoMCwgaG9zdC5sZW5ndGggLSBwb3J0Lmxlbmd0aClcbiAgfVxuICBpZiAoaG9zdCkgeyB0aGlzLmhvc3RuYW1lID0gaG9zdCB9XG59XG5cbmV4cG9ydCBkZWZhdWx0IHVybFBhcnNlXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/mdurl@2.0.0/node_modules/mdurl/lib/parse.mjs\n");

/***/ })

};
;