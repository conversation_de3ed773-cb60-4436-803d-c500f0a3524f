"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+ui-utils@1.2.0_zod@3.24.2";
exports.ids = ["vendor-chunks/@ai-sdk+ui-utils@1.2.0_zod@3.24.2"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(ssr)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.0_zod@3.24.2/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(ssr)/./node_modules/.pnpm/zod-to-json-schema@3.24.5_zod@3.24.2/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage\n}) {\n  var _a, _b;\n  const response = await fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }).catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_b = await response.text()) != null ? _b : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (attachmentsFromOptions instanceof FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // check that assistant has not answered yet:\n    !isLastToolInvocationFollowedByText(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isLastToolInvocationFollowedByText(message) {\n  let isLastToolInvocationFollowedByText2 = false;\n  message.parts.forEach((part) => {\n    if (part.type === \"text\") {\n      isLastToolInvocationFollowedByText2 = true;\n    }\n    if (part.type === \"tool-invocation\") {\n      isLastToolInvocationFollowedByText2 = false;\n    }\n  });\n  return isLastToolInvocationFollowedByText2;\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  return message.role === \"assistant\" && message.parts.filter((part) => part.type === \"tool-invocation\").every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   asSchema: () => (/* binding */ asSchema),\n/* harmony export */   callChatApi: () => (/* binding */ callChatApi),\n/* harmony export */   callCompletionApi: () => (/* binding */ callCompletionApi),\n/* harmony export */   extractMaxToolInvocationStep: () => (/* binding */ extractMaxToolInvocationStep),\n/* harmony export */   fillMessageParts: () => (/* binding */ fillMessageParts),\n/* harmony export */   formatAssistantStreamPart: () => (/* binding */ formatAssistantStreamPart),\n/* harmony export */   formatDataStreamPart: () => (/* binding */ formatDataStreamPart),\n/* harmony export */   generateId: () => (/* reexport safe */ _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId),\n/* harmony export */   getMessageParts: () => (/* binding */ getMessageParts),\n/* harmony export */   getTextFromDataUrl: () => (/* binding */ getTextFromDataUrl),\n/* harmony export */   isAssistantMessageWithCompletedToolCalls: () => (/* binding */ isAssistantMessageWithCompletedToolCalls),\n/* harmony export */   isDeepEqualData: () => (/* binding */ isDeepEqualData),\n/* harmony export */   jsonSchema: () => (/* binding */ jsonSchema),\n/* harmony export */   parseAssistantStreamPart: () => (/* binding */ parseAssistantStreamPart),\n/* harmony export */   parseDataStreamPart: () => (/* binding */ parseDataStreamPart),\n/* harmony export */   parsePartialJson: () => (/* binding */ parsePartialJson),\n/* harmony export */   prepareAttachmentsForRequest: () => (/* binding */ prepareAttachmentsForRequest),\n/* harmony export */   processAssistantStream: () => (/* binding */ processAssistantStream),\n/* harmony export */   processDataStream: () => (/* binding */ processDataStream),\n/* harmony export */   processTextStream: () => (/* binding */ processTextStream),\n/* harmony export */   shouldResubmitMessages: () => (/* binding */ shouldResubmitMessages),\n/* harmony export */   updateToolCallResult: () => (/* binding */ updateToolCallResult),\n/* harmony export */   zodSchema: () => (/* binding */ zodSchema)\n/* harmony export */ });\n/* harmony import */ var _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ai-sdk/provider-utils */ \"(rsc)/./node_modules/.pnpm/@ai-sdk+provider-utils@2.2.0_zod@3.24.2/node_modules/@ai-sdk/provider-utils/dist/index.mjs\");\n/* harmony import */ var zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zod-to-json-schema */ \"(rsc)/./node_modules/.pnpm/zod-to-json-schema@3.24.5_zod@3.24.2/node_modules/zod-to-json-schema/dist/esm/index.js\");\n// src/index.ts\n\n\n// src/assistant-stream-parts.ts\nvar textStreamPart = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar errorStreamPart = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar assistantMessageStreamPart = {\n  code: \"4\",\n  name: \"assistant_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"id\" in value) || !(\"role\" in value) || !(\"content\" in value) || typeof value.id !== \"string\" || typeof value.role !== \"string\" || value.role !== \"assistant\" || !Array.isArray(value.content) || !value.content.every(\n      (item) => item != null && typeof item === \"object\" && \"type\" in item && item.type === \"text\" && \"text\" in item && item.text != null && typeof item.text === \"object\" && \"value\" in item.text && typeof item.text.value === \"string\"\n    )) {\n      throw new Error(\n        '\"assistant_message\" parts expect an object with an \"id\", \"role\", and \"content\" property.'\n      );\n    }\n    return {\n      type: \"assistant_message\",\n      value\n    };\n  }\n};\nvar assistantControlDataStreamPart = {\n  code: \"5\",\n  name: \"assistant_control_data\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"threadId\" in value) || !(\"messageId\" in value) || typeof value.threadId !== \"string\" || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"assistant_control_data\" parts expect an object with a \"threadId\" and \"messageId\" property.'\n      );\n    }\n    return {\n      type: \"assistant_control_data\",\n      value: {\n        threadId: value.threadId,\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar dataMessageStreamPart = {\n  code: \"6\",\n  name: \"data_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"role\" in value) || !(\"data\" in value) || typeof value.role !== \"string\" || value.role !== \"data\") {\n      throw new Error(\n        '\"data_message\" parts expect an object with a \"role\" and \"data\" property.'\n      );\n    }\n    return {\n      type: \"data_message\",\n      value\n    };\n  }\n};\nvar assistantStreamParts = [\n  textStreamPart,\n  errorStreamPart,\n  assistantMessageStreamPart,\n  assistantControlDataStreamPart,\n  dataMessageStreamPart\n];\nvar assistantStreamPartsByCode = {\n  [textStreamPart.code]: textStreamPart,\n  [errorStreamPart.code]: errorStreamPart,\n  [assistantMessageStreamPart.code]: assistantMessageStreamPart,\n  [assistantControlDataStreamPart.code]: assistantControlDataStreamPart,\n  [dataMessageStreamPart.code]: dataMessageStreamPart\n};\nvar StreamStringPrefixes = {\n  [textStreamPart.name]: textStreamPart.code,\n  [errorStreamPart.name]: errorStreamPart.code,\n  [assistantMessageStreamPart.name]: assistantMessageStreamPart.code,\n  [assistantControlDataStreamPart.name]: assistantControlDataStreamPart.code,\n  [dataMessageStreamPart.name]: dataMessageStreamPart.code\n};\nvar validCodes = assistantStreamParts.map((part) => part.code);\nvar parseAssistantStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return assistantStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatAssistantStreamPart(type, value) {\n  const streamPart = assistantStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-chat-response.ts\n\n\n// src/duplicated/usage.ts\nfunction calculateLanguageModelUsage({\n  promptTokens,\n  completionTokens\n}) {\n  return {\n    promptTokens,\n    completionTokens,\n    totalTokens: promptTokens + completionTokens\n  };\n}\n\n// src/parse-partial-json.ts\n\n\n// src/fix-json.ts\nfunction fixJson(input) {\n  const stack = [\"ROOT\"];\n  let lastValidIndex = -1;\n  let literalStart = null;\n  function processValueStart(char, i, swapState) {\n    {\n      switch (char) {\n        case '\"': {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_STRING\");\n          break;\n        }\n        case \"f\":\n        case \"t\":\n        case \"n\": {\n          lastValidIndex = i;\n          literalStart = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_LITERAL\");\n          break;\n        }\n        case \"-\": {\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"0\":\n        case \"1\":\n        case \"2\":\n        case \"3\":\n        case \"4\":\n        case \"5\":\n        case \"6\":\n        case \"7\":\n        case \"8\":\n        case \"9\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_NUMBER\");\n          break;\n        }\n        case \"{\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_OBJECT_START\");\n          break;\n        }\n        case \"[\": {\n          lastValidIndex = i;\n          stack.pop();\n          stack.push(swapState);\n          stack.push(\"INSIDE_ARRAY_START\");\n          break;\n        }\n      }\n    }\n  }\n  function processAfterObjectValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_OBJECT_AFTER_COMMA\");\n        break;\n      }\n      case \"}\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  function processAfterArrayValue(char, i) {\n    switch (char) {\n      case \",\": {\n        stack.pop();\n        stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n        break;\n      }\n      case \"]\": {\n        lastValidIndex = i;\n        stack.pop();\n        break;\n      }\n    }\n  }\n  for (let i = 0; i < input.length; i++) {\n    const char = input[i];\n    const currentState = stack[stack.length - 1];\n    switch (currentState) {\n      case \"ROOT\":\n        processValueStart(char, i, \"FINISH\");\n        break;\n      case \"INSIDE_OBJECT_START\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n          case \"}\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_COMMA\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_AFTER_KEY\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_KEY\": {\n        switch (char) {\n          case \":\": {\n            stack.pop();\n            stack.push(\"INSIDE_OBJECT_BEFORE_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_OBJECT_BEFORE_VALUE\": {\n        processValueStart(char, i, \"INSIDE_OBJECT_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        processAfterObjectValue(char, i);\n        break;\n      }\n      case \"INSIDE_STRING\": {\n        switch (char) {\n          case '\"': {\n            stack.pop();\n            lastValidIndex = i;\n            break;\n          }\n          case \"\\\\\": {\n            stack.push(\"INSIDE_STRING_ESCAPE\");\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_START\": {\n        switch (char) {\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        switch (char) {\n          case \",\": {\n            stack.pop();\n            stack.push(\"INSIDE_ARRAY_AFTER_COMMA\");\n            break;\n          }\n          case \"]\": {\n            lastValidIndex = i;\n            stack.pop();\n            break;\n          }\n          default: {\n            lastValidIndex = i;\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_ARRAY_AFTER_COMMA\": {\n        processValueStart(char, i, \"INSIDE_ARRAY_AFTER_VALUE\");\n        break;\n      }\n      case \"INSIDE_STRING_ESCAPE\": {\n        stack.pop();\n        lastValidIndex = i;\n        break;\n      }\n      case \"INSIDE_NUMBER\": {\n        switch (char) {\n          case \"0\":\n          case \"1\":\n          case \"2\":\n          case \"3\":\n          case \"4\":\n          case \"5\":\n          case \"6\":\n          case \"7\":\n          case \"8\":\n          case \"9\": {\n            lastValidIndex = i;\n            break;\n          }\n          case \"e\":\n          case \"E\":\n          case \"-\":\n          case \".\": {\n            break;\n          }\n          case \",\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"}\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n              processAfterObjectValue(char, i);\n            }\n            break;\n          }\n          case \"]\": {\n            stack.pop();\n            if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n              processAfterArrayValue(char, i);\n            }\n            break;\n          }\n          default: {\n            stack.pop();\n            break;\n          }\n        }\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, i + 1);\n        if (!\"false\".startsWith(partialLiteral) && !\"true\".startsWith(partialLiteral) && !\"null\".startsWith(partialLiteral)) {\n          stack.pop();\n          if (stack[stack.length - 1] === \"INSIDE_OBJECT_AFTER_VALUE\") {\n            processAfterObjectValue(char, i);\n          } else if (stack[stack.length - 1] === \"INSIDE_ARRAY_AFTER_VALUE\") {\n            processAfterArrayValue(char, i);\n          }\n        } else {\n          lastValidIndex = i;\n        }\n        break;\n      }\n    }\n  }\n  let result = input.slice(0, lastValidIndex + 1);\n  for (let i = stack.length - 1; i >= 0; i--) {\n    const state = stack[i];\n    switch (state) {\n      case \"INSIDE_STRING\": {\n        result += '\"';\n        break;\n      }\n      case \"INSIDE_OBJECT_KEY\":\n      case \"INSIDE_OBJECT_AFTER_KEY\":\n      case \"INSIDE_OBJECT_AFTER_COMMA\":\n      case \"INSIDE_OBJECT_START\":\n      case \"INSIDE_OBJECT_BEFORE_VALUE\":\n      case \"INSIDE_OBJECT_AFTER_VALUE\": {\n        result += \"}\";\n        break;\n      }\n      case \"INSIDE_ARRAY_START\":\n      case \"INSIDE_ARRAY_AFTER_COMMA\":\n      case \"INSIDE_ARRAY_AFTER_VALUE\": {\n        result += \"]\";\n        break;\n      }\n      case \"INSIDE_LITERAL\": {\n        const partialLiteral = input.substring(literalStart, input.length);\n        if (\"true\".startsWith(partialLiteral)) {\n          result += \"true\".slice(partialLiteral.length);\n        } else if (\"false\".startsWith(partialLiteral)) {\n          result += \"false\".slice(partialLiteral.length);\n        } else if (\"null\".startsWith(partialLiteral)) {\n          result += \"null\".slice(partialLiteral.length);\n        }\n      }\n    }\n  }\n  return result;\n}\n\n// src/parse-partial-json.ts\nfunction parsePartialJson(jsonText) {\n  if (jsonText === void 0) {\n    return { value: void 0, state: \"undefined-input\" };\n  }\n  let result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: jsonText });\n  if (result.success) {\n    return { value: result.value, state: \"successful-parse\" };\n  }\n  result = (0,_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.safeParseJSON)({ text: fixJson(jsonText) });\n  if (result.success) {\n    return { value: result.value, state: \"repaired-parse\" };\n  }\n  return { value: void 0, state: \"failed-parse\" };\n}\n\n// src/data-stream-parts.ts\nvar textStreamPart2 = {\n  code: \"0\",\n  name: \"text\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"text\" parts expect a string value.');\n    }\n    return { type: \"text\", value };\n  }\n};\nvar dataStreamPart = {\n  code: \"2\",\n  name: \"data\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"data\" parts expect an array value.');\n    }\n    return { type: \"data\", value };\n  }\n};\nvar errorStreamPart2 = {\n  code: \"3\",\n  name: \"error\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"error\" parts expect a string value.');\n    }\n    return { type: \"error\", value };\n  }\n};\nvar messageAnnotationsStreamPart = {\n  code: \"8\",\n  name: \"message_annotations\",\n  parse: (value) => {\n    if (!Array.isArray(value)) {\n      throw new Error('\"message_annotations\" parts expect an array value.');\n    }\n    return { type: \"message_annotations\", value };\n  }\n};\nvar toolCallStreamPart = {\n  code: \"9\",\n  name: \"tool_call\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\" || !(\"args\" in value) || typeof value.args !== \"object\") {\n      throw new Error(\n        '\"tool_call\" parts expect an object with a \"toolCallId\", \"toolName\", and \"args\" property.'\n      );\n    }\n    return {\n      type: \"tool_call\",\n      value\n    };\n  }\n};\nvar toolResultStreamPart = {\n  code: \"a\",\n  name: \"tool_result\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"result\" in value)) {\n      throw new Error(\n        '\"tool_result\" parts expect an object with a \"toolCallId\" and a \"result\" property.'\n      );\n    }\n    return {\n      type: \"tool_result\",\n      value\n    };\n  }\n};\nvar toolCallStreamingStartStreamPart = {\n  code: \"b\",\n  name: \"tool_call_streaming_start\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"toolName\" in value) || typeof value.toolName !== \"string\") {\n      throw new Error(\n        '\"tool_call_streaming_start\" parts expect an object with a \"toolCallId\" and \"toolName\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_streaming_start\",\n      value\n    };\n  }\n};\nvar toolCallDeltaStreamPart = {\n  code: \"c\",\n  name: \"tool_call_delta\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"toolCallId\" in value) || typeof value.toolCallId !== \"string\" || !(\"argsTextDelta\" in value) || typeof value.argsTextDelta !== \"string\") {\n      throw new Error(\n        '\"tool_call_delta\" parts expect an object with a \"toolCallId\" and \"argsTextDelta\" property.'\n      );\n    }\n    return {\n      type: \"tool_call_delta\",\n      value\n    };\n  }\n};\nvar finishMessageStreamPart = {\n  code: \"d\",\n  name: \"finish_message\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_message\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    return {\n      type: \"finish_message\",\n      value: result\n    };\n  }\n};\nvar finishStepStreamPart = {\n  code: \"e\",\n  name: \"finish_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"finishReason\" in value) || typeof value.finishReason !== \"string\") {\n      throw new Error(\n        '\"finish_step\" parts expect an object with a \"finishReason\" property.'\n      );\n    }\n    const result = {\n      finishReason: value.finishReason,\n      isContinued: false\n    };\n    if (\"usage\" in value && value.usage != null && typeof value.usage === \"object\" && \"promptTokens\" in value.usage && \"completionTokens\" in value.usage) {\n      result.usage = {\n        promptTokens: typeof value.usage.promptTokens === \"number\" ? value.usage.promptTokens : Number.NaN,\n        completionTokens: typeof value.usage.completionTokens === \"number\" ? value.usage.completionTokens : Number.NaN\n      };\n    }\n    if (\"isContinued\" in value && typeof value.isContinued === \"boolean\") {\n      result.isContinued = value.isContinued;\n    }\n    return {\n      type: \"finish_step\",\n      value: result\n    };\n  }\n};\nvar startStepStreamPart = {\n  code: \"f\",\n  name: \"start_step\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"messageId\" in value) || typeof value.messageId !== \"string\") {\n      throw new Error(\n        '\"start_step\" parts expect an object with an \"id\" property.'\n      );\n    }\n    return {\n      type: \"start_step\",\n      value: {\n        messageId: value.messageId\n      }\n    };\n  }\n};\nvar reasoningStreamPart = {\n  code: \"g\",\n  name: \"reasoning\",\n  parse: (value) => {\n    if (typeof value !== \"string\") {\n      throw new Error('\"reasoning\" parts expect a string value.');\n    }\n    return { type: \"reasoning\", value };\n  }\n};\nvar sourcePart = {\n  code: \"h\",\n  name: \"source\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\") {\n      throw new Error('\"source\" parts expect a Source object.');\n    }\n    return {\n      type: \"source\",\n      value\n    };\n  }\n};\nvar redactedReasoningStreamPart = {\n  code: \"i\",\n  name: \"redacted_reasoning\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\") {\n      throw new Error(\n        '\"redacted_reasoning\" parts expect an object with a \"data\" property.'\n      );\n    }\n    return { type: \"redacted_reasoning\", value: { data: value.data } };\n  }\n};\nvar reasoningSignatureStreamPart = {\n  code: \"j\",\n  name: \"reasoning_signature\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"signature\" in value) || typeof value.signature !== \"string\") {\n      throw new Error(\n        '\"reasoning_signature\" parts expect an object with a \"signature\" property.'\n      );\n    }\n    return {\n      type: \"reasoning_signature\",\n      value: { signature: value.signature }\n    };\n  }\n};\nvar fileStreamPart = {\n  code: \"k\",\n  name: \"file\",\n  parse: (value) => {\n    if (value == null || typeof value !== \"object\" || !(\"data\" in value) || typeof value.data !== \"string\" || !(\"mimeType\" in value) || typeof value.mimeType !== \"string\") {\n      throw new Error(\n        '\"file\" parts expect an object with a \"data\" and \"mimeType\" property.'\n      );\n    }\n    return { type: \"file\", value };\n  }\n};\nvar dataStreamParts = [\n  textStreamPart2,\n  dataStreamPart,\n  errorStreamPart2,\n  messageAnnotationsStreamPart,\n  toolCallStreamPart,\n  toolResultStreamPart,\n  toolCallStreamingStartStreamPart,\n  toolCallDeltaStreamPart,\n  finishMessageStreamPart,\n  finishStepStreamPart,\n  startStepStreamPart,\n  reasoningStreamPart,\n  sourcePart,\n  redactedReasoningStreamPart,\n  reasoningSignatureStreamPart,\n  fileStreamPart\n];\nvar dataStreamPartsByCode = Object.fromEntries(\n  dataStreamParts.map((part) => [part.code, part])\n);\nvar DataStreamStringPrefixes = Object.fromEntries(\n  dataStreamParts.map((part) => [part.name, part.code])\n);\nvar validCodes2 = dataStreamParts.map((part) => part.code);\nvar parseDataStreamPart = (line) => {\n  const firstSeparatorIndex = line.indexOf(\":\");\n  if (firstSeparatorIndex === -1) {\n    throw new Error(\"Failed to parse stream string. No separator found.\");\n  }\n  const prefix = line.slice(0, firstSeparatorIndex);\n  if (!validCodes2.includes(prefix)) {\n    throw new Error(`Failed to parse stream string. Invalid code ${prefix}.`);\n  }\n  const code = prefix;\n  const textValue = line.slice(firstSeparatorIndex + 1);\n  const jsonValue = JSON.parse(textValue);\n  return dataStreamPartsByCode[code].parse(jsonValue);\n};\nfunction formatDataStreamPart(type, value) {\n  const streamPart = dataStreamParts.find((part) => part.name === type);\n  if (!streamPart) {\n    throw new Error(`Invalid stream part type: ${type}`);\n  }\n  return `${streamPart.code}:${JSON.stringify(value)}\n`;\n}\n\n// src/process-data-stream.ts\nvar NEWLINE = \"\\n\".charCodeAt(0);\nfunction concatChunks(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processDataStream({\n  stream,\n  onTextPart,\n  onReasoningPart,\n  onReasoningSignaturePart,\n  onRedactedReasoningPart,\n  onSourcePart,\n  onFilePart,\n  onDataPart,\n  onErrorPart,\n  onToolCallStreamingStartPart,\n  onToolCallDeltaPart,\n  onToolCallPart,\n  onToolResultPart,\n  onMessageAnnotationsPart,\n  onFinishMessagePart,\n  onFinishStepPart,\n  onStartStepPart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseDataStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"reasoning\":\n          await (onReasoningPart == null ? void 0 : onReasoningPart(value2));\n          break;\n        case \"reasoning_signature\":\n          await (onReasoningSignaturePart == null ? void 0 : onReasoningSignaturePart(value2));\n          break;\n        case \"redacted_reasoning\":\n          await (onRedactedReasoningPart == null ? void 0 : onRedactedReasoningPart(value2));\n          break;\n        case \"file\":\n          await (onFilePart == null ? void 0 : onFilePart(value2));\n          break;\n        case \"source\":\n          await (onSourcePart == null ? void 0 : onSourcePart(value2));\n          break;\n        case \"data\":\n          await (onDataPart == null ? void 0 : onDataPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"message_annotations\":\n          await (onMessageAnnotationsPart == null ? void 0 : onMessageAnnotationsPart(value2));\n          break;\n        case \"tool_call_streaming_start\":\n          await (onToolCallStreamingStartPart == null ? void 0 : onToolCallStreamingStartPart(value2));\n          break;\n        case \"tool_call_delta\":\n          await (onToolCallDeltaPart == null ? void 0 : onToolCallDeltaPart(value2));\n          break;\n        case \"tool_call\":\n          await (onToolCallPart == null ? void 0 : onToolCallPart(value2));\n          break;\n        case \"tool_result\":\n          await (onToolResultPart == null ? void 0 : onToolResultPart(value2));\n          break;\n        case \"finish_message\":\n          await (onFinishMessagePart == null ? void 0 : onFinishMessagePart(value2));\n          break;\n        case \"finish_step\":\n          await (onFinishStepPart == null ? void 0 : onFinishStepPart(value2));\n          break;\n        case \"start_step\":\n          await (onStartStepPart == null ? void 0 : onStartStepPart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/process-chat-response.ts\nasync function processChatResponse({\n  stream,\n  update,\n  onToolCall,\n  onFinish,\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  lastMessage\n}) {\n  var _a, _b;\n  const replaceLastMessage = (lastMessage == null ? void 0 : lastMessage.role) === \"assistant\";\n  let step = replaceLastMessage ? 1 + // find max step in existing tool invocations:\n  ((_b = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.reduce((max, toolInvocation) => {\n    var _a2;\n    return Math.max(max, (_a2 = toolInvocation.step) != null ? _a2 : 0);\n  }, 0)) != null ? _b : 0) : 0;\n  const message = replaceLastMessage ? structuredClone(lastMessage) : {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: []\n  };\n  let currentTextPart = void 0;\n  let currentReasoningPart = void 0;\n  let currentReasoningTextDetail = void 0;\n  function updateToolInvocationPart(toolCallId, invocation) {\n    const part = message.parts.find(\n      (part2) => part2.type === \"tool-invocation\" && part2.toolInvocation.toolCallId === toolCallId\n    );\n    if (part != null) {\n      part.toolInvocation = invocation;\n    } else {\n      message.parts.push({\n        type: \"tool-invocation\",\n        toolInvocation: invocation\n      });\n    }\n  }\n  const data = [];\n  let messageAnnotations = replaceLastMessage ? lastMessage == null ? void 0 : lastMessage.annotations : void 0;\n  const partialToolCalls = {};\n  let usage = {\n    completionTokens: NaN,\n    promptTokens: NaN,\n    totalTokens: NaN\n  };\n  let finishReason = \"unknown\";\n  function execUpdate() {\n    const copiedData = [...data];\n    if (messageAnnotations == null ? void 0 : messageAnnotations.length) {\n      message.annotations = messageAnnotations;\n    }\n    const copiedMessage = {\n      // deep copy the message to ensure that deep changes (msg attachments) are updated\n      // with SolidJS. SolidJS uses referential integration of sub-objects to detect changes.\n      ...structuredClone(message),\n      // add a revision id to ensure that the message is updated with SWR. SWR uses a\n      // hashing approach by default to detect changes, but it only works for shallow\n      // changes. This is why we need to add a revision id to ensure that the message\n      // is updated with SWR (without it, the changes get stuck in SWR and are not\n      // forwarded to rendering):\n      revisionId: generateId2()\n    };\n    update({\n      message: copiedMessage,\n      data: copiedData,\n      replaceLastMessage\n    });\n  }\n  await processDataStream({\n    stream,\n    onTextPart(value) {\n      if (currentTextPart == null) {\n        currentTextPart = {\n          type: \"text\",\n          text: value\n        };\n        message.parts.push(currentTextPart);\n      } else {\n        currentTextPart.text += value;\n      }\n      message.content += value;\n      execUpdate();\n    },\n    onReasoningPart(value) {\n      var _a2;\n      if (currentReasoningTextDetail == null) {\n        currentReasoningTextDetail = { type: \"text\", text: value };\n        if (currentReasoningPart != null) {\n          currentReasoningPart.details.push(currentReasoningTextDetail);\n        }\n      } else {\n        currentReasoningTextDetail.text += value;\n      }\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: value,\n          details: [currentReasoningTextDetail]\n        };\n        message.parts.push(currentReasoningPart);\n      } else {\n        currentReasoningPart.reasoning += value;\n      }\n      message.reasoning = ((_a2 = message.reasoning) != null ? _a2 : \"\") + value;\n      execUpdate();\n    },\n    onReasoningSignaturePart(value) {\n      if (currentReasoningTextDetail != null) {\n        currentReasoningTextDetail.signature = value.signature;\n      }\n    },\n    onRedactedReasoningPart(value) {\n      if (currentReasoningPart == null) {\n        currentReasoningPart = {\n          type: \"reasoning\",\n          reasoning: \"\",\n          details: []\n        };\n        message.parts.push(currentReasoningPart);\n      }\n      currentReasoningPart.details.push({\n        type: \"redacted\",\n        data: value.data\n      });\n      currentReasoningTextDetail = void 0;\n      execUpdate();\n    },\n    onFilePart(value) {\n      message.parts.push({\n        type: \"file\",\n        mimeType: value.mimeType,\n        data: value.data\n      });\n      execUpdate();\n    },\n    onSourcePart(value) {\n      message.parts.push({\n        type: \"source\",\n        source: value\n      });\n      execUpdate();\n    },\n    onToolCallStreamingStartPart(value) {\n      if (message.toolInvocations == null) {\n        message.toolInvocations = [];\n      }\n      partialToolCalls[value.toolCallId] = {\n        text: \"\",\n        step,\n        toolName: value.toolName,\n        index: message.toolInvocations.length\n      };\n      const invocation = {\n        state: \"partial-call\",\n        step,\n        toolCallId: value.toolCallId,\n        toolName: value.toolName,\n        args: void 0\n      };\n      message.toolInvocations.push(invocation);\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onToolCallDeltaPart(value) {\n      const partialToolCall = partialToolCalls[value.toolCallId];\n      partialToolCall.text += value.argsTextDelta;\n      const { value: partialArgs } = parsePartialJson(partialToolCall.text);\n      const invocation = {\n        state: \"partial-call\",\n        step: partialToolCall.step,\n        toolCallId: value.toolCallId,\n        toolName: partialToolCall.toolName,\n        args: partialArgs\n      };\n      message.toolInvocations[partialToolCall.index] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    async onToolCallPart(value) {\n      const invocation = {\n        state: \"call\",\n        step,\n        ...value\n      };\n      if (partialToolCalls[value.toolCallId] != null) {\n        message.toolInvocations[partialToolCalls[value.toolCallId].index] = invocation;\n      } else {\n        if (message.toolInvocations == null) {\n          message.toolInvocations = [];\n        }\n        message.toolInvocations.push(invocation);\n      }\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n      if (onToolCall) {\n        const result = await onToolCall({ toolCall: value });\n        if (result != null) {\n          const invocation2 = {\n            state: \"result\",\n            step,\n            ...value,\n            result\n          };\n          message.toolInvocations[message.toolInvocations.length - 1] = invocation2;\n          updateToolInvocationPart(value.toolCallId, invocation2);\n          execUpdate();\n        }\n      }\n    },\n    onToolResultPart(value) {\n      const toolInvocations = message.toolInvocations;\n      if (toolInvocations == null) {\n        throw new Error(\"tool_result must be preceded by a tool_call\");\n      }\n      const toolInvocationIndex = toolInvocations.findIndex(\n        (invocation2) => invocation2.toolCallId === value.toolCallId\n      );\n      if (toolInvocationIndex === -1) {\n        throw new Error(\n          \"tool_result must be preceded by a tool_call with the same toolCallId\"\n        );\n      }\n      const invocation = {\n        ...toolInvocations[toolInvocationIndex],\n        state: \"result\",\n        ...value\n      };\n      toolInvocations[toolInvocationIndex] = invocation;\n      updateToolInvocationPart(value.toolCallId, invocation);\n      execUpdate();\n    },\n    onDataPart(value) {\n      data.push(...value);\n      execUpdate();\n    },\n    onMessageAnnotationsPart(value) {\n      if (messageAnnotations == null) {\n        messageAnnotations = [...value];\n      } else {\n        messageAnnotations.push(...value);\n      }\n      execUpdate();\n    },\n    onFinishStepPart(value) {\n      step += 1;\n      currentTextPart = value.isContinued ? currentTextPart : void 0;\n      currentReasoningPart = void 0;\n      currentReasoningTextDetail = void 0;\n    },\n    onStartStepPart(value) {\n      if (!replaceLastMessage) {\n        message.id = value.messageId;\n      }\n    },\n    onFinishMessagePart(value) {\n      finishReason = value.finishReason;\n      if (value.usage != null) {\n        usage = calculateLanguageModelUsage(value.usage);\n      }\n    },\n    onErrorPart(error) {\n      throw new Error(error);\n    }\n  });\n  onFinish == null ? void 0 : onFinish({ message, finishReason, usage });\n}\n\n// src/process-chat-text-response.ts\n\n\n// src/process-text-stream.ts\nasync function processTextStream({\n  stream,\n  onTextPart\n}) {\n  const reader = stream.pipeThrough(new TextDecoderStream()).getReader();\n  while (true) {\n    const { done, value } = await reader.read();\n    if (done) {\n      break;\n    }\n    await onTextPart(value);\n  }\n}\n\n// src/process-chat-text-response.ts\nasync function processChatTextResponse({\n  stream,\n  update,\n  onFinish,\n  getCurrentDate = () => /* @__PURE__ */ new Date(),\n  generateId: generateId2 = _ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.generateId\n}) {\n  const textPart = { type: \"text\", text: \"\" };\n  const resultMessage = {\n    id: generateId2(),\n    createdAt: getCurrentDate(),\n    role: \"assistant\",\n    content: \"\",\n    parts: [textPart]\n  };\n  await processTextStream({\n    stream,\n    onTextPart: (chunk) => {\n      resultMessage.content += chunk;\n      textPart.text += chunk;\n      update({\n        message: { ...resultMessage },\n        data: [],\n        replaceLastMessage: false\n      });\n    }\n  });\n  onFinish == null ? void 0 : onFinish(resultMessage, {\n    usage: { completionTokens: NaN, promptTokens: NaN, totalTokens: NaN },\n    finishReason: \"unknown\"\n  });\n}\n\n// src/call-chat-api.ts\nvar getOriginalFetch = () => fetch;\nasync function callChatApi({\n  api,\n  body,\n  streamProtocol = \"data\",\n  credentials,\n  headers,\n  abortController,\n  restoreMessagesOnFailure,\n  onResponse,\n  onUpdate,\n  onFinish,\n  onToolCall,\n  generateId: generateId2,\n  fetch: fetch2 = getOriginalFetch(),\n  lastMessage\n}) {\n  var _a, _b;\n  const response = await fetch2(api, {\n    method: \"POST\",\n    body: JSON.stringify(body),\n    headers: {\n      \"Content-Type\": \"application/json\",\n      ...headers\n    },\n    signal: (_a = abortController == null ? void 0 : abortController()) == null ? void 0 : _a.signal,\n    credentials\n  }).catch((err) => {\n    restoreMessagesOnFailure();\n    throw err;\n  });\n  if (onResponse) {\n    try {\n      await onResponse(response);\n    } catch (err) {\n      throw err;\n    }\n  }\n  if (!response.ok) {\n    restoreMessagesOnFailure();\n    throw new Error(\n      (_b = await response.text()) != null ? _b : \"Failed to fetch the chat response.\"\n    );\n  }\n  if (!response.body) {\n    throw new Error(\"The response body is empty.\");\n  }\n  switch (streamProtocol) {\n    case \"text\": {\n      await processChatTextResponse({\n        stream: response.body,\n        update: onUpdate,\n        onFinish,\n        generateId: generateId2\n      });\n      return;\n    }\n    case \"data\": {\n      await processChatResponse({\n        stream: response.body,\n        update: onUpdate,\n        lastMessage,\n        onToolCall,\n        onFinish({ message, finishReason, usage }) {\n          if (onFinish && message != null) {\n            onFinish(message, { usage, finishReason });\n          }\n        },\n        generateId: generateId2\n      });\n      return;\n    }\n    default: {\n      const exhaustiveCheck = streamProtocol;\n      throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n    }\n  }\n}\n\n// src/call-completion-api.ts\nvar getOriginalFetch2 = () => fetch;\nasync function callCompletionApi({\n  api,\n  prompt,\n  credentials,\n  headers,\n  body,\n  streamProtocol = \"data\",\n  setCompletion,\n  setLoading,\n  setError,\n  setAbortController,\n  onResponse,\n  onFinish,\n  onError,\n  onData,\n  fetch: fetch2 = getOriginalFetch2()\n}) {\n  var _a;\n  try {\n    setLoading(true);\n    setError(void 0);\n    const abortController = new AbortController();\n    setAbortController(abortController);\n    setCompletion(\"\");\n    const response = await fetch2(api, {\n      method: \"POST\",\n      body: JSON.stringify({\n        prompt,\n        ...body\n      }),\n      credentials,\n      headers: {\n        \"Content-Type\": \"application/json\",\n        ...headers\n      },\n      signal: abortController.signal\n    }).catch((err) => {\n      throw err;\n    });\n    if (onResponse) {\n      try {\n        await onResponse(response);\n      } catch (err) {\n        throw err;\n      }\n    }\n    if (!response.ok) {\n      throw new Error(\n        (_a = await response.text()) != null ? _a : \"Failed to fetch the chat response.\"\n      );\n    }\n    if (!response.body) {\n      throw new Error(\"The response body is empty.\");\n    }\n    let result = \"\";\n    switch (streamProtocol) {\n      case \"text\": {\n        await processTextStream({\n          stream: response.body,\n          onTextPart: (chunk) => {\n            result += chunk;\n            setCompletion(result);\n          }\n        });\n        break;\n      }\n      case \"data\": {\n        await processDataStream({\n          stream: response.body,\n          onTextPart(value) {\n            result += value;\n            setCompletion(result);\n          },\n          onDataPart(value) {\n            onData == null ? void 0 : onData(value);\n          },\n          onErrorPart(value) {\n            throw new Error(value);\n          }\n        });\n        break;\n      }\n      default: {\n        const exhaustiveCheck = streamProtocol;\n        throw new Error(`Unknown stream protocol: ${exhaustiveCheck}`);\n      }\n    }\n    if (onFinish) {\n      onFinish(prompt, result);\n    }\n    setAbortController(null);\n    return result;\n  } catch (err) {\n    if (err.name === \"AbortError\") {\n      setAbortController(null);\n      return null;\n    }\n    if (err instanceof Error) {\n      if (onError) {\n        onError(err);\n      }\n    }\n    setError(err);\n  } finally {\n    setLoading(false);\n  }\n}\n\n// src/data-url.ts\nfunction getTextFromDataUrl(dataUrl) {\n  const [header, base64Content] = dataUrl.split(\",\");\n  const mimeType = header.split(\";\")[0].split(\":\")[1];\n  if (mimeType == null || base64Content == null) {\n    throw new Error(\"Invalid data URL format\");\n  }\n  try {\n    return window.atob(base64Content);\n  } catch (error) {\n    throw new Error(`Error decoding data URL`);\n  }\n}\n\n// src/extract-max-tool-invocation-step.ts\nfunction extractMaxToolInvocationStep(toolInvocations) {\n  return toolInvocations == null ? void 0 : toolInvocations.reduce((max, toolInvocation) => {\n    var _a;\n    return Math.max(max, (_a = toolInvocation.step) != null ? _a : 0);\n  }, 0);\n}\n\n// src/get-message-parts.ts\nfunction getMessageParts(message) {\n  var _a;\n  return (_a = message.parts) != null ? _a : [\n    ...message.toolInvocations ? message.toolInvocations.map((toolInvocation) => ({\n      type: \"tool-invocation\",\n      toolInvocation\n    })) : [],\n    ...message.reasoning ? [\n      {\n        type: \"reasoning\",\n        reasoning: message.reasoning,\n        details: [{ type: \"text\", text: message.reasoning }]\n      }\n    ] : [],\n    ...message.content ? [{ type: \"text\", text: message.content }] : []\n  ];\n}\n\n// src/fill-message-parts.ts\nfunction fillMessageParts(messages) {\n  return messages.map((message) => ({\n    ...message,\n    parts: getMessageParts(message)\n  }));\n}\n\n// src/is-deep-equal-data.ts\nfunction isDeepEqualData(obj1, obj2) {\n  if (obj1 === obj2)\n    return true;\n  if (obj1 == null || obj2 == null)\n    return false;\n  if (typeof obj1 !== \"object\" && typeof obj2 !== \"object\")\n    return obj1 === obj2;\n  if (obj1.constructor !== obj2.constructor)\n    return false;\n  if (obj1 instanceof Date && obj2 instanceof Date) {\n    return obj1.getTime() === obj2.getTime();\n  }\n  if (Array.isArray(obj1)) {\n    if (obj1.length !== obj2.length)\n      return false;\n    for (let i = 0; i < obj1.length; i++) {\n      if (!isDeepEqualData(obj1[i], obj2[i]))\n        return false;\n    }\n    return true;\n  }\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n  if (keys1.length !== keys2.length)\n    return false;\n  for (const key of keys1) {\n    if (!keys2.includes(key))\n      return false;\n    if (!isDeepEqualData(obj1[key], obj2[key]))\n      return false;\n  }\n  return true;\n}\n\n// src/prepare-attachments-for-request.ts\nasync function prepareAttachmentsForRequest(attachmentsFromOptions) {\n  if (!attachmentsFromOptions) {\n    return [];\n  }\n  if (attachmentsFromOptions instanceof FileList) {\n    return Promise.all(\n      Array.from(attachmentsFromOptions).map(async (attachment) => {\n        const { name, type } = attachment;\n        const dataUrl = await new Promise((resolve, reject) => {\n          const reader = new FileReader();\n          reader.onload = (readerEvent) => {\n            var _a;\n            resolve((_a = readerEvent.target) == null ? void 0 : _a.result);\n          };\n          reader.onerror = (error) => reject(error);\n          reader.readAsDataURL(attachment);\n        });\n        return {\n          name,\n          contentType: type,\n          url: dataUrl\n        };\n      })\n    );\n  }\n  if (Array.isArray(attachmentsFromOptions)) {\n    return attachmentsFromOptions;\n  }\n  throw new Error(\"Invalid attachments type\");\n}\n\n// src/process-assistant-stream.ts\nvar NEWLINE2 = \"\\n\".charCodeAt(0);\nfunction concatChunks2(chunks, totalLength) {\n  const concatenatedChunks = new Uint8Array(totalLength);\n  let offset = 0;\n  for (const chunk of chunks) {\n    concatenatedChunks.set(chunk, offset);\n    offset += chunk.length;\n  }\n  chunks.length = 0;\n  return concatenatedChunks;\n}\nasync function processAssistantStream({\n  stream,\n  onTextPart,\n  onErrorPart,\n  onAssistantMessagePart,\n  onAssistantControlDataPart,\n  onDataMessagePart\n}) {\n  const reader = stream.getReader();\n  const decoder = new TextDecoder();\n  const chunks = [];\n  let totalLength = 0;\n  while (true) {\n    const { value } = await reader.read();\n    if (value) {\n      chunks.push(value);\n      totalLength += value.length;\n      if (value[value.length - 1] !== NEWLINE2) {\n        continue;\n      }\n    }\n    if (chunks.length === 0) {\n      break;\n    }\n    const concatenatedChunks = concatChunks2(chunks, totalLength);\n    totalLength = 0;\n    const streamParts = decoder.decode(concatenatedChunks, { stream: true }).split(\"\\n\").filter((line) => line !== \"\").map(parseAssistantStreamPart);\n    for (const { type, value: value2 } of streamParts) {\n      switch (type) {\n        case \"text\":\n          await (onTextPart == null ? void 0 : onTextPart(value2));\n          break;\n        case \"error\":\n          await (onErrorPart == null ? void 0 : onErrorPart(value2));\n          break;\n        case \"assistant_message\":\n          await (onAssistantMessagePart == null ? void 0 : onAssistantMessagePart(value2));\n          break;\n        case \"assistant_control_data\":\n          await (onAssistantControlDataPart == null ? void 0 : onAssistantControlDataPart(value2));\n          break;\n        case \"data_message\":\n          await (onDataMessagePart == null ? void 0 : onDataMessagePart(value2));\n          break;\n        default: {\n          const exhaustiveCheck = type;\n          throw new Error(`Unknown stream part type: ${exhaustiveCheck}`);\n        }\n      }\n    }\n  }\n}\n\n// src/schema.ts\n\n\n// src/zod-schema.ts\n\nfunction zodSchema(zodSchema2, options) {\n  var _a;\n  const useReferences = (_a = options == null ? void 0 : options.useReferences) != null ? _a : false;\n  return jsonSchema(\n    (0,zod_to_json_schema__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(zodSchema2, {\n      $refStrategy: useReferences ? \"root\" : \"none\",\n      target: \"jsonSchema7\"\n      // note: openai mode breaks various gemini conversions\n    }),\n    {\n      validate: (value) => {\n        const result = zodSchema2.safeParse(value);\n        return result.success ? { success: true, value: result.data } : { success: false, error: result.error };\n      }\n    }\n  );\n}\n\n// src/schema.ts\nvar schemaSymbol = Symbol.for(\"vercel.ai.schema\");\nfunction jsonSchema(jsonSchema2, {\n  validate\n} = {}) {\n  return {\n    [schemaSymbol]: true,\n    _type: void 0,\n    // should never be used directly\n    [_ai_sdk_provider_utils__WEBPACK_IMPORTED_MODULE_0__.validatorSymbol]: true,\n    jsonSchema: jsonSchema2,\n    validate\n  };\n}\nfunction isSchema(value) {\n  return typeof value === \"object\" && value !== null && schemaSymbol in value && value[schemaSymbol] === true && \"jsonSchema\" in value && \"validate\" in value;\n}\nfunction asSchema(schema) {\n  return isSchema(schema) ? schema : zodSchema(schema);\n}\n\n// src/should-resubmit-messages.ts\nfunction shouldResubmitMessages({\n  originalMaxToolInvocationStep,\n  originalMessageCount,\n  maxSteps,\n  messages\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  return (\n    // check if the feature is enabled:\n    maxSteps > 1 && // ensure there is a last message:\n    lastMessage != null && // ensure we actually have new steps (to prevent infinite loops in case of errors):\n    (messages.length > originalMessageCount || extractMaxToolInvocationStep(lastMessage.toolInvocations) !== originalMaxToolInvocationStep) && // check that next step is possible:\n    isAssistantMessageWithCompletedToolCalls(lastMessage) && // check that assistant has not answered yet:\n    !isLastToolInvocationFollowedByText(lastMessage) && // limit the number of automatic steps:\n    ((_a = extractMaxToolInvocationStep(lastMessage.toolInvocations)) != null ? _a : 0) < maxSteps\n  );\n}\nfunction isLastToolInvocationFollowedByText(message) {\n  let isLastToolInvocationFollowedByText2 = false;\n  message.parts.forEach((part) => {\n    if (part.type === \"text\") {\n      isLastToolInvocationFollowedByText2 = true;\n    }\n    if (part.type === \"tool-invocation\") {\n      isLastToolInvocationFollowedByText2 = false;\n    }\n  });\n  return isLastToolInvocationFollowedByText2;\n}\nfunction isAssistantMessageWithCompletedToolCalls(message) {\n  return message.role === \"assistant\" && message.parts.filter((part) => part.type === \"tool-invocation\").every((part) => \"result\" in part.toolInvocation);\n}\n\n// src/update-tool-call-result.ts\nfunction updateToolCallResult({\n  messages,\n  toolCallId,\n  toolResult: result\n}) {\n  var _a;\n  const lastMessage = messages[messages.length - 1];\n  const invocationPart = lastMessage.parts.find(\n    (part) => part.type === \"tool-invocation\" && part.toolInvocation.toolCallId === toolCallId\n  );\n  if (invocationPart == null) {\n    return;\n  }\n  const toolResult = {\n    ...invocationPart.toolInvocation,\n    state: \"result\",\n    result\n  };\n  invocationPart.toolInvocation = toolResult;\n  lastMessage.toolInvocations = (_a = lastMessage.toolInvocations) == null ? void 0 : _a.map(\n    (toolInvocation) => toolInvocation.toolCallId === toolCallId ? toolResult : toolInvocation\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+ui-utils@1.2.0_zod@3.24.2/node_modules/@ai-sdk/ui-utils/dist/index.mjs\n");

/***/ })

};
;