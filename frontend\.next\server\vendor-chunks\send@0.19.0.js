"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/send@0.19.0";
exports.ids = ["vendor-chunks/send@0.19.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js":
/*!*******************************************************************!*\
  !*** ./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * send\n * Copyright(c) 2012 TJ Holowaychuk\n * Copyright(c) 2014-2022 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar createError = __webpack_require__(/*! http-errors */ \"(rsc)/./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js\")\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('send')\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('send')\nvar destroy = __webpack_require__(/*! destroy */ \"(rsc)/./node_modules/.pnpm/destroy@1.2.0/node_modules/destroy/index.js\")\nvar encodeUrl = __webpack_require__(/*! encodeurl */ \"(rsc)/./node_modules/.pnpm/encodeurl@1.0.2/node_modules/encodeurl/index.js\")\nvar escapeHtml = __webpack_require__(/*! escape-html */ \"(rsc)/./node_modules/.pnpm/escape-html@1.0.3/node_modules/escape-html/index.js\")\nvar etag = __webpack_require__(/*! etag */ \"(rsc)/./node_modules/.pnpm/etag@1.8.1/node_modules/etag/index.js\")\nvar fresh = __webpack_require__(/*! fresh */ \"(rsc)/./node_modules/.pnpm/fresh@0.5.2/node_modules/fresh/index.js\")\nvar fs = __webpack_require__(/*! fs */ \"fs\")\nvar mime = __webpack_require__(/*! mime */ \"(rsc)/./node_modules/.pnpm/mime@1.6.0/node_modules/mime/mime.js\")\nvar ms = __webpack_require__(/*! ms */ \"(rsc)/./node_modules/.pnpm/ms@2.1.3/node_modules/ms/index.js\")\nvar onFinished = __webpack_require__(/*! on-finished */ \"(rsc)/./node_modules/.pnpm/on-finished@2.4.1/node_modules/on-finished/index.js\")\nvar parseRange = __webpack_require__(/*! range-parser */ \"(rsc)/./node_modules/.pnpm/range-parser@1.2.1/node_modules/range-parser/index.js\")\nvar path = __webpack_require__(/*! path */ \"path\")\nvar statuses = __webpack_require__(/*! statuses */ \"(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js\")\nvar Stream = __webpack_require__(/*! stream */ \"stream\")\nvar util = __webpack_require__(/*! util */ \"util\")\n\n/**\n * Path function references.\n * @private\n */\n\nvar extname = path.extname\nvar join = path.join\nvar normalize = path.normalize\nvar resolve = path.resolve\nvar sep = path.sep\n\n/**\n * Regular expression for identifying a bytes Range header.\n * @private\n */\n\nvar BYTES_RANGE_REGEXP = /^ *bytes=/\n\n/**\n * Maximum value allowed for the max age.\n * @private\n */\n\nvar MAX_MAXAGE = 60 * 60 * 24 * 365 * 1000 // 1 year\n\n/**\n * Regular expression to match a path with a directory up component.\n * @private\n */\n\nvar UP_PATH_REGEXP = /(?:^|[\\\\/])\\.\\.(?:[\\\\/]|$)/\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = send\nmodule.exports.mime = mime\n\n/**\n * Return a `SendStream` for `req` and `path`.\n *\n * @param {object} req\n * @param {string} path\n * @param {object} [options]\n * @return {SendStream}\n * @public\n */\n\nfunction send (req, path, options) {\n  return new SendStream(req, path, options)\n}\n\n/**\n * Initialize a `SendStream` with the given `path`.\n *\n * @param {Request} req\n * @param {String} path\n * @param {object} [options]\n * @private\n */\n\nfunction SendStream (req, path, options) {\n  Stream.call(this)\n\n  var opts = options || {}\n\n  this.options = opts\n  this.path = path\n  this.req = req\n\n  this._acceptRanges = opts.acceptRanges !== undefined\n    ? Boolean(opts.acceptRanges)\n    : true\n\n  this._cacheControl = opts.cacheControl !== undefined\n    ? Boolean(opts.cacheControl)\n    : true\n\n  this._etag = opts.etag !== undefined\n    ? Boolean(opts.etag)\n    : true\n\n  this._dotfiles = opts.dotfiles !== undefined\n    ? opts.dotfiles\n    : 'ignore'\n\n  if (this._dotfiles !== 'ignore' && this._dotfiles !== 'allow' && this._dotfiles !== 'deny') {\n    throw new TypeError('dotfiles option must be \"allow\", \"deny\", or \"ignore\"')\n  }\n\n  this._hidden = Boolean(opts.hidden)\n\n  if (opts.hidden !== undefined) {\n    deprecate('hidden: use dotfiles: \\'' + (this._hidden ? 'allow' : 'ignore') + '\\' instead')\n  }\n\n  // legacy support\n  if (opts.dotfiles === undefined) {\n    this._dotfiles = undefined\n  }\n\n  this._extensions = opts.extensions !== undefined\n    ? normalizeList(opts.extensions, 'extensions option')\n    : []\n\n  this._immutable = opts.immutable !== undefined\n    ? Boolean(opts.immutable)\n    : false\n\n  this._index = opts.index !== undefined\n    ? normalizeList(opts.index, 'index option')\n    : ['index.html']\n\n  this._lastModified = opts.lastModified !== undefined\n    ? Boolean(opts.lastModified)\n    : true\n\n  this._maxage = opts.maxAge || opts.maxage\n  this._maxage = typeof this._maxage === 'string'\n    ? ms(this._maxage)\n    : Number(this._maxage)\n  this._maxage = !isNaN(this._maxage)\n    ? Math.min(Math.max(0, this._maxage), MAX_MAXAGE)\n    : 0\n\n  this._root = opts.root\n    ? resolve(opts.root)\n    : null\n\n  if (!this._root && opts.from) {\n    this.from(opts.from)\n  }\n}\n\n/**\n * Inherits from `Stream`.\n */\n\nutil.inherits(SendStream, Stream)\n\n/**\n * Enable or disable etag generation.\n *\n * @param {Boolean} val\n * @return {SendStream}\n * @api public\n */\n\nSendStream.prototype.etag = deprecate.function(function etag (val) {\n  this._etag = Boolean(val)\n  debug('etag %s', this._etag)\n  return this\n}, 'send.etag: pass etag as option')\n\n/**\n * Enable or disable \"hidden\" (dot) files.\n *\n * @param {Boolean} path\n * @return {SendStream}\n * @api public\n */\n\nSendStream.prototype.hidden = deprecate.function(function hidden (val) {\n  this._hidden = Boolean(val)\n  this._dotfiles = undefined\n  debug('hidden %s', this._hidden)\n  return this\n}, 'send.hidden: use dotfiles option')\n\n/**\n * Set index `paths`, set to a falsy\n * value to disable index support.\n *\n * @param {String|Boolean|Array} paths\n * @return {SendStream}\n * @api public\n */\n\nSendStream.prototype.index = deprecate.function(function index (paths) {\n  var index = !paths ? [] : normalizeList(paths, 'paths argument')\n  debug('index %o', paths)\n  this._index = index\n  return this\n}, 'send.index: pass index as option')\n\n/**\n * Set root `path`.\n *\n * @param {String} path\n * @return {SendStream}\n * @api public\n */\n\nSendStream.prototype.root = function root (path) {\n  this._root = resolve(String(path))\n  debug('root %s', this._root)\n  return this\n}\n\nSendStream.prototype.from = deprecate.function(SendStream.prototype.root,\n  'send.from: pass root as option')\n\nSendStream.prototype.root = deprecate.function(SendStream.prototype.root,\n  'send.root: pass root as option')\n\n/**\n * Set max-age to `maxAge`.\n *\n * @param {Number} maxAge\n * @return {SendStream}\n * @api public\n */\n\nSendStream.prototype.maxage = deprecate.function(function maxage (maxAge) {\n  this._maxage = typeof maxAge === 'string'\n    ? ms(maxAge)\n    : Number(maxAge)\n  this._maxage = !isNaN(this._maxage)\n    ? Math.min(Math.max(0, this._maxage), MAX_MAXAGE)\n    : 0\n  debug('max-age %d', this._maxage)\n  return this\n}, 'send.maxage: pass maxAge as option')\n\n/**\n * Emit error with `status`.\n *\n * @param {number} status\n * @param {Error} [err]\n * @private\n */\n\nSendStream.prototype.error = function error (status, err) {\n  // emit if listeners instead of responding\n  if (hasListeners(this, 'error')) {\n    return this.emit('error', createHttpError(status, err))\n  }\n\n  var res = this.res\n  var msg = statuses.message[status] || String(status)\n  var doc = createHtmlDocument('Error', escapeHtml(msg))\n\n  // clear existing headers\n  clearHeaders(res)\n\n  // add error headers\n  if (err && err.headers) {\n    setHeaders(res, err.headers)\n  }\n\n  // send basic response\n  res.statusCode = status\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', Buffer.byteLength(doc))\n  res.setHeader('Content-Security-Policy', \"default-src 'none'\")\n  res.setHeader('X-Content-Type-Options', 'nosniff')\n  res.end(doc)\n}\n\n/**\n * Check if the pathname ends with \"/\".\n *\n * @return {boolean}\n * @private\n */\n\nSendStream.prototype.hasTrailingSlash = function hasTrailingSlash () {\n  return this.path[this.path.length - 1] === '/'\n}\n\n/**\n * Check if this is a conditional GET request.\n *\n * @return {Boolean}\n * @api private\n */\n\nSendStream.prototype.isConditionalGET = function isConditionalGET () {\n  return this.req.headers['if-match'] ||\n    this.req.headers['if-unmodified-since'] ||\n    this.req.headers['if-none-match'] ||\n    this.req.headers['if-modified-since']\n}\n\n/**\n * Check if the request preconditions failed.\n *\n * @return {boolean}\n * @private\n */\n\nSendStream.prototype.isPreconditionFailure = function isPreconditionFailure () {\n  var req = this.req\n  var res = this.res\n\n  // if-match\n  var match = req.headers['if-match']\n  if (match) {\n    var etag = res.getHeader('ETag')\n    return !etag || (match !== '*' && parseTokenList(match).every(function (match) {\n      return match !== etag && match !== 'W/' + etag && 'W/' + match !== etag\n    }))\n  }\n\n  // if-unmodified-since\n  var unmodifiedSince = parseHttpDate(req.headers['if-unmodified-since'])\n  if (!isNaN(unmodifiedSince)) {\n    var lastModified = parseHttpDate(res.getHeader('Last-Modified'))\n    return isNaN(lastModified) || lastModified > unmodifiedSince\n  }\n\n  return false\n}\n\n/**\n * Strip various content header fields for a change in entity.\n *\n * @private\n */\n\nSendStream.prototype.removeContentHeaderFields = function removeContentHeaderFields () {\n  var res = this.res\n\n  res.removeHeader('Content-Encoding')\n  res.removeHeader('Content-Language')\n  res.removeHeader('Content-Length')\n  res.removeHeader('Content-Range')\n  res.removeHeader('Content-Type')\n}\n\n/**\n * Respond with 304 not modified.\n *\n * @api private\n */\n\nSendStream.prototype.notModified = function notModified () {\n  var res = this.res\n  debug('not modified')\n  this.removeContentHeaderFields()\n  res.statusCode = 304\n  res.end()\n}\n\n/**\n * Raise error that headers already sent.\n *\n * @api private\n */\n\nSendStream.prototype.headersAlreadySent = function headersAlreadySent () {\n  var err = new Error('Can\\'t set headers after they are sent.')\n  debug('headers already sent')\n  this.error(500, err)\n}\n\n/**\n * Check if the request is cacheable, aka\n * responded with 2xx or 304 (see RFC 2616 section 14.2{5,6}).\n *\n * @return {Boolean}\n * @api private\n */\n\nSendStream.prototype.isCachable = function isCachable () {\n  var statusCode = this.res.statusCode\n  return (statusCode >= 200 && statusCode < 300) ||\n    statusCode === 304\n}\n\n/**\n * Handle stat() error.\n *\n * @param {Error} error\n * @private\n */\n\nSendStream.prototype.onStatError = function onStatError (error) {\n  switch (error.code) {\n    case 'ENAMETOOLONG':\n    case 'ENOENT':\n    case 'ENOTDIR':\n      this.error(404, error)\n      break\n    default:\n      this.error(500, error)\n      break\n  }\n}\n\n/**\n * Check if the cache is fresh.\n *\n * @return {Boolean}\n * @api private\n */\n\nSendStream.prototype.isFresh = function isFresh () {\n  return fresh(this.req.headers, {\n    etag: this.res.getHeader('ETag'),\n    'last-modified': this.res.getHeader('Last-Modified')\n  })\n}\n\n/**\n * Check if the range is fresh.\n *\n * @return {Boolean}\n * @api private\n */\n\nSendStream.prototype.isRangeFresh = function isRangeFresh () {\n  var ifRange = this.req.headers['if-range']\n\n  if (!ifRange) {\n    return true\n  }\n\n  // if-range as etag\n  if (ifRange.indexOf('\"') !== -1) {\n    var etag = this.res.getHeader('ETag')\n    return Boolean(etag && ifRange.indexOf(etag) !== -1)\n  }\n\n  // if-range as modified date\n  var lastModified = this.res.getHeader('Last-Modified')\n  return parseHttpDate(lastModified) <= parseHttpDate(ifRange)\n}\n\n/**\n * Redirect to path.\n *\n * @param {string} path\n * @private\n */\n\nSendStream.prototype.redirect = function redirect (path) {\n  var res = this.res\n\n  if (hasListeners(this, 'directory')) {\n    this.emit('directory', res, path)\n    return\n  }\n\n  if (this.hasTrailingSlash()) {\n    this.error(403)\n    return\n  }\n\n  var loc = encodeUrl(collapseLeadingSlashes(this.path + '/'))\n  var doc = createHtmlDocument('Redirecting', 'Redirecting to ' + escapeHtml(loc))\n\n  // redirect\n  res.statusCode = 301\n  res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n  res.setHeader('Content-Length', Buffer.byteLength(doc))\n  res.setHeader('Content-Security-Policy', \"default-src 'none'\")\n  res.setHeader('X-Content-Type-Options', 'nosniff')\n  res.setHeader('Location', loc)\n  res.end(doc)\n}\n\n/**\n * Pipe to `res.\n *\n * @param {Stream} res\n * @return {Stream} res\n * @api public\n */\n\nSendStream.prototype.pipe = function pipe (res) {\n  // root path\n  var root = this._root\n\n  // references\n  this.res = res\n\n  // decode the path\n  var path = decode(this.path)\n  if (path === -1) {\n    this.error(400)\n    return res\n  }\n\n  // null byte(s)\n  if (~path.indexOf('\\0')) {\n    this.error(400)\n    return res\n  }\n\n  var parts\n  if (root !== null) {\n    // normalize\n    if (path) {\n      path = normalize('.' + sep + path)\n    }\n\n    // malicious path\n    if (UP_PATH_REGEXP.test(path)) {\n      debug('malicious path \"%s\"', path)\n      this.error(403)\n      return res\n    }\n\n    // explode path parts\n    parts = path.split(sep)\n\n    // join / normalize from optional root dir\n    path = normalize(join(root, path))\n  } else {\n    // \"..\" is malicious without \"root\"\n    if (UP_PATH_REGEXP.test(path)) {\n      debug('malicious path \"%s\"', path)\n      this.error(403)\n      return res\n    }\n\n    // explode path parts\n    parts = normalize(path).split(sep)\n\n    // resolve the path\n    path = resolve(path)\n  }\n\n  // dotfile handling\n  if (containsDotFile(parts)) {\n    var access = this._dotfiles\n\n    // legacy support\n    if (access === undefined) {\n      access = parts[parts.length - 1][0] === '.'\n        ? (this._hidden ? 'allow' : 'ignore')\n        : 'allow'\n    }\n\n    debug('%s dotfile \"%s\"', access, path)\n    switch (access) {\n      case 'allow':\n        break\n      case 'deny':\n        this.error(403)\n        return res\n      case 'ignore':\n      default:\n        this.error(404)\n        return res\n    }\n  }\n\n  // index file support\n  if (this._index.length && this.hasTrailingSlash()) {\n    this.sendIndex(path)\n    return res\n  }\n\n  this.sendFile(path)\n  return res\n}\n\n/**\n * Transfer `path`.\n *\n * @param {String} path\n * @api public\n */\n\nSendStream.prototype.send = function send (path, stat) {\n  var len = stat.size\n  var options = this.options\n  var opts = {}\n  var res = this.res\n  var req = this.req\n  var ranges = req.headers.range\n  var offset = options.start || 0\n\n  if (headersSent(res)) {\n    // impossible to send now\n    this.headersAlreadySent()\n    return\n  }\n\n  debug('pipe \"%s\"', path)\n\n  // set header fields\n  this.setHeader(path, stat)\n\n  // set content-type\n  this.type(path)\n\n  // conditional GET support\n  if (this.isConditionalGET()) {\n    if (this.isPreconditionFailure()) {\n      this.error(412)\n      return\n    }\n\n    if (this.isCachable() && this.isFresh()) {\n      this.notModified()\n      return\n    }\n  }\n\n  // adjust len to start/end options\n  len = Math.max(0, len - offset)\n  if (options.end !== undefined) {\n    var bytes = options.end - offset + 1\n    if (len > bytes) len = bytes\n  }\n\n  // Range support\n  if (this._acceptRanges && BYTES_RANGE_REGEXP.test(ranges)) {\n    // parse\n    ranges = parseRange(len, ranges, {\n      combine: true\n    })\n\n    // If-Range support\n    if (!this.isRangeFresh()) {\n      debug('range stale')\n      ranges = -2\n    }\n\n    // unsatisfiable\n    if (ranges === -1) {\n      debug('range unsatisfiable')\n\n      // Content-Range\n      res.setHeader('Content-Range', contentRange('bytes', len))\n\n      // 416 Requested Range Not Satisfiable\n      return this.error(416, {\n        headers: { 'Content-Range': res.getHeader('Content-Range') }\n      })\n    }\n\n    // valid (syntactically invalid/multiple ranges are treated as a regular response)\n    if (ranges !== -2 && ranges.length === 1) {\n      debug('range %j', ranges)\n\n      // Content-Range\n      res.statusCode = 206\n      res.setHeader('Content-Range', contentRange('bytes', len, ranges[0]))\n\n      // adjust for requested range\n      offset += ranges[0].start\n      len = ranges[0].end - ranges[0].start + 1\n    }\n  }\n\n  // clone options\n  for (var prop in options) {\n    opts[prop] = options[prop]\n  }\n\n  // set read options\n  opts.start = offset\n  opts.end = Math.max(offset, offset + len - 1)\n\n  // content-length\n  res.setHeader('Content-Length', len)\n\n  // HEAD support\n  if (req.method === 'HEAD') {\n    res.end()\n    return\n  }\n\n  this.stream(path, opts)\n}\n\n/**\n * Transfer file for `path`.\n *\n * @param {String} path\n * @api private\n */\nSendStream.prototype.sendFile = function sendFile (path) {\n  var i = 0\n  var self = this\n\n  debug('stat \"%s\"', path)\n  fs.stat(path, function onstat (err, stat) {\n    if (err && err.code === 'ENOENT' && !extname(path) && path[path.length - 1] !== sep) {\n      // not found, check extensions\n      return next(err)\n    }\n    if (err) return self.onStatError(err)\n    if (stat.isDirectory()) return self.redirect(path)\n    self.emit('file', path, stat)\n    self.send(path, stat)\n  })\n\n  function next (err) {\n    if (self._extensions.length <= i) {\n      return err\n        ? self.onStatError(err)\n        : self.error(404)\n    }\n\n    var p = path + '.' + self._extensions[i++]\n\n    debug('stat \"%s\"', p)\n    fs.stat(p, function (err, stat) {\n      if (err) return next(err)\n      if (stat.isDirectory()) return next()\n      self.emit('file', p, stat)\n      self.send(p, stat)\n    })\n  }\n}\n\n/**\n * Transfer index for `path`.\n *\n * @param {String} path\n * @api private\n */\nSendStream.prototype.sendIndex = function sendIndex (path) {\n  var i = -1\n  var self = this\n\n  function next (err) {\n    if (++i >= self._index.length) {\n      if (err) return self.onStatError(err)\n      return self.error(404)\n    }\n\n    var p = join(path, self._index[i])\n\n    debug('stat \"%s\"', p)\n    fs.stat(p, function (err, stat) {\n      if (err) return next(err)\n      if (stat.isDirectory()) return next()\n      self.emit('file', p, stat)\n      self.send(p, stat)\n    })\n  }\n\n  next()\n}\n\n/**\n * Stream `path` to the response.\n *\n * @param {String} path\n * @param {Object} options\n * @api private\n */\n\nSendStream.prototype.stream = function stream (path, options) {\n  var self = this\n  var res = this.res\n\n  // pipe\n  var stream = fs.createReadStream(path, options)\n  this.emit('stream', stream)\n  stream.pipe(res)\n\n  // cleanup\n  function cleanup () {\n    destroy(stream, true)\n  }\n\n  // response finished, cleanup\n  onFinished(res, cleanup)\n\n  // error handling\n  stream.on('error', function onerror (err) {\n    // clean up stream early\n    cleanup()\n\n    // error\n    self.onStatError(err)\n  })\n\n  // end\n  stream.on('end', function onend () {\n    self.emit('end')\n  })\n}\n\n/**\n * Set content-type based on `path`\n * if it hasn't been explicitly set.\n *\n * @param {String} path\n * @api private\n */\n\nSendStream.prototype.type = function type (path) {\n  var res = this.res\n\n  if (res.getHeader('Content-Type')) return\n\n  var type = mime.lookup(path)\n\n  if (!type) {\n    debug('no content-type')\n    return\n  }\n\n  var charset = mime.charsets.lookup(type)\n\n  debug('content-type %s', type)\n  res.setHeader('Content-Type', type + (charset ? '; charset=' + charset : ''))\n}\n\n/**\n * Set response header fields, most\n * fields may be pre-defined.\n *\n * @param {String} path\n * @param {Object} stat\n * @api private\n */\n\nSendStream.prototype.setHeader = function setHeader (path, stat) {\n  var res = this.res\n\n  this.emit('headers', res, path, stat)\n\n  if (this._acceptRanges && !res.getHeader('Accept-Ranges')) {\n    debug('accept ranges')\n    res.setHeader('Accept-Ranges', 'bytes')\n  }\n\n  if (this._cacheControl && !res.getHeader('Cache-Control')) {\n    var cacheControl = 'public, max-age=' + Math.floor(this._maxage / 1000)\n\n    if (this._immutable) {\n      cacheControl += ', immutable'\n    }\n\n    debug('cache-control %s', cacheControl)\n    res.setHeader('Cache-Control', cacheControl)\n  }\n\n  if (this._lastModified && !res.getHeader('Last-Modified')) {\n    var modified = stat.mtime.toUTCString()\n    debug('modified %s', modified)\n    res.setHeader('Last-Modified', modified)\n  }\n\n  if (this._etag && !res.getHeader('ETag')) {\n    var val = etag(stat)\n    debug('etag %s', val)\n    res.setHeader('ETag', val)\n  }\n}\n\n/**\n * Clear all headers from a response.\n *\n * @param {object} res\n * @private\n */\n\nfunction clearHeaders (res) {\n  var headers = getHeaderNames(res)\n\n  for (var i = 0; i < headers.length; i++) {\n    res.removeHeader(headers[i])\n  }\n}\n\n/**\n * Collapse all leading slashes into a single slash\n *\n * @param {string} str\n * @private\n */\nfunction collapseLeadingSlashes (str) {\n  for (var i = 0; i < str.length; i++) {\n    if (str[i] !== '/') {\n      break\n    }\n  }\n\n  return i > 1\n    ? '/' + str.substr(i)\n    : str\n}\n\n/**\n * Determine if path parts contain a dotfile.\n *\n * @api private\n */\n\nfunction containsDotFile (parts) {\n  for (var i = 0; i < parts.length; i++) {\n    var part = parts[i]\n    if (part.length > 1 && part[0] === '.') {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * Create a Content-Range header.\n *\n * @param {string} type\n * @param {number} size\n * @param {array} [range]\n */\n\nfunction contentRange (type, size, range) {\n  return type + ' ' + (range ? range.start + '-' + range.end : '*') + '/' + size\n}\n\n/**\n * Create a minimal HTML document.\n *\n * @param {string} title\n * @param {string} body\n * @private\n */\n\nfunction createHtmlDocument (title, body) {\n  return '<!DOCTYPE html>\\n' +\n    '<html lang=\"en\">\\n' +\n    '<head>\\n' +\n    '<meta charset=\"utf-8\">\\n' +\n    '<title>' + title + '</title>\\n' +\n    '</head>\\n' +\n    '<body>\\n' +\n    '<pre>' + body + '</pre>\\n' +\n    '</body>\\n' +\n    '</html>\\n'\n}\n\n/**\n * Create a HttpError object from simple arguments.\n *\n * @param {number} status\n * @param {Error|object} err\n * @private\n */\n\nfunction createHttpError (status, err) {\n  if (!err) {\n    return createError(status)\n  }\n\n  return err instanceof Error\n    ? createError(status, err, { expose: false })\n    : createError(status, err)\n}\n\n/**\n * decodeURIComponent.\n *\n * Allows V8 to only deoptimize this fn instead of all\n * of send().\n *\n * @param {String} path\n * @api private\n */\n\nfunction decode (path) {\n  try {\n    return decodeURIComponent(path)\n  } catch (err) {\n    return -1\n  }\n}\n\n/**\n * Get the header names on a respnse.\n *\n * @param {object} res\n * @returns {array[string]}\n * @private\n */\n\nfunction getHeaderNames (res) {\n  return typeof res.getHeaderNames !== 'function'\n    ? Object.keys(res._headers || {})\n    : res.getHeaderNames()\n}\n\n/**\n * Determine if emitter has listeners of a given type.\n *\n * The way to do this check is done three different ways in Node.js >= 0.8\n * so this consolidates them into a minimal set using instance methods.\n *\n * @param {EventEmitter} emitter\n * @param {string} type\n * @returns {boolean}\n * @private\n */\n\nfunction hasListeners (emitter, type) {\n  var count = typeof emitter.listenerCount !== 'function'\n    ? emitter.listeners(type).length\n    : emitter.listenerCount(type)\n\n  return count > 0\n}\n\n/**\n * Determine if the response headers have been sent.\n *\n * @param {object} res\n * @returns {boolean}\n * @private\n */\n\nfunction headersSent (res) {\n  return typeof res.headersSent !== 'boolean'\n    ? Boolean(res._header)\n    : res.headersSent\n}\n\n/**\n * Normalize the index option into an array.\n *\n * @param {boolean|string|array} val\n * @param {string} name\n * @private\n */\n\nfunction normalizeList (val, name) {\n  var list = [].concat(val || [])\n\n  for (var i = 0; i < list.length; i++) {\n    if (typeof list[i] !== 'string') {\n      throw new TypeError(name + ' must be array of strings or false')\n    }\n  }\n\n  return list\n}\n\n/**\n * Parse an HTTP Date into a number.\n *\n * @param {string} date\n * @private\n */\n\nfunction parseHttpDate (date) {\n  var timestamp = date && Date.parse(date)\n\n  return typeof timestamp === 'number'\n    ? timestamp\n    : NaN\n}\n\n/**\n * Parse a HTTP token list.\n *\n * @param {string} str\n * @private\n */\n\nfunction parseTokenList (str) {\n  var end = 0\n  var list = []\n  var start = 0\n\n  // gather tokens\n  for (var i = 0, len = str.length; i < len; i++) {\n    switch (str.charCodeAt(i)) {\n      case 0x20: /*   */\n        if (start === end) {\n          start = end = i + 1\n        }\n        break\n      case 0x2c: /* , */\n        if (start !== end) {\n          list.push(str.substring(start, end))\n        }\n        start = end = i + 1\n        break\n      default:\n        end = i + 1\n        break\n    }\n  }\n\n  // final token\n  if (start !== end) {\n    list.push(str.substring(start, end))\n  }\n\n  return list\n}\n\n/**\n * Set an object of headers on a response.\n *\n * @param {object} res\n * @param {object} headers\n * @private\n */\n\nfunction setHeaders (res, headers) {\n  var keys = Object.keys(headers)\n\n  for (var i = 0; i < keys.length; i++) {\n    var key = keys[i]\n    res.setHeader(key, headers[key])\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js\n");

/***/ })

};
;