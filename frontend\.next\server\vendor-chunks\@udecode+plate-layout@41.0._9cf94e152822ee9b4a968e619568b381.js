"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+plate-layout@41.0._9cf94e152822ee9b4a968e619568b381";
exports.ids = ["vendor-chunks/@udecode+plate-layout@41.0._9cf94e152822ee9b4a968e619568b381"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+plate-layout@41.0._9cf94e152822ee9b4a968e619568b381/node_modules/@udecode/plate-layout/dist/react/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+plate-layout@41.0._9cf94e152822ee9b4a968e619568b381/node_modules/@udecode/plate-layout/dist/react/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ColumnItemPlugin: () => (/* binding */ ColumnItemPlugin),\n/* harmony export */   ColumnPlugin: () => (/* binding */ ColumnPlugin),\n/* harmony export */   onKeyDownColumn: () => (/* binding */ onKeyDownColumn),\n/* harmony export */   useDebouncePopoverOpen: () => (/* binding */ useDebouncePopoverOpen)\n/* harmony export */ });\n/* harmony import */ var _udecode_plate_common_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @udecode/plate-common/react */ \"(ssr)/./node_modules/.pnpm/@udecode+plate-core@41.0.13_ccf5ff78b70bf95295f6590d95e28fe8/node_modules/@udecode/plate-core/dist/react/index.mjs\");\n/* harmony import */ var _udecode_plate_common__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @udecode/plate-common */ \"(ssr)/./node_modules/.pnpm/@udecode+plate-core@41.0.13_ccf5ff78b70bf95295f6590d95e28fe8/node_modules/@udecode/plate-core/dist/index.mjs\");\n/* harmony import */ var _udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @udecode/plate-common */ \"(ssr)/./node_modules/.pnpm/@udecode+slate@41.0.0_slate_e66cb11f4de22dcea5a3815dc1b2c7dd/node_modules/@udecode/slate/dist/index.mjs\");\n/* harmony import */ var _udecode_plate_common__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @udecode/plate-common */ \"(ssr)/./node_modules/.pnpm/is-hotkey@0.2.0/node_modules/is-hotkey/lib/index.js\");\n/* harmony import */ var _udecode_plate_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @udecode/plate-common */ \"(ssr)/./node_modules/.pnpm/@udecode+slate-utils@41.0.0_efa817c0901792c9e0d186f107be6a0f/node_modules/@udecode/slate-utils/dist/index.mjs\");\n/* harmony import */ var slate__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! slate */ \"(ssr)/./node_modules/.pnpm/slate@0.103.0/node_modules/slate/dist/index.es.js\");\n/* harmony import */ var slate_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! slate-react */ \"(ssr)/./node_modules/.pnpm/slate-react@0.110.3_react-d_bd30ebaf00652ab3f51a45008f0205b4/node_modules/slate-react/dist/index.es.js\");\n// src/react/ColumnPlugin.tsx\n\n\n// src/lib/BaseColumnPlugin.ts\n\n\n// src/lib/withColumn.ts\n\nvar withColumn = ({ editor }) => {\n  const { deleteBackward, normalizeNode } = editor;\n  editor.normalizeNode = (entry) => {\n    const [n, path] = entry;\n    if ((0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isElement)(n) && n.type === BaseColumnPlugin.key) {\n      const node = n;\n      if (!node.children.some(\n        (child) => (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isElement)(child) && child.type === editor.getType(BaseColumnItemPlugin)\n      )) {\n        (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.removeNodes)(editor, { at: path });\n        return;\n      }\n      if (node.children.length < 2) {\n        editor.withoutNormalizing(() => {\n          (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.unwrapNodes)(editor, { at: path });\n          (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.unwrapNodes)(editor, { at: path });\n        });\n        return;\n      }\n      editor.withoutNormalizing(() => {\n        const totalColumns = node.children.length;\n        let widths = node.children.map((col) => {\n          const parsed = Number.parseFloat(col.width);\n          return Number.isNaN(parsed) ? 0 : parsed;\n        });\n        const sum = widths.reduce((acc, w) => acc + w, 0);\n        if (sum !== 100) {\n          const diff = 100 - sum;\n          const adjustment = diff / totalColumns;\n          widths = widths.map((w) => w + adjustment);\n          widths.forEach((w, i) => {\n            const columnPath = path.concat([i]);\n            (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.setNodes)(\n              editor,\n              { width: `${w}%` },\n              { at: columnPath }\n            );\n          });\n        }\n      });\n    }\n    if ((0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isElement)(n) && n.type === BaseColumnItemPlugin.key) {\n      const node = n;\n      if (node.children.length === 0) {\n        (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.removeNodes)(editor, { at: path });\n        return;\n      }\n    }\n    return normalizeNode(entry);\n  };\n  editor.deleteBackward = (unit) => {\n    var _a;\n    if ((0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isCollapsed)(editor.selection)) {\n      const entry = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.getAboveNode)(editor, {\n        match: (n) => (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isElement)(n) && n.type === BaseColumnItemPlugin.key\n      });\n      if (entry) {\n        const [node, path] = entry;\n        if (node.children.length > 1) return deleteBackward(unit);\n        const isStart = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isStartPoint)(editor, (_a = editor.selection) == null ? void 0 : _a.anchor, path);\n        if (isStart) return;\n      }\n    }\n    deleteBackward(unit);\n  };\n  return editor;\n};\n\n// src/lib/BaseColumnPlugin.ts\nvar BaseColumnItemPlugin = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_1__.createSlatePlugin)({\n  key: \"column\",\n  extendEditor: withColumn,\n  node: { isElement: true }\n});\nvar BaseColumnPlugin = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_1__.createSlatePlugin)({\n  key: \"column_group\",\n  node: { isElement: true },\n  plugins: [BaseColumnItemPlugin]\n});\n\n// src/react/onKeyDownColumn.ts\n\n\nvar onKeyDownColumn = ({ editor, event }) => {\n  if (event.defaultPrevented) return;\n  const at = editor.selection;\n  if ((0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_2__.isHotkey)(\"mod+a\", event) && at) {\n    const aboveNode = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.getAboveNode)(editor);\n    const ancestorNode = (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_3__.getAncestorNode)(editor);\n    if (!ancestorNode) return;\n    if (!aboveNode) return;\n    const [node] = ancestorNode;\n    if (node.type !== ColumnPlugin.key) return;\n    const [, abovePath] = aboveNode;\n    let targetPath = slate__WEBPACK_IMPORTED_MODULE_4__.Path.parent(abovePath);\n    if ((0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_3__.isSelectionCoverBlock)(editor)) {\n      targetPath = slate__WEBPACK_IMPORTED_MODULE_4__.Path.parent(targetPath);\n    }\n    if (targetPath.length === 0) return;\n    (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.select)(editor, targetPath);\n    event.preventDefault();\n    event.stopPropagation();\n  }\n};\n\n// src/react/ColumnPlugin.tsx\nvar ColumnItemPlugin = (0,_udecode_plate_common_react__WEBPACK_IMPORTED_MODULE_5__.toPlatePlugin)(BaseColumnItemPlugin);\nvar ColumnPlugin = (0,_udecode_plate_common_react__WEBPACK_IMPORTED_MODULE_5__.toPlatePlugin)(BaseColumnPlugin, {\n  plugins: [ColumnItemPlugin],\n  handlers: {\n    onKeyDown: onKeyDownColumn\n  }\n});\n\n// src/react/hooks/useDebouncePopoverOpen.ts\n\n\n\nvar useDebouncePopoverOpen = () => {\n  const readOnly = (0,slate_react__WEBPACK_IMPORTED_MODULE_6__.useReadOnly)();\n  const selected = (0,slate_react__WEBPACK_IMPORTED_MODULE_6__.useSelected)();\n  const selectionCollapsed = (0,_udecode_plate_common_react__WEBPACK_IMPORTED_MODULE_5__.useEditorSelector)(\n    (editor) => (0,_udecode_plate_common__WEBPACK_IMPORTED_MODULE_0__.isCollapsed)(editor.selection),\n    []\n  );\n  return !readOnly && selected && selectionCollapsed;\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+plate-layout@41.0._9cf94e152822ee9b4a968e619568b381/node_modules/@udecode/plate-layout/dist/react/index.mjs\n");

/***/ })

};
;