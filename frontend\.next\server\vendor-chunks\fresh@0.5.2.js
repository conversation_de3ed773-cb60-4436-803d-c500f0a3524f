"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/fresh@0.5.2";
exports.ids = ["vendor-chunks/fresh@0.5.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/fresh@0.5.2/node_modules/fresh/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/.pnpm/fresh@0.5.2/node_modules/fresh/index.js ***!
  \********************************************************************/
/***/ ((module) => {

eval("/*!\n * fresh\n * Copyright(c) 2012 TJ Holowaychuk\n * Copyright(c) 2016-2017 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * RegExp to check for no-cache token in Cache-Control.\n * @private\n */\n\nvar CACHE_CONTROL_NO_CACHE_REGEXP = /(?:^|,)\\s*?no-cache\\s*?(?:,|$)/\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = fresh\n\n/**\n * Check freshness of the response using request and response headers.\n *\n * @param {Object} reqHeaders\n * @param {Object} resHeaders\n * @return {Boolean}\n * @public\n */\n\nfunction fresh (reqHeaders, resHeaders) {\n  // fields\n  var modifiedSince = reqHeaders['if-modified-since']\n  var noneMatch = reqHeaders['if-none-match']\n\n  // unconditional request\n  if (!modifiedSince && !noneMatch) {\n    return false\n  }\n\n  // Always return stale when Cache-Control: no-cache\n  // to support end-to-end reload requests\n  // https://tools.ietf.org/html/rfc2616#section-14.9.4\n  var cacheControl = reqHeaders['cache-control']\n  if (cacheControl && CACHE_CONTROL_NO_CACHE_REGEXP.test(cacheControl)) {\n    return false\n  }\n\n  // if-none-match\n  if (noneMatch && noneMatch !== '*') {\n    var etag = resHeaders['etag']\n\n    if (!etag) {\n      return false\n    }\n\n    var etagStale = true\n    var matches = parseTokenList(noneMatch)\n    for (var i = 0; i < matches.length; i++) {\n      var match = matches[i]\n      if (match === etag || match === 'W/' + etag || 'W/' + match === etag) {\n        etagStale = false\n        break\n      }\n    }\n\n    if (etagStale) {\n      return false\n    }\n  }\n\n  // if-modified-since\n  if (modifiedSince) {\n    var lastModified = resHeaders['last-modified']\n    var modifiedStale = !lastModified || !(parseHttpDate(lastModified) <= parseHttpDate(modifiedSince))\n\n    if (modifiedStale) {\n      return false\n    }\n  }\n\n  return true\n}\n\n/**\n * Parse an HTTP Date into a number.\n *\n * @param {string} date\n * @private\n */\n\nfunction parseHttpDate (date) {\n  var timestamp = date && Date.parse(date)\n\n  // istanbul ignore next: guard against date.js Date.parse patching\n  return typeof timestamp === 'number'\n    ? timestamp\n    : NaN\n}\n\n/**\n * Parse a HTTP token list.\n *\n * @param {string} str\n * @private\n */\n\nfunction parseTokenList (str) {\n  var end = 0\n  var list = []\n  var start = 0\n\n  // gather tokens\n  for (var i = 0, len = str.length; i < len; i++) {\n    switch (str.charCodeAt(i)) {\n      case 0x20: /*   */\n        if (start === end) {\n          start = end = i + 1\n        }\n        break\n      case 0x2c: /* , */\n        list.push(str.substring(start, end))\n        start = end = i + 1\n        break\n      default:\n        end = i + 1\n        break\n    }\n  }\n\n  // final token\n  list.push(str.substring(start, end))\n\n  return list\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/fresh@0.5.2/node_modules/fresh/index.js\n");

/***/ })

};
;