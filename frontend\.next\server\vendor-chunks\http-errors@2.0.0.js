"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/http-errors@2.0.0";
exports.ids = ["vendor-chunks/http-errors@2.0.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * http-errors\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('http-errors')\nvar setPrototypeOf = __webpack_require__(/*! setprototypeof */ \"(rsc)/./node_modules/.pnpm/setprototypeof@1.2.0/node_modules/setprototypeof/index.js\")\nvar statuses = __webpack_require__(/*! statuses */ \"(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js\")\nvar inherits = __webpack_require__(/*! inherits */ \"(rsc)/./node_modules/.pnpm/inherits@2.0.4/node_modules/inherits/inherits.js\")\nvar toIdentifier = __webpack_require__(/*! toidentifier */ \"(rsc)/./node_modules/.pnpm/toidentifier@1.0.1/node_modules/toidentifier/index.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = createError\nmodule.exports.HttpError = createHttpErrorConstructor()\nmodule.exports.isHttpError = createIsHttpErrorFunction(module.exports.HttpError)\n\n// Populate exports for all constructors\npopulateConstructorExports(module.exports, statuses.codes, module.exports.HttpError)\n\n/**\n * Get the code class of a status code.\n * @private\n */\n\nfunction codeClass (status) {\n  return Number(String(status).charAt(0) + '00')\n}\n\n/**\n * Create a new HTTP Error.\n *\n * @returns {Error}\n * @public\n */\n\nfunction createError () {\n  // so much arity going on ~_~\n  var err\n  var msg\n  var status = 500\n  var props = {}\n  for (var i = 0; i < arguments.length; i++) {\n    var arg = arguments[i]\n    var type = typeof arg\n    if (type === 'object' && arg instanceof Error) {\n      err = arg\n      status = err.status || err.statusCode || status\n    } else if (type === 'number' && i === 0) {\n      status = arg\n    } else if (type === 'string') {\n      msg = arg\n    } else if (type === 'object') {\n      props = arg\n    } else {\n      throw new TypeError('argument #' + (i + 1) + ' unsupported type ' + type)\n    }\n  }\n\n  if (typeof status === 'number' && (status < 400 || status >= 600)) {\n    deprecate('non-error status code; use only 4xx or 5xx status codes')\n  }\n\n  if (typeof status !== 'number' ||\n    (!statuses.message[status] && (status < 400 || status >= 600))) {\n    status = 500\n  }\n\n  // constructor\n  var HttpError = createError[status] || createError[codeClass(status)]\n\n  if (!err) {\n    // create error\n    err = HttpError\n      ? new HttpError(msg)\n      : new Error(msg || statuses.message[status])\n    Error.captureStackTrace(err, createError)\n  }\n\n  if (!HttpError || !(err instanceof HttpError) || err.status !== status) {\n    // add properties to generic error\n    err.expose = status < 500\n    err.status = err.statusCode = status\n  }\n\n  for (var key in props) {\n    if (key !== 'status' && key !== 'statusCode') {\n      err[key] = props[key]\n    }\n  }\n\n  return err\n}\n\n/**\n * Create HTTP error abstract base class.\n * @private\n */\n\nfunction createHttpErrorConstructor () {\n  function HttpError () {\n    throw new TypeError('cannot construct abstract class')\n  }\n\n  inherits(HttpError, Error)\n\n  return HttpError\n}\n\n/**\n * Create a constructor for a client error.\n * @private\n */\n\nfunction createClientErrorConstructor (HttpError, name, code) {\n  var className = toClassName(name)\n\n  function ClientError (message) {\n    // create the error object\n    var msg = message != null ? message : statuses.message[code]\n    var err = new Error(msg)\n\n    // capture a stack trace to the construction point\n    Error.captureStackTrace(err, ClientError)\n\n    // adjust the [[Prototype]]\n    setPrototypeOf(err, ClientError.prototype)\n\n    // redefine the error message\n    Object.defineProperty(err, 'message', {\n      enumerable: true,\n      configurable: true,\n      value: msg,\n      writable: true\n    })\n\n    // redefine the error name\n    Object.defineProperty(err, 'name', {\n      enumerable: false,\n      configurable: true,\n      value: className,\n      writable: true\n    })\n\n    return err\n  }\n\n  inherits(ClientError, HttpError)\n  nameFunc(ClientError, className)\n\n  ClientError.prototype.status = code\n  ClientError.prototype.statusCode = code\n  ClientError.prototype.expose = true\n\n  return ClientError\n}\n\n/**\n * Create function to test is a value is a HttpError.\n * @private\n */\n\nfunction createIsHttpErrorFunction (HttpError) {\n  return function isHttpError (val) {\n    if (!val || typeof val !== 'object') {\n      return false\n    }\n\n    if (val instanceof HttpError) {\n      return true\n    }\n\n    return val instanceof Error &&\n      typeof val.expose === 'boolean' &&\n      typeof val.statusCode === 'number' && val.status === val.statusCode\n  }\n}\n\n/**\n * Create a constructor for a server error.\n * @private\n */\n\nfunction createServerErrorConstructor (HttpError, name, code) {\n  var className = toClassName(name)\n\n  function ServerError (message) {\n    // create the error object\n    var msg = message != null ? message : statuses.message[code]\n    var err = new Error(msg)\n\n    // capture a stack trace to the construction point\n    Error.captureStackTrace(err, ServerError)\n\n    // adjust the [[Prototype]]\n    setPrototypeOf(err, ServerError.prototype)\n\n    // redefine the error message\n    Object.defineProperty(err, 'message', {\n      enumerable: true,\n      configurable: true,\n      value: msg,\n      writable: true\n    })\n\n    // redefine the error name\n    Object.defineProperty(err, 'name', {\n      enumerable: false,\n      configurable: true,\n      value: className,\n      writable: true\n    })\n\n    return err\n  }\n\n  inherits(ServerError, HttpError)\n  nameFunc(ServerError, className)\n\n  ServerError.prototype.status = code\n  ServerError.prototype.statusCode = code\n  ServerError.prototype.expose = false\n\n  return ServerError\n}\n\n/**\n * Set the name of a function, if possible.\n * @private\n */\n\nfunction nameFunc (func, name) {\n  var desc = Object.getOwnPropertyDescriptor(func, 'name')\n\n  if (desc && desc.configurable) {\n    desc.value = name\n    Object.defineProperty(func, 'name', desc)\n  }\n}\n\n/**\n * Populate the exports object with constructors for every error class.\n * @private\n */\n\nfunction populateConstructorExports (exports, codes, HttpError) {\n  codes.forEach(function forEachCode (code) {\n    var CodeError\n    var name = toIdentifier(statuses.message[code])\n\n    switch (codeClass(code)) {\n      case 400:\n        CodeError = createClientErrorConstructor(HttpError, name, code)\n        break\n      case 500:\n        CodeError = createServerErrorConstructor(HttpError, name, code)\n        break\n    }\n\n    if (CodeError) {\n      // export the constructor\n      exports[code] = CodeError\n      exports[name] = CodeError\n    }\n  })\n}\n\n/**\n * Get a class name from a name identifier.\n * @private\n */\n\nfunction toClassName (name) {\n  return name.substr(-5) !== 'Error'\n    ? name + 'Error'\n    : name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js\n");

/***/ })

};
;