"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai-x@1.2.4_@types+react@_534331be11f97970e14b29bd0baa24f5";
exports.ids = ["vendor-chunks/jotai-x@1.2.4_@types+react@_534331be11f97970e14b29bd0baa24f5"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jotai-x@1.2.4_@types+react@_534331be11f97970e14b29bd0baa24f5/node_modules/jotai-x/dist/index.mjs":
/*!*****************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai-x@1.2.4_@types+react@_534331be11f97970e14b29bd0baa24f5/node_modules/jotai-x/dist/index.mjs ***!
  \*****************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HydrateAtoms: () => (/* binding */ HydrateAtoms),\n/* harmony export */   atomWithFn: () => (/* binding */ atomWithFn),\n/* harmony export */   createAtomProvider: () => (/* binding */ createAtomProvider),\n/* harmony export */   createAtomStore: () => (/* binding */ createAtomStore),\n/* harmony export */   useAtomStore: () => (/* binding */ useAtomStore),\n/* harmony export */   useHydrateStore: () => (/* binding */ useHydrateStore),\n/* harmony export */   useSyncStore: () => (/* binding */ useSyncStore)\n/* harmony export */ });\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var jotai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! jotai */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react.mjs\");\n/* harmony import */ var jotai_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! jotai/utils */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/react/utils.mjs\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/atomWithFn.ts\n\nvar wrapFn = (fnOrValue) => typeof fnOrValue === \"function\" ? { __fn: fnOrValue } : fnOrValue;\nvar unwrapFn = (wrappedFnOrValue) => wrappedFnOrValue && typeof wrappedFnOrValue === \"object\" && \"__fn\" in wrappedFnOrValue ? wrappedFnOrValue.__fn : wrappedFnOrValue;\nvar atomWithFn = (initialValue) => {\n  const baseAtom = (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(wrapFn(initialValue));\n  return (0,jotai__WEBPACK_IMPORTED_MODULE_0__.atom)(\n    (get) => unwrapFn(get(baseAtom)),\n    (_get, set, value) => set(baseAtom, wrapFn(value))\n  );\n};\n\n// src/createAtomProvider.tsx\n\n\n\n// src/useHydrateStore.ts\n\n\n\nvar useHydrateStore = (atoms, initialValues, options = {}) => {\n  const values = [];\n  for (const key of Object.keys(atoms)) {\n    const initialValue = initialValues[key];\n    if (initialValue !== void 0) {\n      values.push([atoms[key], initialValue]);\n    }\n  }\n  (0,jotai_utils__WEBPACK_IMPORTED_MODULE_2__.useHydrateAtoms)(values, options);\n};\nvar useSyncStore = (atoms, values, { store } = {}) => {\n  for (const key of Object.keys(atoms)) {\n    const value = values[key];\n    const atom2 = atoms[key];\n    const set = (0,jotai__WEBPACK_IMPORTED_MODULE_3__.useSetAtom)(atom2, { store });\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n      if (value !== void 0 && value !== null) {\n        set(value);\n      }\n    }, [set, value]);\n  }\n};\n\n// src/createAtomProvider.tsx\nvar getFullyQualifiedScope = (storeName, scope) => {\n  return `${storeName}:${scope}`;\n};\nvar PROVIDER_SCOPE = \"provider\";\nvar AtomStoreContext = react__WEBPACK_IMPORTED_MODULE_1__.createContext(\n  /* @__PURE__ */ new Map()\n);\nvar useAtomStore = (storeName, scope = PROVIDER_SCOPE, warnIfUndefined = true) => {\n  var _a;\n  const storeContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(AtomStoreContext);\n  const store = (_a = storeContext.get(getFullyQualifiedScope(storeName, scope))) != null ? _a : storeContext.get(getFullyQualifiedScope(storeName, PROVIDER_SCOPE));\n  if (!store && warnIfUndefined) {\n    console.warn(\n      `Tried to access jotai store '${storeName}' outside of a matching provider.`\n    );\n  }\n  return store;\n};\nvar HydrateAtoms = (_a) => {\n  var _b = _a, {\n    initialValues,\n    children,\n    store,\n    atoms\n  } = _b, props = __objRest(_b, [\n    \"initialValues\",\n    \"children\",\n    \"store\",\n    \"atoms\"\n  ]);\n  useHydrateStore(atoms, __spreadValues(__spreadValues({}, initialValues), props), {\n    store\n  });\n  useSyncStore(atoms, props, {\n    store\n  });\n  return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, null, children);\n};\nvar createAtomProvider = (storeScope, atoms, options = {}) => {\n  const Effect = options.effect;\n  return (_a) => {\n    var _b = _a, { store, scope, children, resetKey } = _b, props = __objRest(_b, [\"store\", \"scope\", \"children\", \"resetKey\"]);\n    const [storeState, setStoreState] = react__WEBPACK_IMPORTED_MODULE_1__.useState((0,jotai__WEBPACK_IMPORTED_MODULE_0__.createStore)());\n    react__WEBPACK_IMPORTED_MODULE_1__.useEffect(() => {\n      if (resetKey) {\n        setStoreState((0,jotai__WEBPACK_IMPORTED_MODULE_0__.createStore)());\n      }\n    }, [resetKey]);\n    const previousStoreContext = react__WEBPACK_IMPORTED_MODULE_1__.useContext(AtomStoreContext);\n    const storeContext = react__WEBPACK_IMPORTED_MODULE_1__.useMemo(() => {\n      const newStoreContext = new Map(previousStoreContext);\n      if (scope) {\n        newStoreContext.set(\n          getFullyQualifiedScope(storeScope, scope),\n          storeState\n        );\n      }\n      newStoreContext.set(\n        getFullyQualifiedScope(storeScope, PROVIDER_SCOPE),\n        storeState\n      );\n      return newStoreContext;\n    }, [previousStoreContext, scope, storeState]);\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(AtomStoreContext.Provider, { value: storeContext }, /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(HydrateAtoms, __spreadValues({ store: storeState, atoms }, props), !!Effect && /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_1__.createElement(Effect, null), children));\n  };\n};\n\n// src/createAtomStore.ts\n\nvar capitalizeFirstLetter = (str = \"\") => str.length > 0 ? str[0].toUpperCase() + str.slice(1) : \"\";\nvar getProviderIndex = (name = \"\") => `${capitalizeFirstLetter(name)}Provider`;\nvar getStoreIndex = (name = \"\") => name.length > 0 ? `${name}Store` : \"store\";\nvar getUseStoreIndex = (name = \"\") => `use${capitalizeFirstLetter(name)}Store`;\nvar isAtom = (possibleAtom) => !!possibleAtom && typeof possibleAtom === \"object\" && \"read\" in possibleAtom && typeof possibleAtom.read === \"function\";\nvar withDefaultOptions = (fnRecord, defaultOptions) => Object.fromEntries(\n  Object.entries(fnRecord).map(([key, fn]) => [\n    key,\n    (options = {}) => fn(__spreadValues(__spreadValues({}, defaultOptions), options))\n  ])\n);\nvar convertScopeShorthand = (optionsOrScope = {}) => typeof optionsOrScope === \"string\" ? { scope: optionsOrScope } : optionsOrScope;\nvar createAtomStore = (initialState, { name, delay: delayRoot, effect, extend }) => {\n  const providerIndex = getProviderIndex(name);\n  const useStoreIndex = getUseStoreIndex(name);\n  const storeIndex = getStoreIndex(name);\n  const atomsWithoutExtend = {};\n  const writableAtomsWithoutExtend = {};\n  const atomIsWritable = {};\n  for (const [key, atomOrValue] of Object.entries(initialState)) {\n    const atomConfig = isAtom(atomOrValue) ? atomOrValue : atomWithFn(atomOrValue);\n    atomsWithoutExtend[key] = atomConfig;\n    const writable = \"write\" in atomConfig;\n    atomIsWritable[key] = writable;\n    if (writable) {\n      writableAtomsWithoutExtend[key] = atomConfig;\n    }\n  }\n  const atoms = __spreadValues({}, atomsWithoutExtend);\n  if (extend) {\n    const extendedAtoms = extend(atomsWithoutExtend);\n    for (const [key, atomConfig] of Object.entries(extendedAtoms)) {\n      atoms[key] = atomConfig;\n      atomIsWritable[key] = \"write\" in atomConfig;\n    }\n  }\n  const getAtoms = {};\n  const setAtoms = {};\n  const useAtoms = {};\n  const useStore = (optionsOrScope = {}) => {\n    const {\n      scope,\n      store,\n      warnIfNoStore = true\n    } = convertScopeShorthand(optionsOrScope);\n    const contextStore = useAtomStore(name, scope, !store && warnIfNoStore);\n    return store != null ? store : contextStore;\n  };\n  const useAtomValueWithStore = (atomConfig, optionsOrScope) => {\n    var _a;\n    const options = convertScopeShorthand(optionsOrScope);\n    const store = useStore(__spreadValues({ warnIfNoStore: false }, options));\n    return (0,jotai__WEBPACK_IMPORTED_MODULE_3__.useAtomValue)(atomConfig, {\n      store,\n      delay: (_a = options.delay) != null ? _a : delayRoot\n    });\n  };\n  const useSetAtomWithStore = (atomConfig, optionsOrScope) => {\n    const store = useStore(optionsOrScope);\n    return (0,jotai__WEBPACK_IMPORTED_MODULE_3__.useSetAtom)(atomConfig, { store });\n  };\n  const useAtomWithStore = (atomConfig, optionsOrScope) => {\n    const store = useStore(optionsOrScope);\n    const { delay = delayRoot } = convertScopeShorthand(optionsOrScope);\n    return (0,jotai__WEBPACK_IMPORTED_MODULE_3__.useAtom)(atomConfig, { store, delay });\n  };\n  for (const key of Object.keys(atoms)) {\n    const atomConfig = atoms[key];\n    const isWritable = atomIsWritable[key];\n    getAtoms[key] = (optionsOrScope = {}) => useAtomValueWithStore(atomConfig, optionsOrScope);\n    if (isWritable) {\n      setAtoms[key] = (optionsOrScope = {}) => useSetAtomWithStore(\n        atomConfig,\n        optionsOrScope\n      );\n      useAtoms[key] = (optionsOrScope = {}) => useAtomWithStore(\n        atomConfig,\n        optionsOrScope\n      );\n    }\n  }\n  const Provider = createAtomProvider(\n    name,\n    writableAtomsWithoutExtend,\n    { effect }\n  );\n  const storeApi = {\n    atom: atoms,\n    name\n  };\n  const useStoreApi = (defaultOptions = {}) => ({\n    get: __spreadProps(__spreadValues({}, withDefaultOptions(getAtoms, convertScopeShorthand(defaultOptions))), {\n      atom: (atomConfig, options) => useAtomValueWithStore(atomConfig, __spreadValues(__spreadValues({}, convertScopeShorthand(defaultOptions)), convertScopeShorthand(options)))\n    }),\n    set: __spreadProps(__spreadValues({}, withDefaultOptions(setAtoms, convertScopeShorthand(defaultOptions))), {\n      atom: (atomConfig, options) => useSetAtomWithStore(atomConfig, __spreadValues(__spreadValues({}, convertScopeShorthand(defaultOptions)), convertScopeShorthand(options)))\n    }),\n    use: __spreadProps(__spreadValues({}, withDefaultOptions(useAtoms, convertScopeShorthand(defaultOptions))), {\n      atom: (atomConfig, options) => useAtomWithStore(atomConfig, __spreadValues(__spreadValues({}, convertScopeShorthand(defaultOptions)), convertScopeShorthand(options)))\n    }),\n    store: (options) => useStore(__spreadValues(__spreadValues({}, convertScopeShorthand(defaultOptions)), convertScopeShorthand(options)))\n  });\n  return {\n    [providerIndex]: Provider,\n    [useStoreIndex]: useStoreApi,\n    [storeIndex]: storeApi,\n    name\n  };\n};\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vam90YWkteEAxLjIuNF9AdHlwZXMrcmVhY3RAXzUzNDMzMWJlMTFmOTc5NzBlMTRiMjliZDBiYWEyNGY1L25vZGVfbW9kdWxlcy9qb3RhaS14L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOEVBQThFLDZEQUE2RDtBQUMzSTtBQUNBLCtCQUErQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUM2QjtBQUM3QixnRUFBZ0Usa0JBQWtCO0FBQ2xGO0FBQ0E7QUFDQSxtQkFBbUIsMkNBQUk7QUFDdkIsU0FBUywyQ0FBSTtBQUNiO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQzJCO0FBQ2lCOztBQUU1QztBQUMwQjtBQUNTO0FBQ1c7QUFDOUMseURBQXlEO0FBQ3pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsRUFBRSw0REFBZTtBQUNqQjtBQUNBLHFDQUFxQyxRQUFRLElBQUk7QUFDakQ7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGlEQUFVLFVBQVUsT0FBTztBQUMzQyxJQUFJLDRDQUFlO0FBQ25CO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBOztBQUVBO0FBQ0E7QUFDQSxZQUFZLFVBQVUsR0FBRyxNQUFNO0FBQy9CO0FBQ0E7QUFDQSx1QkFBdUIsZ0RBQW9CO0FBQzNDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLDZDQUFpQjtBQUN4QztBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsVUFBVTtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseURBQXlEO0FBQ3pEO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0gseUJBQXlCLGdEQUFvQixDQUFDLDJDQUFlO0FBQzdEO0FBQ0EseURBQXlEO0FBQ3pEO0FBQ0E7QUFDQSxtQkFBbUIsbUNBQW1DO0FBQ3RELHdDQUF3QywyQ0FBZSxDQUFDLGtEQUFXO0FBQ25FLElBQUksNENBQWdCO0FBQ3BCO0FBQ0Esc0JBQXNCLGtEQUFXO0FBQ2pDO0FBQ0EsS0FBSztBQUNMLGlDQUFpQyw2Q0FBaUI7QUFDbEQseUJBQXlCLDBDQUFjO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCwyQkFBMkIsZ0RBQW9CLDhCQUE4QixxQkFBcUIsa0JBQWtCLGdEQUFvQixnQ0FBZ0MsMEJBQTBCLHNDQUFzQyxnREFBb0I7QUFDNVA7QUFDQTs7QUFFQTtBQUN5RTtBQUN6RTtBQUNBLHlDQUF5Qyw0QkFBNEI7QUFDckUsd0RBQXdELEtBQUs7QUFDN0QsNENBQTRDLDRCQUE0QjtBQUN4RTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQix3Q0FBd0M7QUFDekQ7QUFDQTtBQUNBLGdEQUFnRCw0Q0FBNEMsd0JBQXdCO0FBQ3BILHVDQUF1Qyx3Q0FBd0M7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRDQUE0QyxzQkFBc0I7QUFDbEUsV0FBVyxtREFBWTtBQUN2QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLFdBQVcsaURBQVcsZUFBZSxPQUFPO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLFlBQVksb0JBQW9CO0FBQ2hDLFdBQVcsOENBQU8sZUFBZSxjQUFjO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDO0FBQ3hDO0FBQ0EsMENBQTBDO0FBQzFDO0FBQ0E7QUFDQTtBQUNBLDBDQUEwQztBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEM7QUFDMUMsd0NBQXdDO0FBQ3hDLHVHQUF1RztBQUN2RyxLQUFLO0FBQ0wsd0NBQXdDO0FBQ3hDLHFHQUFxRztBQUNyRyxLQUFLO0FBQ0wsd0NBQXdDO0FBQ3hDLGtHQUFrRztBQUNsRyxLQUFLO0FBQ0wsaUVBQWlFO0FBQ2pFLEdBQUc7QUFDSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQVNFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vam90YWkteEAxLjIuNF9AdHlwZXMrcmVhY3RAXzUzNDMzMWJlMTFmOTc5NzBlMTRiMjliZDBiYWEyNGY1L25vZGVfbW9kdWxlcy9qb3RhaS14L2Rpc3QvaW5kZXgubWpzPzAxNjEiXSwic291cmNlc0NvbnRlbnQiOlsidmFyIF9fZGVmUHJvcCA9IE9iamVjdC5kZWZpbmVQcm9wZXJ0eTtcbnZhciBfX2RlZlByb3BzID0gT2JqZWN0LmRlZmluZVByb3BlcnRpZXM7XG52YXIgX19nZXRPd25Qcm9wRGVzY3MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9ycztcbnZhciBfX2dldE93blByb3BTeW1ib2xzID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scztcbnZhciBfX2hhc093blByb3AgPSBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5O1xudmFyIF9fcHJvcElzRW51bSA9IE9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGU7XG52YXIgX19kZWZOb3JtYWxQcm9wID0gKG9iaiwga2V5LCB2YWx1ZSkgPT4ga2V5IGluIG9iaiA/IF9fZGVmUHJvcChvYmosIGtleSwgeyBlbnVtZXJhYmxlOiB0cnVlLCBjb25maWd1cmFibGU6IHRydWUsIHdyaXRhYmxlOiB0cnVlLCB2YWx1ZSB9KSA6IG9ialtrZXldID0gdmFsdWU7XG52YXIgX19zcHJlYWRWYWx1ZXMgPSAoYSwgYikgPT4ge1xuICBmb3IgKHZhciBwcm9wIGluIGIgfHwgKGIgPSB7fSkpXG4gICAgaWYgKF9faGFzT3duUHJvcC5jYWxsKGIsIHByb3ApKVxuICAgICAgX19kZWZOb3JtYWxQcm9wKGEsIHByb3AsIGJbcHJvcF0pO1xuICBpZiAoX19nZXRPd25Qcm9wU3ltYm9scylcbiAgICBmb3IgKHZhciBwcm9wIG9mIF9fZ2V0T3duUHJvcFN5bWJvbHMoYikpIHtcbiAgICAgIGlmIChfX3Byb3BJc0VudW0uY2FsbChiLCBwcm9wKSlcbiAgICAgICAgX19kZWZOb3JtYWxQcm9wKGEsIHByb3AsIGJbcHJvcF0pO1xuICAgIH1cbiAgcmV0dXJuIGE7XG59O1xudmFyIF9fc3ByZWFkUHJvcHMgPSAoYSwgYikgPT4gX19kZWZQcm9wcyhhLCBfX2dldE93blByb3BEZXNjcyhiKSk7XG52YXIgX19vYmpSZXN0ID0gKHNvdXJjZSwgZXhjbHVkZSkgPT4ge1xuICB2YXIgdGFyZ2V0ID0ge307XG4gIGZvciAodmFyIHByb3AgaW4gc291cmNlKVxuICAgIGlmIChfX2hhc093blByb3AuY2FsbChzb3VyY2UsIHByb3ApICYmIGV4Y2x1ZGUuaW5kZXhPZihwcm9wKSA8IDApXG4gICAgICB0YXJnZXRbcHJvcF0gPSBzb3VyY2VbcHJvcF07XG4gIGlmIChzb3VyY2UgIT0gbnVsbCAmJiBfX2dldE93blByb3BTeW1ib2xzKVxuICAgIGZvciAodmFyIHByb3Agb2YgX19nZXRPd25Qcm9wU3ltYm9scyhzb3VyY2UpKSB7XG4gICAgICBpZiAoZXhjbHVkZS5pbmRleE9mKHByb3ApIDwgMCAmJiBfX3Byb3BJc0VudW0uY2FsbChzb3VyY2UsIHByb3ApKVxuICAgICAgICB0YXJnZXRbcHJvcF0gPSBzb3VyY2VbcHJvcF07XG4gICAgfVxuICByZXR1cm4gdGFyZ2V0O1xufTtcblxuLy8gc3JjL2F0b21XaXRoRm4udHNcbmltcG9ydCB7IGF0b20gfSBmcm9tIFwiam90YWlcIjtcbnZhciB3cmFwRm4gPSAoZm5PclZhbHVlKSA9PiB0eXBlb2YgZm5PclZhbHVlID09PSBcImZ1bmN0aW9uXCIgPyB7IF9fZm46IGZuT3JWYWx1ZSB9IDogZm5PclZhbHVlO1xudmFyIHVud3JhcEZuID0gKHdyYXBwZWRGbk9yVmFsdWUpID0+IHdyYXBwZWRGbk9yVmFsdWUgJiYgdHlwZW9mIHdyYXBwZWRGbk9yVmFsdWUgPT09IFwib2JqZWN0XCIgJiYgXCJfX2ZuXCIgaW4gd3JhcHBlZEZuT3JWYWx1ZSA/IHdyYXBwZWRGbk9yVmFsdWUuX19mbiA6IHdyYXBwZWRGbk9yVmFsdWU7XG52YXIgYXRvbVdpdGhGbiA9IChpbml0aWFsVmFsdWUpID0+IHtcbiAgY29uc3QgYmFzZUF0b20gPSBhdG9tKHdyYXBGbihpbml0aWFsVmFsdWUpKTtcbiAgcmV0dXJuIGF0b20oXG4gICAgKGdldCkgPT4gdW53cmFwRm4oZ2V0KGJhc2VBdG9tKSksXG4gICAgKF9nZXQsIHNldCwgdmFsdWUpID0+IHNldChiYXNlQXRvbSwgd3JhcEZuKHZhbHVlKSlcbiAgKTtcbn07XG5cbi8vIHNyYy9jcmVhdGVBdG9tUHJvdmlkZXIudHN4XG5pbXBvcnQgUmVhY3QyIGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgY3JlYXRlU3RvcmUgfSBmcm9tIFwiam90YWkvdmFuaWxsYVwiO1xuXG4vLyBzcmMvdXNlSHlkcmF0ZVN0b3JlLnRzXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyB1c2VTZXRBdG9tIH0gZnJvbSBcImpvdGFpXCI7XG5pbXBvcnQgeyB1c2VIeWRyYXRlQXRvbXMgfSBmcm9tIFwiam90YWkvdXRpbHNcIjtcbnZhciB1c2VIeWRyYXRlU3RvcmUgPSAoYXRvbXMsIGluaXRpYWxWYWx1ZXMsIG9wdGlvbnMgPSB7fSkgPT4ge1xuICBjb25zdCB2YWx1ZXMgPSBbXTtcbiAgZm9yIChjb25zdCBrZXkgb2YgT2JqZWN0LmtleXMoYXRvbXMpKSB7XG4gICAgY29uc3QgaW5pdGlhbFZhbHVlID0gaW5pdGlhbFZhbHVlc1trZXldO1xuICAgIGlmIChpbml0aWFsVmFsdWUgIT09IHZvaWQgMCkge1xuICAgICAgdmFsdWVzLnB1c2goW2F0b21zW2tleV0sIGluaXRpYWxWYWx1ZV0pO1xuICAgIH1cbiAgfVxuICB1c2VIeWRyYXRlQXRvbXModmFsdWVzLCBvcHRpb25zKTtcbn07XG52YXIgdXNlU3luY1N0b3JlID0gKGF0b21zLCB2YWx1ZXMsIHsgc3RvcmUgfSA9IHt9KSA9PiB7XG4gIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKGF0b21zKSkge1xuICAgIGNvbnN0IHZhbHVlID0gdmFsdWVzW2tleV07XG4gICAgY29uc3QgYXRvbTIgPSBhdG9tc1trZXldO1xuICAgIGNvbnN0IHNldCA9IHVzZVNldEF0b20oYXRvbTIsIHsgc3RvcmUgfSk7XG4gICAgUmVhY3QudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmICh2YWx1ZSAhPT0gdm9pZCAwICYmIHZhbHVlICE9PSBudWxsKSB7XG4gICAgICAgIHNldCh2YWx1ZSk7XG4gICAgICB9XG4gICAgfSwgW3NldCwgdmFsdWVdKTtcbiAgfVxufTtcblxuLy8gc3JjL2NyZWF0ZUF0b21Qcm92aWRlci50c3hcbnZhciBnZXRGdWxseVF1YWxpZmllZFNjb3BlID0gKHN0b3JlTmFtZSwgc2NvcGUpID0+IHtcbiAgcmV0dXJuIGAke3N0b3JlTmFtZX06JHtzY29wZX1gO1xufTtcbnZhciBQUk9WSURFUl9TQ09QRSA9IFwicHJvdmlkZXJcIjtcbnZhciBBdG9tU3RvcmVDb250ZXh0ID0gUmVhY3QyLmNyZWF0ZUNvbnRleHQoXG4gIC8qIEBfX1BVUkVfXyAqLyBuZXcgTWFwKClcbik7XG52YXIgdXNlQXRvbVN0b3JlID0gKHN0b3JlTmFtZSwgc2NvcGUgPSBQUk9WSURFUl9TQ09QRSwgd2FybklmVW5kZWZpbmVkID0gdHJ1ZSkgPT4ge1xuICB2YXIgX2E7XG4gIGNvbnN0IHN0b3JlQ29udGV4dCA9IFJlYWN0Mi51c2VDb250ZXh0KEF0b21TdG9yZUNvbnRleHQpO1xuICBjb25zdCBzdG9yZSA9IChfYSA9IHN0b3JlQ29udGV4dC5nZXQoZ2V0RnVsbHlRdWFsaWZpZWRTY29wZShzdG9yZU5hbWUsIHNjb3BlKSkpICE9IG51bGwgPyBfYSA6IHN0b3JlQ29udGV4dC5nZXQoZ2V0RnVsbHlRdWFsaWZpZWRTY29wZShzdG9yZU5hbWUsIFBST1ZJREVSX1NDT1BFKSk7XG4gIGlmICghc3RvcmUgJiYgd2FybklmVW5kZWZpbmVkKSB7XG4gICAgY29uc29sZS53YXJuKFxuICAgICAgYFRyaWVkIHRvIGFjY2VzcyBqb3RhaSBzdG9yZSAnJHtzdG9yZU5hbWV9JyBvdXRzaWRlIG9mIGEgbWF0Y2hpbmcgcHJvdmlkZXIuYFxuICAgICk7XG4gIH1cbiAgcmV0dXJuIHN0b3JlO1xufTtcbnZhciBIeWRyYXRlQXRvbXMgPSAoX2EpID0+IHtcbiAgdmFyIF9iID0gX2EsIHtcbiAgICBpbml0aWFsVmFsdWVzLFxuICAgIGNoaWxkcmVuLFxuICAgIHN0b3JlLFxuICAgIGF0b21zXG4gIH0gPSBfYiwgcHJvcHMgPSBfX29ialJlc3QoX2IsIFtcbiAgICBcImluaXRpYWxWYWx1ZXNcIixcbiAgICBcImNoaWxkcmVuXCIsXG4gICAgXCJzdG9yZVwiLFxuICAgIFwiYXRvbXNcIlxuICBdKTtcbiAgdXNlSHlkcmF0ZVN0b3JlKGF0b21zLCBfX3NwcmVhZFZhbHVlcyhfX3NwcmVhZFZhbHVlcyh7fSwgaW5pdGlhbFZhbHVlcyksIHByb3BzKSwge1xuICAgIHN0b3JlXG4gIH0pO1xuICB1c2VTeW5jU3RvcmUoYXRvbXMsIHByb3BzLCB7XG4gICAgc3RvcmVcbiAgfSk7XG4gIHJldHVybiAvKiBAX19QVVJFX18gKi8gUmVhY3QyLmNyZWF0ZUVsZW1lbnQoUmVhY3QyLkZyYWdtZW50LCBudWxsLCBjaGlsZHJlbik7XG59O1xudmFyIGNyZWF0ZUF0b21Qcm92aWRlciA9IChzdG9yZVNjb3BlLCBhdG9tcywgb3B0aW9ucyA9IHt9KSA9PiB7XG4gIGNvbnN0IEVmZmVjdCA9IG9wdGlvbnMuZWZmZWN0O1xuICByZXR1cm4gKF9hKSA9PiB7XG4gICAgdmFyIF9iID0gX2EsIHsgc3RvcmUsIHNjb3BlLCBjaGlsZHJlbiwgcmVzZXRLZXkgfSA9IF9iLCBwcm9wcyA9IF9fb2JqUmVzdChfYiwgW1wic3RvcmVcIiwgXCJzY29wZVwiLCBcImNoaWxkcmVuXCIsIFwicmVzZXRLZXlcIl0pO1xuICAgIGNvbnN0IFtzdG9yZVN0YXRlLCBzZXRTdG9yZVN0YXRlXSA9IFJlYWN0Mi51c2VTdGF0ZShjcmVhdGVTdG9yZSgpKTtcbiAgICBSZWFjdDIudXNlRWZmZWN0KCgpID0+IHtcbiAgICAgIGlmIChyZXNldEtleSkge1xuICAgICAgICBzZXRTdG9yZVN0YXRlKGNyZWF0ZVN0b3JlKCkpO1xuICAgICAgfVxuICAgIH0sIFtyZXNldEtleV0pO1xuICAgIGNvbnN0IHByZXZpb3VzU3RvcmVDb250ZXh0ID0gUmVhY3QyLnVzZUNvbnRleHQoQXRvbVN0b3JlQ29udGV4dCk7XG4gICAgY29uc3Qgc3RvcmVDb250ZXh0ID0gUmVhY3QyLnVzZU1lbW8oKCkgPT4ge1xuICAgICAgY29uc3QgbmV3U3RvcmVDb250ZXh0ID0gbmV3IE1hcChwcmV2aW91c1N0b3JlQ29udGV4dCk7XG4gICAgICBpZiAoc2NvcGUpIHtcbiAgICAgICAgbmV3U3RvcmVDb250ZXh0LnNldChcbiAgICAgICAgICBnZXRGdWxseVF1YWxpZmllZFNjb3BlKHN0b3JlU2NvcGUsIHNjb3BlKSxcbiAgICAgICAgICBzdG9yZVN0YXRlXG4gICAgICAgICk7XG4gICAgICB9XG4gICAgICBuZXdTdG9yZUNvbnRleHQuc2V0KFxuICAgICAgICBnZXRGdWxseVF1YWxpZmllZFNjb3BlKHN0b3JlU2NvcGUsIFBST1ZJREVSX1NDT1BFKSxcbiAgICAgICAgc3RvcmVTdGF0ZVxuICAgICAgKTtcbiAgICAgIHJldHVybiBuZXdTdG9yZUNvbnRleHQ7XG4gICAgfSwgW3ByZXZpb3VzU3RvcmVDb250ZXh0LCBzY29wZSwgc3RvcmVTdGF0ZV0pO1xuICAgIHJldHVybiAvKiBAX19QVVJFX18gKi8gUmVhY3QyLmNyZWF0ZUVsZW1lbnQoQXRvbVN0b3JlQ29udGV4dC5Qcm92aWRlciwgeyB2YWx1ZTogc3RvcmVDb250ZXh0IH0sIC8qIEBfX1BVUkVfXyAqLyBSZWFjdDIuY3JlYXRlRWxlbWVudChIeWRyYXRlQXRvbXMsIF9fc3ByZWFkVmFsdWVzKHsgc3RvcmU6IHN0b3JlU3RhdGUsIGF0b21zIH0sIHByb3BzKSwgISFFZmZlY3QgJiYgLyogQF9fUFVSRV9fICovIFJlYWN0Mi5jcmVhdGVFbGVtZW50KEVmZmVjdCwgbnVsbCksIGNoaWxkcmVuKSk7XG4gIH07XG59O1xuXG4vLyBzcmMvY3JlYXRlQXRvbVN0b3JlLnRzXG5pbXBvcnQgeyB1c2VBdG9tLCB1c2VBdG9tVmFsdWUsIHVzZVNldEF0b20gYXMgdXNlU2V0QXRvbTIgfSBmcm9tIFwiam90YWlcIjtcbnZhciBjYXBpdGFsaXplRmlyc3RMZXR0ZXIgPSAoc3RyID0gXCJcIikgPT4gc3RyLmxlbmd0aCA+IDAgPyBzdHJbMF0udG9VcHBlckNhc2UoKSArIHN0ci5zbGljZSgxKSA6IFwiXCI7XG52YXIgZ2V0UHJvdmlkZXJJbmRleCA9IChuYW1lID0gXCJcIikgPT4gYCR7Y2FwaXRhbGl6ZUZpcnN0TGV0dGVyKG5hbWUpfVByb3ZpZGVyYDtcbnZhciBnZXRTdG9yZUluZGV4ID0gKG5hbWUgPSBcIlwiKSA9PiBuYW1lLmxlbmd0aCA+IDAgPyBgJHtuYW1lfVN0b3JlYCA6IFwic3RvcmVcIjtcbnZhciBnZXRVc2VTdG9yZUluZGV4ID0gKG5hbWUgPSBcIlwiKSA9PiBgdXNlJHtjYXBpdGFsaXplRmlyc3RMZXR0ZXIobmFtZSl9U3RvcmVgO1xudmFyIGlzQXRvbSA9IChwb3NzaWJsZUF0b20pID0+ICEhcG9zc2libGVBdG9tICYmIHR5cGVvZiBwb3NzaWJsZUF0b20gPT09IFwib2JqZWN0XCIgJiYgXCJyZWFkXCIgaW4gcG9zc2libGVBdG9tICYmIHR5cGVvZiBwb3NzaWJsZUF0b20ucmVhZCA9PT0gXCJmdW5jdGlvblwiO1xudmFyIHdpdGhEZWZhdWx0T3B0aW9ucyA9IChmblJlY29yZCwgZGVmYXVsdE9wdGlvbnMpID0+IE9iamVjdC5mcm9tRW50cmllcyhcbiAgT2JqZWN0LmVudHJpZXMoZm5SZWNvcmQpLm1hcCgoW2tleSwgZm5dKSA9PiBbXG4gICAga2V5LFxuICAgIChvcHRpb25zID0ge30pID0+IGZuKF9fc3ByZWFkVmFsdWVzKF9fc3ByZWFkVmFsdWVzKHt9LCBkZWZhdWx0T3B0aW9ucyksIG9wdGlvbnMpKVxuICBdKVxuKTtcbnZhciBjb252ZXJ0U2NvcGVTaG9ydGhhbmQgPSAob3B0aW9uc09yU2NvcGUgPSB7fSkgPT4gdHlwZW9mIG9wdGlvbnNPclNjb3BlID09PSBcInN0cmluZ1wiID8geyBzY29wZTogb3B0aW9uc09yU2NvcGUgfSA6IG9wdGlvbnNPclNjb3BlO1xudmFyIGNyZWF0ZUF0b21TdG9yZSA9IChpbml0aWFsU3RhdGUsIHsgbmFtZSwgZGVsYXk6IGRlbGF5Um9vdCwgZWZmZWN0LCBleHRlbmQgfSkgPT4ge1xuICBjb25zdCBwcm92aWRlckluZGV4ID0gZ2V0UHJvdmlkZXJJbmRleChuYW1lKTtcbiAgY29uc3QgdXNlU3RvcmVJbmRleCA9IGdldFVzZVN0b3JlSW5kZXgobmFtZSk7XG4gIGNvbnN0IHN0b3JlSW5kZXggPSBnZXRTdG9yZUluZGV4KG5hbWUpO1xuICBjb25zdCBhdG9tc1dpdGhvdXRFeHRlbmQgPSB7fTtcbiAgY29uc3Qgd3JpdGFibGVBdG9tc1dpdGhvdXRFeHRlbmQgPSB7fTtcbiAgY29uc3QgYXRvbUlzV3JpdGFibGUgPSB7fTtcbiAgZm9yIChjb25zdCBba2V5LCBhdG9tT3JWYWx1ZV0gb2YgT2JqZWN0LmVudHJpZXMoaW5pdGlhbFN0YXRlKSkge1xuICAgIGNvbnN0IGF0b21Db25maWcgPSBpc0F0b20oYXRvbU9yVmFsdWUpID8gYXRvbU9yVmFsdWUgOiBhdG9tV2l0aEZuKGF0b21PclZhbHVlKTtcbiAgICBhdG9tc1dpdGhvdXRFeHRlbmRba2V5XSA9IGF0b21Db25maWc7XG4gICAgY29uc3Qgd3JpdGFibGUgPSBcIndyaXRlXCIgaW4gYXRvbUNvbmZpZztcbiAgICBhdG9tSXNXcml0YWJsZVtrZXldID0gd3JpdGFibGU7XG4gICAgaWYgKHdyaXRhYmxlKSB7XG4gICAgICB3cml0YWJsZUF0b21zV2l0aG91dEV4dGVuZFtrZXldID0gYXRvbUNvbmZpZztcbiAgICB9XG4gIH1cbiAgY29uc3QgYXRvbXMgPSBfX3NwcmVhZFZhbHVlcyh7fSwgYXRvbXNXaXRob3V0RXh0ZW5kKTtcbiAgaWYgKGV4dGVuZCkge1xuICAgIGNvbnN0IGV4dGVuZGVkQXRvbXMgPSBleHRlbmQoYXRvbXNXaXRob3V0RXh0ZW5kKTtcbiAgICBmb3IgKGNvbnN0IFtrZXksIGF0b21Db25maWddIG9mIE9iamVjdC5lbnRyaWVzKGV4dGVuZGVkQXRvbXMpKSB7XG4gICAgICBhdG9tc1trZXldID0gYXRvbUNvbmZpZztcbiAgICAgIGF0b21Jc1dyaXRhYmxlW2tleV0gPSBcIndyaXRlXCIgaW4gYXRvbUNvbmZpZztcbiAgICB9XG4gIH1cbiAgY29uc3QgZ2V0QXRvbXMgPSB7fTtcbiAgY29uc3Qgc2V0QXRvbXMgPSB7fTtcbiAgY29uc3QgdXNlQXRvbXMgPSB7fTtcbiAgY29uc3QgdXNlU3RvcmUgPSAob3B0aW9uc09yU2NvcGUgPSB7fSkgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIHNjb3BlLFxuICAgICAgc3RvcmUsXG4gICAgICB3YXJuSWZOb1N0b3JlID0gdHJ1ZVxuICAgIH0gPSBjb252ZXJ0U2NvcGVTaG9ydGhhbmQob3B0aW9uc09yU2NvcGUpO1xuICAgIGNvbnN0IGNvbnRleHRTdG9yZSA9IHVzZUF0b21TdG9yZShuYW1lLCBzY29wZSwgIXN0b3JlICYmIHdhcm5JZk5vU3RvcmUpO1xuICAgIHJldHVybiBzdG9yZSAhPSBudWxsID8gc3RvcmUgOiBjb250ZXh0U3RvcmU7XG4gIH07XG4gIGNvbnN0IHVzZUF0b21WYWx1ZVdpdGhTdG9yZSA9IChhdG9tQ29uZmlnLCBvcHRpb25zT3JTY29wZSkgPT4ge1xuICAgIHZhciBfYTtcbiAgICBjb25zdCBvcHRpb25zID0gY29udmVydFNjb3BlU2hvcnRoYW5kKG9wdGlvbnNPclNjb3BlKTtcbiAgICBjb25zdCBzdG9yZSA9IHVzZVN0b3JlKF9fc3ByZWFkVmFsdWVzKHsgd2FybklmTm9TdG9yZTogZmFsc2UgfSwgb3B0aW9ucykpO1xuICAgIHJldHVybiB1c2VBdG9tVmFsdWUoYXRvbUNvbmZpZywge1xuICAgICAgc3RvcmUsXG4gICAgICBkZWxheTogKF9hID0gb3B0aW9ucy5kZWxheSkgIT0gbnVsbCA/IF9hIDogZGVsYXlSb290XG4gICAgfSk7XG4gIH07XG4gIGNvbnN0IHVzZVNldEF0b21XaXRoU3RvcmUgPSAoYXRvbUNvbmZpZywgb3B0aW9uc09yU2NvcGUpID0+IHtcbiAgICBjb25zdCBzdG9yZSA9IHVzZVN0b3JlKG9wdGlvbnNPclNjb3BlKTtcbiAgICByZXR1cm4gdXNlU2V0QXRvbTIoYXRvbUNvbmZpZywgeyBzdG9yZSB9KTtcbiAgfTtcbiAgY29uc3QgdXNlQXRvbVdpdGhTdG9yZSA9IChhdG9tQ29uZmlnLCBvcHRpb25zT3JTY29wZSkgPT4ge1xuICAgIGNvbnN0IHN0b3JlID0gdXNlU3RvcmUob3B0aW9uc09yU2NvcGUpO1xuICAgIGNvbnN0IHsgZGVsYXkgPSBkZWxheVJvb3QgfSA9IGNvbnZlcnRTY29wZVNob3J0aGFuZChvcHRpb25zT3JTY29wZSk7XG4gICAgcmV0dXJuIHVzZUF0b20oYXRvbUNvbmZpZywgeyBzdG9yZSwgZGVsYXkgfSk7XG4gIH07XG4gIGZvciAoY29uc3Qga2V5IG9mIE9iamVjdC5rZXlzKGF0b21zKSkge1xuICAgIGNvbnN0IGF0b21Db25maWcgPSBhdG9tc1trZXldO1xuICAgIGNvbnN0IGlzV3JpdGFibGUgPSBhdG9tSXNXcml0YWJsZVtrZXldO1xuICAgIGdldEF0b21zW2tleV0gPSAob3B0aW9uc09yU2NvcGUgPSB7fSkgPT4gdXNlQXRvbVZhbHVlV2l0aFN0b3JlKGF0b21Db25maWcsIG9wdGlvbnNPclNjb3BlKTtcbiAgICBpZiAoaXNXcml0YWJsZSkge1xuICAgICAgc2V0QXRvbXNba2V5XSA9IChvcHRpb25zT3JTY29wZSA9IHt9KSA9PiB1c2VTZXRBdG9tV2l0aFN0b3JlKFxuICAgICAgICBhdG9tQ29uZmlnLFxuICAgICAgICBvcHRpb25zT3JTY29wZVxuICAgICAgKTtcbiAgICAgIHVzZUF0b21zW2tleV0gPSAob3B0aW9uc09yU2NvcGUgPSB7fSkgPT4gdXNlQXRvbVdpdGhTdG9yZShcbiAgICAgICAgYXRvbUNvbmZpZyxcbiAgICAgICAgb3B0aW9uc09yU2NvcGVcbiAgICAgICk7XG4gICAgfVxuICB9XG4gIGNvbnN0IFByb3ZpZGVyID0gY3JlYXRlQXRvbVByb3ZpZGVyKFxuICAgIG5hbWUsXG4gICAgd3JpdGFibGVBdG9tc1dpdGhvdXRFeHRlbmQsXG4gICAgeyBlZmZlY3QgfVxuICApO1xuICBjb25zdCBzdG9yZUFwaSA9IHtcbiAgICBhdG9tOiBhdG9tcyxcbiAgICBuYW1lXG4gIH07XG4gIGNvbnN0IHVzZVN0b3JlQXBpID0gKGRlZmF1bHRPcHRpb25zID0ge30pID0+ICh7XG4gICAgZ2V0OiBfX3NwcmVhZFByb3BzKF9fc3ByZWFkVmFsdWVzKHt9LCB3aXRoRGVmYXVsdE9wdGlvbnMoZ2V0QXRvbXMsIGNvbnZlcnRTY29wZVNob3J0aGFuZChkZWZhdWx0T3B0aW9ucykpKSwge1xuICAgICAgYXRvbTogKGF0b21Db25maWcsIG9wdGlvbnMpID0+IHVzZUF0b21WYWx1ZVdpdGhTdG9yZShhdG9tQ29uZmlnLCBfX3NwcmVhZFZhbHVlcyhfX3NwcmVhZFZhbHVlcyh7fSwgY29udmVydFNjb3BlU2hvcnRoYW5kKGRlZmF1bHRPcHRpb25zKSksIGNvbnZlcnRTY29wZVNob3J0aGFuZChvcHRpb25zKSkpXG4gICAgfSksXG4gICAgc2V0OiBfX3NwcmVhZFByb3BzKF9fc3ByZWFkVmFsdWVzKHt9LCB3aXRoRGVmYXVsdE9wdGlvbnMoc2V0QXRvbXMsIGNvbnZlcnRTY29wZVNob3J0aGFuZChkZWZhdWx0T3B0aW9ucykpKSwge1xuICAgICAgYXRvbTogKGF0b21Db25maWcsIG9wdGlvbnMpID0+IHVzZVNldEF0b21XaXRoU3RvcmUoYXRvbUNvbmZpZywgX19zcHJlYWRWYWx1ZXMoX19zcHJlYWRWYWx1ZXMoe30sIGNvbnZlcnRTY29wZVNob3J0aGFuZChkZWZhdWx0T3B0aW9ucykpLCBjb252ZXJ0U2NvcGVTaG9ydGhhbmQob3B0aW9ucykpKVxuICAgIH0pLFxuICAgIHVzZTogX19zcHJlYWRQcm9wcyhfX3NwcmVhZFZhbHVlcyh7fSwgd2l0aERlZmF1bHRPcHRpb25zKHVzZUF0b21zLCBjb252ZXJ0U2NvcGVTaG9ydGhhbmQoZGVmYXVsdE9wdGlvbnMpKSksIHtcbiAgICAgIGF0b206IChhdG9tQ29uZmlnLCBvcHRpb25zKSA9PiB1c2VBdG9tV2l0aFN0b3JlKGF0b21Db25maWcsIF9fc3ByZWFkVmFsdWVzKF9fc3ByZWFkVmFsdWVzKHt9LCBjb252ZXJ0U2NvcGVTaG9ydGhhbmQoZGVmYXVsdE9wdGlvbnMpKSwgY29udmVydFNjb3BlU2hvcnRoYW5kKG9wdGlvbnMpKSlcbiAgICB9KSxcbiAgICBzdG9yZTogKG9wdGlvbnMpID0+IHVzZVN0b3JlKF9fc3ByZWFkVmFsdWVzKF9fc3ByZWFkVmFsdWVzKHt9LCBjb252ZXJ0U2NvcGVTaG9ydGhhbmQoZGVmYXVsdE9wdGlvbnMpKSwgY29udmVydFNjb3BlU2hvcnRoYW5kKG9wdGlvbnMpKSlcbiAgfSk7XG4gIHJldHVybiB7XG4gICAgW3Byb3ZpZGVySW5kZXhdOiBQcm92aWRlcixcbiAgICBbdXNlU3RvcmVJbmRleF06IHVzZVN0b3JlQXBpLFxuICAgIFtzdG9yZUluZGV4XTogc3RvcmVBcGksXG4gICAgbmFtZVxuICB9O1xufTtcbmV4cG9ydCB7XG4gIEh5ZHJhdGVBdG9tcyxcbiAgYXRvbVdpdGhGbixcbiAgY3JlYXRlQXRvbVByb3ZpZGVyLFxuICBjcmVhdGVBdG9tU3RvcmUsXG4gIHVzZUF0b21TdG9yZSxcbiAgdXNlSHlkcmF0ZVN0b3JlLFxuICB1c2VTeW5jU3RvcmVcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai-x@1.2.4_@types+react@_534331be11f97970e14b29bd0baa24f5/node_modules/jotai-x/dist/index.mjs\n");

/***/ })

};
;