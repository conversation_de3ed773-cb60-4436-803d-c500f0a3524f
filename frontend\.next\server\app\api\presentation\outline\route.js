/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/presentation/outline/route";
exports.ids = ["app/api/presentation/outline/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib sync recursive":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/ sync ***!
  \**************************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "async_hooks":
/*!******************************!*\
  !*** external "async_hooks" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("async_hooks");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "node:crypto":
/*!******************************!*\
  !*** external "node:crypto" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("node:crypto");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpresentation%2Foutline%2Froute&page=%2Fapi%2Fpresentation%2Foutline%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpresentation%2Foutline%2Froute.ts&appDir=E%3A%5CMultiAgentPPT%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMultiAgentPPT%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpresentation%2Foutline%2Froute&page=%2Fapi%2Fpresentation%2Foutline%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpresentation%2Foutline%2Froute.ts&appDir=E%3A%5CMultiAgentPPT%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMultiAgentPPT%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var E_MultiAgentPPT_frontend_src_app_api_presentation_outline_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/presentation/outline/route.ts */ \"(rsc)/./src/app/api/presentation/outline/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/presentation/outline/route\",\n        pathname: \"/api/presentation/outline\",\n        filename: \"route\",\n        bundlePath: \"app/api/presentation/outline/route\"\n    },\n    resolvedPagePath: \"E:\\\\MultiAgentPPT\\\\frontend\\\\src\\\\app\\\\api\\\\presentation\\\\outline\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_MultiAgentPPT_frontend_src_app_api_presentation_outline_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/presentation/outline/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpresentation%2Foutline%2Froute&page=%2Fapi%2Fpresentation%2Foutline%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpresentation%2Foutline%2Froute.ts&appDir=E%3A%5CMultiAgentPPT%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMultiAgentPPT%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/presentation/outline/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/presentation/outline/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _a2a_js_sdk__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @a2a-js/sdk */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/index.js\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! node:crypto */ \"node:crypto\");\n/* harmony import */ var node_crypto__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(node_crypto__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var ai__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ai */ \"(rsc)/./node_modules/.pnpm/ai@4.2.0_react@18.2.0_zod@3.24.2/node_modules/ai/dist/index.mjs\");\n\n// app/a2aexample/api/outline/route.ts\n\n\n // crypto is a built-in Node.js module\nfunction generateId() {\n    return node_crypto__WEBPACK_IMPORTED_MODULE_2___default().randomUUID();\n}\n// A2A Agent 服务器的 URL。\n// 在生产环境中，这应该从环境变量中读取。\nconst A2A_AGENT_SERVER_URL = process.env.A2A_AGENT_OUTLINE_URL ?? \"http://localhost:10001\";\nconsole.log(\"A2A Agent Server URL:\", A2A_AGENT_SERVER_URL);\n/**\r\n * Creates an async generator that streams text content from the A2A agent.\r\n * @param {string} serverUrl - The URL of the A2A agent server.\r\n * @param {string} content - The user input (e.g., title for outline generation).\r\n * @yields {string} Each chunk of text received from the agent.\r\n */ function iteratorToStream(iterator) {\n    return new ReadableStream({\n        async pull (controller) {\n            const { value, done } = await iterator.next();\n            if (done) {\n                controller.close();\n            } else {\n                controller.enqueue(value);\n            }\n        }\n    });\n}\nasync function* generateOutlineStream(serverUrl, content) {\n    const client = new _a2a_js_sdk__WEBPACK_IMPORTED_MODULE_1__.A2AClient(serverUrl);\n    const messageId = generateId();\n    const message = {\n        messageId,\n        kind: \"message\",\n        role: \"user\",\n        parts: [\n            {\n                kind: \"text\",\n                text: content\n            }\n        ]\n    };\n    try {\n        const stream = client.sendMessageStream({\n            message\n        });\n        for await (const event of stream){\n            console.log(\"Received event:\", event);\n            // 处理 status-update 事件中嵌套的 message\n            if (event.kind === \"status-update\" && event.status && event.status.message) {\n                const nestedMessage = event.status.message;\n                for (const part of nestedMessage.parts){\n                    if (part.kind === \"text\") {\n                        console.log(\"Yielding text part (from status-update message):\", part.text);\n                        //生成状态更新消息中包含的文本内容\n                        yield part.text; // Yield the text content\n                    }\n                }\n            } else if (event.kind === \"artifact-update\" && event.artifact && event.artifact.parts) {\n                console.log(\"Processing artifact-update event.\");\n                // 遍历 artifact 的 parts。Artifact 的 parts 也可能包含 text 或其他类型 (如 file)\n                for (const part of event.artifact.parts){\n                    if (part.kind === \"text\") {\n                        console.log(\"Yielding text part (from artifact):\", part.text);\n                    //生成的artifact中包含的文本内容\n                    // yield part.text; // 将 Artifact 中的文本内容也输出\n                    }\n                }\n            } else {\n                console.log(\"Received event (ignoring):\", event);\n            }\n        }\n    } catch (error) {\n        console.error(\"Error communicating with A2A client:\", error);\n        // Rethrow or yield an error message to the consumer\n        yield `Error: Failed to communicate with agent. ${error.message}`;\n    }\n}\nasync function POST(request) {\n    try {\n        const { prompt, numberOfCards, language } = await request.json();\n        if (!prompt || !numberOfCards || !language) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        const stream = iteratorToStream(generateOutlineStream(A2A_AGENT_SERVER_URL, prompt));\n        return ai__WEBPACK_IMPORTED_MODULE_3__.LangChainAdapter.toDataStreamResponse(stream);\n    } catch (error) {\n        console.error(\"Error in presentation outline:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to generate presentation outline\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/presentation/outline/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28","vendor-chunks/@opentelemetry+api@1.9.0","vendor-chunks/zod@3.24.2","vendor-chunks/ai@4.2.0_react@18.2.0_zod@3.24.2","vendor-chunks/zod-to-json-schema@3.24.5_zod@3.24.2","vendor-chunks/@ai-sdk+ui-utils@1.2.0_zod@3.24.2","vendor-chunks/@ai-sdk+provider-utils@2.2.0_zod@3.24.2","vendor-chunks/@ai-sdk+provider@1.1.0","vendor-chunks/eventsource-parser@3.0.0","vendor-chunks/secure-json-parse@2.7.0","vendor-chunks/nanoid@3.3.11","vendor-chunks/iconv-lite@0.4.24","vendor-chunks/@a2a-js+sdk@0.2.2","vendor-chunks/express@4.21.2","vendor-chunks/math-intrinsics@1.1.0","vendor-chunks/es-errors@1.3.0","vendor-chunks/uuid@11.1.0","vendor-chunks/body-parser@1.20.3","vendor-chunks/qs@6.13.0","vendor-chunks/negotiator@0.6.3","vendor-chunks/call-bind-apply-helpers@1.0.2","vendor-chunks/debug@2.6.9","vendor-chunks/get-proto@1.0.1","vendor-chunks/statuses@2.0.1","vendor-chunks/object-inspect@1.13.4","vendor-chunks/mime@1.6.0","vendor-chunks/mime-db@1.52.0","vendor-chunks/inherits@2.0.4","vendor-chunks/has-symbols@1.1.0","vendor-chunks/gopd@1.2.0","vendor-chunks/function-bind@1.1.2","vendor-chunks/vary@1.1.2","vendor-chunks/utils-merge@1.0.1","vendor-chunks/unpipe@1.0.0","vendor-chunks/type-is@1.6.18","vendor-chunks/toidentifier@1.0.1","vendor-chunks/side-channel@1.1.0","vendor-chunks/side-channel-weakmap@1.0.2","vendor-chunks/side-channel-map@1.0.1","vendor-chunks/side-channel-list@1.0.0","vendor-chunks/setprototypeof@1.2.0","vendor-chunks/serve-static@1.16.2","vendor-chunks/send@0.19.0","vendor-chunks/safer-buffer@2.1.2","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/raw-body@2.5.2","vendor-chunks/range-parser@1.2.1","vendor-chunks/proxy-addr@2.0.7","vendor-chunks/path-to-regexp@0.1.12","vendor-chunks/parseurl@1.3.3","vendor-chunks/on-finished@2.4.1","vendor-chunks/ms@2.1.3","vendor-chunks/ms@2.0.0","vendor-chunks/mime-types@2.1.35","vendor-chunks/methods@1.1.2","vendor-chunks/merge-descriptors@1.0.3","vendor-chunks/media-typer@0.3.0","vendor-chunks/ipaddr.js@1.9.1","vendor-chunks/http-errors@2.0.0","vendor-chunks/hasown@2.0.2","vendor-chunks/get-intrinsic@1.3.0","vendor-chunks/fresh@0.5.2","vendor-chunks/forwarded@0.2.0","vendor-chunks/finalhandler@1.3.1","vendor-chunks/etag@1.8.1","vendor-chunks/escape-html@1.0.3","vendor-chunks/es-object-atoms@1.1.1","vendor-chunks/es-define-property@1.0.1","vendor-chunks/encodeurl@2.0.0","vendor-chunks/encodeurl@1.0.2","vendor-chunks/ee-first@1.1.1","vendor-chunks/dunder-proto@1.0.1","vendor-chunks/destroy@1.2.0","vendor-chunks/depd@2.0.0","vendor-chunks/cookie@0.7.1","vendor-chunks/cookie-signature@1.0.6","vendor-chunks/content-type@1.0.5","vendor-chunks/content-disposition@0.5.4","vendor-chunks/call-bound@1.0.4","vendor-chunks/bytes@3.1.2","vendor-chunks/array-flatten@1.1.1","vendor-chunks/accepts@1.3.8"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fpresentation%2Foutline%2Froute&page=%2Fapi%2Fpresentation%2Foutline%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpresentation%2Foutline%2Froute.ts&appDir=E%3A%5CMultiAgentPPT%5Cfrontend%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CMultiAgentPPT%5Cfrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();