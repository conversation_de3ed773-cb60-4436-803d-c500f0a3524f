"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@tanstack+react-query@5.69.0_react@18.2.0";
exports.ids = ["vendor-chunks/@tanstack+react-query@5.69.0_react@18.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryClientContext: () => (/* binding */ QueryClientContext),\n/* harmony export */   QueryClientProvider: () => (/* binding */ QueryClientProvider),\n/* harmony export */   useQueryClient: () => (/* binding */ useQueryClient)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryClientContext,QueryClientProvider,useQueryClient auto */ // src/QueryClientProvider.tsx\n\n\nvar QueryClientContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar useQueryClient = (queryClient)=>{\n    const client = react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryClientContext);\n    if (queryClient) {\n        return queryClient;\n    }\n    if (!client) {\n        throw new Error(\"No QueryClient set, use QueryClientProvider to set one\");\n    }\n    return client;\n};\nvar QueryClientProvider = ({ client, children })=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        client.mount();\n        return ()=>{\n            client.unmount();\n        };\n    }, [\n        client\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryClientContext.Provider, {\n        value: client,\n        children\n    });\n};\n //# sourceMappingURL=QueryClientProvider.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   QueryErrorResetBoundary: () => (/* binding */ QueryErrorResetBoundary),\n/* harmony export */   useQueryErrorResetBoundary: () => (/* binding */ useQueryErrorResetBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ QueryErrorResetBoundary,useQueryErrorResetBoundary auto */ // src/QueryErrorResetBoundary.tsx\n\n\nfunction createValue() {\n    let isReset = false;\n    return {\n        clearReset: ()=>{\n            isReset = false;\n        },\n        reset: ()=>{\n            isReset = true;\n        },\n        isReset: ()=>{\n            return isReset;\n        }\n    };\n}\nvar QueryErrorResetBoundaryContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(createValue());\nvar useQueryErrorResetBoundary = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(QueryErrorResetBoundaryContext);\nvar QueryErrorResetBoundary = ({ children })=>{\n    const [value] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>createValue());\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(QueryErrorResetBoundaryContext.Provider, {\n        value,\n        children: typeof children === \"function\" ? children(value) : children\n    });\n};\n //# sourceMappingURL=QueryErrorResetBoundary.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ensurePreventErrorBoundaryRetry: () => (/* binding */ ensurePreventErrorBoundaryRetry),\n/* harmony export */   getHasError: () => (/* binding */ getHasError),\n/* harmony export */   useClearResetErrorBoundary: () => (/* binding */ useClearResetErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ ensurePreventErrorBoundaryRetry,getHasError,useClearResetErrorBoundary auto */ // src/errorBoundaryUtils.ts\n\n\nvar ensurePreventErrorBoundaryRetry = (options, errorResetBoundary)=>{\n    if (options.suspense || options.throwOnError || options.experimental_prefetchInRender) {\n        if (!errorResetBoundary.isReset()) {\n            options.retryOnMount = false;\n        }\n    }\n};\nvar useClearResetErrorBoundary = (errorResetBoundary)=>{\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        errorResetBoundary.clearReset();\n    }, [\n        errorResetBoundary\n    ]);\n};\nvar getHasError = ({ result, errorResetBoundary, throwOnError, query, suspense })=>{\n    return result.isError && !errorResetBoundary.isReset() && !result.isFetching && query && (suspense && result.data === void 0 || (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.shouldThrowError)(throwOnError, [\n        result.error,\n        query\n    ]));\n};\n //# sourceMappingURL=errorBoundaryUtils.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/isRestoring.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/isRestoring.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IsRestoringProvider: () => (/* binding */ IsRestoringProvider),\n/* harmony export */   useIsRestoring: () => (/* binding */ useIsRestoring)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ IsRestoringProvider,useIsRestoring auto */ // src/isRestoring.ts\n\nvar IsRestoringContext = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(false);\nvar useIsRestoring = ()=>react__WEBPACK_IMPORTED_MODULE_0__.useContext(IsRestoringContext);\nvar IsRestoringProvider = IsRestoringContext.Provider;\n //# sourceMappingURL=isRestoring.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuNjkuMF9yZWFjdEAxOC4yLjAvbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vaXNSZXN0b3JpbmcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUN1QjtBQUV2QixJQUFNQyxtQ0FBMkJELGdEQUFBLENBQWM7QUFFeEMsSUFBTUcsaUJBQWlCLElBQVlILDZDQUFBLENBQVdDO0FBQzlDLElBQU1JLHNCQUFzQkosbUJBQW1CSyxRQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4uLy4uL3NyYy9pc1Jlc3RvcmluZy50cz82MmE0Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmNvbnN0IElzUmVzdG9yaW5nQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQoZmFsc2UpXG5cbmV4cG9ydCBjb25zdCB1c2VJc1Jlc3RvcmluZyA9ICgpID0+IFJlYWN0LnVzZUNvbnRleHQoSXNSZXN0b3JpbmdDb250ZXh0KVxuZXhwb3J0IGNvbnN0IElzUmVzdG9yaW5nUHJvdmlkZXIgPSBJc1Jlc3RvcmluZ0NvbnRleHQuUHJvdmlkZXJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsIklzUmVzdG9yaW5nQ29udGV4dCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VJc1Jlc3RvcmluZyIsInVzZUNvbnRleHQiLCJJc1Jlc3RvcmluZ1Byb3ZpZGVyIiwiUHJvdmlkZXIiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/isRestoring.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/suspense.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/suspense.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultThrowOnError: () => (/* binding */ defaultThrowOnError),\n/* harmony export */   ensureSuspenseTimers: () => (/* binding */ ensureSuspenseTimers),\n/* harmony export */   fetchOptimistic: () => (/* binding */ fetchOptimistic),\n/* harmony export */   shouldSuspend: () => (/* binding */ shouldSuspend),\n/* harmony export */   willFetch: () => (/* binding */ willFetch)\n/* harmony export */ });\n// src/suspense.ts\nvar defaultThrowOnError = (_error, query) => query.state.data === void 0;\nvar ensureSuspenseTimers = (defaultedOptions) => {\n  const originalStaleTime = defaultedOptions.staleTime;\n  if (defaultedOptions.suspense) {\n    defaultedOptions.staleTime = typeof originalStaleTime === \"function\" ? (...args) => Math.max(originalStaleTime(...args), 1e3) : Math.max(originalStaleTime ?? 1e3, 1e3);\n    if (typeof defaultedOptions.gcTime === \"number\") {\n      defaultedOptions.gcTime = Math.max(defaultedOptions.gcTime, 1e3);\n    }\n  }\n};\nvar willFetch = (result, isRestoring) => result.isLoading && result.isFetching && !isRestoring;\nvar shouldSuspend = (defaultedOptions, result) => defaultedOptions?.suspense && result.isPending;\nvar fetchOptimistic = (defaultedOptions, observer, errorResetBoundary) => observer.fetchOptimistic(defaultedOptions).catch(() => {\n  errorResetBoundary.clearReset();\n});\n\n//# sourceMappingURL=suspense.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/suspense.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js":
/*!**************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js ***!
  \**************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useBaseQuery: () => (/* binding */ useBaseQuery)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/utils.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./QueryErrorResetBoundary.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryErrorResetBoundary.js\");\n/* harmony import */ var _errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./errorBoundaryUtils.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/errorBoundaryUtils.js\");\n/* harmony import */ var _isRestoring_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./isRestoring.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/isRestoring.js\");\n/* harmony import */ var _suspense_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./suspense.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/suspense.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ useBaseQuery auto */ // src/useBaseQuery.ts\n\n\n\n\n\n\n\n\nfunction useBaseQuery(options, Observer, queryClient) {\n    if (true) {\n        if (typeof options !== \"object\" || Array.isArray(options)) {\n            throw new Error('Bad argument type. Starting with v5, only the \"Object\" form is allowed when calling query related functions. Please use the error stack to find the culprit call. More info here: https://tanstack.com/query/latest/docs/react/guides/migrating-to-v5#supports-a-single-signature-one-object');\n        }\n    }\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const isRestoring = (0,_isRestoring_js__WEBPACK_IMPORTED_MODULE_2__.useIsRestoring)();\n    const errorResetBoundary = (0,_QueryErrorResetBoundary_js__WEBPACK_IMPORTED_MODULE_3__.useQueryErrorResetBoundary)();\n    const defaultedOptions = client.defaultQueryOptions(options);\n    client.getDefaultOptions().queries?._experimental_beforeQuery?.(defaultedOptions);\n    if (true) {\n        if (!defaultedOptions.queryFn) {\n            console.error(`[${defaultedOptions.queryHash}]: No queryFn was passed as an option, and no default queryFn was found. The queryFn parameter is only optional when using a default queryFn. More info here: https://tanstack.com/query/latest/docs/framework/react/guides/default-query-function`);\n        }\n    }\n    defaultedOptions._optimisticResults = isRestoring ? \"isRestoring\" : \"optimistic\";\n    (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.ensureSuspenseTimers)(defaultedOptions);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.ensurePreventErrorBoundaryRetry)(defaultedOptions, errorResetBoundary);\n    (0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.useClearResetErrorBoundary)(errorResetBoundary);\n    const isNewCacheEntry = !client.getQueryCache().get(defaultedOptions.queryHash);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new Observer(client, defaultedOptions));\n    const result = observer.getOptimisticResult(defaultedOptions);\n    const shouldSubscribe = !isRestoring && options.subscribed !== false;\n    react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>{\n        const unsubscribe = shouldSubscribe ? observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_6__.notifyManager.batchCalls(onStoreChange)) : _utils_js__WEBPACK_IMPORTED_MODULE_7__.noop;\n        observer.updateResult();\n        return unsubscribe;\n    }, [\n        observer,\n        shouldSubscribe\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(defaultedOptions, {\n            listeners: false\n        });\n    }, [\n        defaultedOptions,\n        observer\n    ]);\n    if ((0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.shouldSuspend)(defaultedOptions, result)) {\n        throw (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary);\n    }\n    if ((0,_errorBoundaryUtils_js__WEBPACK_IMPORTED_MODULE_5__.getHasError)({\n        result,\n        errorResetBoundary,\n        throwOnError: defaultedOptions.throwOnError,\n        query: client.getQueryCache().get(defaultedOptions.queryHash),\n        suspense: defaultedOptions.suspense\n    })) {\n        throw result.error;\n    }\n    ;\n    client.getDefaultOptions().queries?._experimental_afterQuery?.(defaultedOptions, result);\n    if (defaultedOptions.experimental_prefetchInRender && !_tanstack_query_core__WEBPACK_IMPORTED_MODULE_8__.isServer && (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.willFetch)(result, isRestoring)) {\n        const promise = isNewCacheEntry ? // Fetch immediately on render in order to ensure `.promise` is resolved even if the component is unmounted\n        (0,_suspense_js__WEBPACK_IMPORTED_MODULE_4__.fetchOptimistic)(defaultedOptions, observer, errorResetBoundary) : // subscribe to the \"cache promise\" so that we can finalize the currentThenable once data comes in\n        client.getQueryCache().get(defaultedOptions.queryHash)?.promise;\n        promise?.catch(_utils_js__WEBPACK_IMPORTED_MODULE_7__.noop).finally(()=>{\n            observer.updateResult();\n        });\n    }\n    return !defaultedOptions.notifyOnChangeProps ? observer.trackResult(result) : result;\n}\n //# sourceMappingURL=useBaseQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuNjkuMF9yZWFjdEAxOC4yLjAvbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vdXNlQmFzZVF1ZXJ5LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7O0FBQ3VCO0FBRWlCO0FBQ1Q7QUFDWTtBQUtwQztBQUN3QjtBQU14QjtBQUNjO0FBU2QsU0FBU2MsYUFPZEMsT0FBQSxFQU9BQyxRQUFBLEVBQ0FDLFdBQUE7SUFFQSxJQUFJQyxJQUF5QixFQUFjO1FBQ3pDLElBQUksT0FBT0gsWUFBWSxZQUFZSSxNQUFNQyxPQUFBLENBQVFMLFVBQVU7WUFDekQsTUFBTSxJQUFJTSxNQUNSO1FBRUo7SUFDRjtJQUVBLE1BQU1DLFNBQVNuQix1RUFBY0EsQ0FBQ2M7SUFDOUIsTUFBTU0sY0FBY2YsK0RBQWNBO0lBQ2xDLE1BQU1nQixxQkFBcUJwQix1RkFBMEJBO0lBQ3JELE1BQU1xQixtQkFBbUJILE9BQU9JLG1CQUFBLENBQW9CWDtJQUVsRE8sT0FBT0ssaUJBQUEsR0FBb0JDLE9BQUEsRUFBaUJDLDRCQUM1Q0o7SUFHRixJQUFJUCxJQUF5QixFQUFjO1FBQ3pDLElBQUksQ0FBQ08saUJBQWlCSyxPQUFBLEVBQVM7WUFDN0JDLFFBQVFDLEtBQUEsQ0FDTixJQUFJUCxpQkFBaUJRLFNBQVM7UUFFbEM7SUFDRjtJQUdBUixpQkFBaUJTLGtCQUFBLEdBQXFCWCxjQUNsQyxnQkFDQTtJQUVKZCxrRUFBb0JBLENBQUNnQjtJQUNyQnBCLHVGQUErQkEsQ0FBQ29CLGtCQUFrQkQ7SUFFbERqQixrRkFBMEJBLENBQUNpQjtJQUczQixNQUFNVyxrQkFBa0IsQ0FBQ2IsT0FDdEJjLGFBQUEsR0FDQUMsR0FBQSxDQUFJWixpQkFBaUJRLFNBQVM7SUFFakMsTUFBTSxDQUFDSyxTQUFRLEdBQVV0QywyQ0FBQSxDQUN2QixJQUNFLElBQUlnQixTQUNGTSxRQUNBRztJQUtOLE1BQU1lLFNBQVNGLFNBQVNHLG1CQUFBLENBQW9CaEI7SUFFNUMsTUFBTWlCLGtCQUFrQixDQUFDbkIsZUFBZVIsUUFBUTRCLFVBQUEsS0FBZTtJQUN6RDNDLHVEQUFBLENBQ0VBLDhDQUFBLENBQ0osQ0FBQzhDO1FBQ0MsTUFBTUMsY0FBY0wsa0JBQ2hCSixTQUFTVSxTQUFBLENBQVU5QywrREFBYUEsQ0FBQytDLFVBQUEsQ0FBV0gsa0JBQzVDakMsMkNBQUlBO1FBSVJ5QixTQUFTWSxZQUFBO1FBRVQsT0FBT0g7SUFDVCxHQUNBO1FBQUNUO1FBQVVJO0tBQWUsR0FFNUIsSUFBTUosU0FBU2EsZ0JBQUEsSUFDZixJQUFNYixTQUFTYSxnQkFBQTtJQUdYbkQsNENBQUEsQ0FBVTtRQUdkc0MsU0FBU2UsVUFBQSxDQUFXNUIsa0JBQWtCO1lBQUU2QixXQUFXO1FBQU07SUFDM0QsR0FBRztRQUFDN0I7UUFBa0JhO0tBQVM7SUFHL0IsSUFBSTNCLDJEQUFhQSxDQUFDYyxrQkFBa0JlLFNBQVM7UUFDM0MsTUFBTTlCLDZEQUFlQSxDQUFDZSxrQkFBa0JhLFVBQVVkO0lBQ3BEO0lBR0EsSUFDRWxCLG1FQUFXQSxDQUFDO1FBQ1ZrQztRQUNBaEI7UUFDQStCLGNBQWM5QixpQkFBaUI4QixZQUFBO1FBQy9CQyxPQUFPbEMsT0FDSmMsYUFBQSxHQUNBQyxHQUFBLENBS0NaLGlCQUFpQlEsU0FBUztRQUM5QndCLFVBQVVoQyxpQkFBaUJnQyxRQUFBO0lBQzdCLElBQ0E7UUFDQSxNQUFNakIsT0FBT1IsS0FBQTtJQUNmOztJQUVFVixPQUFPSyxpQkFBQSxHQUFvQkMsT0FBQSxFQUFpQjhCLDJCQUM1Q2pDLGtCQUNBZTtJQUdGLElBQ0VmLGlCQUFpQmtDLDZCQUFBLElBQ2pCLENBQUMxRCwwREFBUUEsSUFDVFcsdURBQVNBLENBQUM0QixRQUFRakIsY0FDbEI7UUFDQSxNQUFNcUMsVUFBVXpCLGtCQUFBO1FBRVp6Qiw2REFBZUEsQ0FBQ2Usa0JBQWtCYSxVQUFVZCxzQkFBa0I7UUFFOURGLE9BQU9jLGFBQUEsR0FBZ0JDLEdBQUEsQ0FBSVosaUJBQWlCUSxTQUFTLEdBQUcyQjtRQUU1REEsU0FBU0MsTUFBTWhELDJDQUFJQSxFQUFFaUQsUUFBUTtZQUUzQnhCLFNBQVNZLFlBQUE7UUFDWDtJQUNGO0lBR0EsT0FBTyxDQUFDekIsaUJBQWlCc0MsbUJBQUEsR0FDckJ6QixTQUFTMEIsV0FBQSxDQUFZeEIsVUFDckJBO0FBQ04iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi4vLi4vc3JjL3VzZUJhc2VRdWVyeS50cz8yOGI1Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5cbmltcG9ydCB7IGlzU2VydmVyLCBub3RpZnlNYW5hZ2VyIH0gZnJvbSAnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnXG5pbXBvcnQgeyB1c2VRdWVyeUNsaWVudCB9IGZyb20gJy4vUXVlcnlDbGllbnRQcm92aWRlcidcbmltcG9ydCB7IHVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5IH0gZnJvbSAnLi9RdWVyeUVycm9yUmVzZXRCb3VuZGFyeSdcbmltcG9ydCB7XG4gIGVuc3VyZVByZXZlbnRFcnJvckJvdW5kYXJ5UmV0cnksXG4gIGdldEhhc0Vycm9yLFxuICB1c2VDbGVhclJlc2V0RXJyb3JCb3VuZGFyeSxcbn0gZnJvbSAnLi9lcnJvckJvdW5kYXJ5VXRpbHMnXG5pbXBvcnQgeyB1c2VJc1Jlc3RvcmluZyB9IGZyb20gJy4vaXNSZXN0b3JpbmcnXG5pbXBvcnQge1xuICBlbnN1cmVTdXNwZW5zZVRpbWVycyxcbiAgZmV0Y2hPcHRpbWlzdGljLFxuICBzaG91bGRTdXNwZW5kLFxuICB3aWxsRmV0Y2gsXG59IGZyb20gJy4vc3VzcGVuc2UnXG5pbXBvcnQgeyBub29wIH0gZnJvbSAnLi91dGlscydcbmltcG9ydCB0eXBlIHtcbiAgUXVlcnlDbGllbnQsXG4gIFF1ZXJ5S2V5LFxuICBRdWVyeU9ic2VydmVyLFxuICBRdWVyeU9ic2VydmVyUmVzdWx0LFxufSBmcm9tICdAdGFuc3RhY2svcXVlcnktY29yZSdcbmltcG9ydCB0eXBlIHsgVXNlQmFzZVF1ZXJ5T3B0aW9ucyB9IGZyb20gJy4vdHlwZXMnXG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VCYXNlUXVlcnk8XG4gIFRRdWVyeUZuRGF0YSxcbiAgVEVycm9yLFxuICBURGF0YSxcbiAgVFF1ZXJ5RGF0YSxcbiAgVFF1ZXJ5S2V5IGV4dGVuZHMgUXVlcnlLZXksXG4+KFxuICBvcHRpb25zOiBVc2VCYXNlUXVlcnlPcHRpb25zPFxuICAgIFRRdWVyeUZuRGF0YSxcbiAgICBURXJyb3IsXG4gICAgVERhdGEsXG4gICAgVFF1ZXJ5RGF0YSxcbiAgICBUUXVlcnlLZXlcbiAgPixcbiAgT2JzZXJ2ZXI6IHR5cGVvZiBRdWVyeU9ic2VydmVyLFxuICBxdWVyeUNsaWVudD86IFF1ZXJ5Q2xpZW50LFxuKTogUXVlcnlPYnNlcnZlclJlc3VsdDxURGF0YSwgVEVycm9yPiB7XG4gIGlmIChwcm9jZXNzLmVudi5OT0RFX0VOViAhPT0gJ3Byb2R1Y3Rpb24nKSB7XG4gICAgaWYgKHR5cGVvZiBvcHRpb25zICE9PSAnb2JqZWN0JyB8fCBBcnJheS5pc0FycmF5KG9wdGlvbnMpKSB7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICdCYWQgYXJndW1lbnQgdHlwZS4gU3RhcnRpbmcgd2l0aCB2NSwgb25seSB0aGUgXCJPYmplY3RcIiBmb3JtIGlzIGFsbG93ZWQgd2hlbiBjYWxsaW5nIHF1ZXJ5IHJlbGF0ZWQgZnVuY3Rpb25zLiBQbGVhc2UgdXNlIHRoZSBlcnJvciBzdGFjayB0byBmaW5kIHRoZSBjdWxwcml0IGNhbGwuIE1vcmUgaW5mbyBoZXJlOiBodHRwczovL3RhbnN0YWNrLmNvbS9xdWVyeS9sYXRlc3QvZG9jcy9yZWFjdC9ndWlkZXMvbWlncmF0aW5nLXRvLXY1I3N1cHBvcnRzLWEtc2luZ2xlLXNpZ25hdHVyZS1vbmUtb2JqZWN0JyxcbiAgICAgIClcbiAgICB9XG4gIH1cblxuICBjb25zdCBjbGllbnQgPSB1c2VRdWVyeUNsaWVudChxdWVyeUNsaWVudClcbiAgY29uc3QgaXNSZXN0b3JpbmcgPSB1c2VJc1Jlc3RvcmluZygpXG4gIGNvbnN0IGVycm9yUmVzZXRCb3VuZGFyeSA9IHVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5KClcbiAgY29uc3QgZGVmYXVsdGVkT3B0aW9ucyA9IGNsaWVudC5kZWZhdWx0UXVlcnlPcHRpb25zKG9wdGlvbnMpXG5cbiAgOyhjbGllbnQuZ2V0RGVmYXVsdE9wdGlvbnMoKS5xdWVyaWVzIGFzIGFueSk/Ll9leHBlcmltZW50YWxfYmVmb3JlUXVlcnk/LihcbiAgICBkZWZhdWx0ZWRPcHRpb25zLFxuICApXG5cbiAgaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WICE9PSAncHJvZHVjdGlvbicpIHtcbiAgICBpZiAoIWRlZmF1bHRlZE9wdGlvbnMucXVlcnlGbikge1xuICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgYFske2RlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNofV06IE5vIHF1ZXJ5Rm4gd2FzIHBhc3NlZCBhcyBhbiBvcHRpb24sIGFuZCBubyBkZWZhdWx0IHF1ZXJ5Rm4gd2FzIGZvdW5kLiBUaGUgcXVlcnlGbiBwYXJhbWV0ZXIgaXMgb25seSBvcHRpb25hbCB3aGVuIHVzaW5nIGEgZGVmYXVsdCBxdWVyeUZuLiBNb3JlIGluZm8gaGVyZTogaHR0cHM6Ly90YW5zdGFjay5jb20vcXVlcnkvbGF0ZXN0L2RvY3MvZnJhbWV3b3JrL3JlYWN0L2d1aWRlcy9kZWZhdWx0LXF1ZXJ5LWZ1bmN0aW9uYCxcbiAgICAgIClcbiAgICB9XG4gIH1cblxuICAvLyBNYWtlIHN1cmUgcmVzdWx0cyBhcmUgb3B0aW1pc3RpY2FsbHkgc2V0IGluIGZldGNoaW5nIHN0YXRlIGJlZm9yZSBzdWJzY3JpYmluZyBvciB1cGRhdGluZyBvcHRpb25zXG4gIGRlZmF1bHRlZE9wdGlvbnMuX29wdGltaXN0aWNSZXN1bHRzID0gaXNSZXN0b3JpbmdcbiAgICA/ICdpc1Jlc3RvcmluZydcbiAgICA6ICdvcHRpbWlzdGljJ1xuXG4gIGVuc3VyZVN1c3BlbnNlVGltZXJzKGRlZmF1bHRlZE9wdGlvbnMpXG4gIGVuc3VyZVByZXZlbnRFcnJvckJvdW5kYXJ5UmV0cnkoZGVmYXVsdGVkT3B0aW9ucywgZXJyb3JSZXNldEJvdW5kYXJ5KVxuXG4gIHVzZUNsZWFyUmVzZXRFcnJvckJvdW5kYXJ5KGVycm9yUmVzZXRCb3VuZGFyeSlcblxuICAvLyB0aGlzIG5lZWRzIHRvIGJlIGludm9rZWQgYmVmb3JlIGNyZWF0aW5nIHRoZSBPYnNlcnZlciBiZWNhdXNlIHRoYXQgY2FuIGNyZWF0ZSBhIGNhY2hlIGVudHJ5XG4gIGNvbnN0IGlzTmV3Q2FjaGVFbnRyeSA9ICFjbGllbnRcbiAgICAuZ2V0UXVlcnlDYWNoZSgpXG4gICAgLmdldChkZWZhdWx0ZWRPcHRpb25zLnF1ZXJ5SGFzaClcblxuICBjb25zdCBbb2JzZXJ2ZXJdID0gUmVhY3QudXNlU3RhdGUoXG4gICAgKCkgPT5cbiAgICAgIG5ldyBPYnNlcnZlcjxUUXVlcnlGbkRhdGEsIFRFcnJvciwgVERhdGEsIFRRdWVyeURhdGEsIFRRdWVyeUtleT4oXG4gICAgICAgIGNsaWVudCxcbiAgICAgICAgZGVmYXVsdGVkT3B0aW9ucyxcbiAgICAgICksXG4gIClcblxuICAvLyBub3RlOiB0aGlzIG11c3QgYmUgY2FsbGVkIGJlZm9yZSB1c2VTeW5jRXh0ZXJuYWxTdG9yZVxuICBjb25zdCByZXN1bHQgPSBvYnNlcnZlci5nZXRPcHRpbWlzdGljUmVzdWx0KGRlZmF1bHRlZE9wdGlvbnMpXG5cbiAgY29uc3Qgc2hvdWxkU3Vic2NyaWJlID0gIWlzUmVzdG9yaW5nICYmIG9wdGlvbnMuc3Vic2NyaWJlZCAhPT0gZmFsc2VcbiAgUmVhY3QudXNlU3luY0V4dGVybmFsU3RvcmUoXG4gICAgUmVhY3QudXNlQ2FsbGJhY2soXG4gICAgICAob25TdG9yZUNoYW5nZSkgPT4ge1xuICAgICAgICBjb25zdCB1bnN1YnNjcmliZSA9IHNob3VsZFN1YnNjcmliZVxuICAgICAgICAgID8gb2JzZXJ2ZXIuc3Vic2NyaWJlKG5vdGlmeU1hbmFnZXIuYmF0Y2hDYWxscyhvblN0b3JlQ2hhbmdlKSlcbiAgICAgICAgICA6IG5vb3BcblxuICAgICAgICAvLyBVcGRhdGUgcmVzdWx0IHRvIG1ha2Ugc3VyZSB3ZSBkaWQgbm90IG1pc3MgYW55IHF1ZXJ5IHVwZGF0ZXNcbiAgICAgICAgLy8gYmV0d2VlbiBjcmVhdGluZyB0aGUgb2JzZXJ2ZXIgYW5kIHN1YnNjcmliaW5nIHRvIGl0LlxuICAgICAgICBvYnNlcnZlci51cGRhdGVSZXN1bHQoKVxuXG4gICAgICAgIHJldHVybiB1bnN1YnNjcmliZVxuICAgICAgfSxcbiAgICAgIFtvYnNlcnZlciwgc2hvdWxkU3Vic2NyaWJlXSxcbiAgICApLFxuICAgICgpID0+IG9ic2VydmVyLmdldEN1cnJlbnRSZXN1bHQoKSxcbiAgICAoKSA9PiBvYnNlcnZlci5nZXRDdXJyZW50UmVzdWx0KCksXG4gIClcblxuICBSZWFjdC51c2VFZmZlY3QoKCkgPT4ge1xuICAgIC8vIERvIG5vdCBub3RpZnkgb24gdXBkYXRlcyBiZWNhdXNlIG9mIGNoYW5nZXMgaW4gdGhlIG9wdGlvbnMgYmVjYXVzZVxuICAgIC8vIHRoZXNlIGNoYW5nZXMgc2hvdWxkIGFscmVhZHkgYmUgcmVmbGVjdGVkIGluIHRoZSBvcHRpbWlzdGljIHJlc3VsdC5cbiAgICBvYnNlcnZlci5zZXRPcHRpb25zKGRlZmF1bHRlZE9wdGlvbnMsIHsgbGlzdGVuZXJzOiBmYWxzZSB9KVxuICB9LCBbZGVmYXVsdGVkT3B0aW9ucywgb2JzZXJ2ZXJdKVxuXG4gIC8vIEhhbmRsZSBzdXNwZW5zZVxuICBpZiAoc2hvdWxkU3VzcGVuZChkZWZhdWx0ZWRPcHRpb25zLCByZXN1bHQpKSB7XG4gICAgdGhyb3cgZmV0Y2hPcHRpbWlzdGljKGRlZmF1bHRlZE9wdGlvbnMsIG9ic2VydmVyLCBlcnJvclJlc2V0Qm91bmRhcnkpXG4gIH1cblxuICAvLyBIYW5kbGUgZXJyb3IgYm91bmRhcnlcbiAgaWYgKFxuICAgIGdldEhhc0Vycm9yKHtcbiAgICAgIHJlc3VsdCxcbiAgICAgIGVycm9yUmVzZXRCb3VuZGFyeSxcbiAgICAgIHRocm93T25FcnJvcjogZGVmYXVsdGVkT3B0aW9ucy50aHJvd09uRXJyb3IsXG4gICAgICBxdWVyeTogY2xpZW50XG4gICAgICAgIC5nZXRRdWVyeUNhY2hlKClcbiAgICAgICAgLmdldDxcbiAgICAgICAgICBUUXVlcnlGbkRhdGEsXG4gICAgICAgICAgVEVycm9yLFxuICAgICAgICAgIFRRdWVyeURhdGEsXG4gICAgICAgICAgVFF1ZXJ5S2V5XG4gICAgICAgID4oZGVmYXVsdGVkT3B0aW9ucy5xdWVyeUhhc2gpLFxuICAgICAgc3VzcGVuc2U6IGRlZmF1bHRlZE9wdGlvbnMuc3VzcGVuc2UsXG4gICAgfSlcbiAgKSB7XG4gICAgdGhyb3cgcmVzdWx0LmVycm9yXG4gIH1cblxuICA7KGNsaWVudC5nZXREZWZhdWx0T3B0aW9ucygpLnF1ZXJpZXMgYXMgYW55KT8uX2V4cGVyaW1lbnRhbF9hZnRlclF1ZXJ5Py4oXG4gICAgZGVmYXVsdGVkT3B0aW9ucyxcbiAgICByZXN1bHQsXG4gIClcblxuICBpZiAoXG4gICAgZGVmYXVsdGVkT3B0aW9ucy5leHBlcmltZW50YWxfcHJlZmV0Y2hJblJlbmRlciAmJlxuICAgICFpc1NlcnZlciAmJlxuICAgIHdpbGxGZXRjaChyZXN1bHQsIGlzUmVzdG9yaW5nKVxuICApIHtcbiAgICBjb25zdCBwcm9taXNlID0gaXNOZXdDYWNoZUVudHJ5XG4gICAgICA/IC8vIEZldGNoIGltbWVkaWF0ZWx5IG9uIHJlbmRlciBpbiBvcmRlciB0byBlbnN1cmUgYC5wcm9taXNlYCBpcyByZXNvbHZlZCBldmVuIGlmIHRoZSBjb21wb25lbnQgaXMgdW5tb3VudGVkXG4gICAgICAgIGZldGNoT3B0aW1pc3RpYyhkZWZhdWx0ZWRPcHRpb25zLCBvYnNlcnZlciwgZXJyb3JSZXNldEJvdW5kYXJ5KVxuICAgICAgOiAvLyBzdWJzY3JpYmUgdG8gdGhlIFwiY2FjaGUgcHJvbWlzZVwiIHNvIHRoYXQgd2UgY2FuIGZpbmFsaXplIHRoZSBjdXJyZW50VGhlbmFibGUgb25jZSBkYXRhIGNvbWVzIGluXG4gICAgICAgIGNsaWVudC5nZXRRdWVyeUNhY2hlKCkuZ2V0KGRlZmF1bHRlZE9wdGlvbnMucXVlcnlIYXNoKT8ucHJvbWlzZVxuXG4gICAgcHJvbWlzZT8uY2F0Y2gobm9vcCkuZmluYWxseSgoKSA9PiB7XG4gICAgICAvLyBgLnVwZGF0ZVJlc3VsdCgpYCB3aWxsIHRyaWdnZXIgYC4jY3VycmVudFRoZW5hYmxlYCB0byBmaW5hbGl6ZVxuICAgICAgb2JzZXJ2ZXIudXBkYXRlUmVzdWx0KClcbiAgICB9KVxuICB9XG5cbiAgLy8gSGFuZGxlIHJlc3VsdCBwcm9wZXJ0eSB1c2FnZSB0cmFja2luZ1xuICByZXR1cm4gIWRlZmF1bHRlZE9wdGlvbnMubm90aWZ5T25DaGFuZ2VQcm9wc1xuICAgID8gb2JzZXJ2ZXIudHJhY2tSZXN1bHQocmVzdWx0KVxuICAgIDogcmVzdWx0XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJpc1NlcnZlciIsIm5vdGlmeU1hbmFnZXIiLCJ1c2VRdWVyeUNsaWVudCIsInVzZVF1ZXJ5RXJyb3JSZXNldEJvdW5kYXJ5IiwiZW5zdXJlUHJldmVudEVycm9yQm91bmRhcnlSZXRyeSIsImdldEhhc0Vycm9yIiwidXNlQ2xlYXJSZXNldEVycm9yQm91bmRhcnkiLCJ1c2VJc1Jlc3RvcmluZyIsImVuc3VyZVN1c3BlbnNlVGltZXJzIiwiZmV0Y2hPcHRpbWlzdGljIiwic2hvdWxkU3VzcGVuZCIsIndpbGxGZXRjaCIsIm5vb3AiLCJ1c2VCYXNlUXVlcnkiLCJvcHRpb25zIiwiT2JzZXJ2ZXIiLCJxdWVyeUNsaWVudCIsInByb2Nlc3MiLCJBcnJheSIsImlzQXJyYXkiLCJFcnJvciIsImNsaWVudCIsImlzUmVzdG9yaW5nIiwiZXJyb3JSZXNldEJvdW5kYXJ5IiwiZGVmYXVsdGVkT3B0aW9ucyIsImRlZmF1bHRRdWVyeU9wdGlvbnMiLCJnZXREZWZhdWx0T3B0aW9ucyIsInF1ZXJpZXMiLCJfZXhwZXJpbWVudGFsX2JlZm9yZVF1ZXJ5IiwicXVlcnlGbiIsImNvbnNvbGUiLCJlcnJvciIsInF1ZXJ5SGFzaCIsIl9vcHRpbWlzdGljUmVzdWx0cyIsImlzTmV3Q2FjaGVFbnRyeSIsImdldFF1ZXJ5Q2FjaGUiLCJnZXQiLCJvYnNlcnZlciIsInVzZVN0YXRlIiwicmVzdWx0IiwiZ2V0T3B0aW1pc3RpY1Jlc3VsdCIsInNob3VsZFN1YnNjcmliZSIsInN1YnNjcmliZWQiLCJ1c2VTeW5jRXh0ZXJuYWxTdG9yZSIsInVzZUNhbGxiYWNrIiwib25TdG9yZUNoYW5nZSIsInVuc3Vic2NyaWJlIiwic3Vic2NyaWJlIiwiYmF0Y2hDYWxscyIsInVwZGF0ZVJlc3VsdCIsImdldEN1cnJlbnRSZXN1bHQiLCJ1c2VFZmZlY3QiLCJzZXRPcHRpb25zIiwibGlzdGVuZXJzIiwidGhyb3dPbkVycm9yIiwicXVlcnkiLCJzdXNwZW5zZSIsIl9leHBlcmltZW50YWxfYWZ0ZXJRdWVyeSIsImV4cGVyaW1lbnRhbF9wcmVmZXRjaEluUmVuZGVyIiwicHJvbWlzZSIsImNhdGNoIiwiZmluYWxseSIsIm5vdGlmeU9uQ2hhbmdlUHJvcHMiLCJ0cmFja1Jlc3VsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useInfiniteQuery: () => (/* binding */ useInfiniteQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/infiniteQueryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useInfiniteQuery auto */ // src/useInfiniteQuery.ts\n\n\nfunction useInfiniteQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.InfiniteQueryObserver, queryClient);\n}\n //# sourceMappingURL=useInfiniteQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useInfiniteQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useMutation.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useMutation.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useMutation: () => (/* binding */ useMutation)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/mutationObserver.js\");\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/notifyManager.js\");\n/* harmony import */ var _QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./QueryClientProvider.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/QueryClientProvider.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js\");\n/* __next_internal_client_entry_do_not_use__ useMutation auto */ // src/useMutation.ts\n\n\n\n\nfunction useMutation(options, queryClient) {\n    const client = (0,_QueryClientProvider_js__WEBPACK_IMPORTED_MODULE_1__.useQueryClient)(queryClient);\n    const [observer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>new _tanstack_query_core__WEBPACK_IMPORTED_MODULE_2__.MutationObserver(client, options));\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        observer.setOptions(options);\n    }, [\n        observer,\n        options\n    ]);\n    const result = react__WEBPACK_IMPORTED_MODULE_0__.useSyncExternalStore(react__WEBPACK_IMPORTED_MODULE_0__.useCallback((onStoreChange)=>observer.subscribe(_tanstack_query_core__WEBPACK_IMPORTED_MODULE_3__.notifyManager.batchCalls(onStoreChange)), [\n        observer\n    ]), ()=>observer.getCurrentResult(), ()=>observer.getCurrentResult());\n    const mutate = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((variables, mutateOptions)=>{\n        observer.mutate(variables, mutateOptions).catch(_utils_js__WEBPACK_IMPORTED_MODULE_4__.noop);\n    }, [\n        observer\n    ]);\n    if (result.error && (0,_utils_js__WEBPACK_IMPORTED_MODULE_4__.shouldThrowError)(observer.options.throwOnError, [\n        result.error\n    ])) {\n        throw result.error;\n    }\n    return {\n        ...result,\n        mutate,\n        mutateAsync: result.mutate\n    };\n}\n //# sourceMappingURL=useMutation.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useMutation.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useQuery.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useQuery.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useQuery: () => (/* binding */ useQuery)\n/* harmony export */ });\n/* harmony import */ var _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @tanstack/query-core */ \"(ssr)/./node_modules/.pnpm/@tanstack+query-core@5.69.0/node_modules/@tanstack/query-core/build/modern/queryObserver.js\");\n/* harmony import */ var _useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useBaseQuery.js */ \"(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useBaseQuery.js\");\n/* __next_internal_client_entry_do_not_use__ useQuery auto */ // src/useQuery.ts\n\n\nfunction useQuery(options, queryClient) {\n    return (0,_useBaseQuery_js__WEBPACK_IMPORTED_MODULE_0__.useBaseQuery)(options, _tanstack_query_core__WEBPACK_IMPORTED_MODULE_1__.QueryObserver, queryClient);\n}\n //# sourceMappingURL=useQuery.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuNjkuMF9yZWFjdEAxOC4yLjAvbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vdXNlUXVlcnkuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUM4QjtBQUNEO0FBMEN0QixTQUFTRSxTQUFTQyxPQUFBLEVBQTBCQyxXQUFBO0lBQ2pELE9BQU9ILDhEQUFZQSxDQUFDRSxTQUFTSCwrREFBYUEsRUFBRUk7QUFDOUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi4vLi4vc3JjL3VzZVF1ZXJ5LnRzP2JkYmQiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5pbXBvcnQgeyBRdWVyeU9ic2VydmVyIH0gZnJvbSAnQHRhbnN0YWNrL3F1ZXJ5LWNvcmUnXG5pbXBvcnQgeyB1c2VCYXNlUXVlcnkgfSBmcm9tICcuL3VzZUJhc2VRdWVyeSdcbmltcG9ydCB0eXBlIHsgRGVmYXVsdEVycm9yLCBRdWVyeUNsaWVudCwgUXVlcnlLZXkgfSBmcm9tICdAdGFuc3RhY2svcXVlcnktY29yZSdcbmltcG9ydCB0eXBlIHtcbiAgRGVmaW5lZFVzZVF1ZXJ5UmVzdWx0LFxuICBVc2VRdWVyeU9wdGlvbnMsXG4gIFVzZVF1ZXJ5UmVzdWx0LFxufSBmcm9tICcuL3R5cGVzJ1xuaW1wb3J0IHR5cGUge1xuICBEZWZpbmVkSW5pdGlhbERhdGFPcHRpb25zLFxuICBVbmRlZmluZWRJbml0aWFsRGF0YU9wdGlvbnMsXG59IGZyb20gJy4vcXVlcnlPcHRpb25zJ1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlUXVlcnk8XG4gIFRRdWVyeUZuRGF0YSA9IHVua25vd24sXG4gIFRFcnJvciA9IERlZmF1bHRFcnJvcixcbiAgVERhdGEgPSBUUXVlcnlGbkRhdGEsXG4gIFRRdWVyeUtleSBleHRlbmRzIFF1ZXJ5S2V5ID0gUXVlcnlLZXksXG4+KFxuICBvcHRpb25zOiBEZWZpbmVkSW5pdGlhbERhdGFPcHRpb25zPFRRdWVyeUZuRGF0YSwgVEVycm9yLCBURGF0YSwgVFF1ZXJ5S2V5PixcbiAgcXVlcnlDbGllbnQ/OiBRdWVyeUNsaWVudCxcbik6IERlZmluZWRVc2VRdWVyeVJlc3VsdDxURGF0YSwgVEVycm9yPlxuXG5leHBvcnQgZnVuY3Rpb24gdXNlUXVlcnk8XG4gIFRRdWVyeUZuRGF0YSA9IHVua25vd24sXG4gIFRFcnJvciA9IERlZmF1bHRFcnJvcixcbiAgVERhdGEgPSBUUXVlcnlGbkRhdGEsXG4gIFRRdWVyeUtleSBleHRlbmRzIFF1ZXJ5S2V5ID0gUXVlcnlLZXksXG4+KFxuICBvcHRpb25zOiBVbmRlZmluZWRJbml0aWFsRGF0YU9wdGlvbnM8VFF1ZXJ5Rm5EYXRhLCBURXJyb3IsIFREYXRhLCBUUXVlcnlLZXk+LFxuICBxdWVyeUNsaWVudD86IFF1ZXJ5Q2xpZW50LFxuKTogVXNlUXVlcnlSZXN1bHQ8VERhdGEsIFRFcnJvcj5cblxuZXhwb3J0IGZ1bmN0aW9uIHVzZVF1ZXJ5PFxuICBUUXVlcnlGbkRhdGEgPSB1bmtub3duLFxuICBURXJyb3IgPSBEZWZhdWx0RXJyb3IsXG4gIFREYXRhID0gVFF1ZXJ5Rm5EYXRhLFxuICBUUXVlcnlLZXkgZXh0ZW5kcyBRdWVyeUtleSA9IFF1ZXJ5S2V5LFxuPihcbiAgb3B0aW9uczogVXNlUXVlcnlPcHRpb25zPFRRdWVyeUZuRGF0YSwgVEVycm9yLCBURGF0YSwgVFF1ZXJ5S2V5PixcbiAgcXVlcnlDbGllbnQ/OiBRdWVyeUNsaWVudCxcbik6IFVzZVF1ZXJ5UmVzdWx0PFREYXRhLCBURXJyb3I+XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VRdWVyeShvcHRpb25zOiBVc2VRdWVyeU9wdGlvbnMsIHF1ZXJ5Q2xpZW50PzogUXVlcnlDbGllbnQpIHtcbiAgcmV0dXJuIHVzZUJhc2VRdWVyeShvcHRpb25zLCBRdWVyeU9ic2VydmVyLCBxdWVyeUNsaWVudClcbn1cbiJdLCJuYW1lcyI6WyJRdWVyeU9ic2VydmVyIiwidXNlQmFzZVF1ZXJ5IiwidXNlUXVlcnkiLCJvcHRpb25zIiwicXVlcnlDbGllbnQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/useQuery.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js":
/*!*******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js ***!
  \*******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   noop: () => (/* binding */ noop),\n/* harmony export */   shouldThrowError: () => (/* binding */ shouldThrowError)\n/* harmony export */ });\n// src/utils.ts\nfunction shouldThrowError(throwError, params) {\n  if (typeof throwError === \"function\") {\n    return throwError(...params);\n  }\n  return !!throwError;\n}\nfunction noop() {\n}\n\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHRhbnN0YWNrK3JlYWN0LXF1ZXJ5QDUuNjkuMF9yZWFjdEAxOC4yLjAvbm9kZV9tb2R1bGVzL0B0YW5zdGFjay9yZWFjdC1xdWVyeS9idWlsZC9tb2Rlcm4vdXRpbHMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0B0YW5zdGFjaytyZWFjdC1xdWVyeUA1LjY5LjBfcmVhY3RAMTguMi4wL25vZGVfbW9kdWxlcy9AdGFuc3RhY2svcmVhY3QtcXVlcnkvYnVpbGQvbW9kZXJuL3V0aWxzLmpzPzQxZmUiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3V0aWxzLnRzXG5mdW5jdGlvbiBzaG91bGRUaHJvd0Vycm9yKHRocm93RXJyb3IsIHBhcmFtcykge1xuICBpZiAodHlwZW9mIHRocm93RXJyb3IgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiB0aHJvd0Vycm9yKC4uLnBhcmFtcyk7XG4gIH1cbiAgcmV0dXJuICEhdGhyb3dFcnJvcjtcbn1cbmZ1bmN0aW9uIG5vb3AoKSB7XG59XG5leHBvcnQge1xuICBub29wLFxuICBzaG91bGRUaHJvd0Vycm9yXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9dXRpbHMuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@tanstack+react-query@5.69.0_react@18.2.0/node_modules/@tanstack/react-query/build/modern/utils.js\n");

/***/ })

};
;