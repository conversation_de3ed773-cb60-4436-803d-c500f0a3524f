"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924";
exports.ids = ["vendor-chunks/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924/node_modules/use-context-selector/dist/index.modern.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924/node_modules/use-context-selector/dist/index.modern.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BridgeProvider: () => (/* binding */ BridgeProvider),\n/* harmony export */   createContext: () => (/* binding */ createContext),\n/* harmony export */   useBridgeValue: () => (/* binding */ useBridgeValue),\n/* harmony export */   useContext: () => (/* binding */ useContext),\n/* harmony export */   useContextSelector: () => (/* binding */ useContextSelector),\n/* harmony export */   useContextUpdate: () => (/* binding */ useContextUpdate)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var scheduler__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! scheduler */ \"(ssr)/./node_modules/.pnpm/scheduler@0.23.2/node_modules/scheduler/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n\n\n\n\nconst CONTEXT_VALUE = Symbol();\nconst ORIGINAL_PROVIDER = Symbol();\nconst isSSR = typeof window === 'undefined' || /ServerSideRendering/.test(window.navigator && window.navigator.userAgent);\nconst useIsomorphicLayoutEffect = isSSR ? react__WEBPACK_IMPORTED_MODULE_0__.useEffect : react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect;\n// for preact that doesn't have runWithPriority\nconst runWithNormalPriority = scheduler__WEBPACK_IMPORTED_MODULE_1__.unstable_runWithPriority ? thunk => {\n  try {\n    (0,scheduler__WEBPACK_IMPORTED_MODULE_1__.unstable_runWithPriority)(scheduler__WEBPACK_IMPORTED_MODULE_1__.unstable_NormalPriority, thunk);\n  } catch (e) {\n    if (e.message === 'Not implemented.') {\n      thunk();\n    } else {\n      throw e;\n    }\n  }\n} : thunk => thunk();\nconst createProvider = ProviderOrig => {\n  const ContextProvider = ({\n    value,\n    children\n  }) => {\n    const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n    const versionRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const [resolve, setResolve] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    if (resolve) {\n      resolve(value);\n      setResolve(null);\n    }\n    const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    if (!contextValue.current) {\n      const listeners = new Set();\n      const update = (thunk, options) => {\n        (0,react_dom__WEBPACK_IMPORTED_MODULE_2__.unstable_batchedUpdates)(() => {\n          versionRef.current += 1;\n          const action = {\n            n: versionRef.current\n          };\n          if (options != null && options.suspense) {\n            action.n *= -1; // this is intentional to make it temporary version\n            action.p = new Promise(r => {\n              setResolve(() => v => {\n                action.v = v;\n                delete action.p;\n                r(v);\n              });\n            });\n          }\n          listeners.forEach(listener => listener(action));\n          thunk();\n        });\n      };\n      contextValue.current = {\n        [CONTEXT_VALUE]: {\n          /* \"v\"alue     */v: valueRef,\n          /* versio\"n\"   */n: versionRef,\n          /* \"l\"isteners */l: listeners,\n          /* \"u\"pdate    */u: update\n        }\n      };\n    }\n    useIsomorphicLayoutEffect(() => {\n      valueRef.current = value;\n      versionRef.current += 1;\n      runWithNormalPriority(() => {\n        contextValue.current[CONTEXT_VALUE].l.forEach(listener => {\n          listener({\n            n: versionRef.current,\n            v: value\n          });\n        });\n      });\n    }, [value]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ProviderOrig, {\n      value: contextValue.current\n    }, children);\n  };\n  return ContextProvider;\n};\nconst identity = x => x;\n/**\n * This creates a special context for `useContextSelector`.\n *\n * @example\n * import { createContext } from 'use-context-selector';\n *\n * const PersonContext = createContext({ firstName: '', familyName: '' });\n */\nfunction createContext(defaultValue) {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({\n    [CONTEXT_VALUE]: {\n      /* \"v\"alue     */v: {\n        current: defaultValue\n      },\n      /* versio\"n\"   */n: {\n        current: -1\n      },\n      /* \"l\"isteners */l: new Set(),\n      /* \"u\"pdate    */u: f => f()\n    }\n  });\n  context[ORIGINAL_PROVIDER] = context.Provider;\n  context.Provider = createProvider(context.Provider);\n  delete context.Consumer; // no support for Consumer\n  return context;\n}\n/**\n * This hook returns context selected value by selector.\n *\n * It will only accept context created by `createContext`.\n * It will trigger re-render if only the selected value is referentially changed.\n *\n * The selector should return referentially equal result for same input for better performance.\n *\n * @example\n * import { useContextSelector } from 'use-context-selector';\n *\n * const firstName = useContextSelector(PersonContext, state => state.firstName);\n */\nfunction useContextSelector(context, selector) {\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(context)[CONTEXT_VALUE];\n  if (typeof process === 'object' && \"development\" !== 'production') {\n    if (!contextValue) {\n      throw new Error('useContextSelector requires special context');\n    }\n  }\n  const {\n    /* \"v\"alue     */v: {\n      current: value\n    },\n    /* versio\"n\"   */n: {\n      current: version\n    },\n    /* \"l\"isteners */l: listeners\n  } = contextValue;\n  const selected = selector(value);\n  const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)((prev, action) => {\n    if (!action) {\n      // case for `dispatch()` below\n      return [value, selected];\n    }\n    if ('p' in action) {\n      throw action.p;\n    }\n    if (action.n === version) {\n      if (Object.is(prev[1], selected)) {\n        return prev; // bail out\n      }\n      return [value, selected];\n    }\n    try {\n      if ('v' in action) {\n        if (Object.is(prev[0], action.v)) {\n          return prev; // do not update\n        }\n        const nextSelected = selector(action.v);\n        if (Object.is(prev[1], nextSelected)) {\n          return prev; // do not update\n        }\n        return [action.v, nextSelected];\n      }\n    } catch (e) {\n      // ignored (stale props or some other reason)\n    }\n    return [...prev]; // schedule update\n  }, [value, selected]);\n  if (!Object.is(state[1], selected)) {\n    // schedule re-render\n    // this is safe because it's self contained\n    dispatch();\n  }\n  useIsomorphicLayoutEffect(() => {\n    listeners.add(dispatch);\n    return () => {\n      listeners.delete(dispatch);\n    };\n  }, [listeners]);\n  return state[1];\n}\n/**\n * This hook returns the entire context value.\n * Use this instead of React.useContext for consistent behavior.\n *\n * @example\n * import { useContext } from 'use-context-selector';\n *\n * const person = useContext(PersonContext);\n */\nfunction useContext(context) {\n  return useContextSelector(context, identity);\n}\n/**\n * This hook returns an update function that accepts a thunk function\n *\n * Use this for a function that will change a value in\n * concurrent rendering in React 18.\n * Otherwise, there's no need to use this hook.\n *\n * @example\n * import { useContextUpdate } from 'use-context-selector';\n *\n * const update = useContextUpdate();\n *\n * // Wrap set state function\n * update(() => setState(...));\n *\n * // Experimental suspense mode\n * update(() => setState(...), { suspense: true });\n */\nfunction useContextUpdate(context) {\n  const contextValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(context)[CONTEXT_VALUE];\n  if (typeof process === 'object' && \"development\" !== 'production') {\n    if (!contextValue) {\n      throw new Error('useContextUpdate requires special context');\n    }\n  }\n  const {\n    u: update\n  } = contextValue;\n  return update;\n}\n/**\n * This is a Provider component for bridging multiple react roots\n *\n * @example\n * const valueToBridge = useBridgeValue(PersonContext);\n * return (\n *   <Renderer>\n *     <BridgeProvider context={PersonContext} value={valueToBridge}>\n *       {children}\n *     </BridgeProvider>\n *   </Renderer>\n * );\n */\nconst BridgeProvider = ({\n  context,\n  value,\n  children\n}) => {\n  const {\n    [ORIGINAL_PROVIDER]: ProviderOrig\n  } = context;\n  if (typeof process === 'object' && \"development\" !== 'production') {\n    if (!ProviderOrig) {\n      throw new Error('BridgeProvider requires special context');\n    }\n  }\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ProviderOrig, {\n    value\n  }, children);\n};\n/**\n * This hook return a value for BridgeProvider\n */\nconst useBridgeValue = context => {\n  const bridgeValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(context);\n  if (typeof process === 'object' && \"development\" !== 'production') {\n    if (!bridgeValue[CONTEXT_VALUE]) {\n      throw new Error('useBridgeValue requires special context');\n    }\n  }\n  return bridgeValue;\n};\n\n\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924/node_modules/use-context-selector/dist/index.modern.mjs\n");

/***/ })

};
;