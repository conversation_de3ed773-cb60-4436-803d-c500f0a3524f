"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@a2a-js+sdk@0.2.2";
exports.ids = ["vendor-chunks/@a2a-js+sdk@0.2.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/client/client.js":
/*!**************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/client/client.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A2AClient: () => (/* binding */ A2AClient)\n/* harmony export */ });\n/**\n * A2AClient is a TypeScript HTTP client for interacting with A2A-compliant agents.\n */\nclass A2AClient {\n    agentBaseUrl;\n    agentCardPromise;\n    requestIdCounter = 1;\n    serviceEndpointUrl; // To be populated from AgentCard after fetching\n    /**\n     * Constructs an A2AClient instance.\n     * It initiates fetching the agent card from the provided agent baseUrl.\n     * The Agent Card is expected at `${agentBaseUrl}/.well-known/agent.json`.\n     * The `url` field from the Agent Card will be used as the RPC service endpoint.\n     * @param agentBaseUrl The base URL of the A2A agent (e.g., https://agent.example.com).\n     */\n    constructor(agentBaseUrl) {\n        this.agentBaseUrl = agentBaseUrl.replace(/\\/$/, \"\"); // Remove trailing slash if any\n        this.agentCardPromise = this._fetchAndCacheAgentCard();\n    }\n    /**\n     * Fetches the Agent Card from the agent's well-known URI and caches its service endpoint URL.\n     * This method is called by the constructor.\n     * @returns A Promise that resolves to the AgentCard.\n     */\n    async _fetchAndCacheAgentCard() {\n        const agentCardUrl = `${this.agentBaseUrl}/.well-known/agent.json`;\n        try {\n            const response = await fetch(agentCardUrl, {\n                headers: { 'Accept': 'application/json' },\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to fetch Agent Card from ${agentCardUrl}: ${response.status} ${response.statusText}`);\n            }\n            const agentCard = await response.json();\n            if (!agentCard.url) {\n                throw new Error(\"Fetched Agent Card does not contain a valid 'url' for the service endpoint.\");\n            }\n            this.serviceEndpointUrl = agentCard.url; // Cache the service endpoint URL from the agent card\n            console.log(\"ENDOPINT\", this.serviceEndpointUrl);\n            return agentCard;\n        }\n        catch (error) {\n            console.error(\"Error fetching or parsing Agent Card:\");\n            // Allow the promise to reject so users of agentCardPromise can handle it.\n            throw error;\n        }\n    }\n    /**\n     * Retrieves the Agent Card.\n     * If an `agentBaseUrl` is provided, it fetches the card from that specific URL.\n     * Otherwise, it returns the card fetched and cached during client construction.\n     * @param agentBaseUrl Optional. The base URL of the agent to fetch the card from.\n     * If provided, this will fetch a new card, not use the cached one from the constructor's URL.\n     * @returns A Promise that resolves to the AgentCard.\n     */\n    async getAgentCard(agentBaseUrl) {\n        if (agentBaseUrl) {\n            const specificAgentBaseUrl = agentBaseUrl.replace(/\\/$/, \"\");\n            const agentCardUrl = `${specificAgentBaseUrl}/.well-known/agent.json`;\n            const response = await fetch(agentCardUrl, {\n                headers: { 'Accept': 'application/json' },\n            });\n            if (!response.ok) {\n                throw new Error(`Failed to fetch Agent Card from ${agentCardUrl}: ${response.status} ${response.statusText}`);\n            }\n            return await response.json();\n        }\n        // If no specific URL is given, return the promise for the initially configured agent's card.\n        return this.agentCardPromise;\n    }\n    /**\n     * Gets the RPC service endpoint URL. Ensures the agent card has been fetched first.\n     * @returns A Promise that resolves to the service endpoint URL string.\n     */\n    async _getServiceEndpoint() {\n        if (this.serviceEndpointUrl) {\n            return this.serviceEndpointUrl;\n        }\n        // If serviceEndpointUrl is not set, it means the agent card fetch is pending or failed.\n        // Awaiting agentCardPromise will either resolve it or throw if fetching failed.\n        await this.agentCardPromise;\n        if (!this.serviceEndpointUrl) {\n            // This case should ideally be covered by the error handling in _fetchAndCacheAgentCard\n            throw new Error(\"Agent Card URL for RPC endpoint is not available. Fetching might have failed.\");\n        }\n        return this.serviceEndpointUrl;\n    }\n    /**\n     * Helper method to make a generic JSON-RPC POST request.\n     * @param method The RPC method name.\n     * @param params The parameters for the RPC method.\n     * @returns A Promise that resolves to the RPC response.\n     */\n    async _postRpcRequest(method, params) {\n        const endpoint = await this._getServiceEndpoint();\n        const requestId = this.requestIdCounter++;\n        const rpcRequest = {\n            jsonrpc: \"2.0\",\n            method,\n            params: params, // Cast because TParams structure varies per method\n            id: requestId,\n        };\n        const httpResponse = await fetch(endpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"application/json\", // Expect JSON response for non-streaming requests\n            },\n            body: JSON.stringify(rpcRequest),\n        });\n        if (!httpResponse.ok) {\n            let errorBodyText = '(empty or non-JSON response)';\n            try {\n                errorBodyText = await httpResponse.text();\n                const errorJson = JSON.parse(errorBodyText);\n                // If the body is a valid JSON-RPC error response, let it be handled by the standard parsing below.\n                // However, if it's not even a JSON-RPC structure but still an error, throw based on HTTP status.\n                if (!errorJson.jsonrpc && errorJson.error) { // Check if it's a JSON-RPC error structure\n                    throw new Error(`RPC error for ${method}: ${errorJson.error.message} (Code: ${errorJson.error.code}, HTTP Status: ${httpResponse.status}) Data: ${JSON.stringify(errorJson.error.data)}`);\n                }\n                else if (!errorJson.jsonrpc) {\n                    throw new Error(`HTTP error for ${method}! Status: ${httpResponse.status} ${httpResponse.statusText}. Response: ${errorBodyText}`);\n                }\n            }\n            catch (e) {\n                // If parsing the error body fails or it's not a JSON-RPC error, throw a generic HTTP error.\n                // If it was already an error thrown from within the try block, rethrow it.\n                if (e.message.startsWith('RPC error for') || e.message.startsWith('HTTP error for'))\n                    throw e;\n                throw new Error(`HTTP error for ${method}! Status: ${httpResponse.status} ${httpResponse.statusText}. Response: ${errorBodyText}`);\n            }\n        }\n        const rpcResponse = await httpResponse.json();\n        if (rpcResponse.id !== requestId) {\n            // This is a significant issue for request-response matching.\n            console.error(`CRITICAL: RPC response ID mismatch for method ${method}. Expected ${requestId}, got ${rpcResponse.id}. This may lead to incorrect response handling.`);\n            // Depending on strictness, one might throw an error here.\n            // throw new Error(`RPC response ID mismatch for method ${method}. Expected ${requestId}, got ${rpcResponse.id}`);\n        }\n        return rpcResponse;\n    }\n    /**\n     * Sends a message to the agent.\n     * The behavior (blocking/non-blocking) and push notification configuration\n     * are specified within the `params.configuration` object.\n     * Optionally, `params.message.contextId` or `params.message.taskId` can be provided.\n     * @param params The parameters for sending the message, including the message content and configuration.\n     * @returns A Promise resolving to SendMessageResponse, which can be a Message, Task, or an error.\n     */\n    async sendMessage(params) {\n        return this._postRpcRequest(\"message/send\", params);\n    }\n    /**\n     * Sends a message to the agent and streams back responses using Server-Sent Events (SSE).\n     * Push notification configuration can be specified in `params.configuration`.\n     * Optionally, `params.message.contextId` or `params.message.taskId` can be provided.\n     * Requires the agent to support streaming (`capabilities.streaming: true` in AgentCard).\n     * @param params The parameters for sending the message.\n     * @returns An AsyncGenerator yielding A2AStreamEventData (Message, Task, TaskStatusUpdateEvent, or TaskArtifactUpdateEvent).\n     * The generator throws an error if streaming is not supported or if an HTTP/SSE error occurs.\n     */\n    async *sendMessageStream(params) {\n        const agentCard = await this.agentCardPromise; // Ensure agent card is fetched\n        if (!agentCard.capabilities?.streaming) {\n            throw new Error(\"Agent does not support streaming (AgentCard.capabilities.streaming is not true).\");\n        }\n        const endpoint = await this._getServiceEndpoint();\n        const clientRequestId = this.requestIdCounter++; // Use a unique ID for this stream request\n        const rpcRequest = {\n            jsonrpc: \"2.0\",\n            method: \"message/stream\",\n            params: params,\n            id: clientRequestId,\n        };\n        const response = await fetch(endpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"text/event-stream\", // Crucial for SSE\n            },\n            body: JSON.stringify(rpcRequest),\n        });\n        if (!response.ok) {\n            // Attempt to read error body for more details\n            let errorBody = \"\";\n            try {\n                errorBody = await response.text();\n                const errorJson = JSON.parse(errorBody);\n                if (errorJson.error) {\n                    throw new Error(`HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}. RPC Error: ${errorJson.error.message} (Code: ${errorJson.error.code})`);\n                }\n            }\n            catch (e) {\n                if (e.message.startsWith('HTTP error establishing stream'))\n                    throw e;\n                // Fallback if body is not JSON or parsing fails\n                throw new Error(`HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}. Response: ${errorBody || '(empty)'}`);\n            }\n            throw new Error(`HTTP error establishing stream for message/stream: ${response.status} ${response.statusText}`);\n        }\n        if (!response.headers.get(\"Content-Type\")?.startsWith(\"text/event-stream\")) {\n            // Server should explicitly set this content type for SSE.\n            throw new Error(\"Invalid response Content-Type for SSE stream. Expected 'text/event-stream'.\");\n        }\n        // Yield events from the parsed SSE stream.\n        // Each event's 'data' field is a JSON-RPC response.\n        yield* this._parseA2ASseStream(response, clientRequestId);\n    }\n    /**\n     * Sets or updates the push notification configuration for a given task.\n     * Requires the agent to support push notifications (`capabilities.pushNotifications: true` in AgentCard).\n     * @param params Parameters containing the taskId and the TaskPushNotificationConfig.\n     * @returns A Promise resolving to SetTaskPushNotificationConfigResponse.\n     */\n    async setTaskPushNotificationConfig(params) {\n        const agentCard = await this.agentCardPromise;\n        if (!agentCard.capabilities?.pushNotifications) {\n            throw new Error(\"Agent does not support push notifications (AgentCard.capabilities.pushNotifications is not true).\");\n        }\n        // The 'params' directly matches the structure expected by the RPC method.\n        return this._postRpcRequest(\"tasks/pushNotificationConfig/set\", params);\n    }\n    /**\n     * Gets the push notification configuration for a given task.\n     * @param params Parameters containing the taskId.\n     * @returns A Promise resolving to GetTaskPushNotificationConfigResponse.\n     */\n    async getTaskPushNotificationConfig(params) {\n        // The 'params' (TaskIdParams) directly matches the structure expected by the RPC method.\n        return this._postRpcRequest(\"tasks/pushNotificationConfig/get\", params);\n    }\n    /**\n     * Retrieves a task by its ID.\n     * @param params Parameters containing the taskId and optional historyLength.\n     * @returns A Promise resolving to GetTaskResponse, which contains the Task object or an error.\n     */\n    async getTask(params) {\n        return this._postRpcRequest(\"tasks/get\", params);\n    }\n    /**\n     * Cancels a task by its ID.\n     * @param params Parameters containing the taskId.\n     * @returns A Promise resolving to CancelTaskResponse, which contains the updated Task object or an error.\n     */\n    async cancelTask(params) {\n        return this._postRpcRequest(\"tasks/cancel\", params);\n    }\n    /**\n     * Resubscribes to a task's event stream using Server-Sent Events (SSE).\n     * This is used if a previous SSE connection for an active task was broken.\n     * Requires the agent to support streaming (`capabilities.streaming: true` in AgentCard).\n     * @param params Parameters containing the taskId.\n     * @returns An AsyncGenerator yielding A2AStreamEventData (Message, Task, TaskStatusUpdateEvent, or TaskArtifactUpdateEvent).\n     */\n    async *resubscribeTask(params) {\n        const agentCard = await this.agentCardPromise;\n        if (!agentCard.capabilities?.streaming) {\n            throw new Error(\"Agent does not support streaming (required for tasks/resubscribe).\");\n        }\n        const endpoint = await this._getServiceEndpoint();\n        const clientRequestId = this.requestIdCounter++; // Unique ID for this resubscribe request\n        const rpcRequest = {\n            jsonrpc: \"2.0\",\n            method: \"tasks/resubscribe\",\n            params: params,\n            id: clientRequestId,\n        };\n        const response = await fetch(endpoint, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\",\n                \"Accept\": \"text/event-stream\",\n            },\n            body: JSON.stringify(rpcRequest),\n        });\n        if (!response.ok) {\n            let errorBody = \"\";\n            try {\n                errorBody = await response.text();\n                const errorJson = JSON.parse(errorBody);\n                if (errorJson.error) {\n                    throw new Error(`HTTP error establishing stream for tasks/resubscribe: ${response.status} ${response.statusText}. RPC Error: ${errorJson.error.message} (Code: ${errorJson.error.code})`);\n                }\n            }\n            catch (e) {\n                if (e.message.startsWith('HTTP error establishing stream'))\n                    throw e;\n                throw new Error(`HTTP error establishing stream for tasks/resubscribe: ${response.status} ${response.statusText}. Response: ${errorBody || '(empty)'}`);\n            }\n            throw new Error(`HTTP error establishing stream for tasks/resubscribe: ${response.status} ${response.statusText}`);\n        }\n        if (!response.headers.get(\"Content-Type\")?.startsWith(\"text/event-stream\")) {\n            throw new Error(\"Invalid response Content-Type for SSE stream on resubscribe. Expected 'text/event-stream'.\");\n        }\n        // The events structure for resubscribe is assumed to be the same as message/stream.\n        // Each event's 'data' field is a JSON-RPC response.\n        yield* this._parseA2ASseStream(response, clientRequestId);\n    }\n    /**\n     * Parses an HTTP response body as an A2A Server-Sent Event stream.\n     * Each 'data' field of an SSE event is expected to be a JSON-RPC 2.0 Response object,\n     * specifically a SendStreamingMessageResponse (or similar structure for resubscribe).\n     * @param response The HTTP Response object whose body is the SSE stream.\n     * @param originalRequestId The ID of the client's JSON-RPC request that initiated this stream.\n     * Used to validate the `id` in the streamed JSON-RPC responses.\n     * @returns An AsyncGenerator yielding the `result` field of each valid JSON-RPC success response from the stream.\n     */\n    async *_parseA2ASseStream(response, originalRequestId) {\n        if (!response.body) {\n            throw new Error(\"SSE response body is undefined. Cannot read stream.\");\n        }\n        const reader = response.body.pipeThrough(new TextDecoderStream()).getReader();\n        let buffer = \"\"; // Holds incomplete lines from the stream\n        let eventDataBuffer = \"\"; // Holds accumulated 'data:' lines for the current event\n        try {\n            while (true) {\n                const { done, value } = await reader.read();\n                if (done) {\n                    // Process any final buffered event data if the stream ends abruptly after a 'data:' line\n                    if (eventDataBuffer.trim()) {\n                        const result = this._processSseEventData(eventDataBuffer, originalRequestId);\n                        yield result;\n                    }\n                    break; // Stream finished\n                }\n                buffer += value; // Append new chunk to buffer\n                let lineEndIndex;\n                // Process all complete lines in the buffer\n                while ((lineEndIndex = buffer.indexOf('\\n')) >= 0) {\n                    const line = buffer.substring(0, lineEndIndex).trim(); // Get and trim the line\n                    buffer = buffer.substring(lineEndIndex + 1); // Remove processed line from buffer\n                    if (line === \"\") { // Empty line: signifies the end of an event\n                        if (eventDataBuffer) { // If we have accumulated data for an event\n                            const result = this._processSseEventData(eventDataBuffer, originalRequestId);\n                            yield result;\n                            eventDataBuffer = \"\"; // Reset buffer for the next event\n                        }\n                    }\n                    else if (line.startsWith(\"data:\")) {\n                        eventDataBuffer += line.substring(5).trimStart() + \"\\n\"; // Append data (multi-line data is possible)\n                    }\n                    else if (line.startsWith(\":\")) {\n                        // This is a comment line in SSE, ignore it.\n                    }\n                    else if (line.includes(\":\")) {\n                        // Other SSE fields like 'event:', 'id:', 'retry:'.\n                        // The A2A spec primarily focuses on the 'data' field for JSON-RPC payloads.\n                        // For now, we don't specifically handle these other SSE fields unless required by spec.\n                    }\n                }\n            }\n        }\n        catch (error) {\n            // Log and re-throw errors encountered during stream processing\n            console.error(\"Error reading or parsing SSE stream:\", error.message);\n            throw error;\n        }\n        finally {\n            reader.releaseLock(); // Ensure the reader lock is released\n        }\n    }\n    /**\n     * Processes a single SSE event's data string, expecting it to be a JSON-RPC response.\n     * @param jsonData The string content from one or more 'data:' lines of an SSE event.\n     * @param originalRequestId The ID of the client's request that initiated the stream.\n     * @returns The `result` field of the parsed JSON-RPC success response.\n     * @throws Error if data is not valid JSON, not a valid JSON-RPC response, an error response, or ID mismatch.\n     */\n    _processSseEventData(jsonData, originalRequestId) {\n        if (!jsonData.trim()) {\n            throw new Error(\"Attempted to process empty SSE event data.\");\n        }\n        try {\n            // SSE data can be multi-line, ensure it's treated as a single JSON string.\n            const sseJsonRpcResponse = JSON.parse(jsonData.replace(/\\n$/, '')); // Remove trailing newline if any\n            // Type assertion to SendStreamingMessageResponse, as this is the expected structure for A2A streams.\n            const a2aStreamResponse = sseJsonRpcResponse;\n            if (a2aStreamResponse.id !== originalRequestId) {\n                // According to JSON-RPC spec, notifications (which SSE events can be seen as) might not have an ID,\n                // or if they do, it should match. A2A spec implies streamed events are tied to the initial request.\n                console.warn(`SSE Event's JSON-RPC response ID mismatch. Client request ID: ${originalRequestId}, event response ID: ${a2aStreamResponse.id}.`);\n                // Depending on strictness, this could be an error. For now, it's a warning.\n            }\n            if (this.isErrorResponse(a2aStreamResponse)) {\n                const err = a2aStreamResponse.error;\n                throw new Error(`SSE event contained an error: ${err.message} (Code: ${err.code}) Data: ${JSON.stringify(err.data)}`);\n            }\n            // Check if 'result' exists, as it's mandatory for successful JSON-RPC responses\n            if (!('result' in a2aStreamResponse) || typeof a2aStreamResponse.result === 'undefined') {\n                throw new Error(`SSE event JSON-RPC response is missing 'result' field. Data: ${jsonData}`);\n            }\n            const successResponse = a2aStreamResponse;\n            return successResponse.result;\n        }\n        catch (e) {\n            // Catch errors from JSON.parse or if it's an error response that was thrown by this function\n            if (e.message.startsWith(\"SSE event contained an error\") || e.message.startsWith(\"SSE event JSON-RPC response is missing 'result' field\")) {\n                throw e; // Re-throw errors already processed/identified by this function\n            }\n            // For other parsing errors or unexpected structures:\n            console.error(\"Failed to parse SSE event data string or unexpected JSON-RPC structure:\", jsonData, e);\n            throw new Error(`Failed to parse SSE event data: \"${jsonData.substring(0, 100)}...\". Original error: ${e.message}`);\n        }\n    }\n    isErrorResponse(response) {\n        return \"error\" in response;\n    }\n}\n//# sourceMappingURL=client.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9jbGllbnQvY2xpZW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBLHdCQUF3QjtBQUN4QjtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0MsYUFBYTtBQUNyRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RDtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDLGtCQUFrQjtBQUNsRDtBQUNBO0FBQ0EsMkJBQTJCLDhCQUE4QjtBQUN6RCxhQUFhO0FBQ2I7QUFDQSxtRUFBbUUsYUFBYSxJQUFJLGlCQUFpQixFQUFFLG9CQUFvQjtBQUMzSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EscURBQXFEO0FBQ3JEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxvQ0FBb0MscUJBQXFCO0FBQ3pEO0FBQ0EsMkJBQTJCLDhCQUE4QjtBQUN6RCxhQUFhO0FBQ2I7QUFDQSxtRUFBbUUsYUFBYSxJQUFJLGlCQUFpQixFQUFFLG9CQUFvQjtBQUMzSDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZEQUE2RDtBQUM3RCxxREFBcUQsT0FBTyxJQUFJLHlCQUF5QixTQUFTLHFCQUFxQixpQkFBaUIsb0JBQW9CLFVBQVUscUNBQXFDO0FBQzNNO0FBQ0E7QUFDQSxzREFBc0QsT0FBTyxZQUFZLHFCQUFxQixFQUFFLHdCQUF3QixjQUFjLGNBQWM7QUFDcEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrREFBa0QsT0FBTyxZQUFZLHFCQUFxQixFQUFFLHdCQUF3QixjQUFjLGNBQWM7QUFDaEo7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRSxPQUFPLGFBQWEsVUFBVSxRQUFRLGVBQWU7QUFDaEk7QUFDQSxzRUFBc0UsT0FBTyxhQUFhLFVBQVUsUUFBUSxlQUFlO0FBQzNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEZBQTBGLGlCQUFpQixFQUFFLG9CQUFvQixlQUFlLHlCQUF5QixTQUFTLHFCQUFxQjtBQUN2TTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzRkFBc0YsaUJBQWlCLEVBQUUsb0JBQW9CLGNBQWMsdUJBQXVCO0FBQ2xLO0FBQ0Esa0ZBQWtGLGlCQUFpQixFQUFFLG9CQUFvQjtBQUN6SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlEQUF5RDtBQUN6RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZGQUE2RixpQkFBaUIsRUFBRSxvQkFBb0IsZUFBZSx5QkFBeUIsU0FBUyxxQkFBcUI7QUFDMU07QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlGQUF5RixpQkFBaUIsRUFBRSxvQkFBb0IsY0FBYyx1QkFBdUI7QUFDcks7QUFDQSxxRkFBcUYsaUJBQWlCLEVBQUUsb0JBQW9CO0FBQzVIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCLGtDQUFrQztBQUNsQztBQUNBO0FBQ0Esd0JBQXdCLGNBQWM7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCO0FBQ0EsaUNBQWlDO0FBQ2pDO0FBQ0E7QUFDQTtBQUNBLDJFQUEyRTtBQUMzRSxpRUFBaUU7QUFDakUsdUNBQXVDO0FBQ3ZDLCtDQUErQztBQUMvQztBQUNBO0FBQ0Esa0RBQWtEO0FBQ2xEO0FBQ0E7QUFDQTtBQUNBLGlGQUFpRjtBQUNqRjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0M7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0ZBQWdGO0FBQ2hGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw4RkFBOEYsa0JBQWtCLHVCQUF1QixxQkFBcUI7QUFDNUo7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpRUFBaUUsYUFBYSxTQUFTLFNBQVMsVUFBVSx5QkFBeUI7QUFDbkk7QUFDQTtBQUNBO0FBQ0EsZ0dBQWdHLFNBQVM7QUFDekc7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx5QkFBeUI7QUFDekI7QUFDQTtBQUNBO0FBQ0EsZ0VBQWdFLDJCQUEyQix3QkFBd0IsVUFBVTtBQUM3SDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYTJhLWpzK3Nka0AwLjIuMi9ub2RlX21vZHVsZXMvQGEyYS1qcy9zZGsvYnVpbGQvc3JjL2NsaWVudC9jbGllbnQuanM/YzU2OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEEyQUNsaWVudCBpcyBhIFR5cGVTY3JpcHQgSFRUUCBjbGllbnQgZm9yIGludGVyYWN0aW5nIHdpdGggQTJBLWNvbXBsaWFudCBhZ2VudHMuXG4gKi9cbmV4cG9ydCBjbGFzcyBBMkFDbGllbnQge1xuICAgIGFnZW50QmFzZVVybDtcbiAgICBhZ2VudENhcmRQcm9taXNlO1xuICAgIHJlcXVlc3RJZENvdW50ZXIgPSAxO1xuICAgIHNlcnZpY2VFbmRwb2ludFVybDsgLy8gVG8gYmUgcG9wdWxhdGVkIGZyb20gQWdlbnRDYXJkIGFmdGVyIGZldGNoaW5nXG4gICAgLyoqXG4gICAgICogQ29uc3RydWN0cyBhbiBBMkFDbGllbnQgaW5zdGFuY2UuXG4gICAgICogSXQgaW5pdGlhdGVzIGZldGNoaW5nIHRoZSBhZ2VudCBjYXJkIGZyb20gdGhlIHByb3ZpZGVkIGFnZW50IGJhc2VVcmwuXG4gICAgICogVGhlIEFnZW50IENhcmQgaXMgZXhwZWN0ZWQgYXQgYCR7YWdlbnRCYXNlVXJsfS8ud2VsbC1rbm93bi9hZ2VudC5qc29uYC5cbiAgICAgKiBUaGUgYHVybGAgZmllbGQgZnJvbSB0aGUgQWdlbnQgQ2FyZCB3aWxsIGJlIHVzZWQgYXMgdGhlIFJQQyBzZXJ2aWNlIGVuZHBvaW50LlxuICAgICAqIEBwYXJhbSBhZ2VudEJhc2VVcmwgVGhlIGJhc2UgVVJMIG9mIHRoZSBBMkEgYWdlbnQgKGUuZy4sIGh0dHBzOi8vYWdlbnQuZXhhbXBsZS5jb20pLlxuICAgICAqL1xuICAgIGNvbnN0cnVjdG9yKGFnZW50QmFzZVVybCkge1xuICAgICAgICB0aGlzLmFnZW50QmFzZVVybCA9IGFnZW50QmFzZVVybC5yZXBsYWNlKC9cXC8kLywgXCJcIik7IC8vIFJlbW92ZSB0cmFpbGluZyBzbGFzaCBpZiBhbnlcbiAgICAgICAgdGhpcy5hZ2VudENhcmRQcm9taXNlID0gdGhpcy5fZmV0Y2hBbmRDYWNoZUFnZW50Q2FyZCgpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBGZXRjaGVzIHRoZSBBZ2VudCBDYXJkIGZyb20gdGhlIGFnZW50J3Mgd2VsbC1rbm93biBVUkkgYW5kIGNhY2hlcyBpdHMgc2VydmljZSBlbmRwb2ludCBVUkwuXG4gICAgICogVGhpcyBtZXRob2QgaXMgY2FsbGVkIGJ5IHRoZSBjb25zdHJ1Y3Rvci5cbiAgICAgKiBAcmV0dXJucyBBIFByb21pc2UgdGhhdCByZXNvbHZlcyB0byB0aGUgQWdlbnRDYXJkLlxuICAgICAqL1xuICAgIGFzeW5jIF9mZXRjaEFuZENhY2hlQWdlbnRDYXJkKCkge1xuICAgICAgICBjb25zdCBhZ2VudENhcmRVcmwgPSBgJHt0aGlzLmFnZW50QmFzZVVybH0vLndlbGwta25vd24vYWdlbnQuanNvbmA7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGFnZW50Q2FyZFVybCwge1xuICAgICAgICAgICAgICAgIGhlYWRlcnM6IHsgJ0FjY2VwdCc6ICdhcHBsaWNhdGlvbi9qc29uJyB9LFxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBGYWlsZWQgdG8gZmV0Y2ggQWdlbnQgQ2FyZCBmcm9tICR7YWdlbnRDYXJkVXJsfTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNvbnN0IGFnZW50Q2FyZCA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgICAgICAgIGlmICghYWdlbnRDYXJkLnVybCkge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkZldGNoZWQgQWdlbnQgQ2FyZCBkb2VzIG5vdCBjb250YWluIGEgdmFsaWQgJ3VybCcgZm9yIHRoZSBzZXJ2aWNlIGVuZHBvaW50LlwiKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHRoaXMuc2VydmljZUVuZHBvaW50VXJsID0gYWdlbnRDYXJkLnVybDsgLy8gQ2FjaGUgdGhlIHNlcnZpY2UgZW5kcG9pbnQgVVJMIGZyb20gdGhlIGFnZW50IGNhcmRcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKFwiRU5ET1BJTlRcIiwgdGhpcy5zZXJ2aWNlRW5kcG9pbnRVcmwpO1xuICAgICAgICAgICAgcmV0dXJuIGFnZW50Q2FyZDtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciBmZXRjaGluZyBvciBwYXJzaW5nIEFnZW50IENhcmQ6XCIpO1xuICAgICAgICAgICAgLy8gQWxsb3cgdGhlIHByb21pc2UgdG8gcmVqZWN0IHNvIHVzZXJzIG9mIGFnZW50Q2FyZFByb21pc2UgY2FuIGhhbmRsZSBpdC5cbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFJldHJpZXZlcyB0aGUgQWdlbnQgQ2FyZC5cbiAgICAgKiBJZiBhbiBgYWdlbnRCYXNlVXJsYCBpcyBwcm92aWRlZCwgaXQgZmV0Y2hlcyB0aGUgY2FyZCBmcm9tIHRoYXQgc3BlY2lmaWMgVVJMLlxuICAgICAqIE90aGVyd2lzZSwgaXQgcmV0dXJucyB0aGUgY2FyZCBmZXRjaGVkIGFuZCBjYWNoZWQgZHVyaW5nIGNsaWVudCBjb25zdHJ1Y3Rpb24uXG4gICAgICogQHBhcmFtIGFnZW50QmFzZVVybCBPcHRpb25hbC4gVGhlIGJhc2UgVVJMIG9mIHRoZSBhZ2VudCB0byBmZXRjaCB0aGUgY2FyZCBmcm9tLlxuICAgICAqIElmIHByb3ZpZGVkLCB0aGlzIHdpbGwgZmV0Y2ggYSBuZXcgY2FyZCwgbm90IHVzZSB0aGUgY2FjaGVkIG9uZSBmcm9tIHRoZSBjb25zdHJ1Y3RvcidzIFVSTC5cbiAgICAgKiBAcmV0dXJucyBBIFByb21pc2UgdGhhdCByZXNvbHZlcyB0byB0aGUgQWdlbnRDYXJkLlxuICAgICAqL1xuICAgIGFzeW5jIGdldEFnZW50Q2FyZChhZ2VudEJhc2VVcmwpIHtcbiAgICAgICAgaWYgKGFnZW50QmFzZVVybCkge1xuICAgICAgICAgICAgY29uc3Qgc3BlY2lmaWNBZ2VudEJhc2VVcmwgPSBhZ2VudEJhc2VVcmwucmVwbGFjZSgvXFwvJC8sIFwiXCIpO1xuICAgICAgICAgICAgY29uc3QgYWdlbnRDYXJkVXJsID0gYCR7c3BlY2lmaWNBZ2VudEJhc2VVcmx9Ly53ZWxsLWtub3duL2FnZW50Lmpzb25gO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChhZ2VudENhcmRVcmwsIHtcbiAgICAgICAgICAgICAgICBoZWFkZXJzOiB7ICdBY2NlcHQnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIGZldGNoIEFnZW50IENhcmQgZnJvbSAke2FnZW50Q2FyZFVybH06ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICByZXR1cm4gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIG5vIHNwZWNpZmljIFVSTCBpcyBnaXZlbiwgcmV0dXJuIHRoZSBwcm9taXNlIGZvciB0aGUgaW5pdGlhbGx5IGNvbmZpZ3VyZWQgYWdlbnQncyBjYXJkLlxuICAgICAgICByZXR1cm4gdGhpcy5hZ2VudENhcmRQcm9taXNlO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBHZXRzIHRoZSBSUEMgc2VydmljZSBlbmRwb2ludCBVUkwuIEVuc3VyZXMgdGhlIGFnZW50IGNhcmQgaGFzIGJlZW4gZmV0Y2hlZCBmaXJzdC5cbiAgICAgKiBAcmV0dXJucyBBIFByb21pc2UgdGhhdCByZXNvbHZlcyB0byB0aGUgc2VydmljZSBlbmRwb2ludCBVUkwgc3RyaW5nLlxuICAgICAqL1xuICAgIGFzeW5jIF9nZXRTZXJ2aWNlRW5kcG9pbnQoKSB7XG4gICAgICAgIGlmICh0aGlzLnNlcnZpY2VFbmRwb2ludFVybCkge1xuICAgICAgICAgICAgcmV0dXJuIHRoaXMuc2VydmljZUVuZHBvaW50VXJsO1xuICAgICAgICB9XG4gICAgICAgIC8vIElmIHNlcnZpY2VFbmRwb2ludFVybCBpcyBub3Qgc2V0LCBpdCBtZWFucyB0aGUgYWdlbnQgY2FyZCBmZXRjaCBpcyBwZW5kaW5nIG9yIGZhaWxlZC5cbiAgICAgICAgLy8gQXdhaXRpbmcgYWdlbnRDYXJkUHJvbWlzZSB3aWxsIGVpdGhlciByZXNvbHZlIGl0IG9yIHRocm93IGlmIGZldGNoaW5nIGZhaWxlZC5cbiAgICAgICAgYXdhaXQgdGhpcy5hZ2VudENhcmRQcm9taXNlO1xuICAgICAgICBpZiAoIXRoaXMuc2VydmljZUVuZHBvaW50VXJsKSB7XG4gICAgICAgICAgICAvLyBUaGlzIGNhc2Ugc2hvdWxkIGlkZWFsbHkgYmUgY292ZXJlZCBieSB0aGUgZXJyb3IgaGFuZGxpbmcgaW4gX2ZldGNoQW5kQ2FjaGVBZ2VudENhcmRcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkFnZW50IENhcmQgVVJMIGZvciBSUEMgZW5kcG9pbnQgaXMgbm90IGF2YWlsYWJsZS4gRmV0Y2hpbmcgbWlnaHQgaGF2ZSBmYWlsZWQuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB0aGlzLnNlcnZpY2VFbmRwb2ludFVybDtcbiAgICB9XG4gICAgLyoqXG4gICAgICogSGVscGVyIG1ldGhvZCB0byBtYWtlIGEgZ2VuZXJpYyBKU09OLVJQQyBQT1NUIHJlcXVlc3QuXG4gICAgICogQHBhcmFtIG1ldGhvZCBUaGUgUlBDIG1ldGhvZCBuYW1lLlxuICAgICAqIEBwYXJhbSBwYXJhbXMgVGhlIHBhcmFtZXRlcnMgZm9yIHRoZSBSUEMgbWV0aG9kLlxuICAgICAqIEByZXR1cm5zIEEgUHJvbWlzZSB0aGF0IHJlc29sdmVzIHRvIHRoZSBSUEMgcmVzcG9uc2UuXG4gICAgICovXG4gICAgYXN5bmMgX3Bvc3RScGNSZXF1ZXN0KG1ldGhvZCwgcGFyYW1zKSB7XG4gICAgICAgIGNvbnN0IGVuZHBvaW50ID0gYXdhaXQgdGhpcy5fZ2V0U2VydmljZUVuZHBvaW50KCk7XG4gICAgICAgIGNvbnN0IHJlcXVlc3RJZCA9IHRoaXMucmVxdWVzdElkQ291bnRlcisrO1xuICAgICAgICBjb25zdCBycGNSZXF1ZXN0ID0ge1xuICAgICAgICAgICAganNvbnJwYzogXCIyLjBcIixcbiAgICAgICAgICAgIG1ldGhvZCxcbiAgICAgICAgICAgIHBhcmFtczogcGFyYW1zLCAvLyBDYXN0IGJlY2F1c2UgVFBhcmFtcyBzdHJ1Y3R1cmUgdmFyaWVzIHBlciBtZXRob2RcbiAgICAgICAgICAgIGlkOiByZXF1ZXN0SWQsXG4gICAgICAgIH07XG4gICAgICAgIGNvbnN0IGh0dHBSZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCB7XG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgICAgIFwiQWNjZXB0XCI6IFwiYXBwbGljYXRpb24vanNvblwiLCAvLyBFeHBlY3QgSlNPTiByZXNwb25zZSBmb3Igbm9uLXN0cmVhbWluZyByZXF1ZXN0c1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHJwY1JlcXVlc3QpLFxuICAgICAgICB9KTtcbiAgICAgICAgaWYgKCFodHRwUmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgIGxldCBlcnJvckJvZHlUZXh0ID0gJyhlbXB0eSBvciBub24tSlNPTiByZXNwb25zZSknO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBlcnJvckJvZHlUZXh0ID0gYXdhaXQgaHR0cFJlc3BvbnNlLnRleHQoKTtcbiAgICAgICAgICAgICAgICBjb25zdCBlcnJvckpzb24gPSBKU09OLnBhcnNlKGVycm9yQm9keVRleHQpO1xuICAgICAgICAgICAgICAgIC8vIElmIHRoZSBib2R5IGlzIGEgdmFsaWQgSlNPTi1SUEMgZXJyb3IgcmVzcG9uc2UsIGxldCBpdCBiZSBoYW5kbGVkIGJ5IHRoZSBzdGFuZGFyZCBwYXJzaW5nIGJlbG93LlxuICAgICAgICAgICAgICAgIC8vIEhvd2V2ZXIsIGlmIGl0J3Mgbm90IGV2ZW4gYSBKU09OLVJQQyBzdHJ1Y3R1cmUgYnV0IHN0aWxsIGFuIGVycm9yLCB0aHJvdyBiYXNlZCBvbiBIVFRQIHN0YXR1cy5cbiAgICAgICAgICAgICAgICBpZiAoIWVycm9ySnNvbi5qc29ucnBjICYmIGVycm9ySnNvbi5lcnJvcikgeyAvLyBDaGVjayBpZiBpdCdzIGEgSlNPTi1SUEMgZXJyb3Igc3RydWN0dXJlXG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgUlBDIGVycm9yIGZvciAke21ldGhvZH06ICR7ZXJyb3JKc29uLmVycm9yLm1lc3NhZ2V9IChDb2RlOiAke2Vycm9ySnNvbi5lcnJvci5jb2RlfSwgSFRUUCBTdGF0dXM6ICR7aHR0cFJlc3BvbnNlLnN0YXR1c30pIERhdGE6ICR7SlNPTi5zdHJpbmdpZnkoZXJyb3JKc29uLmVycm9yLmRhdGEpfWApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIGlmICghZXJyb3JKc29uLmpzb25ycGMpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yIGZvciAke21ldGhvZH0hIFN0YXR1czogJHtodHRwUmVzcG9uc2Uuc3RhdHVzfSAke2h0dHBSZXNwb25zZS5zdGF0dXNUZXh0fS4gUmVzcG9uc2U6ICR7ZXJyb3JCb2R5VGV4dH1gKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAgIC8vIElmIHBhcnNpbmcgdGhlIGVycm9yIGJvZHkgZmFpbHMgb3IgaXQncyBub3QgYSBKU09OLVJQQyBlcnJvciwgdGhyb3cgYSBnZW5lcmljIEhUVFAgZXJyb3IuXG4gICAgICAgICAgICAgICAgLy8gSWYgaXQgd2FzIGFscmVhZHkgYW4gZXJyb3IgdGhyb3duIGZyb20gd2l0aGluIHRoZSB0cnkgYmxvY2ssIHJldGhyb3cgaXQuXG4gICAgICAgICAgICAgICAgaWYgKGUubWVzc2FnZS5zdGFydHNXaXRoKCdSUEMgZXJyb3IgZm9yJykgfHwgZS5tZXNzYWdlLnN0YXJ0c1dpdGgoJ0hUVFAgZXJyb3IgZm9yJykpXG4gICAgICAgICAgICAgICAgICAgIHRocm93IGU7XG4gICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yIGZvciAke21ldGhvZH0hIFN0YXR1czogJHtodHRwUmVzcG9uc2Uuc3RhdHVzfSAke2h0dHBSZXNwb25zZS5zdGF0dXNUZXh0fS4gUmVzcG9uc2U6ICR7ZXJyb3JCb2R5VGV4dH1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBjb25zdCBycGNSZXNwb25zZSA9IGF3YWl0IGh0dHBSZXNwb25zZS5qc29uKCk7XG4gICAgICAgIGlmIChycGNSZXNwb25zZS5pZCAhPT0gcmVxdWVzdElkKSB7XG4gICAgICAgICAgICAvLyBUaGlzIGlzIGEgc2lnbmlmaWNhbnQgaXNzdWUgZm9yIHJlcXVlc3QtcmVzcG9uc2UgbWF0Y2hpbmcuXG4gICAgICAgICAgICBjb25zb2xlLmVycm9yKGBDUklUSUNBTDogUlBDIHJlc3BvbnNlIElEIG1pc21hdGNoIGZvciBtZXRob2QgJHttZXRob2R9LiBFeHBlY3RlZCAke3JlcXVlc3RJZH0sIGdvdCAke3JwY1Jlc3BvbnNlLmlkfS4gVGhpcyBtYXkgbGVhZCB0byBpbmNvcnJlY3QgcmVzcG9uc2UgaGFuZGxpbmcuYCk7XG4gICAgICAgICAgICAvLyBEZXBlbmRpbmcgb24gc3RyaWN0bmVzcywgb25lIG1pZ2h0IHRocm93IGFuIGVycm9yIGhlcmUuXG4gICAgICAgICAgICAvLyB0aHJvdyBuZXcgRXJyb3IoYFJQQyByZXNwb25zZSBJRCBtaXNtYXRjaCBmb3IgbWV0aG9kICR7bWV0aG9kfS4gRXhwZWN0ZWQgJHtyZXF1ZXN0SWR9LCBnb3QgJHtycGNSZXNwb25zZS5pZH1gKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcnBjUmVzcG9uc2U7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNlbmRzIGEgbWVzc2FnZSB0byB0aGUgYWdlbnQuXG4gICAgICogVGhlIGJlaGF2aW9yIChibG9ja2luZy9ub24tYmxvY2tpbmcpIGFuZCBwdXNoIG5vdGlmaWNhdGlvbiBjb25maWd1cmF0aW9uXG4gICAgICogYXJlIHNwZWNpZmllZCB3aXRoaW4gdGhlIGBwYXJhbXMuY29uZmlndXJhdGlvbmAgb2JqZWN0LlxuICAgICAqIE9wdGlvbmFsbHksIGBwYXJhbXMubWVzc2FnZS5jb250ZXh0SWRgIG9yIGBwYXJhbXMubWVzc2FnZS50YXNrSWRgIGNhbiBiZSBwcm92aWRlZC5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIFRoZSBwYXJhbWV0ZXJzIGZvciBzZW5kaW5nIHRoZSBtZXNzYWdlLCBpbmNsdWRpbmcgdGhlIG1lc3NhZ2UgY29udGVudCBhbmQgY29uZmlndXJhdGlvbi5cbiAgICAgKiBAcmV0dXJucyBBIFByb21pc2UgcmVzb2x2aW5nIHRvIFNlbmRNZXNzYWdlUmVzcG9uc2UsIHdoaWNoIGNhbiBiZSBhIE1lc3NhZ2UsIFRhc2ssIG9yIGFuIGVycm9yLlxuICAgICAqL1xuICAgIGFzeW5jIHNlbmRNZXNzYWdlKHBhcmFtcykge1xuICAgICAgICByZXR1cm4gdGhpcy5fcG9zdFJwY1JlcXVlc3QoXCJtZXNzYWdlL3NlbmRcIiwgcGFyYW1zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogU2VuZHMgYSBtZXNzYWdlIHRvIHRoZSBhZ2VudCBhbmQgc3RyZWFtcyBiYWNrIHJlc3BvbnNlcyB1c2luZyBTZXJ2ZXItU2VudCBFdmVudHMgKFNTRSkuXG4gICAgICogUHVzaCBub3RpZmljYXRpb24gY29uZmlndXJhdGlvbiBjYW4gYmUgc3BlY2lmaWVkIGluIGBwYXJhbXMuY29uZmlndXJhdGlvbmAuXG4gICAgICogT3B0aW9uYWxseSwgYHBhcmFtcy5tZXNzYWdlLmNvbnRleHRJZGAgb3IgYHBhcmFtcy5tZXNzYWdlLnRhc2tJZGAgY2FuIGJlIHByb3ZpZGVkLlxuICAgICAqIFJlcXVpcmVzIHRoZSBhZ2VudCB0byBzdXBwb3J0IHN0cmVhbWluZyAoYGNhcGFiaWxpdGllcy5zdHJlYW1pbmc6IHRydWVgIGluIEFnZW50Q2FyZCkuXG4gICAgICogQHBhcmFtIHBhcmFtcyBUaGUgcGFyYW1ldGVycyBmb3Igc2VuZGluZyB0aGUgbWVzc2FnZS5cbiAgICAgKiBAcmV0dXJucyBBbiBBc3luY0dlbmVyYXRvciB5aWVsZGluZyBBMkFTdHJlYW1FdmVudERhdGEgKE1lc3NhZ2UsIFRhc2ssIFRhc2tTdGF0dXNVcGRhdGVFdmVudCwgb3IgVGFza0FydGlmYWN0VXBkYXRlRXZlbnQpLlxuICAgICAqIFRoZSBnZW5lcmF0b3IgdGhyb3dzIGFuIGVycm9yIGlmIHN0cmVhbWluZyBpcyBub3Qgc3VwcG9ydGVkIG9yIGlmIGFuIEhUVFAvU1NFIGVycm9yIG9jY3Vycy5cbiAgICAgKi9cbiAgICBhc3luYyAqc2VuZE1lc3NhZ2VTdHJlYW0ocGFyYW1zKSB7XG4gICAgICAgIGNvbnN0IGFnZW50Q2FyZCA9IGF3YWl0IHRoaXMuYWdlbnRDYXJkUHJvbWlzZTsgLy8gRW5zdXJlIGFnZW50IGNhcmQgaXMgZmV0Y2hlZFxuICAgICAgICBpZiAoIWFnZW50Q2FyZC5jYXBhYmlsaXRpZXM/LnN0cmVhbWluZykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQWdlbnQgZG9lcyBub3Qgc3VwcG9ydCBzdHJlYW1pbmcgKEFnZW50Q2FyZC5jYXBhYmlsaXRpZXMuc3RyZWFtaW5nIGlzIG5vdCB0cnVlKS5cIik7XG4gICAgICAgIH1cbiAgICAgICAgY29uc3QgZW5kcG9pbnQgPSBhd2FpdCB0aGlzLl9nZXRTZXJ2aWNlRW5kcG9pbnQoKTtcbiAgICAgICAgY29uc3QgY2xpZW50UmVxdWVzdElkID0gdGhpcy5yZXF1ZXN0SWRDb3VudGVyKys7IC8vIFVzZSBhIHVuaXF1ZSBJRCBmb3IgdGhpcyBzdHJlYW0gcmVxdWVzdFxuICAgICAgICBjb25zdCBycGNSZXF1ZXN0ID0ge1xuICAgICAgICAgICAganNvbnJwYzogXCIyLjBcIixcbiAgICAgICAgICAgIG1ldGhvZDogXCJtZXNzYWdlL3N0cmVhbVwiLFxuICAgICAgICAgICAgcGFyYW1zOiBwYXJhbXMsXG4gICAgICAgICAgICBpZDogY2xpZW50UmVxdWVzdElkLFxuICAgICAgICB9O1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCB7XG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgICAgIFwiQWNjZXB0XCI6IFwidGV4dC9ldmVudC1zdHJlYW1cIiwgLy8gQ3J1Y2lhbCBmb3IgU1NFXG4gICAgICAgICAgICB9LFxuICAgICAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocnBjUmVxdWVzdCksXG4gICAgICAgIH0pO1xuICAgICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgICAgICAvLyBBdHRlbXB0IHRvIHJlYWQgZXJyb3IgYm9keSBmb3IgbW9yZSBkZXRhaWxzXG4gICAgICAgICAgICBsZXQgZXJyb3JCb2R5ID0gXCJcIjtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgICAgZXJyb3JCb2R5ID0gYXdhaXQgcmVzcG9uc2UudGV4dCgpO1xuICAgICAgICAgICAgICAgIGNvbnN0IGVycm9ySnNvbiA9IEpTT04ucGFyc2UoZXJyb3JCb2R5KTtcbiAgICAgICAgICAgICAgICBpZiAoZXJyb3JKc29uLmVycm9yKSB7XG4gICAgICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciBlc3RhYmxpc2hpbmcgc3RyZWFtIGZvciBtZXNzYWdlL3N0cmVhbTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH0uIFJQQyBFcnJvcjogJHtlcnJvckpzb24uZXJyb3IubWVzc2FnZX0gKENvZGU6ICR7ZXJyb3JKc29uLmVycm9yLmNvZGV9KWApO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgaWYgKGUubWVzc2FnZS5zdGFydHNXaXRoKCdIVFRQIGVycm9yIGVzdGFibGlzaGluZyBzdHJlYW0nKSlcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgZTtcbiAgICAgICAgICAgICAgICAvLyBGYWxsYmFjayBpZiBib2R5IGlzIG5vdCBKU09OIG9yIHBhcnNpbmcgZmFpbHNcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IgZXN0YWJsaXNoaW5nIHN0cmVhbSBmb3IgbWVzc2FnZS9zdHJlYW06ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9LiBSZXNwb25zZTogJHtlcnJvckJvZHkgfHwgJyhlbXB0eSknfWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yIGVzdGFibGlzaGluZyBzdHJlYW0gZm9yIG1lc3NhZ2Uvc3RyZWFtOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fWApO1xuICAgICAgICB9XG4gICAgICAgIGlmICghcmVzcG9uc2UuaGVhZGVycy5nZXQoXCJDb250ZW50LVR5cGVcIik/LnN0YXJ0c1dpdGgoXCJ0ZXh0L2V2ZW50LXN0cmVhbVwiKSkge1xuICAgICAgICAgICAgLy8gU2VydmVyIHNob3VsZCBleHBsaWNpdGx5IHNldCB0aGlzIGNvbnRlbnQgdHlwZSBmb3IgU1NFLlxuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCByZXNwb25zZSBDb250ZW50LVR5cGUgZm9yIFNTRSBzdHJlYW0uIEV4cGVjdGVkICd0ZXh0L2V2ZW50LXN0cmVhbScuXCIpO1xuICAgICAgICB9XG4gICAgICAgIC8vIFlpZWxkIGV2ZW50cyBmcm9tIHRoZSBwYXJzZWQgU1NFIHN0cmVhbS5cbiAgICAgICAgLy8gRWFjaCBldmVudCdzICdkYXRhJyBmaWVsZCBpcyBhIEpTT04tUlBDIHJlc3BvbnNlLlxuICAgICAgICB5aWVsZCogdGhpcy5fcGFyc2VBMkFTc2VTdHJlYW0ocmVzcG9uc2UsIGNsaWVudFJlcXVlc3RJZCk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIFNldHMgb3IgdXBkYXRlcyB0aGUgcHVzaCBub3RpZmljYXRpb24gY29uZmlndXJhdGlvbiBmb3IgYSBnaXZlbiB0YXNrLlxuICAgICAqIFJlcXVpcmVzIHRoZSBhZ2VudCB0byBzdXBwb3J0IHB1c2ggbm90aWZpY2F0aW9ucyAoYGNhcGFiaWxpdGllcy5wdXNoTm90aWZpY2F0aW9uczogdHJ1ZWAgaW4gQWdlbnRDYXJkKS5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIFBhcmFtZXRlcnMgY29udGFpbmluZyB0aGUgdGFza0lkIGFuZCB0aGUgVGFza1B1c2hOb3RpZmljYXRpb25Db25maWcuXG4gICAgICogQHJldHVybnMgQSBQcm9taXNlIHJlc29sdmluZyB0byBTZXRUYXNrUHVzaE5vdGlmaWNhdGlvbkNvbmZpZ1Jlc3BvbnNlLlxuICAgICAqL1xuICAgIGFzeW5jIHNldFRhc2tQdXNoTm90aWZpY2F0aW9uQ29uZmlnKHBhcmFtcykge1xuICAgICAgICBjb25zdCBhZ2VudENhcmQgPSBhd2FpdCB0aGlzLmFnZW50Q2FyZFByb21pc2U7XG4gICAgICAgIGlmICghYWdlbnRDYXJkLmNhcGFiaWxpdGllcz8ucHVzaE5vdGlmaWNhdGlvbnMpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIkFnZW50IGRvZXMgbm90IHN1cHBvcnQgcHVzaCBub3RpZmljYXRpb25zIChBZ2VudENhcmQuY2FwYWJpbGl0aWVzLnB1c2hOb3RpZmljYXRpb25zIGlzIG5vdCB0cnVlKS5cIik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gVGhlICdwYXJhbXMnIGRpcmVjdGx5IG1hdGNoZXMgdGhlIHN0cnVjdHVyZSBleHBlY3RlZCBieSB0aGUgUlBDIG1ldGhvZC5cbiAgICAgICAgcmV0dXJuIHRoaXMuX3Bvc3RScGNSZXF1ZXN0KFwidGFza3MvcHVzaE5vdGlmaWNhdGlvbkNvbmZpZy9zZXRcIiwgcGFyYW1zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogR2V0cyB0aGUgcHVzaCBub3RpZmljYXRpb24gY29uZmlndXJhdGlvbiBmb3IgYSBnaXZlbiB0YXNrLlxuICAgICAqIEBwYXJhbSBwYXJhbXMgUGFyYW1ldGVycyBjb250YWluaW5nIHRoZSB0YXNrSWQuXG4gICAgICogQHJldHVybnMgQSBQcm9taXNlIHJlc29sdmluZyB0byBHZXRUYXNrUHVzaE5vdGlmaWNhdGlvbkNvbmZpZ1Jlc3BvbnNlLlxuICAgICAqL1xuICAgIGFzeW5jIGdldFRhc2tQdXNoTm90aWZpY2F0aW9uQ29uZmlnKHBhcmFtcykge1xuICAgICAgICAvLyBUaGUgJ3BhcmFtcycgKFRhc2tJZFBhcmFtcykgZGlyZWN0bHkgbWF0Y2hlcyB0aGUgc3RydWN0dXJlIGV4cGVjdGVkIGJ5IHRoZSBSUEMgbWV0aG9kLlxuICAgICAgICByZXR1cm4gdGhpcy5fcG9zdFJwY1JlcXVlc3QoXCJ0YXNrcy9wdXNoTm90aWZpY2F0aW9uQ29uZmlnL2dldFwiLCBwYXJhbXMpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBSZXRyaWV2ZXMgYSB0YXNrIGJ5IGl0cyBJRC5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIFBhcmFtZXRlcnMgY29udGFpbmluZyB0aGUgdGFza0lkIGFuZCBvcHRpb25hbCBoaXN0b3J5TGVuZ3RoLlxuICAgICAqIEByZXR1cm5zIEEgUHJvbWlzZSByZXNvbHZpbmcgdG8gR2V0VGFza1Jlc3BvbnNlLCB3aGljaCBjb250YWlucyB0aGUgVGFzayBvYmplY3Qgb3IgYW4gZXJyb3IuXG4gICAgICovXG4gICAgYXN5bmMgZ2V0VGFzayhwYXJhbXMpIHtcbiAgICAgICAgcmV0dXJuIHRoaXMuX3Bvc3RScGNSZXF1ZXN0KFwidGFza3MvZ2V0XCIsIHBhcmFtcyk7XG4gICAgfVxuICAgIC8qKlxuICAgICAqIENhbmNlbHMgYSB0YXNrIGJ5IGl0cyBJRC5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIFBhcmFtZXRlcnMgY29udGFpbmluZyB0aGUgdGFza0lkLlxuICAgICAqIEByZXR1cm5zIEEgUHJvbWlzZSByZXNvbHZpbmcgdG8gQ2FuY2VsVGFza1Jlc3BvbnNlLCB3aGljaCBjb250YWlucyB0aGUgdXBkYXRlZCBUYXNrIG9iamVjdCBvciBhbiBlcnJvci5cbiAgICAgKi9cbiAgICBhc3luYyBjYW5jZWxUYXNrKHBhcmFtcykge1xuICAgICAgICByZXR1cm4gdGhpcy5fcG9zdFJwY1JlcXVlc3QoXCJ0YXNrcy9jYW5jZWxcIiwgcGFyYW1zKTtcbiAgICB9XG4gICAgLyoqXG4gICAgICogUmVzdWJzY3JpYmVzIHRvIGEgdGFzaydzIGV2ZW50IHN0cmVhbSB1c2luZyBTZXJ2ZXItU2VudCBFdmVudHMgKFNTRSkuXG4gICAgICogVGhpcyBpcyB1c2VkIGlmIGEgcHJldmlvdXMgU1NFIGNvbm5lY3Rpb24gZm9yIGFuIGFjdGl2ZSB0YXNrIHdhcyBicm9rZW4uXG4gICAgICogUmVxdWlyZXMgdGhlIGFnZW50IHRvIHN1cHBvcnQgc3RyZWFtaW5nIChgY2FwYWJpbGl0aWVzLnN0cmVhbWluZzogdHJ1ZWAgaW4gQWdlbnRDYXJkKS5cbiAgICAgKiBAcGFyYW0gcGFyYW1zIFBhcmFtZXRlcnMgY29udGFpbmluZyB0aGUgdGFza0lkLlxuICAgICAqIEByZXR1cm5zIEFuIEFzeW5jR2VuZXJhdG9yIHlpZWxkaW5nIEEyQVN0cmVhbUV2ZW50RGF0YSAoTWVzc2FnZSwgVGFzaywgVGFza1N0YXR1c1VwZGF0ZUV2ZW50LCBvciBUYXNrQXJ0aWZhY3RVcGRhdGVFdmVudCkuXG4gICAgICovXG4gICAgYXN5bmMgKnJlc3Vic2NyaWJlVGFzayhwYXJhbXMpIHtcbiAgICAgICAgY29uc3QgYWdlbnRDYXJkID0gYXdhaXQgdGhpcy5hZ2VudENhcmRQcm9taXNlO1xuICAgICAgICBpZiAoIWFnZW50Q2FyZC5jYXBhYmlsaXRpZXM/LnN0cmVhbWluZykge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQWdlbnQgZG9lcyBub3Qgc3VwcG9ydCBzdHJlYW1pbmcgKHJlcXVpcmVkIGZvciB0YXNrcy9yZXN1YnNjcmliZSkuXCIpO1xuICAgICAgICB9XG4gICAgICAgIGNvbnN0IGVuZHBvaW50ID0gYXdhaXQgdGhpcy5fZ2V0U2VydmljZUVuZHBvaW50KCk7XG4gICAgICAgIGNvbnN0IGNsaWVudFJlcXVlc3RJZCA9IHRoaXMucmVxdWVzdElkQ291bnRlcisrOyAvLyBVbmlxdWUgSUQgZm9yIHRoaXMgcmVzdWJzY3JpYmUgcmVxdWVzdFxuICAgICAgICBjb25zdCBycGNSZXF1ZXN0ID0ge1xuICAgICAgICAgICAganNvbnJwYzogXCIyLjBcIixcbiAgICAgICAgICAgIG1ldGhvZDogXCJ0YXNrcy9yZXN1YnNjcmliZVwiLFxuICAgICAgICAgICAgcGFyYW1zOiBwYXJhbXMsXG4gICAgICAgICAgICBpZDogY2xpZW50UmVxdWVzdElkLFxuICAgICAgICB9O1xuICAgICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGVuZHBvaW50LCB7XG4gICAgICAgICAgICBtZXRob2Q6IFwiUE9TVFwiLFxuICAgICAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICAgICAgIFwiQ29udGVudC1UeXBlXCI6IFwiYXBwbGljYXRpb24vanNvblwiLFxuICAgICAgICAgICAgICAgIFwiQWNjZXB0XCI6IFwidGV4dC9ldmVudC1zdHJlYW1cIixcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShycGNSZXF1ZXN0KSxcbiAgICAgICAgfSk7XG4gICAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgICAgIGxldCBlcnJvckJvZHkgPSBcIlwiO1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICBlcnJvckJvZHkgPSBhd2FpdCByZXNwb25zZS50ZXh0KCk7XG4gICAgICAgICAgICAgICAgY29uc3QgZXJyb3JKc29uID0gSlNPTi5wYXJzZShlcnJvckJvZHkpO1xuICAgICAgICAgICAgICAgIGlmIChlcnJvckpzb24uZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKGBIVFRQIGVycm9yIGVzdGFibGlzaGluZyBzdHJlYW0gZm9yIHRhc2tzL3Jlc3Vic2NyaWJlOiAke3Jlc3BvbnNlLnN0YXR1c30gJHtyZXNwb25zZS5zdGF0dXNUZXh0fS4gUlBDIEVycm9yOiAke2Vycm9ySnNvbi5lcnJvci5tZXNzYWdlfSAoQ29kZTogJHtlcnJvckpzb24uZXJyb3IuY29kZX0pYCk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgICBpZiAoZS5tZXNzYWdlLnN0YXJ0c1dpdGgoJ0hUVFAgZXJyb3IgZXN0YWJsaXNoaW5nIHN0cmVhbScpKVxuICAgICAgICAgICAgICAgICAgICB0aHJvdyBlO1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciBlc3RhYmxpc2hpbmcgc3RyZWFtIGZvciB0YXNrcy9yZXN1YnNjcmliZTogJHtyZXNwb25zZS5zdGF0dXN9ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH0uIFJlc3BvbnNlOiAke2Vycm9yQm9keSB8fCAnKGVtcHR5KSd9YCk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYEhUVFAgZXJyb3IgZXN0YWJsaXNoaW5nIHN0cmVhbSBmb3IgdGFza3MvcmVzdWJzY3JpYmU6ICR7cmVzcG9uc2Uuc3RhdHVzfSAke3Jlc3BvbnNlLnN0YXR1c1RleHR9YCk7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKCFyZXNwb25zZS5oZWFkZXJzLmdldChcIkNvbnRlbnQtVHlwZVwiKT8uc3RhcnRzV2l0aChcInRleHQvZXZlbnQtc3RyZWFtXCIpKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXCJJbnZhbGlkIHJlc3BvbnNlIENvbnRlbnQtVHlwZSBmb3IgU1NFIHN0cmVhbSBvbiByZXN1YnNjcmliZS4gRXhwZWN0ZWQgJ3RleHQvZXZlbnQtc3RyZWFtJy5cIik7XG4gICAgICAgIH1cbiAgICAgICAgLy8gVGhlIGV2ZW50cyBzdHJ1Y3R1cmUgZm9yIHJlc3Vic2NyaWJlIGlzIGFzc3VtZWQgdG8gYmUgdGhlIHNhbWUgYXMgbWVzc2FnZS9zdHJlYW0uXG4gICAgICAgIC8vIEVhY2ggZXZlbnQncyAnZGF0YScgZmllbGQgaXMgYSBKU09OLVJQQyByZXNwb25zZS5cbiAgICAgICAgeWllbGQqIHRoaXMuX3BhcnNlQTJBU3NlU3RyZWFtKHJlc3BvbnNlLCBjbGllbnRSZXF1ZXN0SWQpO1xuICAgIH1cbiAgICAvKipcbiAgICAgKiBQYXJzZXMgYW4gSFRUUCByZXNwb25zZSBib2R5IGFzIGFuIEEyQSBTZXJ2ZXItU2VudCBFdmVudCBzdHJlYW0uXG4gICAgICogRWFjaCAnZGF0YScgZmllbGQgb2YgYW4gU1NFIGV2ZW50IGlzIGV4cGVjdGVkIHRvIGJlIGEgSlNPTi1SUEMgMi4wIFJlc3BvbnNlIG9iamVjdCxcbiAgICAgKiBzcGVjaWZpY2FsbHkgYSBTZW5kU3RyZWFtaW5nTWVzc2FnZVJlc3BvbnNlIChvciBzaW1pbGFyIHN0cnVjdHVyZSBmb3IgcmVzdWJzY3JpYmUpLlxuICAgICAqIEBwYXJhbSByZXNwb25zZSBUaGUgSFRUUCBSZXNwb25zZSBvYmplY3Qgd2hvc2UgYm9keSBpcyB0aGUgU1NFIHN0cmVhbS5cbiAgICAgKiBAcGFyYW0gb3JpZ2luYWxSZXF1ZXN0SWQgVGhlIElEIG9mIHRoZSBjbGllbnQncyBKU09OLVJQQyByZXF1ZXN0IHRoYXQgaW5pdGlhdGVkIHRoaXMgc3RyZWFtLlxuICAgICAqIFVzZWQgdG8gdmFsaWRhdGUgdGhlIGBpZGAgaW4gdGhlIHN0cmVhbWVkIEpTT04tUlBDIHJlc3BvbnNlcy5cbiAgICAgKiBAcmV0dXJucyBBbiBBc3luY0dlbmVyYXRvciB5aWVsZGluZyB0aGUgYHJlc3VsdGAgZmllbGQgb2YgZWFjaCB2YWxpZCBKU09OLVJQQyBzdWNjZXNzIHJlc3BvbnNlIGZyb20gdGhlIHN0cmVhbS5cbiAgICAgKi9cbiAgICBhc3luYyAqX3BhcnNlQTJBU3NlU3RyZWFtKHJlc3BvbnNlLCBvcmlnaW5hbFJlcXVlc3RJZCkge1xuICAgICAgICBpZiAoIXJlc3BvbnNlLmJvZHkpIHtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihcIlNTRSByZXNwb25zZSBib2R5IGlzIHVuZGVmaW5lZC4gQ2Fubm90IHJlYWQgc3RyZWFtLlwiKTtcbiAgICAgICAgfVxuICAgICAgICBjb25zdCByZWFkZXIgPSByZXNwb25zZS5ib2R5LnBpcGVUaHJvdWdoKG5ldyBUZXh0RGVjb2RlclN0cmVhbSgpKS5nZXRSZWFkZXIoKTtcbiAgICAgICAgbGV0IGJ1ZmZlciA9IFwiXCI7IC8vIEhvbGRzIGluY29tcGxldGUgbGluZXMgZnJvbSB0aGUgc3RyZWFtXG4gICAgICAgIGxldCBldmVudERhdGFCdWZmZXIgPSBcIlwiOyAvLyBIb2xkcyBhY2N1bXVsYXRlZCAnZGF0YTonIGxpbmVzIGZvciB0aGUgY3VycmVudCBldmVudFxuICAgICAgICB0cnkge1xuICAgICAgICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuICAgICAgICAgICAgICAgIGlmIChkb25lKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIFByb2Nlc3MgYW55IGZpbmFsIGJ1ZmZlcmVkIGV2ZW50IGRhdGEgaWYgdGhlIHN0cmVhbSBlbmRzIGFicnVwdGx5IGFmdGVyIGEgJ2RhdGE6JyBsaW5lXG4gICAgICAgICAgICAgICAgICAgIGlmIChldmVudERhdGFCdWZmZXIudHJpbSgpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSB0aGlzLl9wcm9jZXNzU3NlRXZlbnREYXRhKGV2ZW50RGF0YUJ1ZmZlciwgb3JpZ2luYWxSZXF1ZXN0SWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgeWllbGQgcmVzdWx0O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIGJyZWFrOyAvLyBTdHJlYW0gZmluaXNoZWRcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgYnVmZmVyICs9IHZhbHVlOyAvLyBBcHBlbmQgbmV3IGNodW5rIHRvIGJ1ZmZlclxuICAgICAgICAgICAgICAgIGxldCBsaW5lRW5kSW5kZXg7XG4gICAgICAgICAgICAgICAgLy8gUHJvY2VzcyBhbGwgY29tcGxldGUgbGluZXMgaW4gdGhlIGJ1ZmZlclxuICAgICAgICAgICAgICAgIHdoaWxlICgobGluZUVuZEluZGV4ID0gYnVmZmVyLmluZGV4T2YoJ1xcbicpKSA+PSAwKSB7XG4gICAgICAgICAgICAgICAgICAgIGNvbnN0IGxpbmUgPSBidWZmZXIuc3Vic3RyaW5nKDAsIGxpbmVFbmRJbmRleCkudHJpbSgpOyAvLyBHZXQgYW5kIHRyaW0gdGhlIGxpbmVcbiAgICAgICAgICAgICAgICAgICAgYnVmZmVyID0gYnVmZmVyLnN1YnN0cmluZyhsaW5lRW5kSW5kZXggKyAxKTsgLy8gUmVtb3ZlIHByb2Nlc3NlZCBsaW5lIGZyb20gYnVmZmVyXG4gICAgICAgICAgICAgICAgICAgIGlmIChsaW5lID09PSBcIlwiKSB7IC8vIEVtcHR5IGxpbmU6IHNpZ25pZmllcyB0aGUgZW5kIG9mIGFuIGV2ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoZXZlbnREYXRhQnVmZmVyKSB7IC8vIElmIHdlIGhhdmUgYWNjdW11bGF0ZWQgZGF0YSBmb3IgYW4gZXZlbnRcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBjb25zdCByZXN1bHQgPSB0aGlzLl9wcm9jZXNzU3NlRXZlbnREYXRhKGV2ZW50RGF0YUJ1ZmZlciwgb3JpZ2luYWxSZXF1ZXN0SWQpO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIHlpZWxkIHJlc3VsdDtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBldmVudERhdGFCdWZmZXIgPSBcIlwiOyAvLyBSZXNldCBidWZmZXIgZm9yIHRoZSBuZXh0IGV2ZW50XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAobGluZS5zdGFydHNXaXRoKFwiZGF0YTpcIikpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGV2ZW50RGF0YUJ1ZmZlciArPSBsaW5lLnN1YnN0cmluZyg1KS50cmltU3RhcnQoKSArIFwiXFxuXCI7IC8vIEFwcGVuZCBkYXRhIChtdWx0aS1saW5lIGRhdGEgaXMgcG9zc2libGUpXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgZWxzZSBpZiAobGluZS5zdGFydHNXaXRoKFwiOlwiKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gVGhpcyBpcyBhIGNvbW1lbnQgbGluZSBpbiBTU0UsIGlnbm9yZSBpdC5cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICBlbHNlIGlmIChsaW5lLmluY2x1ZGVzKFwiOlwiKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgLy8gT3RoZXIgU1NFIGZpZWxkcyBsaWtlICdldmVudDonLCAnaWQ6JywgJ3JldHJ5OicuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBUaGUgQTJBIHNwZWMgcHJpbWFyaWx5IGZvY3VzZXMgb24gdGhlICdkYXRhJyBmaWVsZCBmb3IgSlNPTi1SUEMgcGF5bG9hZHMuXG4gICAgICAgICAgICAgICAgICAgICAgICAvLyBGb3Igbm93LCB3ZSBkb24ndCBzcGVjaWZpY2FsbHkgaGFuZGxlIHRoZXNlIG90aGVyIFNTRSBmaWVsZHMgdW5sZXNzIHJlcXVpcmVkIGJ5IHNwZWMuXG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgY2F0Y2ggKGVycm9yKSB7XG4gICAgICAgICAgICAvLyBMb2cgYW5kIHJlLXRocm93IGVycm9ycyBlbmNvdW50ZXJlZCBkdXJpbmcgc3RyZWFtIHByb2Nlc3NpbmdcbiAgICAgICAgICAgIGNvbnNvbGUuZXJyb3IoXCJFcnJvciByZWFkaW5nIG9yIHBhcnNpbmcgU1NFIHN0cmVhbTpcIiwgZXJyb3IubWVzc2FnZSk7XG4gICAgICAgICAgICB0aHJvdyBlcnJvcjtcbiAgICAgICAgfVxuICAgICAgICBmaW5hbGx5IHtcbiAgICAgICAgICAgIHJlYWRlci5yZWxlYXNlTG9jaygpOyAvLyBFbnN1cmUgdGhlIHJlYWRlciBsb2NrIGlzIHJlbGVhc2VkXG4gICAgICAgIH1cbiAgICB9XG4gICAgLyoqXG4gICAgICogUHJvY2Vzc2VzIGEgc2luZ2xlIFNTRSBldmVudCdzIGRhdGEgc3RyaW5nLCBleHBlY3RpbmcgaXQgdG8gYmUgYSBKU09OLVJQQyByZXNwb25zZS5cbiAgICAgKiBAcGFyYW0ganNvbkRhdGEgVGhlIHN0cmluZyBjb250ZW50IGZyb20gb25lIG9yIG1vcmUgJ2RhdGE6JyBsaW5lcyBvZiBhbiBTU0UgZXZlbnQuXG4gICAgICogQHBhcmFtIG9yaWdpbmFsUmVxdWVzdElkIFRoZSBJRCBvZiB0aGUgY2xpZW50J3MgcmVxdWVzdCB0aGF0IGluaXRpYXRlZCB0aGUgc3RyZWFtLlxuICAgICAqIEByZXR1cm5zIFRoZSBgcmVzdWx0YCBmaWVsZCBvZiB0aGUgcGFyc2VkIEpTT04tUlBDIHN1Y2Nlc3MgcmVzcG9uc2UuXG4gICAgICogQHRocm93cyBFcnJvciBpZiBkYXRhIGlzIG5vdCB2YWxpZCBKU09OLCBub3QgYSB2YWxpZCBKU09OLVJQQyByZXNwb25zZSwgYW4gZXJyb3IgcmVzcG9uc2UsIG9yIElEIG1pc21hdGNoLlxuICAgICAqL1xuICAgIF9wcm9jZXNzU3NlRXZlbnREYXRhKGpzb25EYXRhLCBvcmlnaW5hbFJlcXVlc3RJZCkge1xuICAgICAgICBpZiAoIWpzb25EYXRhLnRyaW0oKSkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiQXR0ZW1wdGVkIHRvIHByb2Nlc3MgZW1wdHkgU1NFIGV2ZW50IGRhdGEuXCIpO1xuICAgICAgICB9XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgICAvLyBTU0UgZGF0YSBjYW4gYmUgbXVsdGktbGluZSwgZW5zdXJlIGl0J3MgdHJlYXRlZCBhcyBhIHNpbmdsZSBKU09OIHN0cmluZy5cbiAgICAgICAgICAgIGNvbnN0IHNzZUpzb25ScGNSZXNwb25zZSA9IEpTT04ucGFyc2UoanNvbkRhdGEucmVwbGFjZSgvXFxuJC8sICcnKSk7IC8vIFJlbW92ZSB0cmFpbGluZyBuZXdsaW5lIGlmIGFueVxuICAgICAgICAgICAgLy8gVHlwZSBhc3NlcnRpb24gdG8gU2VuZFN0cmVhbWluZ01lc3NhZ2VSZXNwb25zZSwgYXMgdGhpcyBpcyB0aGUgZXhwZWN0ZWQgc3RydWN0dXJlIGZvciBBMkEgc3RyZWFtcy5cbiAgICAgICAgICAgIGNvbnN0IGEyYVN0cmVhbVJlc3BvbnNlID0gc3NlSnNvblJwY1Jlc3BvbnNlO1xuICAgICAgICAgICAgaWYgKGEyYVN0cmVhbVJlc3BvbnNlLmlkICE9PSBvcmlnaW5hbFJlcXVlc3RJZCkge1xuICAgICAgICAgICAgICAgIC8vIEFjY29yZGluZyB0byBKU09OLVJQQyBzcGVjLCBub3RpZmljYXRpb25zICh3aGljaCBTU0UgZXZlbnRzIGNhbiBiZSBzZWVuIGFzKSBtaWdodCBub3QgaGF2ZSBhbiBJRCxcbiAgICAgICAgICAgICAgICAvLyBvciBpZiB0aGV5IGRvLCBpdCBzaG91bGQgbWF0Y2guIEEyQSBzcGVjIGltcGxpZXMgc3RyZWFtZWQgZXZlbnRzIGFyZSB0aWVkIHRvIHRoZSBpbml0aWFsIHJlcXVlc3QuXG4gICAgICAgICAgICAgICAgY29uc29sZS53YXJuKGBTU0UgRXZlbnQncyBKU09OLVJQQyByZXNwb25zZSBJRCBtaXNtYXRjaC4gQ2xpZW50IHJlcXVlc3QgSUQ6ICR7b3JpZ2luYWxSZXF1ZXN0SWR9LCBldmVudCByZXNwb25zZSBJRDogJHthMmFTdHJlYW1SZXNwb25zZS5pZH0uYCk7XG4gICAgICAgICAgICAgICAgLy8gRGVwZW5kaW5nIG9uIHN0cmljdG5lc3MsIHRoaXMgY291bGQgYmUgYW4gZXJyb3IuIEZvciBub3csIGl0J3MgYSB3YXJuaW5nLlxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgaWYgKHRoaXMuaXNFcnJvclJlc3BvbnNlKGEyYVN0cmVhbVJlc3BvbnNlKSkge1xuICAgICAgICAgICAgICAgIGNvbnN0IGVyciA9IGEyYVN0cmVhbVJlc3BvbnNlLmVycm9yO1xuICAgICAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgU1NFIGV2ZW50IGNvbnRhaW5lZCBhbiBlcnJvcjogJHtlcnIubWVzc2FnZX0gKENvZGU6ICR7ZXJyLmNvZGV9KSBEYXRhOiAke0pTT04uc3RyaW5naWZ5KGVyci5kYXRhKX1gKTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIC8vIENoZWNrIGlmICdyZXN1bHQnIGV4aXN0cywgYXMgaXQncyBtYW5kYXRvcnkgZm9yIHN1Y2Nlc3NmdWwgSlNPTi1SUEMgcmVzcG9uc2VzXG4gICAgICAgICAgICBpZiAoISgncmVzdWx0JyBpbiBhMmFTdHJlYW1SZXNwb25zZSkgfHwgdHlwZW9mIGEyYVN0cmVhbVJlc3BvbnNlLnJlc3VsdCA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoYFNTRSBldmVudCBKU09OLVJQQyByZXNwb25zZSBpcyBtaXNzaW5nICdyZXN1bHQnIGZpZWxkLiBEYXRhOiAke2pzb25EYXRhfWApO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3Qgc3VjY2Vzc1Jlc3BvbnNlID0gYTJhU3RyZWFtUmVzcG9uc2U7XG4gICAgICAgICAgICByZXR1cm4gc3VjY2Vzc1Jlc3BvbnNlLnJlc3VsdDtcbiAgICAgICAgfVxuICAgICAgICBjYXRjaCAoZSkge1xuICAgICAgICAgICAgLy8gQ2F0Y2ggZXJyb3JzIGZyb20gSlNPTi5wYXJzZSBvciBpZiBpdCdzIGFuIGVycm9yIHJlc3BvbnNlIHRoYXQgd2FzIHRocm93biBieSB0aGlzIGZ1bmN0aW9uXG4gICAgICAgICAgICBpZiAoZS5tZXNzYWdlLnN0YXJ0c1dpdGgoXCJTU0UgZXZlbnQgY29udGFpbmVkIGFuIGVycm9yXCIpIHx8IGUubWVzc2FnZS5zdGFydHNXaXRoKFwiU1NFIGV2ZW50IEpTT04tUlBDIHJlc3BvbnNlIGlzIG1pc3NpbmcgJ3Jlc3VsdCcgZmllbGRcIikpIHtcbiAgICAgICAgICAgICAgICB0aHJvdyBlOyAvLyBSZS10aHJvdyBlcnJvcnMgYWxyZWFkeSBwcm9jZXNzZWQvaWRlbnRpZmllZCBieSB0aGlzIGZ1bmN0aW9uXG4gICAgICAgICAgICB9XG4gICAgICAgICAgICAvLyBGb3Igb3RoZXIgcGFyc2luZyBlcnJvcnMgb3IgdW5leHBlY3RlZCBzdHJ1Y3R1cmVzOlxuICAgICAgICAgICAgY29uc29sZS5lcnJvcihcIkZhaWxlZCB0byBwYXJzZSBTU0UgZXZlbnQgZGF0YSBzdHJpbmcgb3IgdW5leHBlY3RlZCBKU09OLVJQQyBzdHJ1Y3R1cmU6XCIsIGpzb25EYXRhLCBlKTtcbiAgICAgICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHBhcnNlIFNTRSBldmVudCBkYXRhOiBcIiR7anNvbkRhdGEuc3Vic3RyaW5nKDAsIDEwMCl9Li4uXCIuIE9yaWdpbmFsIGVycm9yOiAke2UubWVzc2FnZX1gKTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBpc0Vycm9yUmVzcG9uc2UocmVzcG9uc2UpIHtcbiAgICAgICAgcmV0dXJuIFwiZXJyb3JcIiBpbiByZXNwb25zZTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jbGllbnQuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/client/client.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A2AClient: () => (/* reexport safe */ _client_client_js__WEBPACK_IMPORTED_MODULE_9__.A2AClient),\n/* harmony export */   A2AError: () => (/* reexport safe */ _server_error_js__WEBPACK_IMPORTED_MODULE_8__.A2AError),\n/* harmony export */   A2AExpressApp: () => (/* reexport safe */ _server_a2a_express_app_js__WEBPACK_IMPORTED_MODULE_7__.A2AExpressApp),\n/* harmony export */   DefaultExecutionEventBus: () => (/* reexport safe */ _server_events_execution_event_bus_js__WEBPACK_IMPORTED_MODULE_1__.DefaultExecutionEventBus),\n/* harmony export */   DefaultExecutionEventBusManager: () => (/* reexport safe */ _server_events_execution_event_bus_manager_js__WEBPACK_IMPORTED_MODULE_2__.DefaultExecutionEventBusManager),\n/* harmony export */   DefaultRequestHandler: () => (/* reexport safe */ _server_request_handler_default_request_handler_js__WEBPACK_IMPORTED_MODULE_3__.DefaultRequestHandler),\n/* harmony export */   InMemoryTaskStore: () => (/* reexport safe */ _server_store_js__WEBPACK_IMPORTED_MODULE_5__.InMemoryTaskStore),\n/* harmony export */   JsonRpcTransportHandler: () => (/* reexport safe */ _server_transports_jsonrpc_transport_handler_js__WEBPACK_IMPORTED_MODULE_6__.JsonRpcTransportHandler),\n/* harmony export */   RequestContext: () => (/* reexport safe */ _server_agent_execution_request_context_js__WEBPACK_IMPORTED_MODULE_0__.RequestContext),\n/* harmony export */   ResultManager: () => (/* reexport safe */ _server_result_manager_js__WEBPACK_IMPORTED_MODULE_4__.ResultManager)\n/* harmony export */ });\n/* harmony import */ var _server_agent_execution_request_context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./server/agent_execution/request_context.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/agent_execution/request_context.js\");\n/* harmony import */ var _server_events_execution_event_bus_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./server/events/execution_event_bus.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus.js\");\n/* harmony import */ var _server_events_execution_event_bus_manager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./server/events/execution_event_bus_manager.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus_manager.js\");\n/* harmony import */ var _server_request_handler_default_request_handler_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./server/request_handler/default_request_handler.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/request_handler/default_request_handler.js\");\n/* harmony import */ var _server_result_manager_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./server/result_manager.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/result_manager.js\");\n/* harmony import */ var _server_store_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./server/store.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/store.js\");\n/* harmony import */ var _server_transports_jsonrpc_transport_handler_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./server/transports/jsonrpc_transport_handler.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/transports/jsonrpc_transport_handler.js\");\n/* harmony import */ var _server_a2a_express_app_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./server/a2a_express_app.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/a2a_express_app.js\");\n/* harmony import */ var _server_error_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./server/error.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js\");\n/* harmony import */ var _client_client_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./client/client.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/client/client.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/types.js\");\n/**\n * Main entry point for the A2A Server V2 library.\n * Exports the server class, store implementations, and core types.\n */\n\n\n\n\n\n\n\n\n\n// Export Client\n\n// Re-export all schema types for convenience\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUM2RTtBQUNLO0FBQ2U7QUFDTDtBQUNqQztBQUNMO0FBQ3FDO0FBQy9CO0FBQ2Y7QUFDN0M7QUFDK0M7QUFDL0M7QUFDMkI7QUFDM0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9pbmRleC5qcz82YTI0Il0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWFpbiBlbnRyeSBwb2ludCBmb3IgdGhlIEEyQSBTZXJ2ZXIgVjIgbGlicmFyeS5cbiAqIEV4cG9ydHMgdGhlIHNlcnZlciBjbGFzcywgc3RvcmUgaW1wbGVtZW50YXRpb25zLCBhbmQgY29yZSB0eXBlcy5cbiAqL1xuZXhwb3J0IHsgUmVxdWVzdENvbnRleHQgfSBmcm9tIFwiLi9zZXJ2ZXIvYWdlbnRfZXhlY3V0aW9uL3JlcXVlc3RfY29udGV4dC5qc1wiO1xuZXhwb3J0IHsgRGVmYXVsdEV4ZWN1dGlvbkV2ZW50QnVzIH0gZnJvbSBcIi4vc2VydmVyL2V2ZW50cy9leGVjdXRpb25fZXZlbnRfYnVzLmpzXCI7XG5leHBvcnQgeyBEZWZhdWx0RXhlY3V0aW9uRXZlbnRCdXNNYW5hZ2VyIH0gZnJvbSBcIi4vc2VydmVyL2V2ZW50cy9leGVjdXRpb25fZXZlbnRfYnVzX21hbmFnZXIuanNcIjtcbmV4cG9ydCB7IERlZmF1bHRSZXF1ZXN0SGFuZGxlciB9IGZyb20gXCIuL3NlcnZlci9yZXF1ZXN0X2hhbmRsZXIvZGVmYXVsdF9yZXF1ZXN0X2hhbmRsZXIuanNcIjtcbmV4cG9ydCB7IFJlc3VsdE1hbmFnZXIgfSBmcm9tIFwiLi9zZXJ2ZXIvcmVzdWx0X21hbmFnZXIuanNcIjtcbmV4cG9ydCB7IEluTWVtb3J5VGFza1N0b3JlIH0gZnJvbSBcIi4vc2VydmVyL3N0b3JlLmpzXCI7XG5leHBvcnQgeyBKc29uUnBjVHJhbnNwb3J0SGFuZGxlciB9IGZyb20gXCIuL3NlcnZlci90cmFuc3BvcnRzL2pzb25ycGNfdHJhbnNwb3J0X2hhbmRsZXIuanNcIjtcbmV4cG9ydCB7IEEyQUV4cHJlc3NBcHAgfSBmcm9tIFwiLi9zZXJ2ZXIvYTJhX2V4cHJlc3NfYXBwLmpzXCI7XG5leHBvcnQgeyBBMkFFcnJvciB9IGZyb20gXCIuL3NlcnZlci9lcnJvci5qc1wiO1xuLy8gRXhwb3J0IENsaWVudFxuZXhwb3J0IHsgQTJBQ2xpZW50IH0gZnJvbSBcIi4vY2xpZW50L2NsaWVudC5qc1wiO1xuLy8gUmUtZXhwb3J0IGFsbCBzY2hlbWEgdHlwZXMgZm9yIGNvbnZlbmllbmNlXG5leHBvcnQgKiBmcm9tIFwiLi90eXBlcy5qc1wiO1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXguanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/a2a_express_app.js":
/*!***********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/a2a_express_app.js ***!
  \***********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A2AExpressApp: () => (/* binding */ A2AExpressApp)\n/* harmony export */ });\n/* harmony import */ var express__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! express */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/index.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./error.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js\");\n/* harmony import */ var _transports_jsonrpc_transport_handler_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./transports/jsonrpc_transport_handler.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/transports/jsonrpc_transport_handler.js\");\n\n\n\nclass A2AExpressApp {\n    requestHandler; // Kept for getAgentCard\n    jsonRpcTransportHandler;\n    constructor(requestHandler) {\n        this.requestHandler = requestHandler; // DefaultRequestHandler instance\n        this.jsonRpcTransportHandler = new _transports_jsonrpc_transport_handler_js__WEBPACK_IMPORTED_MODULE_2__.JsonRpcTransportHandler(requestHandler);\n    }\n    /**\n     * Adds A2A routes to an existing Express app.\n     * @param app Optional existing Express app.\n     * @param baseUrl The base URL for A2A endpoints (e.g., \"/a2a/api\").\n     * @returns The Express app with A2A routes.\n     */\n    setupRoutes(app, baseUrl = '') {\n        app.use(express__WEBPACK_IMPORTED_MODULE_0__.json());\n        app.get(`${baseUrl}/.well-known/agent.json`, async (req, res) => {\n            try {\n                // getAgentCard is on A2ARequestHandler, which DefaultRequestHandler implements\n                const agentCard = await this.requestHandler.getAgentCard();\n                res.json(agentCard);\n            }\n            catch (error) {\n                console.error(\"Error fetching agent card:\", error);\n                res.status(500).json({ error: \"Failed to retrieve agent card\" });\n            }\n        });\n        app.post(baseUrl, async (req, res) => {\n            try {\n                const rpcResponseOrStream = await this.jsonRpcTransportHandler.handle(req.body);\n                // Check if it's an AsyncGenerator (stream)\n                if (typeof rpcResponseOrStream?.[Symbol.asyncIterator] === 'function') {\n                    const stream = rpcResponseOrStream;\n                    res.setHeader('Content-Type', 'text/event-stream');\n                    res.setHeader('Cache-Control', 'no-cache');\n                    res.setHeader('Connection', 'keep-alive');\n                    res.flushHeaders();\n                    try {\n                        for await (const event of stream) {\n                            // Each event from the stream is already a JSONRPCResult\n                            res.write(`id: ${new Date().getTime()}\\n`);\n                            res.write(`data: ${JSON.stringify(event)}\\n\\n`);\n                        }\n                    }\n                    catch (streamError) {\n                        console.error(`Error during SSE streaming (request ${req.body?.id}):`, streamError);\n                        // If the stream itself throws an error, send a final JSONRPCErrorResponse\n                        const a2aError = streamError instanceof _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError ? streamError : _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.internalError(streamError.message || 'Streaming error.');\n                        const errorResponse = {\n                            jsonrpc: '2.0',\n                            id: req.body?.id || null, // Use original request ID if available\n                            error: a2aError.toJSONRPCError(),\n                        };\n                        if (!res.headersSent) { // Should not happen if flushHeaders worked\n                            res.status(500).json(errorResponse); // Should be JSON, not SSE here\n                        }\n                        else {\n                            // Try to send as last SSE event if possible, though client might have disconnected\n                            res.write(`id: ${new Date().getTime()}\\n`);\n                            res.write(`event: error\\n`); // Custom event type for client-side handling\n                            res.write(`data: ${JSON.stringify(errorResponse)}\\n\\n`);\n                        }\n                    }\n                    finally {\n                        if (!res.writableEnded) {\n                            res.end();\n                        }\n                    }\n                }\n                else { // Single JSON-RPC response\n                    const rpcResponse = rpcResponseOrStream;\n                    res.status(200).json(rpcResponse);\n                }\n            }\n            catch (error) { // Catch errors from jsonRpcTransportHandler.handle itself (e.g., initial parse error)\n                console.error(\"Unhandled error in A2AExpressApp POST handler:\", error);\n                const a2aError = error instanceof _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError ? error : _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.internalError('General processing error.');\n                const errorResponse = {\n                    jsonrpc: '2.0',\n                    id: req.body?.id || null,\n                    error: a2aError.toJSONRPCError(),\n                };\n                if (!res.headersSent) {\n                    res.status(500).json(errorResponse);\n                }\n                else if (!res.writableEnded) {\n                    // If headers sent (likely during a stream attempt that failed early), try to end gracefully\n                    res.end();\n                }\n            }\n        });\n        // The separate /stream endpoint is no longer needed.\n        return app;\n    }\n}\n//# sourceMappingURL=a2a_express_app.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/a2a_express_app.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/agent_execution/request_context.js":
/*!***************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/agent_execution/request_context.js ***!
  \***************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RequestContext: () => (/* binding */ RequestContext)\n/* harmony export */ });\nclass RequestContext {\n    userMessage;\n    task;\n    referenceTasks;\n    taskId;\n    contextId;\n    constructor(userMessage, taskId, contextId, task, referenceTasks) {\n        this.userMessage = userMessage;\n        this.taskId = taskId;\n        this.contextId = contextId;\n        this.task = task;\n        this.referenceTasks = referenceTasks;\n    }\n}\n//# sourceMappingURL=request_context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9zZXJ2ZXIvYWdlbnRfZXhlY3V0aW9uL3JlcXVlc3RfY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0BhMmEtanMrc2RrQDAuMi4yL25vZGVfbW9kdWxlcy9AYTJhLWpzL3Nkay9idWlsZC9zcmMvc2VydmVyL2FnZW50X2V4ZWN1dGlvbi9yZXF1ZXN0X2NvbnRleHQuanM/NmVhZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY2xhc3MgUmVxdWVzdENvbnRleHQge1xuICAgIHVzZXJNZXNzYWdlO1xuICAgIHRhc2s7XG4gICAgcmVmZXJlbmNlVGFza3M7XG4gICAgdGFza0lkO1xuICAgIGNvbnRleHRJZDtcbiAgICBjb25zdHJ1Y3Rvcih1c2VyTWVzc2FnZSwgdGFza0lkLCBjb250ZXh0SWQsIHRhc2ssIHJlZmVyZW5jZVRhc2tzKSB7XG4gICAgICAgIHRoaXMudXNlck1lc3NhZ2UgPSB1c2VyTWVzc2FnZTtcbiAgICAgICAgdGhpcy50YXNrSWQgPSB0YXNrSWQ7XG4gICAgICAgIHRoaXMuY29udGV4dElkID0gY29udGV4dElkO1xuICAgICAgICB0aGlzLnRhc2sgPSB0YXNrO1xuICAgICAgICB0aGlzLnJlZmVyZW5jZVRhc2tzID0gcmVmZXJlbmNlVGFza3M7XG4gICAgfVxufVxuLy8jIHNvdXJjZU1hcHBpbmdVUkw9cmVxdWVzdF9jb250ZXh0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/agent_execution/request_context.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   A2AError: () => (/* binding */ A2AError)\n/* harmony export */ });\n/**\n * Custom error class for A2A server operations, incorporating JSON-RPC error codes.\n */\nclass A2AError extends Error {\n    code;\n    data;\n    taskId; // Optional task ID context\n    constructor(code, message, data, taskId) {\n        super(message);\n        this.name = \"A2AError\";\n        this.code = code;\n        this.data = data;\n        this.taskId = taskId; // Store associated task ID if provided\n    }\n    /**\n     * Formats the error into a standard JSON-RPC error object structure.\n     */\n    toJSONRPCError() {\n        const errorObject = {\n            code: this.code,\n            message: this.message,\n        };\n        if (this.data !== undefined) {\n            errorObject.data = this.data;\n        }\n        return errorObject;\n    }\n    // Static factory methods for common errors\n    static parseError(message, data) {\n        return new A2AError(-32700, message, data);\n    }\n    static invalidRequest(message, data) {\n        return new A2AError(-32600, message, data);\n    }\n    static methodNotFound(method) {\n        return new A2AError(-32601, `Method not found: ${method}`);\n    }\n    static invalidParams(message, data) {\n        return new A2AError(-32602, message, data);\n    }\n    static internalError(message, data) {\n        return new A2AError(-32603, message, data);\n    }\n    static taskNotFound(taskId) {\n        return new A2AError(-32001, `Task not found: ${taskId}`, undefined, taskId);\n    }\n    static taskNotCancelable(taskId) {\n        return new A2AError(-32002, `Task not cancelable: ${taskId}`, undefined, taskId);\n    }\n    static pushNotificationNotSupported() {\n        return new A2AError(-32003, \"Push Notification is not supported\");\n    }\n    static unsupportedOperation(operation) {\n        return new A2AError(-32004, `Unsupported operation: ${operation}`);\n    }\n}\n//# sourceMappingURL=error.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus.js":
/*!**********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus.js ***!
  \**********************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultExecutionEventBus: () => (/* binding */ DefaultExecutionEventBus)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n\nclass DefaultExecutionEventBus extends events__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n    constructor() {\n        super();\n    }\n    publish(event) {\n        this.emit('event', event);\n    }\n    finished() {\n        this.emit('finished');\n    }\n}\n//# sourceMappingURL=execution_event_bus.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9zZXJ2ZXIvZXZlbnRzL2V4ZWN1dGlvbl9ldmVudF9idXMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBc0M7QUFDL0IsdUNBQXVDLGdEQUFZO0FBQzFEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9zZXJ2ZXIvZXZlbnRzL2V4ZWN1dGlvbl9ldmVudF9idXMuanM/MzM5MiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBFdmVudEVtaXR0ZXIgfSBmcm9tICdldmVudHMnO1xuZXhwb3J0IGNsYXNzIERlZmF1bHRFeGVjdXRpb25FdmVudEJ1cyBleHRlbmRzIEV2ZW50RW1pdHRlciB7XG4gICAgY29uc3RydWN0b3IoKSB7XG4gICAgICAgIHN1cGVyKCk7XG4gICAgfVxuICAgIHB1Ymxpc2goZXZlbnQpIHtcbiAgICAgICAgdGhpcy5lbWl0KCdldmVudCcsIGV2ZW50KTtcbiAgICB9XG4gICAgZmluaXNoZWQoKSB7XG4gICAgICAgIHRoaXMuZW1pdCgnZmluaXNoZWQnKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1leGVjdXRpb25fZXZlbnRfYnVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus_manager.js":
/*!******************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus_manager.js ***!
  \******************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultExecutionEventBusManager: () => (/* binding */ DefaultExecutionEventBusManager)\n/* harmony export */ });\n/* harmony import */ var _execution_event_bus_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./execution_event_bus.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus.js\");\n\nclass DefaultExecutionEventBusManager {\n    taskIdToBus = new Map();\n    /**\n     * Creates or retrieves an existing ExecutionEventBus based on the taskId.\n     * @param taskId The ID of the task.\n     * @returns An instance of IExecutionEventBus.\n     */\n    createOrGetByTaskId(taskId) {\n        if (!this.taskIdToBus.has(taskId)) {\n            this.taskIdToBus.set(taskId, new _execution_event_bus_js__WEBPACK_IMPORTED_MODULE_0__.DefaultExecutionEventBus());\n        }\n        return this.taskIdToBus.get(taskId);\n    }\n    /**\n     * Retrieves an existing ExecutionEventBus based on the taskId.\n     * @param taskId The ID of the task.\n     * @returns An instance of IExecutionEventBus or undefined if not found.\n     */\n    getByTaskId(taskId) {\n        return this.taskIdToBus.get(taskId);\n    }\n    /**\n     * Removes the event bus for a given taskId.\n     * This should be called when an execution flow is complete to free resources.\n     * @param taskId The ID of the task.\n     */\n    cleanupByTaskId(taskId) {\n        const bus = this.taskIdToBus.get(taskId);\n        if (bus) {\n            bus.removeAllListeners();\n        }\n        this.taskIdToBus.delete(taskId);\n    }\n}\n//# sourceMappingURL=execution_event_bus_manager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus_manager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_queue.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_queue.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ExecutionEventQueue: () => (/* binding */ ExecutionEventQueue)\n/* harmony export */ });\n/**\n * An async queue that subscribes to an ExecutionEventBus for events\n * and provides an async generator to consume them.\n */\nclass ExecutionEventQueue {\n    eventBus;\n    eventQueue = [];\n    resolvePromise;\n    stopped = false;\n    boundHandleEvent;\n    constructor(eventBus) {\n        this.eventBus = eventBus;\n        this.eventBus.on('event', this.handleEvent);\n        this.eventBus.on('finished', this.handleFinished);\n    }\n    handleEvent = (event) => {\n        if (this.stopped)\n            return;\n        this.eventQueue.push(event);\n        if (this.resolvePromise) {\n            this.resolvePromise();\n            this.resolvePromise = undefined;\n        }\n    };\n    handleFinished = () => {\n        this.stop();\n    };\n    /**\n     * Provides an async generator that yields events from the event bus.\n     * Stops when a Message event is received or a TaskStatusUpdateEvent with final=true is received.\n     */\n    async *events() {\n        while (!this.stopped || this.eventQueue.length > 0) {\n            if (this.eventQueue.length > 0) {\n                const event = this.eventQueue.shift();\n                yield event;\n                if (event.kind === 'message' || (event.kind === 'status-update' &&\n                    event.final)) {\n                    this.handleFinished();\n                    break;\n                }\n            }\n            else if (!this.stopped) {\n                await new Promise((resolve) => {\n                    this.resolvePromise = resolve;\n                });\n            }\n        }\n    }\n    /**\n     * Stops the event queue from processing further events.\n     */\n    stop() {\n        this.stopped = true;\n        if (this.resolvePromise) {\n            this.resolvePromise(); // Unblock any pending await\n            this.resolvePromise = undefined;\n        }\n        this.eventBus.off('event', this.handleEvent);\n        this.eventBus.off('finished', this.handleFinished);\n    }\n}\n//# sourceMappingURL=execution_event_queue.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_queue.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/request_handler/default_request_handler.js":
/*!***********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/request_handler/default_request_handler.js ***!
  \***********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DefaultRequestHandler: () => (/* binding */ DefaultRequestHandler)\n/* harmony export */ });\n/* harmony import */ var uuid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! uuid */ \"(rsc)/./node_modules/.pnpm/uuid@11.1.0/node_modules/uuid/dist/esm/v4.js\");\n/* harmony import */ var _agent_execution_request_context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../agent_execution/request_context.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/agent_execution/request_context.js\");\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../error.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js\");\n/* harmony import */ var _events_execution_event_bus_manager_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../events/execution_event_bus_manager.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_bus_manager.js\");\n/* harmony import */ var _events_execution_event_queue_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../events/execution_event_queue.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/events/execution_event_queue.js\");\n/* harmony import */ var _result_manager_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../result_manager.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/result_manager.js\");\n // For generating unique IDs\n\n\n\n\n\nconst terminalStates = [\"completed\", \"failed\", \"canceled\", \"rejected\"];\nclass DefaultRequestHandler {\n    agentCard;\n    taskStore;\n    agentExecutor;\n    eventBusManager;\n    // Store for push notification configurations (could be part of TaskStore or separate)\n    pushNotificationConfigs = new Map();\n    constructor(agentCard, taskStore, agentExecutor, eventBusManager = new _events_execution_event_bus_manager_js__WEBPACK_IMPORTED_MODULE_2__.DefaultExecutionEventBusManager()) {\n        this.agentCard = agentCard;\n        this.taskStore = taskStore;\n        this.agentExecutor = agentExecutor;\n        this.eventBusManager = eventBusManager;\n    }\n    async getAgentCard() {\n        return this.agentCard;\n    }\n    async _createRequestContext(incomingMessage, taskId, isStream) {\n        let task;\n        let referenceTasks;\n        // incomingMessage would contain taskId, if a task already exists.\n        if (incomingMessage.taskId) {\n            task = await this.taskStore.load(incomingMessage.taskId);\n            if (!task) {\n                throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(incomingMessage.taskId);\n            }\n            if (terminalStates.includes(task.status.state)) {\n                // Throw an error that conforms to the JSON-RPC Invalid Request error specification.\n                throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.invalidRequest(`Task ${task.id} is in a terminal state (${task.status.state}) and cannot be modified.`);\n            }\n        }\n        if (incomingMessage.referenceTaskIds && incomingMessage.referenceTaskIds.length > 0) {\n            referenceTasks = [];\n            for (const refId of incomingMessage.referenceTaskIds) {\n                const refTask = await this.taskStore.load(refId);\n                if (refTask) {\n                    referenceTasks.push(refTask);\n                }\n                else {\n                    console.warn(`Reference task ${refId} not found.`);\n                    // Optionally, throw an error or handle as per specific requirements\n                }\n            }\n        }\n        // Ensure contextId is present\n        const messageForContext = { ...incomingMessage };\n        if (!messageForContext.contextId) {\n            messageForContext.contextId = task?.contextId || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        }\n        const contextId = incomingMessage.contextId || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        return new _agent_execution_request_context_js__WEBPACK_IMPORTED_MODULE_0__.RequestContext(messageForContext, taskId, contextId, task, referenceTasks);\n    }\n    async _processEvents(taskId, resultManager, eventQueue, options) {\n        let firstResultSent = false;\n        try {\n            for await (const event of eventQueue.events()) {\n                await resultManager.processEvent(event);\n                if (options?.firstResultResolver && !firstResultSent) {\n                    if (event.kind === 'message' || event.kind === 'task') {\n                        options.firstResultResolver(event);\n                        firstResultSent = true;\n                    }\n                }\n            }\n            if (options?.firstResultRejector && !firstResultSent) {\n                options.firstResultRejector(_error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.internalError('Execution finished before a message or task was produced.'));\n            }\n        }\n        catch (error) {\n            console.error(`Event processing loop failed for task ${taskId}:`, error);\n            if (options?.firstResultRejector && !firstResultSent) {\n                options.firstResultRejector(error);\n            }\n            // re-throw error for blocking case to catch\n            throw error;\n        }\n        finally {\n            this.eventBusManager.cleanupByTaskId(taskId);\n        }\n    }\n    async sendMessage(params) {\n        const incomingMessage = params.message;\n        if (!incomingMessage.messageId) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.invalidParams('message.messageId is required.');\n        }\n        // Default to blocking behavior if 'blocking' is not explicitly false.\n        const isBlocking = params.configuration?.blocking !== false;\n        const taskId = incomingMessage.taskId || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        // Instantiate ResultManager before creating RequestContext\n        const resultManager = new _result_manager_js__WEBPACK_IMPORTED_MODULE_4__.ResultManager(this.taskStore);\n        resultManager.setContext(incomingMessage); // Set context for ResultManager\n        const requestContext = await this._createRequestContext(incomingMessage, taskId, false);\n        // Use the (potentially updated) contextId from requestContext\n        const finalMessageForAgent = requestContext.userMessage;\n        const eventBus = this.eventBusManager.createOrGetByTaskId(taskId);\n        // EventQueue should be attached to the bus, before the agent execution begins.\n        const eventQueue = new _events_execution_event_queue_js__WEBPACK_IMPORTED_MODULE_3__.ExecutionEventQueue(eventBus);\n        // Start agent execution (non-blocking).\n        // It runs in the background and publishes events to the eventBus.\n        this.agentExecutor.execute(requestContext, eventBus).catch(err => {\n            console.error(`Agent execution failed for message ${finalMessageForAgent.messageId}:`, err);\n            // Publish a synthetic error event, which will be handled by the ResultManager\n            // and will also settle the firstResultPromise for non-blocking calls.\n            const errorTask = {\n                id: requestContext.task?.id || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(), // Use existing task ID or generate new\n                contextId: finalMessageForAgent.contextId,\n                status: {\n                    state: \"failed\",\n                    message: {\n                        kind: \"message\",\n                        role: \"agent\",\n                        messageId: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                        parts: [{ kind: \"text\", text: `Agent execution error: ${err.message}` }],\n                        taskId: requestContext.task?.id,\n                        contextId: finalMessageForAgent.contextId,\n                    },\n                    timestamp: new Date().toISOString(),\n                },\n                history: requestContext.task?.history ? [...requestContext.task.history] : [],\n                kind: \"task\",\n            };\n            if (finalMessageForAgent) { // Add incoming message to history\n                if (!errorTask.history?.find(m => m.messageId === finalMessageForAgent.messageId)) {\n                    errorTask.history?.push(finalMessageForAgent);\n                }\n            }\n            eventBus.publish(errorTask);\n            eventBus.publish({\n                kind: \"status-update\",\n                taskId: errorTask.id,\n                contextId: errorTask.contextId,\n                status: errorTask.status,\n                final: true,\n            });\n            eventBus.finished();\n        });\n        if (isBlocking) {\n            // In blocking mode, wait for the full processing to complete.\n            await this._processEvents(taskId, resultManager, eventQueue);\n            const finalResult = resultManager.getFinalResult();\n            if (!finalResult) {\n                throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.internalError('Agent execution finished without a result, and no task context found.');\n            }\n            return finalResult;\n        }\n        else {\n            // In non-blocking mode, return a promise that will be settled by fullProcessing.\n            return new Promise((resolve, reject) => {\n                this._processEvents(taskId, resultManager, eventQueue, {\n                    firstResultResolver: resolve,\n                    firstResultRejector: reject,\n                });\n            });\n        }\n    }\n    async *sendMessageStream(params) {\n        const incomingMessage = params.message;\n        if (!incomingMessage.messageId) {\n            // For streams, messageId might be set by client, or server can generate if not present.\n            // Let's assume client provides it or throw for now.\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.invalidParams('message.messageId is required for streaming.');\n        }\n        const taskId = incomingMessage.taskId || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])();\n        // Instantiate ResultManager before creating RequestContext\n        const resultManager = new _result_manager_js__WEBPACK_IMPORTED_MODULE_4__.ResultManager(this.taskStore);\n        resultManager.setContext(incomingMessage); // Set context for ResultManager\n        const requestContext = await this._createRequestContext(incomingMessage, taskId, true);\n        const finalMessageForAgent = requestContext.userMessage;\n        const eventBus = this.eventBusManager.createOrGetByTaskId(taskId);\n        const eventQueue = new _events_execution_event_queue_js__WEBPACK_IMPORTED_MODULE_3__.ExecutionEventQueue(eventBus);\n        // Start agent execution (non-blocking)\n        this.agentExecutor.execute(requestContext, eventBus).catch(err => {\n            console.error(`Agent execution failed for stream message ${finalMessageForAgent.messageId}:`, err);\n            // Publish a synthetic error event if needed\n            const errorTaskStatus = {\n                kind: \"status-update\",\n                taskId: requestContext.task?.id || (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(), // Use existing or a placeholder\n                contextId: finalMessageForAgent.contextId,\n                status: {\n                    state: \"failed\",\n                    message: {\n                        kind: \"message\",\n                        role: \"agent\",\n                        messageId: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                        parts: [{ kind: \"text\", text: `Agent execution error: ${err.message}` }],\n                        taskId: requestContext.task?.id,\n                        contextId: finalMessageForAgent.contextId,\n                    },\n                    timestamp: new Date().toISOString(),\n                },\n                final: true, // This will terminate the stream for the client\n            };\n            eventBus.publish(errorTaskStatus);\n        });\n        try {\n            for await (const event of eventQueue.events()) {\n                await resultManager.processEvent(event); // Update store in background\n                yield event; // Stream the event to the client\n            }\n        }\n        finally {\n            // Cleanup when the stream is fully consumed or breaks\n            this.eventBusManager.cleanupByTaskId(taskId);\n        }\n    }\n    async getTask(params) {\n        const task = await this.taskStore.load(params.id);\n        if (!task) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(params.id);\n        }\n        if (params.historyLength !== undefined && params.historyLength >= 0) {\n            if (task.history) {\n                task.history = task.history.slice(-params.historyLength);\n            }\n        }\n        else {\n            // Negative or invalid historyLength means no history\n            task.history = [];\n        }\n        return task;\n    }\n    async cancelTask(params) {\n        const task = await this.taskStore.load(params.id);\n        if (!task) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(params.id);\n        }\n        // Check if task is in a cancelable state\n        const nonCancelableStates = [\"completed\", \"failed\", \"canceled\", \"rejected\"];\n        if (nonCancelableStates.includes(task.status.state)) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotCancelable(params.id);\n        }\n        const eventBus = this.eventBusManager.getByTaskId(params.id);\n        if (eventBus) {\n            await this.agentExecutor.cancelTask(params.id, eventBus);\n        }\n        else {\n            // Here we are marking task as cancelled. We are not waiting for the executor to actually cancel processing.\n            task.status = {\n                state: \"canceled\",\n                message: {\n                    kind: \"message\",\n                    role: \"agent\",\n                    messageId: (0,uuid__WEBPACK_IMPORTED_MODULE_5__[\"default\"])(),\n                    parts: [{ kind: \"text\", text: \"Task cancellation requested by user.\" }],\n                    taskId: task.id,\n                    contextId: task.contextId,\n                },\n                timestamp: new Date().toISOString(),\n            };\n            // Add cancellation message to history\n            task.history = [...(task.history || []), task.status.message];\n            await this.taskStore.save(task);\n        }\n        const latestTask = await this.taskStore.load(params.id);\n        return latestTask;\n    }\n    async setTaskPushNotificationConfig(params) {\n        if (!this.agentCard.capabilities.pushNotifications) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.pushNotificationNotSupported();\n        }\n        const taskAndHistory = await this.taskStore.load(params.taskId);\n        if (!taskAndHistory) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(params.taskId);\n        }\n        // Store the config. In a real app, this might be stored in the TaskStore\n        // or a dedicated push notification service.\n        this.pushNotificationConfigs.set(params.taskId, params.pushNotificationConfig);\n        return params;\n    }\n    async getTaskPushNotificationConfig(params) {\n        if (!this.agentCard.capabilities.pushNotifications) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.pushNotificationNotSupported();\n        }\n        const taskAndHistory = await this.taskStore.load(params.id); // Ensure task exists\n        if (!taskAndHistory) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(params.id);\n        }\n        const config = this.pushNotificationConfigs.get(params.id);\n        if (!config) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.internalError(`Push notification config not found for task ${params.id}.`);\n        }\n        return { taskId: params.id, pushNotificationConfig: config };\n    }\n    async *resubscribe(params) {\n        if (!this.agentCard.capabilities.streaming) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.unsupportedOperation(\"Streaming (and thus resubscription) is not supported.\");\n        }\n        const task = await this.taskStore.load(params.id);\n        if (!task) {\n            throw _error_js__WEBPACK_IMPORTED_MODULE_1__.A2AError.taskNotFound(params.id);\n        }\n        // Yield the current task state first\n        yield task;\n        // If task is already in a final state, no more events will come.\n        const finalStates = [\"completed\", \"failed\", \"canceled\", \"rejected\"];\n        if (finalStates.includes(task.status.state)) {\n            return;\n        }\n        const eventBus = this.eventBusManager.getByTaskId(params.id);\n        if (!eventBus) {\n            // No active execution for this task, so no live events.\n            console.warn(`Resubscribe: No active event bus for task ${params.id}.`);\n            return;\n        }\n        // Attach a new queue to the existing bus for this resubscription\n        const eventQueue = new _events_execution_event_queue_js__WEBPACK_IMPORTED_MODULE_3__.ExecutionEventQueue(eventBus);\n        // Note: The ResultManager part is already handled by the original execution flow.\n        // Resubscribe just listens for new events.\n        try {\n            for await (const event of eventQueue.events()) {\n                // We only care about updates related to *this* task.\n                // The event bus might be shared if messageId was reused, though\n                // ExecutionEventBusManager tries to give one bus per original message.\n                if (event.kind === 'status-update' && event.taskId === params.id) {\n                    yield event;\n                }\n                else if (event.kind === 'artifact-update' && event.taskId === params.id) {\n                    yield event;\n                }\n                else if (event.kind === 'task' && event.id === params.id) {\n                    // This implies the task was re-emitted, yield it.\n                    yield event;\n                }\n                // We don't yield 'message' events on resubscribe typically,\n                // as those signal the end of an interaction for the *original* request.\n                // If a 'message' event for the original request terminates the bus, this loop will also end.\n            }\n        }\n        finally {\n            eventQueue.stop();\n        }\n    }\n}\n//# sourceMappingURL=default_request_handler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/request_handler/default_request_handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/result_manager.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/result_manager.js ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ResultManager: () => (/* binding */ ResultManager)\n/* harmony export */ });\nclass ResultManager {\n    taskStore;\n    currentTask;\n    latestUserMessage; // To add to history if a new task is created\n    finalMessageResult; // Stores the message if it's the final result\n    constructor(taskStore) {\n        this.taskStore = taskStore;\n    }\n    setContext(latestUserMessage) {\n        this.latestUserMessage = latestUserMessage;\n    }\n    /**\n     * Processes an agent execution event and updates the task store.\n     * @param event The agent execution event.\n     */\n    async processEvent(event) {\n        if (event.kind === 'message') {\n            this.finalMessageResult = event;\n            // If a message is received, it's usually the final result,\n            // but we continue processing to ensure task state (if any) is also saved.\n            // The ExecutionEventQueue will stop after a message event.\n        }\n        else if (event.kind === 'task') {\n            const taskEvent = event;\n            this.currentTask = { ...taskEvent }; // Make a copy\n            // Ensure the latest user message is in history if not already present\n            if (this.latestUserMessage) {\n                if (!this.currentTask.history?.find(msg => msg.messageId === this.latestUserMessage.messageId)) {\n                    this.currentTask.history = [this.latestUserMessage, ...(this.currentTask.history || [])];\n                }\n            }\n            await this.saveCurrentTask();\n        }\n        else if (event.kind === 'status-update') {\n            const updateEvent = event;\n            if (this.currentTask && this.currentTask.id === updateEvent.taskId) {\n                this.currentTask.status = updateEvent.status;\n                if (updateEvent.status.message) {\n                    // Add message to history if not already present\n                    if (!this.currentTask.history?.find(msg => msg.messageId === updateEvent.status.message.messageId)) {\n                        this.currentTask.history = [...(this.currentTask.history || []), updateEvent.status.message];\n                    }\n                }\n                await this.saveCurrentTask();\n            }\n            else if (!this.currentTask && updateEvent.taskId) {\n                // Potentially an update for a task we haven't seen the 'task' event for yet,\n                // or we are rehydrating. Attempt to load.\n                const loaded = await this.taskStore.load(updateEvent.taskId);\n                if (loaded) {\n                    this.currentTask = loaded;\n                    this.currentTask.status = updateEvent.status;\n                    if (updateEvent.status.message) {\n                        if (!this.currentTask.history?.find(msg => msg.messageId === updateEvent.status.message.messageId)) {\n                            this.currentTask.history = [...(this.currentTask.history || []), updateEvent.status.message];\n                        }\n                    }\n                    await this.saveCurrentTask();\n                }\n                else {\n                    console.warn(`ResultManager: Received status update for unknown task ${updateEvent.taskId}`);\n                }\n            }\n            // If it's a final status update, the ExecutionEventQueue will stop.\n            // The final result will be the currentTask.\n        }\n        else if (event.kind === 'artifact-update') {\n            const artifactEvent = event;\n            if (this.currentTask && this.currentTask.id === artifactEvent.taskId) {\n                if (!this.currentTask.artifacts) {\n                    this.currentTask.artifacts = [];\n                }\n                const existingArtifactIndex = this.currentTask.artifacts.findIndex((art) => art.artifactId === artifactEvent.artifact.artifactId);\n                if (existingArtifactIndex !== -1) {\n                    if (artifactEvent.append) {\n                        // Basic append logic, assuming parts are compatible\n                        // More sophisticated merging might be needed for specific part types\n                        const existingArtifact = this.currentTask.artifacts[existingArtifactIndex];\n                        existingArtifact.parts.push(...artifactEvent.artifact.parts);\n                        if (artifactEvent.artifact.description)\n                            existingArtifact.description = artifactEvent.artifact.description;\n                        if (artifactEvent.artifact.name)\n                            existingArtifact.name = artifactEvent.artifact.name;\n                        if (artifactEvent.artifact.metadata)\n                            existingArtifact.metadata = { ...existingArtifact.metadata, ...artifactEvent.artifact.metadata };\n                    }\n                    else {\n                        this.currentTask.artifacts[existingArtifactIndex] = artifactEvent.artifact;\n                    }\n                }\n                else {\n                    this.currentTask.artifacts.push(artifactEvent.artifact);\n                }\n                await this.saveCurrentTask();\n            }\n            else if (!this.currentTask && artifactEvent.taskId) {\n                // Similar to status update, try to load if task not in memory\n                const loaded = await this.taskStore.load(artifactEvent.taskId);\n                if (loaded) {\n                    this.currentTask = loaded;\n                    if (!this.currentTask.artifacts)\n                        this.currentTask.artifacts = [];\n                    // Apply artifact update logic (as above)\n                    const existingArtifactIndex = this.currentTask.artifacts.findIndex((art) => art.artifactId === artifactEvent.artifact.artifactId);\n                    if (existingArtifactIndex !== -1) {\n                        if (artifactEvent.append) {\n                            this.currentTask.artifacts[existingArtifactIndex].parts.push(...artifactEvent.artifact.parts);\n                        }\n                        else {\n                            this.currentTask.artifacts[existingArtifactIndex] = artifactEvent.artifact;\n                        }\n                    }\n                    else {\n                        this.currentTask.artifacts.push(artifactEvent.artifact);\n                    }\n                    await this.saveCurrentTask();\n                }\n                else {\n                    console.warn(`ResultManager: Received artifact update for unknown task ${artifactEvent.taskId}`);\n                }\n            }\n        }\n    }\n    async saveCurrentTask() {\n        if (this.currentTask) {\n            await this.taskStore.save(this.currentTask);\n        }\n    }\n    /**\n     * Gets the final result, which could be a Message or a Task.\n     * This should be called after the event stream has been fully processed.\n     * @returns The final Message or the current Task.\n     */\n    getFinalResult() {\n        if (this.finalMessageResult) {\n            return this.finalMessageResult;\n        }\n        return this.currentTask;\n    }\n    /**\n     * Gets the task currently being managed by this ResultManager instance.\n     * This task could be one that was started with or one created during agent execution.\n     * @returns The current Task or undefined if no task is active.\n     */\n    getCurrentTask() {\n        return this.currentTask;\n    }\n}\n//# sourceMappingURL=result_manager.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/result_manager.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/store.js":
/*!*************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/store.js ***!
  \*************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InMemoryTaskStore: () => (/* binding */ InMemoryTaskStore)\n/* harmony export */ });\n// ========================\n// InMemoryTaskStore\n// ========================\n// Use Task directly for storage\nclass InMemoryTaskStore {\n    store = new Map();\n    async load(taskId) {\n        const entry = this.store.get(taskId);\n        // Return copies to prevent external mutation\n        return entry ? { ...entry } : undefined;\n    }\n    async save(task) {\n        // Store copies to prevent internal mutation if caller reuses objects\n        this.store.set(task.id, { ...task });\n    }\n}\n//# sourceMappingURL=store.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy9zZXJ2ZXIvc3RvcmUuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixXQUFXO0FBQ3BDO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyxTQUFTO0FBQzNDO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYTJhLWpzK3Nka0AwLjIuMi9ub2RlX21vZHVsZXMvQGEyYS1qcy9zZGsvYnVpbGQvc3JjL3NlcnZlci9zdG9yZS5qcz83ZDU4Il0sInNvdXJjZXNDb250ZW50IjpbIi8vID09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gSW5NZW1vcnlUYXNrU3RvcmVcbi8vID09PT09PT09PT09PT09PT09PT09PT09PVxuLy8gVXNlIFRhc2sgZGlyZWN0bHkgZm9yIHN0b3JhZ2VcbmV4cG9ydCBjbGFzcyBJbk1lbW9yeVRhc2tTdG9yZSB7XG4gICAgc3RvcmUgPSBuZXcgTWFwKCk7XG4gICAgYXN5bmMgbG9hZCh0YXNrSWQpIHtcbiAgICAgICAgY29uc3QgZW50cnkgPSB0aGlzLnN0b3JlLmdldCh0YXNrSWQpO1xuICAgICAgICAvLyBSZXR1cm4gY29waWVzIHRvIHByZXZlbnQgZXh0ZXJuYWwgbXV0YXRpb25cbiAgICAgICAgcmV0dXJuIGVudHJ5ID8geyAuLi5lbnRyeSB9IDogdW5kZWZpbmVkO1xuICAgIH1cbiAgICBhc3luYyBzYXZlKHRhc2spIHtcbiAgICAgICAgLy8gU3RvcmUgY29waWVzIHRvIHByZXZlbnQgaW50ZXJuYWwgbXV0YXRpb24gaWYgY2FsbGVyIHJldXNlcyBvYmplY3RzXG4gICAgICAgIHRoaXMuc3RvcmUuc2V0KHRhc2suaWQsIHsgLi4udGFzayB9KTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1zdG9yZS5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/store.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/transports/jsonrpc_transport_handler.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/transports/jsonrpc_transport_handler.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JsonRpcTransportHandler: () => (/* binding */ JsonRpcTransportHandler)\n/* harmony export */ });\n/* harmony import */ var _error_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../error.js */ \"(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/error.js\");\n\n/**\n * Handles JSON-RPC transport layer, routing requests to A2ARequestHandler.\n */\nclass JsonRpcTransportHandler {\n    requestHandler;\n    constructor(requestHandler) {\n        this.requestHandler = requestHandler;\n    }\n    /**\n     * Handles an incoming JSON-RPC request.\n     * For streaming methods, it returns an AsyncGenerator of JSONRPCResult.\n     * For non-streaming methods, it returns a Promise of a single JSONRPCMessage (Result or ErrorResponse).\n     */\n    async handle(requestBody) {\n        let rpcRequest;\n        try {\n            if (typeof requestBody === 'string') {\n                rpcRequest = JSON.parse(requestBody);\n            }\n            else if (typeof requestBody === 'object' && requestBody !== null) {\n                rpcRequest = requestBody;\n            }\n            else {\n                throw _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.parseError('Invalid request body type.');\n            }\n            if (rpcRequest.jsonrpc !== '2.0' ||\n                !rpcRequest.method ||\n                typeof rpcRequest.method !== 'string') {\n                throw _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.invalidRequest('Invalid JSON-RPC request structure.');\n            }\n        }\n        catch (error) {\n            const a2aError = error instanceof _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError ? error : _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.parseError(error.message || 'Failed to parse JSON request.');\n            return {\n                jsonrpc: '2.0',\n                id: (typeof rpcRequest?.id !== 'undefined' ? rpcRequest.id : null),\n                error: a2aError.toJSONRPCError(),\n            };\n        }\n        const { method, params = {}, id: requestId = null } = rpcRequest;\n        try {\n            if (method === 'message/stream' || method === 'tasks/resubscribe') {\n                const agentCard = await this.requestHandler.getAgentCard();\n                if (!agentCard.capabilities.streaming) {\n                    throw _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.unsupportedOperation(`Method ${method} requires streaming capability.`);\n                }\n                const agentEventStream = method === 'message/stream'\n                    ? this.requestHandler.sendMessageStream(params)\n                    : this.requestHandler.resubscribe(params);\n                // Wrap the agent event stream into a JSON-RPC result stream\n                return (async function* jsonRpcEventStream() {\n                    try {\n                        for await (const event of agentEventStream) {\n                            yield {\n                                jsonrpc: '2.0',\n                                id: requestId, // Use the original request ID for all streamed responses\n                                result: event,\n                            };\n                        }\n                    }\n                    catch (streamError) {\n                        // If the underlying agent stream throws an error, we need to yield a JSONRPCErrorResponse.\n                        // However, an AsyncGenerator is expected to yield JSONRPCResult.\n                        // This indicates an issue with how errors from the agent's stream are propagated.\n                        // For now, log it. The Express layer will handle the generator ending.\n                        console.error(`Error in agent event stream for ${method} (request ${requestId}):`, streamError);\n                        // Ideally, the Express layer should catch this and send a final error to the client if the stream breaks.\n                        // Or, the agentEventStream itself should yield a final error event that gets wrapped.\n                        // For now, we re-throw so it can be caught by A2AExpressApp's stream handling.\n                        throw streamError;\n                    }\n                })();\n            }\n            else {\n                // Handle non-streaming methods\n                let result;\n                switch (method) {\n                    case 'message/send':\n                        result = await this.requestHandler.sendMessage(params);\n                        break;\n                    case 'tasks/get':\n                        result = await this.requestHandler.getTask(params);\n                        break;\n                    case 'tasks/cancel':\n                        result = await this.requestHandler.cancelTask(params);\n                        break;\n                    case 'tasks/pushNotificationConfig/set':\n                        result = await this.requestHandler.setTaskPushNotificationConfig(params);\n                        break;\n                    case 'tasks/pushNotificationConfig/get':\n                        result = await this.requestHandler.getTaskPushNotificationConfig(params);\n                        break;\n                    default:\n                        throw _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.methodNotFound(method);\n                }\n                return {\n                    jsonrpc: '2.0',\n                    id: requestId,\n                    result: result,\n                };\n            }\n        }\n        catch (error) {\n            const a2aError = error instanceof _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError ? error : _error_js__WEBPACK_IMPORTED_MODULE_0__.A2AError.internalError(error.message || 'An unexpected error occurred.');\n            return {\n                jsonrpc: '2.0',\n                id: requestId,\n                error: a2aError.toJSONRPCError(),\n            };\n        }\n    }\n}\n//# sourceMappingURL=jsonrpc_transport_handler.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/server/transports/jsonrpc_transport_handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/types.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/types.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* eslint-disable */\n/**\n * This file was automatically generated by json-schema-to-typescript.\n * DO NOT MODIFY IT BY HAND. Instead, modify the source JSONSchema file,\n * and run json-schema-to-typescript to regenerate this file.\n */\n\n//# sourceMappingURL=types.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGEyYS1qcytzZGtAMC4yLjIvbm9kZV9tb2R1bGVzL0BhMmEtanMvc2RrL2J1aWxkL3NyYy90eXBlcy5qcyIsIm1hcHBpbmdzIjoiO0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ1U7QUFDViIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYTJhLWpzK3Nka0AwLjIuMi9ub2RlX21vZHVsZXMvQGEyYS1qcy9zZGsvYnVpbGQvc3JjL3R5cGVzLmpzP2MzNGIiXSwic291cmNlc0NvbnRlbnQiOlsiLyogZXNsaW50LWRpc2FibGUgKi9cbi8qKlxuICogVGhpcyBmaWxlIHdhcyBhdXRvbWF0aWNhbGx5IGdlbmVyYXRlZCBieSBqc29uLXNjaGVtYS10by10eXBlc2NyaXB0LlxuICogRE8gTk9UIE1PRElGWSBJVCBCWSBIQU5ELiBJbnN0ZWFkLCBtb2RpZnkgdGhlIHNvdXJjZSBKU09OU2NoZW1hIGZpbGUsXG4gKiBhbmQgcnVuIGpzb24tc2NoZW1hLXRvLXR5cGVzY3JpcHQgdG8gcmVnZW5lcmF0ZSB0aGlzIGZpbGUuXG4gKi9cbmV4cG9ydCB7fTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXR5cGVzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@a2a-js+sdk@0.2.2/node_modules/@a2a-js/sdk/build/src/types.js\n");

/***/ })

};
;