"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/express@4.21.2";
exports.ids = ["vendor-chunks/express@4.21.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\nmodule.exports = __webpack_require__(/*! ./lib/express */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/express.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXhwcmVzc0A0LjIxLjIvbm9kZV9tb2R1bGVzL2V4cHJlc3MvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7O0FBRWIsMElBQXlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL2V4cHJlc3NANC4yMS4yL25vZGVfbW9kdWxlcy9leHByZXNzL2luZGV4LmpzPzExOTIiXSwic291cmNlc0NvbnRlbnQiOlsiLyohXG4gKiBleHByZXNzXG4gKiBDb3B5cmlnaHQoYykgMjAwOS0yMDEzIFRKIEhvbG93YXljaHVrXG4gKiBDb3B5cmlnaHQoYykgMjAxMyBSb21hbiBTaHR5bG1hblxuICogQ29weXJpZ2h0KGMpIDIwMTQtMjAxNSBEb3VnbGFzIENocmlzdG9waGVyIFdpbHNvblxuICogTUlUIExpY2Vuc2VkXG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbGliL2V4cHJlc3MnKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/application.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/application.js ***!
  \***********************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar finalhandler = __webpack_require__(/*! finalhandler */ \"(rsc)/./node_modules/.pnpm/finalhandler@1.3.1/node_modules/finalhandler/index.js\");\nvar Router = __webpack_require__(/*! ./router */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js\");\nvar methods = __webpack_require__(/*! methods */ \"(rsc)/./node_modules/.pnpm/methods@1.1.2/node_modules/methods/index.js\");\nvar middleware = __webpack_require__(/*! ./middleware/init */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/init.js\");\nvar query = __webpack_require__(/*! ./middleware/query */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/query.js\");\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('express:application');\nvar View = __webpack_require__(/*! ./view */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/view.js\");\nvar http = __webpack_require__(/*! http */ \"http\");\nvar compileETag = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").compileETag);\nvar compileQueryParser = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").compileQueryParser);\nvar compileTrust = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").compileTrust);\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('express');\nvar flatten = __webpack_require__(/*! array-flatten */ \"(rsc)/./node_modules/.pnpm/array-flatten@1.1.1/node_modules/array-flatten/array-flatten.js\");\nvar merge = __webpack_require__(/*! utils-merge */ \"(rsc)/./node_modules/.pnpm/utils-merge@1.0.1/node_modules/utils-merge/index.js\");\nvar resolve = (__webpack_require__(/*! path */ \"path\").resolve);\nvar setPrototypeOf = __webpack_require__(/*! setprototypeof */ \"(rsc)/./node_modules/.pnpm/setprototypeof@1.2.0/node_modules/setprototypeof/index.js\")\n\n/**\n * Module variables.\n * @private\n */\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty\nvar slice = Array.prototype.slice;\n\n/**\n * Application prototype.\n */\n\nvar app = exports = module.exports = {};\n\n/**\n * Variable for trust proxy inheritance back-compat\n * @private\n */\n\nvar trustProxyDefaultSymbol = '@@symbol:trust_proxy_default';\n\n/**\n * Initialize the server.\n *\n *   - setup default configuration\n *   - setup default middleware\n *   - setup route reflection methods\n *\n * @private\n */\n\napp.init = function init() {\n  this.cache = {};\n  this.engines = {};\n  this.settings = {};\n\n  this.defaultConfiguration();\n};\n\n/**\n * Initialize application configuration.\n * @private\n */\n\napp.defaultConfiguration = function defaultConfiguration() {\n  var env = \"development\" || 0;\n\n  // default settings\n  this.enable('x-powered-by');\n  this.set('etag', 'weak');\n  this.set('env', env);\n  this.set('query parser', 'extended');\n  this.set('subdomain offset', 2);\n  this.set('trust proxy', false);\n\n  // trust proxy inherit back-compat\n  Object.defineProperty(this.settings, trustProxyDefaultSymbol, {\n    configurable: true,\n    value: true\n  });\n\n  debug('booting in %s mode', env);\n\n  this.on('mount', function onmount(parent) {\n    // inherit trust proxy\n    if (this.settings[trustProxyDefaultSymbol] === true\n      && typeof parent.settings['trust proxy fn'] === 'function') {\n      delete this.settings['trust proxy'];\n      delete this.settings['trust proxy fn'];\n    }\n\n    // inherit protos\n    setPrototypeOf(this.request, parent.request)\n    setPrototypeOf(this.response, parent.response)\n    setPrototypeOf(this.engines, parent.engines)\n    setPrototypeOf(this.settings, parent.settings)\n  });\n\n  // setup locals\n  this.locals = Object.create(null);\n\n  // top-most app is mounted at /\n  this.mountpath = '/';\n\n  // default locals\n  this.locals.settings = this.settings;\n\n  // default configuration\n  this.set('view', View);\n  this.set('views', resolve('views'));\n  this.set('jsonp callback name', 'callback');\n\n  if (env === 'production') {\n    this.enable('view cache');\n  }\n\n  Object.defineProperty(this, 'router', {\n    get: function() {\n      throw new Error('\\'app.router\\' is deprecated!\\nPlease see the 3.x to 4.x migration guide for details on how to update your app.');\n    }\n  });\n};\n\n/**\n * lazily adds the base router if it has not yet been added.\n *\n * We cannot add the base router in the defaultConfiguration because\n * it reads app settings which might be set after that has run.\n *\n * @private\n */\napp.lazyrouter = function lazyrouter() {\n  if (!this._router) {\n    this._router = new Router({\n      caseSensitive: this.enabled('case sensitive routing'),\n      strict: this.enabled('strict routing')\n    });\n\n    this._router.use(query(this.get('query parser fn')));\n    this._router.use(middleware.init(this));\n  }\n};\n\n/**\n * Dispatch a req, res pair into the application. Starts pipeline processing.\n *\n * If no callback is provided, then default error handlers will respond\n * in the event of an error bubbling through the stack.\n *\n * @private\n */\n\napp.handle = function handle(req, res, callback) {\n  var router = this._router;\n\n  // final handler\n  var done = callback || finalhandler(req, res, {\n    env: this.get('env'),\n    onerror: logerror.bind(this)\n  });\n\n  // no routes\n  if (!router) {\n    debug('no routes defined on app');\n    done();\n    return;\n  }\n\n  router.handle(req, res, done);\n};\n\n/**\n * Proxy `Router#use()` to add middleware to the app router.\n * See Router#use() documentation for details.\n *\n * If the _fn_ parameter is an express app, then it will be\n * mounted at the _route_ specified.\n *\n * @public\n */\n\napp.use = function use(fn) {\n  var offset = 0;\n  var path = '/';\n\n  // default path to '/'\n  // disambiguate app.use([fn])\n  if (typeof fn !== 'function') {\n    var arg = fn;\n\n    while (Array.isArray(arg) && arg.length !== 0) {\n      arg = arg[0];\n    }\n\n    // first arg is the path\n    if (typeof arg !== 'function') {\n      offset = 1;\n      path = fn;\n    }\n  }\n\n  var fns = flatten(slice.call(arguments, offset));\n\n  if (fns.length === 0) {\n    throw new TypeError('app.use() requires a middleware function')\n  }\n\n  // setup router\n  this.lazyrouter();\n  var router = this._router;\n\n  fns.forEach(function (fn) {\n    // non-express app\n    if (!fn || !fn.handle || !fn.set) {\n      return router.use(path, fn);\n    }\n\n    debug('.use app under %s', path);\n    fn.mountpath = path;\n    fn.parent = this;\n\n    // restore .app property on req and res\n    router.use(path, function mounted_app(req, res, next) {\n      var orig = req.app;\n      fn.handle(req, res, function (err) {\n        setPrototypeOf(req, orig.request)\n        setPrototypeOf(res, orig.response)\n        next(err);\n      });\n    });\n\n    // mounted an app\n    fn.emit('mount', this);\n  }, this);\n\n  return this;\n};\n\n/**\n * Proxy to the app `Router#route()`\n * Returns a new `Route` instance for the _path_.\n *\n * Routes are isolated middleware stacks for specific paths.\n * See the Route api docs for details.\n *\n * @public\n */\n\napp.route = function route(path) {\n  this.lazyrouter();\n  return this._router.route(path);\n};\n\n/**\n * Register the given template engine callback `fn`\n * as `ext`.\n *\n * By default will `require()` the engine based on the\n * file extension. For example if you try to render\n * a \"foo.ejs\" file Express will invoke the following internally:\n *\n *     app.engine('ejs', require('ejs').__express);\n *\n * For engines that do not provide `.__express` out of the box,\n * or if you wish to \"map\" a different extension to the template engine\n * you may use this method. For example mapping the EJS template engine to\n * \".html\" files:\n *\n *     app.engine('html', require('ejs').renderFile);\n *\n * In this case EJS provides a `.renderFile()` method with\n * the same signature that Express expects: `(path, options, callback)`,\n * though note that it aliases this method as `ejs.__express` internally\n * so if you're using \".ejs\" extensions you don't need to do anything.\n *\n * Some template engines do not follow this convention, the\n * [Consolidate.js](https://github.com/tj/consolidate.js)\n * library was created to map all of node's popular template\n * engines to follow this convention, thus allowing them to\n * work seamlessly within Express.\n *\n * @param {String} ext\n * @param {Function} fn\n * @return {app} for chaining\n * @public\n */\n\napp.engine = function engine(ext, fn) {\n  if (typeof fn !== 'function') {\n    throw new Error('callback function required');\n  }\n\n  // get file extension\n  var extension = ext[0] !== '.'\n    ? '.' + ext\n    : ext;\n\n  // store engine\n  this.engines[extension] = fn;\n\n  return this;\n};\n\n/**\n * Proxy to `Router#param()` with one added api feature. The _name_ parameter\n * can be an array of names.\n *\n * See the Router#param() docs for more details.\n *\n * @param {String|Array} name\n * @param {Function} fn\n * @return {app} for chaining\n * @public\n */\n\napp.param = function param(name, fn) {\n  this.lazyrouter();\n\n  if (Array.isArray(name)) {\n    for (var i = 0; i < name.length; i++) {\n      this.param(name[i], fn);\n    }\n\n    return this;\n  }\n\n  this._router.param(name, fn);\n\n  return this;\n};\n\n/**\n * Assign `setting` to `val`, or return `setting`'s value.\n *\n *    app.set('foo', 'bar');\n *    app.set('foo');\n *    // => \"bar\"\n *\n * Mounted servers inherit their parent server's settings.\n *\n * @param {String} setting\n * @param {*} [val]\n * @return {Server} for chaining\n * @public\n */\n\napp.set = function set(setting, val) {\n  if (arguments.length === 1) {\n    // app.get(setting)\n    var settings = this.settings\n\n    while (settings && settings !== Object.prototype) {\n      if (hasOwnProperty.call(settings, setting)) {\n        return settings[setting]\n      }\n\n      settings = Object.getPrototypeOf(settings)\n    }\n\n    return undefined\n  }\n\n  debug('set \"%s\" to %o', setting, val);\n\n  // set value\n  this.settings[setting] = val;\n\n  // trigger matched settings\n  switch (setting) {\n    case 'etag':\n      this.set('etag fn', compileETag(val));\n      break;\n    case 'query parser':\n      this.set('query parser fn', compileQueryParser(val));\n      break;\n    case 'trust proxy':\n      this.set('trust proxy fn', compileTrust(val));\n\n      // trust proxy inherit back-compat\n      Object.defineProperty(this.settings, trustProxyDefaultSymbol, {\n        configurable: true,\n        value: false\n      });\n\n      break;\n  }\n\n  return this;\n};\n\n/**\n * Return the app's absolute pathname\n * based on the parent(s) that have\n * mounted it.\n *\n * For example if the application was\n * mounted as \"/admin\", which itself\n * was mounted as \"/blog\" then the\n * return value would be \"/blog/admin\".\n *\n * @return {String}\n * @private\n */\n\napp.path = function path() {\n  return this.parent\n    ? this.parent.path() + this.mountpath\n    : '';\n};\n\n/**\n * Check if `setting` is enabled (truthy).\n *\n *    app.enabled('foo')\n *    // => false\n *\n *    app.enable('foo')\n *    app.enabled('foo')\n *    // => true\n *\n * @param {String} setting\n * @return {Boolean}\n * @public\n */\n\napp.enabled = function enabled(setting) {\n  return Boolean(this.set(setting));\n};\n\n/**\n * Check if `setting` is disabled.\n *\n *    app.disabled('foo')\n *    // => true\n *\n *    app.enable('foo')\n *    app.disabled('foo')\n *    // => false\n *\n * @param {String} setting\n * @return {Boolean}\n * @public\n */\n\napp.disabled = function disabled(setting) {\n  return !this.set(setting);\n};\n\n/**\n * Enable `setting`.\n *\n * @param {String} setting\n * @return {app} for chaining\n * @public\n */\n\napp.enable = function enable(setting) {\n  return this.set(setting, true);\n};\n\n/**\n * Disable `setting`.\n *\n * @param {String} setting\n * @return {app} for chaining\n * @public\n */\n\napp.disable = function disable(setting) {\n  return this.set(setting, false);\n};\n\n/**\n * Delegate `.VERB(...)` calls to `router.VERB(...)`.\n */\n\nmethods.forEach(function(method){\n  app[method] = function(path){\n    if (method === 'get' && arguments.length === 1) {\n      // app.get(setting)\n      return this.set(path);\n    }\n\n    this.lazyrouter();\n\n    var route = this._router.route(path);\n    route[method].apply(route, slice.call(arguments, 1));\n    return this;\n  };\n});\n\n/**\n * Special-cased \"all\" method, applying the given route `path`,\n * middleware, and callback to _every_ HTTP method.\n *\n * @param {String} path\n * @param {Function} ...\n * @return {app} for chaining\n * @public\n */\n\napp.all = function all(path) {\n  this.lazyrouter();\n\n  var route = this._router.route(path);\n  var args = slice.call(arguments, 1);\n\n  for (var i = 0; i < methods.length; i++) {\n    route[methods[i]].apply(route, args);\n  }\n\n  return this;\n};\n\n// del -> delete alias\n\napp.del = deprecate.function(app.delete, 'app.del: Use app.delete instead');\n\n/**\n * Render the given view `name` name with `options`\n * and a callback accepting an error and the\n * rendered template string.\n *\n * Example:\n *\n *    app.render('email', { name: 'Tobi' }, function(err, html){\n *      // ...\n *    })\n *\n * @param {String} name\n * @param {Object|Function} options or fn\n * @param {Function} callback\n * @public\n */\n\napp.render = function render(name, options, callback) {\n  var cache = this.cache;\n  var done = callback;\n  var engines = this.engines;\n  var opts = options;\n  var renderOptions = {};\n  var view;\n\n  // support callback function as second arg\n  if (typeof options === 'function') {\n    done = options;\n    opts = {};\n  }\n\n  // merge app.locals\n  merge(renderOptions, this.locals);\n\n  // merge options._locals\n  if (opts._locals) {\n    merge(renderOptions, opts._locals);\n  }\n\n  // merge options\n  merge(renderOptions, opts);\n\n  // set .cache unless explicitly provided\n  if (renderOptions.cache == null) {\n    renderOptions.cache = this.enabled('view cache');\n  }\n\n  // primed cache\n  if (renderOptions.cache) {\n    view = cache[name];\n  }\n\n  // view\n  if (!view) {\n    var View = this.get('view');\n\n    view = new View(name, {\n      defaultEngine: this.get('view engine'),\n      root: this.get('views'),\n      engines: engines\n    });\n\n    if (!view.path) {\n      var dirs = Array.isArray(view.root) && view.root.length > 1\n        ? 'directories \"' + view.root.slice(0, -1).join('\", \"') + '\" or \"' + view.root[view.root.length - 1] + '\"'\n        : 'directory \"' + view.root + '\"'\n      var err = new Error('Failed to lookup view \"' + name + '\" in views ' + dirs);\n      err.view = view;\n      return done(err);\n    }\n\n    // prime the cache\n    if (renderOptions.cache) {\n      cache[name] = view;\n    }\n  }\n\n  // render\n  tryRender(view, renderOptions, done);\n};\n\n/**\n * Listen for connections.\n *\n * A node `http.Server` is returned, with this\n * application (which is a `Function`) as its\n * callback. If you wish to create both an HTTP\n * and HTTPS server you may do so with the \"http\"\n * and \"https\" modules as shown here:\n *\n *    var http = require('http')\n *      , https = require('https')\n *      , express = require('express')\n *      , app = express();\n *\n *    http.createServer(app).listen(80);\n *    https.createServer({ ... }, app).listen(443);\n *\n * @return {http.Server}\n * @public\n */\n\napp.listen = function listen() {\n  var server = http.createServer(this);\n  return server.listen.apply(server, arguments);\n};\n\n/**\n * Log error using console.error.\n *\n * @param {Error} err\n * @private\n */\n\nfunction logerror(err) {\n  /* istanbul ignore next */\n  if (this.get('env') !== 'test') console.error(err.stack || err.toString());\n}\n\n/**\n * Try rendering a view.\n * @private\n */\n\nfunction tryRender(view, options, callback) {\n  try {\n    view.render(options, callback);\n  } catch (err) {\n    callback(err);\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/application.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/express.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/express.js ***!
  \*******************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n */\n\nvar bodyParser = __webpack_require__(/*! body-parser */ \"(rsc)/./node_modules/.pnpm/body-parser@1.20.3/node_modules/body-parser/index.js\")\nvar EventEmitter = (__webpack_require__(/*! events */ \"events\").EventEmitter);\nvar mixin = __webpack_require__(/*! merge-descriptors */ \"(rsc)/./node_modules/.pnpm/merge-descriptors@1.0.3/node_modules/merge-descriptors/index.js\");\nvar proto = __webpack_require__(/*! ./application */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/application.js\");\nvar Route = __webpack_require__(/*! ./router/route */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js\");\nvar Router = __webpack_require__(/*! ./router */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js\");\nvar req = __webpack_require__(/*! ./request */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/request.js\");\nvar res = __webpack_require__(/*! ./response */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js\");\n\n/**\n * Expose `createApplication()`.\n */\n\nexports = module.exports = createApplication;\n\n/**\n * Create an express application.\n *\n * @return {Function}\n * @api public\n */\n\nfunction createApplication() {\n  var app = function(req, res, next) {\n    app.handle(req, res, next);\n  };\n\n  mixin(app, EventEmitter.prototype, false);\n  mixin(app, proto, false);\n\n  // expose the prototype that will get set on requests\n  app.request = Object.create(req, {\n    app: { configurable: true, enumerable: true, writable: true, value: app }\n  })\n\n  // expose the prototype that will get set on responses\n  app.response = Object.create(res, {\n    app: { configurable: true, enumerable: true, writable: true, value: app }\n  })\n\n  app.init();\n  return app;\n}\n\n/**\n * Expose the prototypes.\n */\n\nexports.application = proto;\nexports.request = req;\nexports.response = res;\n\n/**\n * Expose constructors.\n */\n\nexports.Route = Route;\nexports.Router = Router;\n\n/**\n * Expose middleware\n */\n\nexports.json = bodyParser.json\nexports.query = __webpack_require__(/*! ./middleware/query */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/query.js\");\nexports.raw = bodyParser.raw\nexports[\"static\"] = __webpack_require__(/*! serve-static */ \"(rsc)/./node_modules/.pnpm/serve-static@1.16.2/node_modules/serve-static/index.js\");\nexports.text = bodyParser.text\nexports.urlencoded = bodyParser.urlencoded\n\n/**\n * Replace removed middleware with an appropriate error message.\n */\n\nvar removedMiddlewares = [\n  'bodyParser',\n  'compress',\n  'cookieSession',\n  'session',\n  'logger',\n  'cookieParser',\n  'favicon',\n  'responseTime',\n  'errorHandler',\n  'timeout',\n  'methodOverride',\n  'vhost',\n  'csrf',\n  'directory',\n  'limit',\n  'multipart',\n  'staticCache'\n]\n\nremovedMiddlewares.forEach(function (name) {\n  Object.defineProperty(exports, name, {\n    get: function () {\n      throw new Error('Most middleware (like ' + name + ') is no longer bundled with Express and must be installed separately. Please see https://github.com/senchalabs/connect#middleware.');\n    },\n    configurable: true\n  });\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/express.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/init.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/init.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar setPrototypeOf = __webpack_require__(/*! setprototypeof */ \"(rsc)/./node_modules/.pnpm/setprototypeof@1.2.0/node_modules/setprototypeof/index.js\")\n\n/**\n * Initialization middleware, exposing the\n * request and response to each other, as well\n * as defaulting the X-Powered-By header field.\n *\n * @param {Function} app\n * @return {Function}\n * @api private\n */\n\nexports.init = function(app){\n  return function expressInit(req, res, next){\n    if (app.enabled('x-powered-by')) res.setHeader('X-Powered-By', 'Express');\n    req.res = res;\n    res.req = req;\n    req.next = next;\n\n    setPrototypeOf(req, app.request)\n    setPrototypeOf(res, app.response)\n\n    res.locals = res.locals || Object.create(null);\n\n    next();\n  };\n};\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXhwcmVzc0A0LjIxLjIvbm9kZV9tb2R1bGVzL2V4cHJlc3MvbGliL21pZGRsZXdhcmUvaW5pdC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxxQkFBcUIsbUJBQU8sQ0FBQyw0R0FBZ0I7O0FBRTdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFVBQVU7QUFDckIsWUFBWTtBQUNaO0FBQ0E7O0FBRUEsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vZXhwcmVzc0A0LjIxLjIvbm9kZV9tb2R1bGVzL2V4cHJlc3MvbGliL21pZGRsZXdhcmUvaW5pdC5qcz9iZGY2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogZXhwcmVzc1xuICogQ29weXJpZ2h0KGMpIDIwMDktMjAxMyBUSiBIb2xvd2F5Y2h1a1xuICogQ29weXJpZ2h0KGMpIDIwMTMgUm9tYW4gU2h0eWxtYW5cbiAqIENvcHlyaWdodChjKSAyMDE0LTIwMTUgRG91Z2xhcyBDaHJpc3RvcGhlciBXaWxzb25cbiAqIE1JVCBMaWNlbnNlZFxuICovXG5cbid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzLlxuICogQHByaXZhdGVcbiAqL1xuXG52YXIgc2V0UHJvdG90eXBlT2YgPSByZXF1aXJlKCdzZXRwcm90b3R5cGVvZicpXG5cbi8qKlxuICogSW5pdGlhbGl6YXRpb24gbWlkZGxld2FyZSwgZXhwb3NpbmcgdGhlXG4gKiByZXF1ZXN0IGFuZCByZXNwb25zZSB0byBlYWNoIG90aGVyLCBhcyB3ZWxsXG4gKiBhcyBkZWZhdWx0aW5nIHRoZSBYLVBvd2VyZWQtQnkgaGVhZGVyIGZpZWxkLlxuICpcbiAqIEBwYXJhbSB7RnVuY3Rpb259IGFwcFxuICogQHJldHVybiB7RnVuY3Rpb259XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLmluaXQgPSBmdW5jdGlvbihhcHApe1xuICByZXR1cm4gZnVuY3Rpb24gZXhwcmVzc0luaXQocmVxLCByZXMsIG5leHQpe1xuICAgIGlmIChhcHAuZW5hYmxlZCgneC1wb3dlcmVkLWJ5JykpIHJlcy5zZXRIZWFkZXIoJ1gtUG93ZXJlZC1CeScsICdFeHByZXNzJyk7XG4gICAgcmVxLnJlcyA9IHJlcztcbiAgICByZXMucmVxID0gcmVxO1xuICAgIHJlcS5uZXh0ID0gbmV4dDtcblxuICAgIHNldFByb3RvdHlwZU9mKHJlcSwgYXBwLnJlcXVlc3QpXG4gICAgc2V0UHJvdG90eXBlT2YocmVzLCBhcHAucmVzcG9uc2UpXG5cbiAgICByZXMubG9jYWxzID0gcmVzLmxvY2FscyB8fCBPYmplY3QuY3JlYXRlKG51bGwpO1xuXG4gICAgbmV4dCgpO1xuICB9O1xufTtcblxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/init.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/query.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/query.js ***!
  \****************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n */\n\nvar merge = __webpack_require__(/*! utils-merge */ \"(rsc)/./node_modules/.pnpm/utils-merge@1.0.1/node_modules/utils-merge/index.js\")\nvar parseUrl = __webpack_require__(/*! parseurl */ \"(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\");\nvar qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/.pnpm/qs@6.13.0/node_modules/qs/lib/index.js\");\n\n/**\n * @param {Object} options\n * @return {Function}\n * @api public\n */\n\nmodule.exports = function query(options) {\n  var opts = merge({}, options)\n  var queryparse = qs.parse;\n\n  if (typeof options === 'function') {\n    queryparse = options;\n    opts = undefined;\n  }\n\n  if (opts !== undefined && opts.allowPrototypes === undefined) {\n    // back-compat for qs module\n    opts.allowPrototypes = true;\n  }\n\n  return function query(req, res, next){\n    if (!req.query) {\n      var val = parseUrl(req).query;\n      req.query = queryparse(val, opts);\n    }\n\n    next();\n  };\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXhwcmVzc0A0LjIxLjIvbm9kZV9tb2R1bGVzL2V4cHJlc3MvbGliL21pZGRsZXdhcmUvcXVlcnkuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWE7O0FBRWI7QUFDQTtBQUNBOztBQUVBLFlBQVksbUJBQU8sQ0FBQyxtR0FBYTtBQUNqQyxlQUFlLG1CQUFPLENBQUMsMEZBQVU7QUFDakMsU0FBUyxtQkFBTyxDQUFDLDZFQUFJOztBQUVyQjtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQTtBQUNBLHFCQUFxQjtBQUNyQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9leHByZXNzQDQuMjEuMi9ub2RlX21vZHVsZXMvZXhwcmVzcy9saWIvbWlkZGxld2FyZS9xdWVyeS5qcz8xNmMxIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIVxuICogZXhwcmVzc1xuICogQ29weXJpZ2h0KGMpIDIwMDktMjAxMyBUSiBIb2xvd2F5Y2h1a1xuICogQ29weXJpZ2h0KGMpIDIwMTMgUm9tYW4gU2h0eWxtYW5cbiAqIENvcHlyaWdodChjKSAyMDE0LTIwMTUgRG91Z2xhcyBDaHJpc3RvcGhlciBXaWxzb25cbiAqIE1JVCBMaWNlbnNlZFxuICovXG5cbid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzLlxuICovXG5cbnZhciBtZXJnZSA9IHJlcXVpcmUoJ3V0aWxzLW1lcmdlJylcbnZhciBwYXJzZVVybCA9IHJlcXVpcmUoJ3BhcnNldXJsJyk7XG52YXIgcXMgPSByZXF1aXJlKCdxcycpO1xuXG4vKipcbiAqIEBwYXJhbSB7T2JqZWN0fSBvcHRpb25zXG4gKiBAcmV0dXJuIHtGdW5jdGlvbn1cbiAqIEBhcGkgcHVibGljXG4gKi9cblxubW9kdWxlLmV4cG9ydHMgPSBmdW5jdGlvbiBxdWVyeShvcHRpb25zKSB7XG4gIHZhciBvcHRzID0gbWVyZ2Uoe30sIG9wdGlvbnMpXG4gIHZhciBxdWVyeXBhcnNlID0gcXMucGFyc2U7XG5cbiAgaWYgKHR5cGVvZiBvcHRpb25zID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcXVlcnlwYXJzZSA9IG9wdGlvbnM7XG4gICAgb3B0cyA9IHVuZGVmaW5lZDtcbiAgfVxuXG4gIGlmIChvcHRzICE9PSB1bmRlZmluZWQgJiYgb3B0cy5hbGxvd1Byb3RvdHlwZXMgPT09IHVuZGVmaW5lZCkge1xuICAgIC8vIGJhY2stY29tcGF0IGZvciBxcyBtb2R1bGVcbiAgICBvcHRzLmFsbG93UHJvdG90eXBlcyA9IHRydWU7XG4gIH1cblxuICByZXR1cm4gZnVuY3Rpb24gcXVlcnkocmVxLCByZXMsIG5leHQpe1xuICAgIGlmICghcmVxLnF1ZXJ5KSB7XG4gICAgICB2YXIgdmFsID0gcGFyc2VVcmwocmVxKS5xdWVyeTtcbiAgICAgIHJlcS5xdWVyeSA9IHF1ZXJ5cGFyc2UodmFsLCBvcHRzKTtcbiAgICB9XG5cbiAgICBuZXh0KCk7XG4gIH07XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/middleware/query.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/request.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/request.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar accepts = __webpack_require__(/*! accepts */ \"(rsc)/./node_modules/.pnpm/accepts@1.3.8/node_modules/accepts/index.js\");\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('express');\nvar isIP = (__webpack_require__(/*! net */ \"net\").isIP);\nvar typeis = __webpack_require__(/*! type-is */ \"(rsc)/./node_modules/.pnpm/type-is@1.6.18/node_modules/type-is/index.js\");\nvar http = __webpack_require__(/*! http */ \"http\");\nvar fresh = __webpack_require__(/*! fresh */ \"(rsc)/./node_modules/.pnpm/fresh@0.5.2/node_modules/fresh/index.js\");\nvar parseRange = __webpack_require__(/*! range-parser */ \"(rsc)/./node_modules/.pnpm/range-parser@1.2.1/node_modules/range-parser/index.js\");\nvar parse = __webpack_require__(/*! parseurl */ \"(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\");\nvar proxyaddr = __webpack_require__(/*! proxy-addr */ \"(rsc)/./node_modules/.pnpm/proxy-addr@2.0.7/node_modules/proxy-addr/index.js\");\n\n/**\n * Request prototype.\n * @public\n */\n\nvar req = Object.create(http.IncomingMessage.prototype)\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = req\n\n/**\n * Return request header.\n *\n * The `Referrer` header field is special-cased,\n * both `Referrer` and `Referer` are interchangeable.\n *\n * Examples:\n *\n *     req.get('Content-Type');\n *     // => \"text/plain\"\n *\n *     req.get('content-type');\n *     // => \"text/plain\"\n *\n *     req.get('Something');\n *     // => undefined\n *\n * Aliased as `req.header()`.\n *\n * @param {String} name\n * @return {String}\n * @public\n */\n\nreq.get =\nreq.header = function header(name) {\n  if (!name) {\n    throw new TypeError('name argument is required to req.get');\n  }\n\n  if (typeof name !== 'string') {\n    throw new TypeError('name must be a string to req.get');\n  }\n\n  var lc = name.toLowerCase();\n\n  switch (lc) {\n    case 'referer':\n    case 'referrer':\n      return this.headers.referrer\n        || this.headers.referer;\n    default:\n      return this.headers[lc];\n  }\n};\n\n/**\n * To do: update docs.\n *\n * Check if the given `type(s)` is acceptable, returning\n * the best match when true, otherwise `undefined`, in which\n * case you should respond with 406 \"Not Acceptable\".\n *\n * The `type` value may be a single MIME type string\n * such as \"application/json\", an extension name\n * such as \"json\", a comma-delimited list such as \"json, html, text/plain\",\n * an argument list such as `\"json\", \"html\", \"text/plain\"`,\n * or an array `[\"json\", \"html\", \"text/plain\"]`. When a list\n * or array is given, the _best_ match, if any is returned.\n *\n * Examples:\n *\n *     // Accept: text/html\n *     req.accepts('html');\n *     // => \"html\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('html');\n *     // => \"html\"\n *     req.accepts('text/html');\n *     // => \"text/html\"\n *     req.accepts('json, text');\n *     // => \"json\"\n *     req.accepts('application/json');\n *     // => \"application/json\"\n *\n *     // Accept: text/*, application/json\n *     req.accepts('image/png');\n *     req.accepts('png');\n *     // => undefined\n *\n *     // Accept: text/*;q=.5, application/json\n *     req.accepts(['html', 'json']);\n *     req.accepts('html', 'json');\n *     req.accepts('html, json');\n *     // => \"json\"\n *\n * @param {String|Array} type(s)\n * @return {String|Array|Boolean}\n * @public\n */\n\nreq.accepts = function(){\n  var accept = accepts(this);\n  return accept.types.apply(accept, arguments);\n};\n\n/**\n * Check if the given `encoding`s are accepted.\n *\n * @param {String} ...encoding\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsEncodings = function(){\n  var accept = accepts(this);\n  return accept.encodings.apply(accept, arguments);\n};\n\nreq.acceptsEncoding = deprecate.function(req.acceptsEncodings,\n  'req.acceptsEncoding: Use acceptsEncodings instead');\n\n/**\n * Check if the given `charset`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...charset\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsCharsets = function(){\n  var accept = accepts(this);\n  return accept.charsets.apply(accept, arguments);\n};\n\nreq.acceptsCharset = deprecate.function(req.acceptsCharsets,\n  'req.acceptsCharset: Use acceptsCharsets instead');\n\n/**\n * Check if the given `lang`s are acceptable,\n * otherwise you should respond with 406 \"Not Acceptable\".\n *\n * @param {String} ...lang\n * @return {String|Array}\n * @public\n */\n\nreq.acceptsLanguages = function(){\n  var accept = accepts(this);\n  return accept.languages.apply(accept, arguments);\n};\n\nreq.acceptsLanguage = deprecate.function(req.acceptsLanguages,\n  'req.acceptsLanguage: Use acceptsLanguages instead');\n\n/**\n * Parse Range header field, capping to the given `size`.\n *\n * Unspecified ranges such as \"0-\" require knowledge of your resource length. In\n * the case of a byte range this is of course the total number of bytes. If the\n * Range header field is not given `undefined` is returned, `-1` when unsatisfiable,\n * and `-2` when syntactically invalid.\n *\n * When ranges are returned, the array has a \"type\" property which is the type of\n * range that is required (most commonly, \"bytes\"). Each array element is an object\n * with a \"start\" and \"end\" property for the portion of the range.\n *\n * The \"combine\" option can be set to `true` and overlapping & adjacent ranges\n * will be combined into a single range.\n *\n * NOTE: remember that ranges are inclusive, so for example \"Range: users=0-3\"\n * should respond with 4 users when available, not 3.\n *\n * @param {number} size\n * @param {object} [options]\n * @param {boolean} [options.combine=false]\n * @return {number|array}\n * @public\n */\n\nreq.range = function range(size, options) {\n  var range = this.get('Range');\n  if (!range) return;\n  return parseRange(size, range, options);\n};\n\n/**\n * Return the value of param `name` when present or `defaultValue`.\n *\n *  - Checks route placeholders, ex: _/user/:id_\n *  - Checks body params, ex: id=12, {\"id\":12}\n *  - Checks query string params, ex: ?id=12\n *\n * To utilize request bodies, `req.body`\n * should be an object. This can be done by using\n * the `bodyParser()` middleware.\n *\n * @param {String} name\n * @param {Mixed} [defaultValue]\n * @return {String}\n * @public\n */\n\nreq.param = function param(name, defaultValue) {\n  var params = this.params || {};\n  var body = this.body || {};\n  var query = this.query || {};\n\n  var args = arguments.length === 1\n    ? 'name'\n    : 'name, default';\n  deprecate('req.param(' + args + '): Use req.params, req.body, or req.query instead');\n\n  if (null != params[name] && params.hasOwnProperty(name)) return params[name];\n  if (null != body[name]) return body[name];\n  if (null != query[name]) return query[name];\n\n  return defaultValue;\n};\n\n/**\n * Check if the incoming request contains the \"Content-Type\"\n * header field, and it contains the given mime `type`.\n *\n * Examples:\n *\n *      // With Content-Type: text/html; charset=utf-8\n *      req.is('html');\n *      req.is('text/html');\n *      req.is('text/*');\n *      // => true\n *\n *      // When Content-Type is application/json\n *      req.is('json');\n *      req.is('application/json');\n *      req.is('application/*');\n *      // => true\n *\n *      req.is('html');\n *      // => false\n *\n * @param {String|Array} types...\n * @return {String|false|null}\n * @public\n */\n\nreq.is = function is(types) {\n  var arr = types;\n\n  // support flattened arguments\n  if (!Array.isArray(types)) {\n    arr = new Array(arguments.length);\n    for (var i = 0; i < arr.length; i++) {\n      arr[i] = arguments[i];\n    }\n  }\n\n  return typeis(this, arr);\n};\n\n/**\n * Return the protocol string \"http\" or \"https\"\n * when requested with TLS. When the \"trust proxy\"\n * setting trusts the socket address, the\n * \"X-Forwarded-Proto\" header field will be trusted\n * and used if present.\n *\n * If you're running behind a reverse proxy that\n * supplies https for you this may be enabled.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'protocol', function protocol(){\n  var proto = this.connection.encrypted\n    ? 'https'\n    : 'http';\n  var trust = this.app.get('trust proxy fn');\n\n  if (!trust(this.connection.remoteAddress, 0)) {\n    return proto;\n  }\n\n  // Note: X-Forwarded-Proto is normally only ever a\n  //       single value, but this is to be safe.\n  var header = this.get('X-Forwarded-Proto') || proto\n  var index = header.indexOf(',')\n\n  return index !== -1\n    ? header.substring(0, index).trim()\n    : header.trim()\n});\n\n/**\n * Short-hand for:\n *\n *    req.protocol === 'https'\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'secure', function secure(){\n  return this.protocol === 'https';\n});\n\n/**\n * Return the remote address from the trusted proxy.\n *\n * The is the remote address on the socket unless\n * \"trust proxy\" is set.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'ip', function ip(){\n  var trust = this.app.get('trust proxy fn');\n  return proxyaddr(this, trust);\n});\n\n/**\n * When \"trust proxy\" is set, trusted proxy addresses + client.\n *\n * For example if the value were \"client, proxy1, proxy2\"\n * you would receive the array `[\"client\", \"proxy1\", \"proxy2\"]`\n * where \"proxy2\" is the furthest down-stream and \"proxy1\" and\n * \"proxy2\" were trusted.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'ips', function ips() {\n  var trust = this.app.get('trust proxy fn');\n  var addrs = proxyaddr.all(this, trust);\n\n  // reverse the order (to farthest -> closest)\n  // and remove socket address\n  addrs.reverse().pop()\n\n  return addrs\n});\n\n/**\n * Return subdomains as an array.\n *\n * Subdomains are the dot-separated parts of the host before the main domain of\n * the app. By default, the domain of the app is assumed to be the last two\n * parts of the host. This can be changed by setting \"subdomain offset\".\n *\n * For example, if the domain is \"tobi.ferrets.example.com\":\n * If \"subdomain offset\" is not set, req.subdomains is `[\"ferrets\", \"tobi\"]`.\n * If \"subdomain offset\" is 3, req.subdomains is `[\"tobi\"]`.\n *\n * @return {Array}\n * @public\n */\n\ndefineGetter(req, 'subdomains', function subdomains() {\n  var hostname = this.hostname;\n\n  if (!hostname) return [];\n\n  var offset = this.app.get('subdomain offset');\n  var subdomains = !isIP(hostname)\n    ? hostname.split('.').reverse()\n    : [hostname];\n\n  return subdomains.slice(offset);\n});\n\n/**\n * Short-hand for `url.parse(req.url).pathname`.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'path', function path() {\n  return parse(this).pathname;\n});\n\n/**\n * Parse the \"Host\" header field to a hostname.\n *\n * When the \"trust proxy\" setting trusts the socket\n * address, the \"X-Forwarded-Host\" header field will\n * be trusted.\n *\n * @return {String}\n * @public\n */\n\ndefineGetter(req, 'hostname', function hostname(){\n  var trust = this.app.get('trust proxy fn');\n  var host = this.get('X-Forwarded-Host');\n\n  if (!host || !trust(this.connection.remoteAddress, 0)) {\n    host = this.get('Host');\n  } else if (host.indexOf(',') !== -1) {\n    // Note: X-Forwarded-Host is normally only ever a\n    //       single value, but this is to be safe.\n    host = host.substring(0, host.indexOf(',')).trimRight()\n  }\n\n  if (!host) return;\n\n  // IPv6 literal support\n  var offset = host[0] === '['\n    ? host.indexOf(']') + 1\n    : 0;\n  var index = host.indexOf(':', offset);\n\n  return index !== -1\n    ? host.substring(0, index)\n    : host;\n});\n\n// TODO: change req.host to return host in next major\n\ndefineGetter(req, 'host', deprecate.function(function host(){\n  return this.hostname;\n}, 'req.host: Use req.hostname instead'));\n\n/**\n * Check if the request is fresh, aka\n * Last-Modified and/or the ETag\n * still match.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'fresh', function(){\n  var method = this.method;\n  var res = this.res\n  var status = res.statusCode\n\n  // GET or HEAD for weak freshness validation only\n  if ('GET' !== method && 'HEAD' !== method) return false;\n\n  // 2xx or 304 as per rfc2616 14.26\n  if ((status >= 200 && status < 300) || 304 === status) {\n    return fresh(this.headers, {\n      'etag': res.get('ETag'),\n      'last-modified': res.get('Last-Modified')\n    })\n  }\n\n  return false;\n});\n\n/**\n * Check if the request is stale, aka\n * \"Last-Modified\" and / or the \"ETag\" for the\n * resource has changed.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'stale', function stale(){\n  return !this.fresh;\n});\n\n/**\n * Check if the request was an _XMLHttpRequest_.\n *\n * @return {Boolean}\n * @public\n */\n\ndefineGetter(req, 'xhr', function xhr(){\n  var val = this.get('X-Requested-With') || '';\n  return val.toLowerCase() === 'xmlhttprequest';\n});\n\n/**\n * Helper function for creating a getter on an object.\n *\n * @param {Object} obj\n * @param {String} name\n * @param {Function} getter\n * @private\n */\nfunction defineGetter(obj, name, getter) {\n  Object.defineProperty(obj, name, {\n    configurable: true,\n    enumerable: true,\n    get: getter\n  });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/request.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(rsc)/./node_modules/.pnpm/safe-buffer@5.2.1/node_modules/safe-buffer/index.js\").Buffer)\nvar contentDisposition = __webpack_require__(/*! content-disposition */ \"(rsc)/./node_modules/.pnpm/content-disposition@0.5.4/node_modules/content-disposition/index.js\");\nvar createError = __webpack_require__(/*! http-errors */ \"(rsc)/./node_modules/.pnpm/http-errors@2.0.0/node_modules/http-errors/index.js\")\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('express');\nvar encodeUrl = __webpack_require__(/*! encodeurl */ \"(rsc)/./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js\");\nvar escapeHtml = __webpack_require__(/*! escape-html */ \"(rsc)/./node_modules/.pnpm/escape-html@1.0.3/node_modules/escape-html/index.js\");\nvar http = __webpack_require__(/*! http */ \"http\");\nvar isAbsolute = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").isAbsolute);\nvar onFinished = __webpack_require__(/*! on-finished */ \"(rsc)/./node_modules/.pnpm/on-finished@2.4.1/node_modules/on-finished/index.js\");\nvar path = __webpack_require__(/*! path */ \"path\");\nvar statuses = __webpack_require__(/*! statuses */ \"(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js\")\nvar merge = __webpack_require__(/*! utils-merge */ \"(rsc)/./node_modules/.pnpm/utils-merge@1.0.1/node_modules/utils-merge/index.js\");\nvar sign = (__webpack_require__(/*! cookie-signature */ \"(rsc)/./node_modules/.pnpm/cookie-signature@1.0.6/node_modules/cookie-signature/index.js\").sign);\nvar normalizeType = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").normalizeType);\nvar normalizeTypes = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").normalizeTypes);\nvar setCharset = (__webpack_require__(/*! ./utils */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\").setCharset);\nvar cookie = __webpack_require__(/*! cookie */ \"(rsc)/./node_modules/.pnpm/cookie@0.7.1/node_modules/cookie/index.js\");\nvar send = __webpack_require__(/*! send */ \"(rsc)/./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js\");\nvar extname = path.extname;\nvar mime = send.mime;\nvar resolve = path.resolve;\nvar vary = __webpack_require__(/*! vary */ \"(rsc)/./node_modules/.pnpm/vary@1.1.2/node_modules/vary/index.js\");\n\n/**\n * Response prototype.\n * @public\n */\n\nvar res = Object.create(http.ServerResponse.prototype)\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = res\n\n/**\n * Module variables.\n * @private\n */\n\nvar charsetRegExp = /;\\s*charset\\s*=/;\n\n/**\n * Set status `code`.\n *\n * @param {Number} code\n * @return {ServerResponse}\n * @public\n */\n\nres.status = function status(code) {\n  if ((typeof code === 'string' || Math.floor(code) !== code) && code > 99 && code < 1000) {\n    deprecate('res.status(' + JSON.stringify(code) + '): use res.status(' + Math.floor(code) + ') instead')\n  }\n  this.statusCode = code;\n  return this;\n};\n\n/**\n * Set Link header field with the given `links`.\n *\n * Examples:\n *\n *    res.links({\n *      next: 'http://api.example.com/users?page=2',\n *      last: 'http://api.example.com/users?page=5'\n *    });\n *\n * @param {Object} links\n * @return {ServerResponse}\n * @public\n */\n\nres.links = function(links){\n  var link = this.get('Link') || '';\n  if (link) link += ', ';\n  return this.set('Link', link + Object.keys(links).map(function(rel){\n    return '<' + links[rel] + '>; rel=\"' + rel + '\"';\n  }).join(', '));\n};\n\n/**\n * Send a response.\n *\n * Examples:\n *\n *     res.send(Buffer.from('wahoo'));\n *     res.send({ some: 'json' });\n *     res.send('<p>some html</p>');\n *\n * @param {string|number|boolean|object|Buffer} body\n * @public\n */\n\nres.send = function send(body) {\n  var chunk = body;\n  var encoding;\n  var req = this.req;\n  var type;\n\n  // settings\n  var app = this.app;\n\n  // allow status / body\n  if (arguments.length === 2) {\n    // res.send(body, status) backwards compat\n    if (typeof arguments[0] !== 'number' && typeof arguments[1] === 'number') {\n      deprecate('res.send(body, status): Use res.status(status).send(body) instead');\n      this.statusCode = arguments[1];\n    } else {\n      deprecate('res.send(status, body): Use res.status(status).send(body) instead');\n      this.statusCode = arguments[0];\n      chunk = arguments[1];\n    }\n  }\n\n  // disambiguate res.send(status) and res.send(status, num)\n  if (typeof chunk === 'number' && arguments.length === 1) {\n    // res.send(status) will set status message as text string\n    if (!this.get('Content-Type')) {\n      this.type('txt');\n    }\n\n    deprecate('res.send(status): Use res.sendStatus(status) instead');\n    this.statusCode = chunk;\n    chunk = statuses.message[chunk]\n  }\n\n  switch (typeof chunk) {\n    // string defaulting to html\n    case 'string':\n      if (!this.get('Content-Type')) {\n        this.type('html');\n      }\n      break;\n    case 'boolean':\n    case 'number':\n    case 'object':\n      if (chunk === null) {\n        chunk = '';\n      } else if (Buffer.isBuffer(chunk)) {\n        if (!this.get('Content-Type')) {\n          this.type('bin');\n        }\n      } else {\n        return this.json(chunk);\n      }\n      break;\n  }\n\n  // write strings in utf-8\n  if (typeof chunk === 'string') {\n    encoding = 'utf8';\n    type = this.get('Content-Type');\n\n    // reflect this in content-type\n    if (typeof type === 'string') {\n      this.set('Content-Type', setCharset(type, 'utf-8'));\n    }\n  }\n\n  // determine if ETag should be generated\n  var etagFn = app.get('etag fn')\n  var generateETag = !this.get('ETag') && typeof etagFn === 'function'\n\n  // populate Content-Length\n  var len\n  if (chunk !== undefined) {\n    if (Buffer.isBuffer(chunk)) {\n      // get length of Buffer\n      len = chunk.length\n    } else if (!generateETag && chunk.length < 1000) {\n      // just calculate length when no ETag + small chunk\n      len = Buffer.byteLength(chunk, encoding)\n    } else {\n      // convert chunk to Buffer and calculate\n      chunk = Buffer.from(chunk, encoding)\n      encoding = undefined;\n      len = chunk.length\n    }\n\n    this.set('Content-Length', len);\n  }\n\n  // populate ETag\n  var etag;\n  if (generateETag && len !== undefined) {\n    if ((etag = etagFn(chunk, encoding))) {\n      this.set('ETag', etag);\n    }\n  }\n\n  // freshness\n  if (req.fresh) this.statusCode = 304;\n\n  // strip irrelevant headers\n  if (204 === this.statusCode || 304 === this.statusCode) {\n    this.removeHeader('Content-Type');\n    this.removeHeader('Content-Length');\n    this.removeHeader('Transfer-Encoding');\n    chunk = '';\n  }\n\n  // alter headers for 205\n  if (this.statusCode === 205) {\n    this.set('Content-Length', '0')\n    this.removeHeader('Transfer-Encoding')\n    chunk = ''\n  }\n\n  if (req.method === 'HEAD') {\n    // skip body for HEAD\n    this.end();\n  } else {\n    // respond\n    this.end(chunk, encoding);\n  }\n\n  return this;\n};\n\n/**\n * Send JSON response.\n *\n * Examples:\n *\n *     res.json(null);\n *     res.json({ user: 'tj' });\n *\n * @param {string|number|boolean|object} obj\n * @public\n */\n\nres.json = function json(obj) {\n  var val = obj;\n\n  // allow status / body\n  if (arguments.length === 2) {\n    // res.json(body, status) backwards compat\n    if (typeof arguments[1] === 'number') {\n      deprecate('res.json(obj, status): Use res.status(status).json(obj) instead');\n      this.statusCode = arguments[1];\n    } else {\n      deprecate('res.json(status, obj): Use res.status(status).json(obj) instead');\n      this.statusCode = arguments[0];\n      val = arguments[1];\n    }\n  }\n\n  // settings\n  var app = this.app;\n  var escape = app.get('json escape')\n  var replacer = app.get('json replacer');\n  var spaces = app.get('json spaces');\n  var body = stringify(val, replacer, spaces, escape)\n\n  // content-type\n  if (!this.get('Content-Type')) {\n    this.set('Content-Type', 'application/json');\n  }\n\n  return this.send(body);\n};\n\n/**\n * Send JSON response with JSONP callback support.\n *\n * Examples:\n *\n *     res.jsonp(null);\n *     res.jsonp({ user: 'tj' });\n *\n * @param {string|number|boolean|object} obj\n * @public\n */\n\nres.jsonp = function jsonp(obj) {\n  var val = obj;\n\n  // allow status / body\n  if (arguments.length === 2) {\n    // res.jsonp(body, status) backwards compat\n    if (typeof arguments[1] === 'number') {\n      deprecate('res.jsonp(obj, status): Use res.status(status).jsonp(obj) instead');\n      this.statusCode = arguments[1];\n    } else {\n      deprecate('res.jsonp(status, obj): Use res.status(status).jsonp(obj) instead');\n      this.statusCode = arguments[0];\n      val = arguments[1];\n    }\n  }\n\n  // settings\n  var app = this.app;\n  var escape = app.get('json escape')\n  var replacer = app.get('json replacer');\n  var spaces = app.get('json spaces');\n  var body = stringify(val, replacer, spaces, escape)\n  var callback = this.req.query[app.get('jsonp callback name')];\n\n  // content-type\n  if (!this.get('Content-Type')) {\n    this.set('X-Content-Type-Options', 'nosniff');\n    this.set('Content-Type', 'application/json');\n  }\n\n  // fixup callback\n  if (Array.isArray(callback)) {\n    callback = callback[0];\n  }\n\n  // jsonp\n  if (typeof callback === 'string' && callback.length !== 0) {\n    this.set('X-Content-Type-Options', 'nosniff');\n    this.set('Content-Type', 'text/javascript');\n\n    // restrict callback charset\n    callback = callback.replace(/[^\\[\\]\\w$.]/g, '');\n\n    if (body === undefined) {\n      // empty argument\n      body = ''\n    } else if (typeof body === 'string') {\n      // replace chars not allowed in JavaScript that are in JSON\n      body = body\n        .replace(/\\u2028/g, '\\\\u2028')\n        .replace(/\\u2029/g, '\\\\u2029')\n    }\n\n    // the /**/ is a specific security mitigation for \"Rosetta Flash JSONP abuse\"\n    // the typeof check is just to reduce client error noise\n    body = '/**/ typeof ' + callback + ' === \\'function\\' && ' + callback + '(' + body + ');';\n  }\n\n  return this.send(body);\n};\n\n/**\n * Send given HTTP status code.\n *\n * Sets the response status to `statusCode` and the body of the\n * response to the standard description from node's http.STATUS_CODES\n * or the statusCode number if no description.\n *\n * Examples:\n *\n *     res.sendStatus(200);\n *\n * @param {number} statusCode\n * @public\n */\n\nres.sendStatus = function sendStatus(statusCode) {\n  var body = statuses.message[statusCode] || String(statusCode)\n\n  this.statusCode = statusCode;\n  this.type('txt');\n\n  return this.send(body);\n};\n\n/**\n * Transfer the file at the given `path`.\n *\n * Automatically sets the _Content-Type_ response header field.\n * The callback `callback(err)` is invoked when the transfer is complete\n * or when an error occurs. Be sure to check `res.headersSent`\n * if you wish to attempt responding, as the header and some data\n * may have already been transferred.\n *\n * Options:\n *\n *   - `maxAge`   defaulting to 0 (can be string converted by `ms`)\n *   - `root`     root directory for relative filenames\n *   - `headers`  object of headers to serve with file\n *   - `dotfiles` serve dotfiles, defaulting to false; can be `\"allow\"` to send them\n *\n * Other options are passed along to `send`.\n *\n * Examples:\n *\n *  The following example illustrates how `res.sendFile()` may\n *  be used as an alternative for the `static()` middleware for\n *  dynamic situations. The code backing `res.sendFile()` is actually\n *  the same code, so HTTP cache support etc is identical.\n *\n *     app.get('/user/:uid/photos/:file', function(req, res){\n *       var uid = req.params.uid\n *         , file = req.params.file;\n *\n *       req.user.mayViewFilesFrom(uid, function(yes){\n *         if (yes) {\n *           res.sendFile('/uploads/' + uid + '/' + file);\n *         } else {\n *           res.send(403, 'Sorry! you cant see that.');\n *         }\n *       });\n *     });\n *\n * @public\n */\n\nres.sendFile = function sendFile(path, options, callback) {\n  var done = callback;\n  var req = this.req;\n  var res = this;\n  var next = req.next;\n  var opts = options || {};\n\n  if (!path) {\n    throw new TypeError('path argument is required to res.sendFile');\n  }\n\n  if (typeof path !== 'string') {\n    throw new TypeError('path must be a string to res.sendFile')\n  }\n\n  // support function as second arg\n  if (typeof options === 'function') {\n    done = options;\n    opts = {};\n  }\n\n  if (!opts.root && !isAbsolute(path)) {\n    throw new TypeError('path must be absolute or specify root to res.sendFile');\n  }\n\n  // create file stream\n  var pathname = encodeURI(path);\n  var file = send(req, pathname, opts);\n\n  // transfer\n  sendfile(res, file, opts, function (err) {\n    if (done) return done(err);\n    if (err && err.code === 'EISDIR') return next();\n\n    // next() all but write errors\n    if (err && err.code !== 'ECONNABORTED' && err.syscall !== 'write') {\n      next(err);\n    }\n  });\n};\n\n/**\n * Transfer the file at the given `path`.\n *\n * Automatically sets the _Content-Type_ response header field.\n * The callback `callback(err)` is invoked when the transfer is complete\n * or when an error occurs. Be sure to check `res.headersSent`\n * if you wish to attempt responding, as the header and some data\n * may have already been transferred.\n *\n * Options:\n *\n *   - `maxAge`   defaulting to 0 (can be string converted by `ms`)\n *   - `root`     root directory for relative filenames\n *   - `headers`  object of headers to serve with file\n *   - `dotfiles` serve dotfiles, defaulting to false; can be `\"allow\"` to send them\n *\n * Other options are passed along to `send`.\n *\n * Examples:\n *\n *  The following example illustrates how `res.sendfile()` may\n *  be used as an alternative for the `static()` middleware for\n *  dynamic situations. The code backing `res.sendfile()` is actually\n *  the same code, so HTTP cache support etc is identical.\n *\n *     app.get('/user/:uid/photos/:file', function(req, res){\n *       var uid = req.params.uid\n *         , file = req.params.file;\n *\n *       req.user.mayViewFilesFrom(uid, function(yes){\n *         if (yes) {\n *           res.sendfile('/uploads/' + uid + '/' + file);\n *         } else {\n *           res.send(403, 'Sorry! you cant see that.');\n *         }\n *       });\n *     });\n *\n * @public\n */\n\nres.sendfile = function (path, options, callback) {\n  var done = callback;\n  var req = this.req;\n  var res = this;\n  var next = req.next;\n  var opts = options || {};\n\n  // support function as second arg\n  if (typeof options === 'function') {\n    done = options;\n    opts = {};\n  }\n\n  // create file stream\n  var file = send(req, path, opts);\n\n  // transfer\n  sendfile(res, file, opts, function (err) {\n    if (done) return done(err);\n    if (err && err.code === 'EISDIR') return next();\n\n    // next() all but write errors\n    if (err && err.code !== 'ECONNABORTED' && err.syscall !== 'write') {\n      next(err);\n    }\n  });\n};\n\nres.sendfile = deprecate.function(res.sendfile,\n  'res.sendfile: Use res.sendFile instead');\n\n/**\n * Transfer the file at the given `path` as an attachment.\n *\n * Optionally providing an alternate attachment `filename`,\n * and optional callback `callback(err)`. The callback is invoked\n * when the data transfer is complete, or when an error has\n * occurred. Be sure to check `res.headersSent` if you plan to respond.\n *\n * Optionally providing an `options` object to use with `res.sendFile()`.\n * This function will set the `Content-Disposition` header, overriding\n * any `Content-Disposition` header passed as header options in order\n * to set the attachment and filename.\n *\n * This method uses `res.sendFile()`.\n *\n * @public\n */\n\nres.download = function download (path, filename, options, callback) {\n  var done = callback;\n  var name = filename;\n  var opts = options || null\n\n  // support function as second or third arg\n  if (typeof filename === 'function') {\n    done = filename;\n    name = null;\n    opts = null\n  } else if (typeof options === 'function') {\n    done = options\n    opts = null\n  }\n\n  // support optional filename, where options may be in it's place\n  if (typeof filename === 'object' &&\n    (typeof options === 'function' || options === undefined)) {\n    name = null\n    opts = filename\n  }\n\n  // set Content-Disposition when file is sent\n  var headers = {\n    'Content-Disposition': contentDisposition(name || path)\n  };\n\n  // merge user-provided headers\n  if (opts && opts.headers) {\n    var keys = Object.keys(opts.headers)\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i]\n      if (key.toLowerCase() !== 'content-disposition') {\n        headers[key] = opts.headers[key]\n      }\n    }\n  }\n\n  // merge user-provided options\n  opts = Object.create(opts)\n  opts.headers = headers\n\n  // Resolve the full path for sendFile\n  var fullPath = !opts.root\n    ? resolve(path)\n    : path\n\n  // send file\n  return this.sendFile(fullPath, opts, done)\n};\n\n/**\n * Set _Content-Type_ response header with `type` through `mime.lookup()`\n * when it does not contain \"/\", or set the Content-Type to `type` otherwise.\n *\n * Examples:\n *\n *     res.type('.html');\n *     res.type('html');\n *     res.type('json');\n *     res.type('application/json');\n *     res.type('png');\n *\n * @param {String} type\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.contentType =\nres.type = function contentType(type) {\n  var ct = type.indexOf('/') === -1\n    ? mime.lookup(type)\n    : type;\n\n  return this.set('Content-Type', ct);\n};\n\n/**\n * Respond to the Acceptable formats using an `obj`\n * of mime-type callbacks.\n *\n * This method uses `req.accepted`, an array of\n * acceptable types ordered by their quality values.\n * When \"Accept\" is not present the _first_ callback\n * is invoked, otherwise the first match is used. When\n * no match is performed the server responds with\n * 406 \"Not Acceptable\".\n *\n * Content-Type is set for you, however if you choose\n * you may alter this within the callback using `res.type()`\n * or `res.set('Content-Type', ...)`.\n *\n *    res.format({\n *      'text/plain': function(){\n *        res.send('hey');\n *      },\n *\n *      'text/html': function(){\n *        res.send('<p>hey</p>');\n *      },\n *\n *      'application/json': function () {\n *        res.send({ message: 'hey' });\n *      }\n *    });\n *\n * In addition to canonicalized MIME types you may\n * also use extnames mapped to these types:\n *\n *    res.format({\n *      text: function(){\n *        res.send('hey');\n *      },\n *\n *      html: function(){\n *        res.send('<p>hey</p>');\n *      },\n *\n *      json: function(){\n *        res.send({ message: 'hey' });\n *      }\n *    });\n *\n * By default Express passes an `Error`\n * with a `.status` of 406 to `next(err)`\n * if a match is not made. If you provide\n * a `.default` callback it will be invoked\n * instead.\n *\n * @param {Object} obj\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.format = function(obj){\n  var req = this.req;\n  var next = req.next;\n\n  var keys = Object.keys(obj)\n    .filter(function (v) { return v !== 'default' })\n\n  var key = keys.length > 0\n    ? req.accepts(keys)\n    : false;\n\n  this.vary(\"Accept\");\n\n  if (key) {\n    this.set('Content-Type', normalizeType(key).value);\n    obj[key](req, this, next);\n  } else if (obj.default) {\n    obj.default(req, this, next)\n  } else {\n    next(createError(406, {\n      types: normalizeTypes(keys).map(function (o) { return o.value })\n    }))\n  }\n\n  return this;\n};\n\n/**\n * Set _Content-Disposition_ header to _attachment_ with optional `filename`.\n *\n * @param {String} filename\n * @return {ServerResponse}\n * @public\n */\n\nres.attachment = function attachment(filename) {\n  if (filename) {\n    this.type(extname(filename));\n  }\n\n  this.set('Content-Disposition', contentDisposition(filename));\n\n  return this;\n};\n\n/**\n * Append additional header `field` with value `val`.\n *\n * Example:\n *\n *    res.append('Link', ['<http://localhost/>', '<http://localhost:3000/>']);\n *    res.append('Set-Cookie', 'foo=bar; Path=/; HttpOnly');\n *    res.append('Warning', '199 Miscellaneous warning');\n *\n * @param {String} field\n * @param {String|Array} val\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.append = function append(field, val) {\n  var prev = this.get(field);\n  var value = val;\n\n  if (prev) {\n    // concat the new and prev vals\n    value = Array.isArray(prev) ? prev.concat(val)\n      : Array.isArray(val) ? [prev].concat(val)\n        : [prev, val]\n  }\n\n  return this.set(field, value);\n};\n\n/**\n * Set header `field` to `val`, or pass\n * an object of header fields.\n *\n * Examples:\n *\n *    res.set('Foo', ['bar', 'baz']);\n *    res.set('Accept', 'application/json');\n *    res.set({ Accept: 'text/plain', 'X-API-Key': 'tobi' });\n *\n * Aliased as `res.header()`.\n *\n * @param {String|Object} field\n * @param {String|Array} val\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.set =\nres.header = function header(field, val) {\n  if (arguments.length === 2) {\n    var value = Array.isArray(val)\n      ? val.map(String)\n      : String(val);\n\n    // add charset to content-type\n    if (field.toLowerCase() === 'content-type') {\n      if (Array.isArray(value)) {\n        throw new TypeError('Content-Type cannot be set to an Array');\n      }\n      if (!charsetRegExp.test(value)) {\n        var charset = mime.charsets.lookup(value.split(';')[0]);\n        if (charset) value += '; charset=' + charset.toLowerCase();\n      }\n    }\n\n    this.setHeader(field, value);\n  } else {\n    for (var key in field) {\n      this.set(key, field[key]);\n    }\n  }\n  return this;\n};\n\n/**\n * Get value for header `field`.\n *\n * @param {String} field\n * @return {String}\n * @public\n */\n\nres.get = function(field){\n  return this.getHeader(field);\n};\n\n/**\n * Clear cookie `name`.\n *\n * @param {String} name\n * @param {Object} [options]\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.clearCookie = function clearCookie(name, options) {\n  if (options) {\n    if (options.maxAge) {\n      deprecate('res.clearCookie: Passing \"options.maxAge\" is deprecated. In v5.0.0 of Express, this option will be ignored, as res.clearCookie will automatically set cookies to expire immediately. Please update your code to omit this option.');\n    }\n    if (options.expires) {\n      deprecate('res.clearCookie: Passing \"options.expires\" is deprecated. In v5.0.0 of Express, this option will be ignored, as res.clearCookie will automatically set cookies to expire immediately. Please update your code to omit this option.');\n    }\n  }\n  var opts = merge({ expires: new Date(1), path: '/' }, options);\n\n  return this.cookie(name, '', opts);\n};\n\n/**\n * Set cookie `name` to `value`, with the given `options`.\n *\n * Options:\n *\n *    - `maxAge`   max-age in milliseconds, converted to `expires`\n *    - `signed`   sign the cookie\n *    - `path`     defaults to \"/\"\n *\n * Examples:\n *\n *    // \"Remember Me\" for 15 minutes\n *    res.cookie('rememberme', '1', { expires: new Date(Date.now() + 900000), httpOnly: true });\n *\n *    // same as above\n *    res.cookie('rememberme', '1', { maxAge: 900000, httpOnly: true })\n *\n * @param {String} name\n * @param {String|Object} value\n * @param {Object} [options]\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.cookie = function (name, value, options) {\n  var opts = merge({}, options);\n  var secret = this.req.secret;\n  var signed = opts.signed;\n\n  if (signed && !secret) {\n    throw new Error('cookieParser(\"secret\") required for signed cookies');\n  }\n\n  var val = typeof value === 'object'\n    ? 'j:' + JSON.stringify(value)\n    : String(value);\n\n  if (signed) {\n    val = 's:' + sign(val, secret);\n  }\n\n  if (opts.maxAge != null) {\n    var maxAge = opts.maxAge - 0\n\n    if (!isNaN(maxAge)) {\n      opts.expires = new Date(Date.now() + maxAge)\n      opts.maxAge = Math.floor(maxAge / 1000)\n    }\n  }\n\n  if (opts.path == null) {\n    opts.path = '/';\n  }\n\n  this.append('Set-Cookie', cookie.serialize(name, String(val), opts));\n\n  return this;\n};\n\n/**\n * Set the location header to `url`.\n *\n * The given `url` can also be \"back\", which redirects\n * to the _Referrer_ or _Referer_ headers or \"/\".\n *\n * Examples:\n *\n *    res.location('/foo/bar').;\n *    res.location('http://example.com');\n *    res.location('../login');\n *\n * @param {String} url\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.location = function location(url) {\n  var loc;\n\n  // \"back\" is an alias for the referrer\n  if (url === 'back') {\n    deprecate('res.location(\"back\"): use res.location(req.get(\"Referrer\") || \"/\") and refer to https://dub.sh/security-redirect for best practices');\n    loc = this.req.get('Referrer') || '/';\n  } else {\n    loc = String(url);\n  }\n\n  return this.set('Location', encodeUrl(loc));\n};\n\n/**\n * Redirect to the given `url` with optional response `status`\n * defaulting to 302.\n *\n * The resulting `url` is determined by `res.location()`, so\n * it will play nicely with mounted apps, relative paths,\n * `\"back\"` etc.\n *\n * Examples:\n *\n *    res.redirect('/foo/bar');\n *    res.redirect('http://example.com');\n *    res.redirect(301, 'http://example.com');\n *    res.redirect('../login'); // /blog/post/1 -> /blog/login\n *\n * @public\n */\n\nres.redirect = function redirect(url) {\n  var address = url;\n  var body;\n  var status = 302;\n\n  // allow status / url\n  if (arguments.length === 2) {\n    if (typeof arguments[0] === 'number') {\n      status = arguments[0];\n      address = arguments[1];\n    } else {\n      deprecate('res.redirect(url, status): Use res.redirect(status, url) instead');\n      status = arguments[1];\n    }\n  }\n\n  // Set location header\n  address = this.location(address).get('Location');\n\n  // Support text/{plain,html} by default\n  this.format({\n    text: function(){\n      body = statuses.message[status] + '. Redirecting to ' + address\n    },\n\n    html: function(){\n      var u = escapeHtml(address);\n      body = '<p>' + statuses.message[status] + '. Redirecting to ' + u + '</p>'\n    },\n\n    default: function(){\n      body = '';\n    }\n  });\n\n  // Respond\n  this.statusCode = status;\n  this.set('Content-Length', Buffer.byteLength(body));\n\n  if (this.req.method === 'HEAD') {\n    this.end();\n  } else {\n    this.end(body);\n  }\n};\n\n/**\n * Add `field` to Vary. If already present in the Vary set, then\n * this call is simply ignored.\n *\n * @param {Array|String} field\n * @return {ServerResponse} for chaining\n * @public\n */\n\nres.vary = function(field){\n  // checks for back-compat\n  if (!field || (Array.isArray(field) && !field.length)) {\n    deprecate('res.vary(): Provide a field name');\n    return this;\n  }\n\n  vary(this, field);\n\n  return this;\n};\n\n/**\n * Render `view` with the given `options` and optional callback `fn`.\n * When a callback function is given a response will _not_ be made\n * automatically, otherwise a response of _200_ and _text/html_ is given.\n *\n * Options:\n *\n *  - `cache`     boolean hinting to the engine it should cache\n *  - `filename`  filename of the view being rendered\n *\n * @public\n */\n\nres.render = function render(view, options, callback) {\n  var app = this.req.app;\n  var done = callback;\n  var opts = options || {};\n  var req = this.req;\n  var self = this;\n\n  // support callback function as second arg\n  if (typeof options === 'function') {\n    done = options;\n    opts = {};\n  }\n\n  // merge res.locals\n  opts._locals = self.locals;\n\n  // default callback to respond\n  done = done || function (err, str) {\n    if (err) return req.next(err);\n    self.send(str);\n  };\n\n  // render\n  app.render(view, opts, done);\n};\n\n// pipe the send file stream\nfunction sendfile(res, file, options, callback) {\n  var done = false;\n  var streaming;\n\n  // request aborted\n  function onaborted() {\n    if (done) return;\n    done = true;\n\n    var err = new Error('Request aborted');\n    err.code = 'ECONNABORTED';\n    callback(err);\n  }\n\n  // directory\n  function ondirectory() {\n    if (done) return;\n    done = true;\n\n    var err = new Error('EISDIR, read');\n    err.code = 'EISDIR';\n    callback(err);\n  }\n\n  // errors\n  function onerror(err) {\n    if (done) return;\n    done = true;\n    callback(err);\n  }\n\n  // ended\n  function onend() {\n    if (done) return;\n    done = true;\n    callback();\n  }\n\n  // file\n  function onfile() {\n    streaming = false;\n  }\n\n  // finished\n  function onfinish(err) {\n    if (err && err.code === 'ECONNRESET') return onaborted();\n    if (err) return onerror(err);\n    if (done) return;\n\n    setImmediate(function () {\n      if (streaming !== false && !done) {\n        onaborted();\n        return;\n      }\n\n      if (done) return;\n      done = true;\n      callback();\n    });\n  }\n\n  // streaming\n  function onstream() {\n    streaming = true;\n  }\n\n  file.on('directory', ondirectory);\n  file.on('end', onend);\n  file.on('error', onerror);\n  file.on('file', onfile);\n  file.on('stream', onstream);\n  onFinished(res, onfinish);\n\n  if (options.headers) {\n    // set headers on successful transfer\n    file.on('headers', function headers(res) {\n      var obj = options.headers;\n      var keys = Object.keys(obj);\n\n      for (var i = 0; i < keys.length; i++) {\n        var k = keys[i];\n        res.setHeader(k, obj[k]);\n      }\n    });\n  }\n\n  // pipe\n  file.pipe(res);\n}\n\n/**\n * Stringify JSON, like JSON.stringify, but v8 optimized, with the\n * ability to escape characters that can trigger HTML sniffing.\n *\n * @param {*} value\n * @param {function} replacer\n * @param {number} spaces\n * @param {boolean} escape\n * @returns {string}\n * @private\n */\n\nfunction stringify (value, replacer, spaces, escape) {\n  // v8 checks arguments.length for optimizing simple call\n  // https://bugs.chromium.org/p/v8/issues/detail?id=4730\n  var json = replacer || spaces\n    ? JSON.stringify(value, replacer, spaces)\n    : JSON.stringify(value);\n\n  if (escape && typeof json === 'string') {\n    json = json.replace(/[<>&]/g, function (c) {\n      switch (c.charCodeAt(0)) {\n        case 0x3c:\n          return '\\\\u003c'\n        case 0x3e:\n          return '\\\\u003e'\n        case 0x26:\n          return '\\\\u0026'\n        /* istanbul ignore next: unreachable default */\n        default:\n          return c\n      }\n    })\n  }\n\n  return json\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/response.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar Route = __webpack_require__(/*! ./route */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js\");\nvar Layer = __webpack_require__(/*! ./layer */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js\");\nvar methods = __webpack_require__(/*! methods */ \"(rsc)/./node_modules/.pnpm/methods@1.1.2/node_modules/methods/index.js\");\nvar mixin = __webpack_require__(/*! utils-merge */ \"(rsc)/./node_modules/.pnpm/utils-merge@1.0.1/node_modules/utils-merge/index.js\");\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('express:router');\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('express');\nvar flatten = __webpack_require__(/*! array-flatten */ \"(rsc)/./node_modules/.pnpm/array-flatten@1.1.1/node_modules/array-flatten/array-flatten.js\");\nvar parseUrl = __webpack_require__(/*! parseurl */ \"(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\");\nvar setPrototypeOf = __webpack_require__(/*! setprototypeof */ \"(rsc)/./node_modules/.pnpm/setprototypeof@1.2.0/node_modules/setprototypeof/index.js\")\n\n/**\n * Module variables.\n * @private\n */\n\nvar objectRegExp = /^\\[object (\\S+)\\]$/;\nvar slice = Array.prototype.slice;\nvar toString = Object.prototype.toString;\n\n/**\n * Initialize a new `Router` with the given `options`.\n *\n * @param {Object} [options]\n * @return {Router} which is a callable function\n * @public\n */\n\nvar proto = module.exports = function(options) {\n  var opts = options || {};\n\n  function router(req, res, next) {\n    router.handle(req, res, next);\n  }\n\n  // mixin Router class functions\n  setPrototypeOf(router, proto)\n\n  router.params = {};\n  router._params = [];\n  router.caseSensitive = opts.caseSensitive;\n  router.mergeParams = opts.mergeParams;\n  router.strict = opts.strict;\n  router.stack = [];\n\n  return router;\n};\n\n/**\n * Map the given param placeholder `name`(s) to the given callback.\n *\n * Parameter mapping is used to provide pre-conditions to routes\n * which use normalized placeholders. For example a _:user_id_ parameter\n * could automatically load a user's information from the database without\n * any additional code,\n *\n * The callback uses the same signature as middleware, the only difference\n * being that the value of the placeholder is passed, in this case the _id_\n * of the user. Once the `next()` function is invoked, just like middleware\n * it will continue on to execute the route, or subsequent parameter functions.\n *\n * Just like in middleware, you must either respond to the request or call next\n * to avoid stalling the request.\n *\n *  app.param('user_id', function(req, res, next, id){\n *    User.find(id, function(err, user){\n *      if (err) {\n *        return next(err);\n *      } else if (!user) {\n *        return next(new Error('failed to load user'));\n *      }\n *      req.user = user;\n *      next();\n *    });\n *  });\n *\n * @param {String} name\n * @param {Function} fn\n * @return {app} for chaining\n * @public\n */\n\nproto.param = function param(name, fn) {\n  // param logic\n  if (typeof name === 'function') {\n    deprecate('router.param(fn): Refactor to use path params');\n    this._params.push(name);\n    return;\n  }\n\n  // apply param functions\n  var params = this._params;\n  var len = params.length;\n  var ret;\n\n  if (name[0] === ':') {\n    deprecate('router.param(' + JSON.stringify(name) + ', fn): Use router.param(' + JSON.stringify(name.slice(1)) + ', fn) instead')\n    name = name.slice(1)\n  }\n\n  for (var i = 0; i < len; ++i) {\n    if (ret = params[i](name, fn)) {\n      fn = ret;\n    }\n  }\n\n  // ensure we end up with a\n  // middleware function\n  if ('function' !== typeof fn) {\n    throw new Error('invalid param() call for ' + name + ', got ' + fn);\n  }\n\n  (this.params[name] = this.params[name] || []).push(fn);\n  return this;\n};\n\n/**\n * Dispatch a req, res into the router.\n * @private\n */\n\nproto.handle = function handle(req, res, out) {\n  var self = this;\n\n  debug('dispatching %s %s', req.method, req.url);\n\n  var idx = 0;\n  var protohost = getProtohost(req.url) || ''\n  var removed = '';\n  var slashAdded = false;\n  var sync = 0\n  var paramcalled = {};\n\n  // store options for OPTIONS request\n  // only used if OPTIONS request\n  var options = [];\n\n  // middleware and routes\n  var stack = self.stack;\n\n  // manage inter-router variables\n  var parentParams = req.params;\n  var parentUrl = req.baseUrl || '';\n  var done = restore(out, req, 'baseUrl', 'next', 'params');\n\n  // setup next layer\n  req.next = next;\n\n  // for options requests, respond with a default if nothing else responds\n  if (req.method === 'OPTIONS') {\n    done = wrap(done, function(old, err) {\n      if (err || options.length === 0) return old(err);\n      sendOptionsResponse(res, options, old);\n    });\n  }\n\n  // setup basic req values\n  req.baseUrl = parentUrl;\n  req.originalUrl = req.originalUrl || req.url;\n\n  next();\n\n  function next(err) {\n    var layerError = err === 'route'\n      ? null\n      : err;\n\n    // remove added slash\n    if (slashAdded) {\n      req.url = req.url.slice(1)\n      slashAdded = false;\n    }\n\n    // restore altered req.url\n    if (removed.length !== 0) {\n      req.baseUrl = parentUrl;\n      req.url = protohost + removed + req.url.slice(protohost.length)\n      removed = '';\n    }\n\n    // signal to exit router\n    if (layerError === 'router') {\n      setImmediate(done, null)\n      return\n    }\n\n    // no more matching layers\n    if (idx >= stack.length) {\n      setImmediate(done, layerError);\n      return;\n    }\n\n    // max sync stack\n    if (++sync > 100) {\n      return setImmediate(next, err)\n    }\n\n    // get pathname of request\n    var path = getPathname(req);\n\n    if (path == null) {\n      return done(layerError);\n    }\n\n    // find next matching layer\n    var layer;\n    var match;\n    var route;\n\n    while (match !== true && idx < stack.length) {\n      layer = stack[idx++];\n      match = matchLayer(layer, path);\n      route = layer.route;\n\n      if (typeof match !== 'boolean') {\n        // hold on to layerError\n        layerError = layerError || match;\n      }\n\n      if (match !== true) {\n        continue;\n      }\n\n      if (!route) {\n        // process non-route handlers normally\n        continue;\n      }\n\n      if (layerError) {\n        // routes do not match with a pending error\n        match = false;\n        continue;\n      }\n\n      var method = req.method;\n      var has_method = route._handles_method(method);\n\n      // build up automatic options response\n      if (!has_method && method === 'OPTIONS') {\n        appendMethods(options, route._options());\n      }\n\n      // don't even bother matching route\n      if (!has_method && method !== 'HEAD') {\n        match = false;\n      }\n    }\n\n    // no match\n    if (match !== true) {\n      return done(layerError);\n    }\n\n    // store route for dispatch on change\n    if (route) {\n      req.route = route;\n    }\n\n    // Capture one-time layer values\n    req.params = self.mergeParams\n      ? mergeParams(layer.params, parentParams)\n      : layer.params;\n    var layerPath = layer.path;\n\n    // this should be done for the layer\n    self.process_params(layer, paramcalled, req, res, function (err) {\n      if (err) {\n        next(layerError || err)\n      } else if (route) {\n        layer.handle_request(req, res, next)\n      } else {\n        trim_prefix(layer, layerError, layerPath, path)\n      }\n\n      sync = 0\n    });\n  }\n\n  function trim_prefix(layer, layerError, layerPath, path) {\n    if (layerPath.length !== 0) {\n      // Validate path is a prefix match\n      if (layerPath !== path.slice(0, layerPath.length)) {\n        next(layerError)\n        return\n      }\n\n      // Validate path breaks on a path separator\n      var c = path[layerPath.length]\n      if (c && c !== '/' && c !== '.') return next(layerError)\n\n      // Trim off the part of the url that matches the route\n      // middleware (.use stuff) needs to have the path stripped\n      debug('trim prefix (%s) from url %s', layerPath, req.url);\n      removed = layerPath;\n      req.url = protohost + req.url.slice(protohost.length + removed.length)\n\n      // Ensure leading slash\n      if (!protohost && req.url[0] !== '/') {\n        req.url = '/' + req.url;\n        slashAdded = true;\n      }\n\n      // Setup base URL (no trailing slash)\n      req.baseUrl = parentUrl + (removed[removed.length - 1] === '/'\n        ? removed.substring(0, removed.length - 1)\n        : removed);\n    }\n\n    debug('%s %s : %s', layer.name, layerPath, req.originalUrl);\n\n    if (layerError) {\n      layer.handle_error(layerError, req, res, next);\n    } else {\n      layer.handle_request(req, res, next);\n    }\n  }\n};\n\n/**\n * Process any parameters for the layer.\n * @private\n */\n\nproto.process_params = function process_params(layer, called, req, res, done) {\n  var params = this.params;\n\n  // captured parameters from the layer, keys and values\n  var keys = layer.keys;\n\n  // fast track\n  if (!keys || keys.length === 0) {\n    return done();\n  }\n\n  var i = 0;\n  var name;\n  var paramIndex = 0;\n  var key;\n  var paramVal;\n  var paramCallbacks;\n  var paramCalled;\n\n  // process params in order\n  // param callbacks can be async\n  function param(err) {\n    if (err) {\n      return done(err);\n    }\n\n    if (i >= keys.length ) {\n      return done();\n    }\n\n    paramIndex = 0;\n    key = keys[i++];\n    name = key.name;\n    paramVal = req.params[name];\n    paramCallbacks = params[name];\n    paramCalled = called[name];\n\n    if (paramVal === undefined || !paramCallbacks) {\n      return param();\n    }\n\n    // param previously called with same value or error occurred\n    if (paramCalled && (paramCalled.match === paramVal\n      || (paramCalled.error && paramCalled.error !== 'route'))) {\n      // restore value\n      req.params[name] = paramCalled.value;\n\n      // next param\n      return param(paramCalled.error);\n    }\n\n    called[name] = paramCalled = {\n      error: null,\n      match: paramVal,\n      value: paramVal\n    };\n\n    paramCallback();\n  }\n\n  // single param callbacks\n  function paramCallback(err) {\n    var fn = paramCallbacks[paramIndex++];\n\n    // store updated value\n    paramCalled.value = req.params[key.name];\n\n    if (err) {\n      // store error\n      paramCalled.error = err;\n      param(err);\n      return;\n    }\n\n    if (!fn) return param();\n\n    try {\n      fn(req, res, paramCallback, paramVal, key.name);\n    } catch (e) {\n      paramCallback(e);\n    }\n  }\n\n  param();\n};\n\n/**\n * Use the given middleware function, with optional path, defaulting to \"/\".\n *\n * Use (like `.all`) will run for any http METHOD, but it will not add\n * handlers for those methods so OPTIONS requests will not consider `.use`\n * functions even if they could respond.\n *\n * The other difference is that _route_ path is stripped and not visible\n * to the handler function. The main effect of this feature is that mounted\n * handlers can operate without any code changes regardless of the \"prefix\"\n * pathname.\n *\n * @public\n */\n\nproto.use = function use(fn) {\n  var offset = 0;\n  var path = '/';\n\n  // default path to '/'\n  // disambiguate router.use([fn])\n  if (typeof fn !== 'function') {\n    var arg = fn;\n\n    while (Array.isArray(arg) && arg.length !== 0) {\n      arg = arg[0];\n    }\n\n    // first arg is the path\n    if (typeof arg !== 'function') {\n      offset = 1;\n      path = fn;\n    }\n  }\n\n  var callbacks = flatten(slice.call(arguments, offset));\n\n  if (callbacks.length === 0) {\n    throw new TypeError('Router.use() requires a middleware function')\n  }\n\n  for (var i = 0; i < callbacks.length; i++) {\n    var fn = callbacks[i];\n\n    if (typeof fn !== 'function') {\n      throw new TypeError('Router.use() requires a middleware function but got a ' + gettype(fn))\n    }\n\n    // add the middleware\n    debug('use %o %s', path, fn.name || '<anonymous>')\n\n    var layer = new Layer(path, {\n      sensitive: this.caseSensitive,\n      strict: false,\n      end: false\n    }, fn);\n\n    layer.route = undefined;\n\n    this.stack.push(layer);\n  }\n\n  return this;\n};\n\n/**\n * Create a new Route for the given path.\n *\n * Each route contains a separate middleware stack and VERB handlers.\n *\n * See the Route api documentation for details on adding handlers\n * and middleware to routes.\n *\n * @param {String} path\n * @return {Route}\n * @public\n */\n\nproto.route = function route(path) {\n  var route = new Route(path);\n\n  var layer = new Layer(path, {\n    sensitive: this.caseSensitive,\n    strict: this.strict,\n    end: true\n  }, route.dispatch.bind(route));\n\n  layer.route = route;\n\n  this.stack.push(layer);\n  return route;\n};\n\n// create Router#VERB functions\nmethods.concat('all').forEach(function(method){\n  proto[method] = function(path){\n    var route = this.route(path)\n    route[method].apply(route, slice.call(arguments, 1));\n    return this;\n  };\n});\n\n// append methods to a list of methods\nfunction appendMethods(list, addition) {\n  for (var i = 0; i < addition.length; i++) {\n    var method = addition[i];\n    if (list.indexOf(method) === -1) {\n      list.push(method);\n    }\n  }\n}\n\n// get pathname of request\nfunction getPathname(req) {\n  try {\n    return parseUrl(req).pathname;\n  } catch (err) {\n    return undefined;\n  }\n}\n\n// Get get protocol + host for a URL\nfunction getProtohost(url) {\n  if (typeof url !== 'string' || url.length === 0 || url[0] === '/') {\n    return undefined\n  }\n\n  var searchIndex = url.indexOf('?')\n  var pathLength = searchIndex !== -1\n    ? searchIndex\n    : url.length\n  var fqdnIndex = url.slice(0, pathLength).indexOf('://')\n\n  return fqdnIndex !== -1\n    ? url.substring(0, url.indexOf('/', 3 + fqdnIndex))\n    : undefined\n}\n\n// get type for error message\nfunction gettype(obj) {\n  var type = typeof obj;\n\n  if (type !== 'object') {\n    return type;\n  }\n\n  // inspect [[Class]] for objects\n  return toString.call(obj)\n    .replace(objectRegExp, '$1');\n}\n\n/**\n * Match path to a layer.\n *\n * @param {Layer} layer\n * @param {string} path\n * @private\n */\n\nfunction matchLayer(layer, path) {\n  try {\n    return layer.match(path);\n  } catch (err) {\n    return err;\n  }\n}\n\n// merge params with parent params\nfunction mergeParams(params, parent) {\n  if (typeof parent !== 'object' || !parent) {\n    return params;\n  }\n\n  // make copy of parent for base\n  var obj = mixin({}, parent);\n\n  // simple non-numeric merging\n  if (!(0 in params) || !(0 in parent)) {\n    return mixin(obj, params);\n  }\n\n  var i = 0;\n  var o = 0;\n\n  // determine numeric gaps\n  while (i in params) {\n    i++;\n  }\n\n  while (o in parent) {\n    o++;\n  }\n\n  // offset numeric indices in params before merge\n  for (i--; i >= 0; i--) {\n    params[i + o] = params[i];\n\n    // create holes for the merge when necessary\n    if (i < o) {\n      delete params[i];\n    }\n  }\n\n  return mixin(obj, params);\n}\n\n// restore obj props after function\nfunction restore(fn, obj) {\n  var props = new Array(arguments.length - 2);\n  var vals = new Array(arguments.length - 2);\n\n  for (var i = 0; i < props.length; i++) {\n    props[i] = arguments[i + 2];\n    vals[i] = obj[props[i]];\n  }\n\n  return function () {\n    // restore vals\n    for (var i = 0; i < props.length; i++) {\n      obj[props[i]] = vals[i];\n    }\n\n    return fn.apply(this, arguments);\n  };\n}\n\n// send an OPTIONS response\nfunction sendOptionsResponse(res, options, next) {\n  try {\n    var body = options.join(',');\n    res.set('Allow', body);\n    res.send(body);\n  } catch (err) {\n    next(err);\n  }\n}\n\n// wrap a function\nfunction wrap(old, fn) {\n  return function proxy() {\n    var args = new Array(arguments.length + 1);\n\n    args[0] = old;\n    for (var i = 0, len = arguments.length; i < len; i++) {\n      args[i + 1] = arguments[i];\n    }\n\n    fn.apply(this, args);\n  };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar pathRegexp = __webpack_require__(/*! path-to-regexp */ \"(rsc)/./node_modules/.pnpm/path-to-regexp@0.1.12/node_modules/path-to-regexp/index.js\");\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('express:router:layer');\n\n/**\n * Module variables.\n * @private\n */\n\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Layer;\n\nfunction Layer(path, options, fn) {\n  if (!(this instanceof Layer)) {\n    return new Layer(path, options, fn);\n  }\n\n  debug('new %o', path)\n  var opts = options || {};\n\n  this.handle = fn;\n  this.name = fn.name || '<anonymous>';\n  this.params = undefined;\n  this.path = undefined;\n  this.regexp = pathRegexp(path, this.keys = [], opts);\n\n  // set fast path flags\n  this.regexp.fast_star = path === '*'\n  this.regexp.fast_slash = path === '/' && opts.end === false\n}\n\n/**\n * Handle the error for the layer.\n *\n * @param {Error} error\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_error = function handle_error(error, req, res, next) {\n  var fn = this.handle;\n\n  if (fn.length !== 4) {\n    // not a standard error handler\n    return next(error);\n  }\n\n  try {\n    fn(error, req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Handle the request for the layer.\n *\n * @param {Request} req\n * @param {Response} res\n * @param {function} next\n * @api private\n */\n\nLayer.prototype.handle_request = function handle(req, res, next) {\n  var fn = this.handle;\n\n  if (fn.length > 3) {\n    // not a standard request handler\n    return next();\n  }\n\n  try {\n    fn(req, res, next);\n  } catch (err) {\n    next(err);\n  }\n};\n\n/**\n * Check if this route matches `path`, if so\n * populate `.params`.\n *\n * @param {String} path\n * @return {Boolean}\n * @api private\n */\n\nLayer.prototype.match = function match(path) {\n  var match\n\n  if (path != null) {\n    // fast path non-ending match for / (any path matches)\n    if (this.regexp.fast_slash) {\n      this.params = {}\n      this.path = ''\n      return true\n    }\n\n    // fast path for * (everything matched in a param)\n    if (this.regexp.fast_star) {\n      this.params = {'0': decode_param(path)}\n      this.path = path\n      return true\n    }\n\n    // match the path\n    match = this.regexp.exec(path)\n  }\n\n  if (!match) {\n    this.params = undefined;\n    this.path = undefined;\n    return false;\n  }\n\n  // store values\n  this.params = {};\n  this.path = match[0]\n\n  var keys = this.keys;\n  var params = this.params;\n\n  for (var i = 1; i < match.length; i++) {\n    var key = keys[i - 1];\n    var prop = key.name;\n    var val = decode_param(match[i])\n\n    if (val !== undefined || !(hasOwnProperty.call(params, prop))) {\n      params[prop] = val;\n    }\n  }\n\n  return true;\n};\n\n/**\n * Decode param value.\n *\n * @param {string} val\n * @return {string}\n * @private\n */\n\nfunction decode_param(val) {\n  if (typeof val !== 'string' || val.length === 0) {\n    return val;\n  }\n\n  try {\n    return decodeURIComponent(val);\n  } catch (err) {\n    if (err instanceof URIError) {\n      err.message = 'Failed to decode param \\'' + val + '\\'';\n      err.status = err.statusCode = 400;\n    }\n\n    throw err;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js":
/*!************************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js ***!
  \************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('express:router:route');\nvar flatten = __webpack_require__(/*! array-flatten */ \"(rsc)/./node_modules/.pnpm/array-flatten@1.1.1/node_modules/array-flatten/array-flatten.js\");\nvar Layer = __webpack_require__(/*! ./layer */ \"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js\");\nvar methods = __webpack_require__(/*! methods */ \"(rsc)/./node_modules/.pnpm/methods@1.1.2/node_modules/methods/index.js\");\n\n/**\n * Module variables.\n * @private\n */\n\nvar slice = Array.prototype.slice;\nvar toString = Object.prototype.toString;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = Route;\n\n/**\n * Initialize `Route` with the given `path`,\n *\n * @param {String} path\n * @public\n */\n\nfunction Route(path) {\n  this.path = path;\n  this.stack = [];\n\n  debug('new %o', path)\n\n  // route handlers for various http methods\n  this.methods = {};\n}\n\n/**\n * Determine if the route handles a given method.\n * @private\n */\n\nRoute.prototype._handles_method = function _handles_method(method) {\n  if (this.methods._all) {\n    return true;\n  }\n\n  // normalize name\n  var name = typeof method === 'string'\n    ? method.toLowerCase()\n    : method\n\n  if (name === 'head' && !this.methods['head']) {\n    name = 'get';\n  }\n\n  return Boolean(this.methods[name]);\n};\n\n/**\n * @return {Array} supported HTTP methods\n * @private\n */\n\nRoute.prototype._options = function _options() {\n  var methods = Object.keys(this.methods);\n\n  // append automatic head\n  if (this.methods.get && !this.methods.head) {\n    methods.push('head');\n  }\n\n  for (var i = 0; i < methods.length; i++) {\n    // make upper case\n    methods[i] = methods[i].toUpperCase();\n  }\n\n  return methods;\n};\n\n/**\n * dispatch req, res into this route\n * @private\n */\n\nRoute.prototype.dispatch = function dispatch(req, res, done) {\n  var idx = 0;\n  var stack = this.stack;\n  var sync = 0\n\n  if (stack.length === 0) {\n    return done();\n  }\n  var method = typeof req.method === 'string'\n    ? req.method.toLowerCase()\n    : req.method\n\n  if (method === 'head' && !this.methods['head']) {\n    method = 'get';\n  }\n\n  req.route = this;\n\n  next();\n\n  function next(err) {\n    // signal to exit route\n    if (err && err === 'route') {\n      return done();\n    }\n\n    // signal to exit router\n    if (err && err === 'router') {\n      return done(err)\n    }\n\n    // max sync stack\n    if (++sync > 100) {\n      return setImmediate(next, err)\n    }\n\n    var layer = stack[idx++]\n\n    // end of layers\n    if (!layer) {\n      return done(err)\n    }\n\n    if (layer.method && layer.method !== method) {\n      next(err)\n    } else if (err) {\n      layer.handle_error(err, req, res, next);\n    } else {\n      layer.handle_request(req, res, next);\n    }\n\n    sync = 0\n  }\n};\n\n/**\n * Add a handler for all HTTP verbs to this route.\n *\n * Behaves just like middleware and can respond or call `next`\n * to continue processing.\n *\n * You can use multiple `.all` call to add multiple handlers.\n *\n *   function check_something(req, res, next){\n *     next();\n *   };\n *\n *   function validate_user(req, res, next){\n *     next();\n *   };\n *\n *   route\n *   .all(validate_user)\n *   .all(check_something)\n *   .get(function(req, res, next){\n *     res.send('hello world');\n *   });\n *\n * @param {function} handler\n * @return {Route} for chaining\n * @api public\n */\n\nRoute.prototype.all = function all() {\n  var handles = flatten(slice.call(arguments));\n\n  for (var i = 0; i < handles.length; i++) {\n    var handle = handles[i];\n\n    if (typeof handle !== 'function') {\n      var type = toString.call(handle);\n      var msg = 'Route.all() requires a callback function but got a ' + type\n      throw new TypeError(msg);\n    }\n\n    var layer = Layer('/', {}, handle);\n    layer.method = undefined;\n\n    this.methods._all = true;\n    this.stack.push(layer);\n  }\n\n  return this;\n};\n\nmethods.forEach(function(method){\n  Route.prototype[method] = function(){\n    var handles = flatten(slice.call(arguments));\n\n    for (var i = 0; i < handles.length; i++) {\n      var handle = handles[i];\n\n      if (typeof handle !== 'function') {\n        var type = toString.call(handle);\n        var msg = 'Route.' + method + '() requires a callback function but got a ' + type\n        throw new Error(msg);\n      }\n\n      debug('%s %o', method, this.path)\n\n      var layer = Layer('/', {}, handle);\n      layer.method = method;\n\n      this.methods[method] = true;\n      this.stack.push(layer);\n    }\n\n    return this;\n  };\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @api private\n */\n\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(rsc)/./node_modules/.pnpm/safe-buffer@5.2.1/node_modules/safe-buffer/index.js\").Buffer)\nvar contentDisposition = __webpack_require__(/*! content-disposition */ \"(rsc)/./node_modules/.pnpm/content-disposition@0.5.4/node_modules/content-disposition/index.js\");\nvar contentType = __webpack_require__(/*! content-type */ \"(rsc)/./node_modules/.pnpm/content-type@1.0.5/node_modules/content-type/index.js\");\nvar deprecate = __webpack_require__(/*! depd */ \"(rsc)/./node_modules/.pnpm/depd@2.0.0/node_modules/depd/index.js\")('express');\nvar flatten = __webpack_require__(/*! array-flatten */ \"(rsc)/./node_modules/.pnpm/array-flatten@1.1.1/node_modules/array-flatten/array-flatten.js\");\nvar mime = (__webpack_require__(/*! send */ \"(rsc)/./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js\").mime);\nvar etag = __webpack_require__(/*! etag */ \"(rsc)/./node_modules/.pnpm/etag@1.8.1/node_modules/etag/index.js\");\nvar proxyaddr = __webpack_require__(/*! proxy-addr */ \"(rsc)/./node_modules/.pnpm/proxy-addr@2.0.7/node_modules/proxy-addr/index.js\");\nvar qs = __webpack_require__(/*! qs */ \"(rsc)/./node_modules/.pnpm/qs@6.13.0/node_modules/qs/lib/index.js\");\nvar querystring = __webpack_require__(/*! querystring */ \"querystring\");\n\n/**\n * Return strong ETag for `body`.\n *\n * @param {String|Buffer} body\n * @param {String} [encoding]\n * @return {String}\n * @api private\n */\n\nexports.etag = createETagGenerator({ weak: false })\n\n/**\n * Return weak ETag for `body`.\n *\n * @param {String|Buffer} body\n * @param {String} [encoding]\n * @return {String}\n * @api private\n */\n\nexports.wetag = createETagGenerator({ weak: true })\n\n/**\n * Check if `path` looks absolute.\n *\n * @param {String} path\n * @return {Boolean}\n * @api private\n */\n\nexports.isAbsolute = function(path){\n  if ('/' === path[0]) return true;\n  if (':' === path[1] && ('\\\\' === path[2] || '/' === path[2])) return true; // Windows device path\n  if ('\\\\\\\\' === path.substring(0, 2)) return true; // Microsoft Azure absolute path\n};\n\n/**\n * Flatten the given `arr`.\n *\n * @param {Array} arr\n * @return {Array}\n * @api private\n */\n\nexports.flatten = deprecate.function(flatten,\n  'utils.flatten: use array-flatten npm module instead');\n\n/**\n * Normalize the given `type`, for example \"html\" becomes \"text/html\".\n *\n * @param {String} type\n * @return {Object}\n * @api private\n */\n\nexports.normalizeType = function(type){\n  return ~type.indexOf('/')\n    ? acceptParams(type)\n    : { value: mime.lookup(type), params: {} };\n};\n\n/**\n * Normalize `types`, for example \"html\" becomes \"text/html\".\n *\n * @param {Array} types\n * @return {Array}\n * @api private\n */\n\nexports.normalizeTypes = function(types){\n  var ret = [];\n\n  for (var i = 0; i < types.length; ++i) {\n    ret.push(exports.normalizeType(types[i]));\n  }\n\n  return ret;\n};\n\n/**\n * Generate Content-Disposition header appropriate for the filename.\n * non-ascii filenames are urlencoded and a filename* parameter is added\n *\n * @param {String} filename\n * @return {String}\n * @api private\n */\n\nexports.contentDisposition = deprecate.function(contentDisposition,\n  'utils.contentDisposition: use content-disposition npm module instead');\n\n/**\n * Parse accept params `str` returning an\n * object with `.value`, `.quality` and `.params`.\n *\n * @param {String} str\n * @return {Object}\n * @api private\n */\n\nfunction acceptParams (str) {\n  var parts = str.split(/ *; */);\n  var ret = { value: parts[0], quality: 1, params: {} }\n\n  for (var i = 1; i < parts.length; ++i) {\n    var pms = parts[i].split(/ *= */);\n    if ('q' === pms[0]) {\n      ret.quality = parseFloat(pms[1]);\n    } else {\n      ret.params[pms[0]] = pms[1];\n    }\n  }\n\n  return ret;\n}\n\n/**\n * Compile \"etag\" value to function.\n *\n * @param  {Boolean|String|Function} val\n * @return {Function}\n * @api private\n */\n\nexports.compileETag = function(val) {\n  var fn;\n\n  if (typeof val === 'function') {\n    return val;\n  }\n\n  switch (val) {\n    case true:\n    case 'weak':\n      fn = exports.wetag;\n      break;\n    case false:\n      break;\n    case 'strong':\n      fn = exports.etag;\n      break;\n    default:\n      throw new TypeError('unknown value for etag function: ' + val);\n  }\n\n  return fn;\n}\n\n/**\n * Compile \"query parser\" value to function.\n *\n * @param  {String|Function} val\n * @return {Function}\n * @api private\n */\n\nexports.compileQueryParser = function compileQueryParser(val) {\n  var fn;\n\n  if (typeof val === 'function') {\n    return val;\n  }\n\n  switch (val) {\n    case true:\n    case 'simple':\n      fn = querystring.parse;\n      break;\n    case false:\n      fn = newObject;\n      break;\n    case 'extended':\n      fn = parseExtendedQueryString;\n      break;\n    default:\n      throw new TypeError('unknown value for query parser function: ' + val);\n  }\n\n  return fn;\n}\n\n/**\n * Compile \"proxy trust\" value to function.\n *\n * @param  {Boolean|String|Number|Array|Function} val\n * @return {Function}\n * @api private\n */\n\nexports.compileTrust = function(val) {\n  if (typeof val === 'function') return val;\n\n  if (val === true) {\n    // Support plain true/false\n    return function(){ return true };\n  }\n\n  if (typeof val === 'number') {\n    // Support trusting hop count\n    return function(a, i){ return i < val };\n  }\n\n  if (typeof val === 'string') {\n    // Support comma-separated values\n    val = val.split(',')\n      .map(function (v) { return v.trim() })\n  }\n\n  return proxyaddr.compile(val || []);\n}\n\n/**\n * Set the charset in a given Content-Type string.\n *\n * @param {String} type\n * @param {String} charset\n * @return {String}\n * @api private\n */\n\nexports.setCharset = function setCharset(type, charset) {\n  if (!type || !charset) {\n    return type;\n  }\n\n  // parse type\n  var parsed = contentType.parse(type);\n\n  // set charset\n  parsed.parameters.charset = charset;\n\n  // format type\n  return contentType.format(parsed);\n};\n\n/**\n * Create an ETag generator function, generating ETags with\n * the given options.\n *\n * @param {object} options\n * @return {function}\n * @private\n */\n\nfunction createETagGenerator (options) {\n  return function generateETag (body, encoding) {\n    var buf = !Buffer.isBuffer(body)\n      ? Buffer.from(body, encoding)\n      : body\n\n    return etag(buf, options)\n  }\n}\n\n/**\n * Parse an extended query string with qs.\n *\n * @param {String} str\n * @return {Object}\n * @private\n */\n\nfunction parseExtendedQueryString(str) {\n  return qs.parse(str, {\n    allowPrototypes: true\n  });\n}\n\n/**\n * Return new empty object.\n *\n * @return {Object}\n * @api private\n */\n\nfunction newObject() {\n  return {};\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vZXhwcmVzc0A0LjIxLjIvbm9kZV9tb2R1bGVzL2V4cHJlc3MvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFYTs7QUFFYjtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxhQUFhLGlJQUE2QjtBQUMxQyx5QkFBeUIsbUJBQU8sQ0FBQywySEFBcUI7QUFDdEQsa0JBQWtCLG1CQUFPLENBQUMsc0dBQWM7QUFDeEMsZ0JBQWdCLG1CQUFPLENBQUMsOEVBQU07QUFDOUIsY0FBYyxtQkFBTyxDQUFDLGlIQUFlO0FBQ3JDLFdBQVcsMkdBQW9CO0FBQy9CLFdBQVcsbUJBQU8sQ0FBQyw4RUFBTTtBQUN6QixnQkFBZ0IsbUJBQU8sQ0FBQyxnR0FBWTtBQUNwQyxTQUFTLG1CQUFPLENBQUMsNkVBQUk7QUFDckIsa0JBQWtCLG1CQUFPLENBQUMsZ0NBQWE7O0FBRXZDO0FBQ0E7QUFDQTtBQUNBLFdBQVcsZUFBZTtBQUMxQixXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ0E7O0FBRUEsWUFBWSx5QkFBeUIsYUFBYTs7QUFFbEQ7QUFDQTtBQUNBO0FBQ0EsV0FBVyxlQUFlO0FBQzFCLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQSxhQUFhLHlCQUF5QixZQUFZOztBQUVsRDtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ0E7O0FBRUEsa0JBQWtCO0FBQ2xCO0FBQ0EsNkVBQTZFO0FBQzdFLG9EQUFvRDtBQUNwRDs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEIsWUFBWTtBQUNaO0FBQ0E7O0FBRUEsZUFBZTtBQUNmOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQSxxQkFBcUI7QUFDckI7QUFDQTtBQUNBLFFBQVE7QUFDUjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEIsWUFBWTtBQUNaO0FBQ0E7O0FBRUEsc0JBQXNCO0FBQ3RCOztBQUVBLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNBOztBQUVBLDBCQUEwQjtBQUMxQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQTtBQUNBLDZCQUE2QjtBQUM3QixjQUFjOztBQUVkLGtCQUFrQixrQkFBa0I7QUFDcEM7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVkseUJBQXlCO0FBQ3JDLFlBQVk7QUFDWjtBQUNBOztBQUVBLG1CQUFtQjtBQUNuQjs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLGlCQUFpQjtBQUM3QixZQUFZO0FBQ1o7QUFDQTs7QUFFQSwwQkFBMEI7QUFDMUI7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxZQUFZLHNDQUFzQztBQUNsRCxZQUFZO0FBQ1o7QUFDQTs7QUFFQSxvQkFBb0I7QUFDcEI7O0FBRUE7QUFDQTtBQUNBLHVCQUF1QjtBQUN2Qjs7QUFFQTtBQUNBO0FBQ0EsMkJBQTJCO0FBQzNCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLDBCQUEwQixpQkFBaUI7QUFDM0M7O0FBRUE7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsV0FBVyxRQUFRO0FBQ25CLFlBQVk7QUFDWjtBQUNBOztBQUVBLGtCQUFrQjtBQUNsQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsUUFBUTtBQUNuQixZQUFZO0FBQ1o7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFFBQVE7QUFDbkIsWUFBWTtBQUNaO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9leHByZXNzQDQuMjEuMi9ub2RlX21vZHVsZXMvZXhwcmVzcy9saWIvdXRpbHMuanM/ZWMxZCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKiFcbiAqIGV4cHJlc3NcbiAqIENvcHlyaWdodChjKSAyMDA5LTIwMTMgVEogSG9sb3dheWNodWtcbiAqIENvcHlyaWdodChjKSAyMDE0LTIwMTUgRG91Z2xhcyBDaHJpc3RvcGhlciBXaWxzb25cbiAqIE1JVCBMaWNlbnNlZFxuICovXG5cbid1c2Ugc3RyaWN0JztcblxuLyoqXG4gKiBNb2R1bGUgZGVwZW5kZW5jaWVzLlxuICogQGFwaSBwcml2YXRlXG4gKi9cblxudmFyIEJ1ZmZlciA9IHJlcXVpcmUoJ3NhZmUtYnVmZmVyJykuQnVmZmVyXG52YXIgY29udGVudERpc3Bvc2l0aW9uID0gcmVxdWlyZSgnY29udGVudC1kaXNwb3NpdGlvbicpO1xudmFyIGNvbnRlbnRUeXBlID0gcmVxdWlyZSgnY29udGVudC10eXBlJyk7XG52YXIgZGVwcmVjYXRlID0gcmVxdWlyZSgnZGVwZCcpKCdleHByZXNzJyk7XG52YXIgZmxhdHRlbiA9IHJlcXVpcmUoJ2FycmF5LWZsYXR0ZW4nKTtcbnZhciBtaW1lID0gcmVxdWlyZSgnc2VuZCcpLm1pbWU7XG52YXIgZXRhZyA9IHJlcXVpcmUoJ2V0YWcnKTtcbnZhciBwcm94eWFkZHIgPSByZXF1aXJlKCdwcm94eS1hZGRyJyk7XG52YXIgcXMgPSByZXF1aXJlKCdxcycpO1xudmFyIHF1ZXJ5c3RyaW5nID0gcmVxdWlyZSgncXVlcnlzdHJpbmcnKTtcblxuLyoqXG4gKiBSZXR1cm4gc3Ryb25nIEVUYWcgZm9yIGBib2R5YC5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ3xCdWZmZXJ9IGJvZHlcbiAqIEBwYXJhbSB7U3RyaW5nfSBbZW5jb2RpbmddXG4gKiBAcmV0dXJuIHtTdHJpbmd9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLmV0YWcgPSBjcmVhdGVFVGFnR2VuZXJhdG9yKHsgd2VhazogZmFsc2UgfSlcblxuLyoqXG4gKiBSZXR1cm4gd2VhayBFVGFnIGZvciBgYm9keWAuXG4gKlxuICogQHBhcmFtIHtTdHJpbmd8QnVmZmVyfSBib2R5XG4gKiBAcGFyYW0ge1N0cmluZ30gW2VuY29kaW5nXVxuICogQHJldHVybiB7U3RyaW5nfVxuICogQGFwaSBwcml2YXRlXG4gKi9cblxuZXhwb3J0cy53ZXRhZyA9IGNyZWF0ZUVUYWdHZW5lcmF0b3IoeyB3ZWFrOiB0cnVlIH0pXG5cbi8qKlxuICogQ2hlY2sgaWYgYHBhdGhgIGxvb2tzIGFic29sdXRlLlxuICpcbiAqIEBwYXJhbSB7U3RyaW5nfSBwYXRoXG4gKiBAcmV0dXJuIHtCb29sZWFufVxuICogQGFwaSBwcml2YXRlXG4gKi9cblxuZXhwb3J0cy5pc0Fic29sdXRlID0gZnVuY3Rpb24ocGF0aCl7XG4gIGlmICgnLycgPT09IHBhdGhbMF0pIHJldHVybiB0cnVlO1xuICBpZiAoJzonID09PSBwYXRoWzFdICYmICgnXFxcXCcgPT09IHBhdGhbMl0gfHwgJy8nID09PSBwYXRoWzJdKSkgcmV0dXJuIHRydWU7IC8vIFdpbmRvd3MgZGV2aWNlIHBhdGhcbiAgaWYgKCdcXFxcXFxcXCcgPT09IHBhdGguc3Vic3RyaW5nKDAsIDIpKSByZXR1cm4gdHJ1ZTsgLy8gTWljcm9zb2Z0IEF6dXJlIGFic29sdXRlIHBhdGhcbn07XG5cbi8qKlxuICogRmxhdHRlbiB0aGUgZ2l2ZW4gYGFycmAuXG4gKlxuICogQHBhcmFtIHtBcnJheX0gYXJyXG4gKiBAcmV0dXJuIHtBcnJheX1cbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5cbmV4cG9ydHMuZmxhdHRlbiA9IGRlcHJlY2F0ZS5mdW5jdGlvbihmbGF0dGVuLFxuICAndXRpbHMuZmxhdHRlbjogdXNlIGFycmF5LWZsYXR0ZW4gbnBtIG1vZHVsZSBpbnN0ZWFkJyk7XG5cbi8qKlxuICogTm9ybWFsaXplIHRoZSBnaXZlbiBgdHlwZWAsIGZvciBleGFtcGxlIFwiaHRtbFwiIGJlY29tZXMgXCJ0ZXh0L2h0bWxcIi5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gdHlwZVxuICogQHJldHVybiB7T2JqZWN0fVxuICogQGFwaSBwcml2YXRlXG4gKi9cblxuZXhwb3J0cy5ub3JtYWxpemVUeXBlID0gZnVuY3Rpb24odHlwZSl7XG4gIHJldHVybiB+dHlwZS5pbmRleE9mKCcvJylcbiAgICA/IGFjY2VwdFBhcmFtcyh0eXBlKVxuICAgIDogeyB2YWx1ZTogbWltZS5sb29rdXAodHlwZSksIHBhcmFtczoge30gfTtcbn07XG5cbi8qKlxuICogTm9ybWFsaXplIGB0eXBlc2AsIGZvciBleGFtcGxlIFwiaHRtbFwiIGJlY29tZXMgXCJ0ZXh0L2h0bWxcIi5cbiAqXG4gKiBAcGFyYW0ge0FycmF5fSB0eXBlc1xuICogQHJldHVybiB7QXJyYXl9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLm5vcm1hbGl6ZVR5cGVzID0gZnVuY3Rpb24odHlwZXMpe1xuICB2YXIgcmV0ID0gW107XG5cbiAgZm9yICh2YXIgaSA9IDA7IGkgPCB0eXBlcy5sZW5ndGg7ICsraSkge1xuICAgIHJldC5wdXNoKGV4cG9ydHMubm9ybWFsaXplVHlwZSh0eXBlc1tpXSkpO1xuICB9XG5cbiAgcmV0dXJuIHJldDtcbn07XG5cbi8qKlxuICogR2VuZXJhdGUgQ29udGVudC1EaXNwb3NpdGlvbiBoZWFkZXIgYXBwcm9wcmlhdGUgZm9yIHRoZSBmaWxlbmFtZS5cbiAqIG5vbi1hc2NpaSBmaWxlbmFtZXMgYXJlIHVybGVuY29kZWQgYW5kIGEgZmlsZW5hbWUqIHBhcmFtZXRlciBpcyBhZGRlZFxuICpcbiAqIEBwYXJhbSB7U3RyaW5nfSBmaWxlbmFtZVxuICogQHJldHVybiB7U3RyaW5nfVxuICogQGFwaSBwcml2YXRlXG4gKi9cblxuZXhwb3J0cy5jb250ZW50RGlzcG9zaXRpb24gPSBkZXByZWNhdGUuZnVuY3Rpb24oY29udGVudERpc3Bvc2l0aW9uLFxuICAndXRpbHMuY29udGVudERpc3Bvc2l0aW9uOiB1c2UgY29udGVudC1kaXNwb3NpdGlvbiBucG0gbW9kdWxlIGluc3RlYWQnKTtcblxuLyoqXG4gKiBQYXJzZSBhY2NlcHQgcGFyYW1zIGBzdHJgIHJldHVybmluZyBhblxuICogb2JqZWN0IHdpdGggYC52YWx1ZWAsIGAucXVhbGl0eWAgYW5kIGAucGFyYW1zYC5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gc3RyXG4gKiBAcmV0dXJuIHtPYmplY3R9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5mdW5jdGlvbiBhY2NlcHRQYXJhbXMgKHN0cikge1xuICB2YXIgcGFydHMgPSBzdHIuc3BsaXQoLyAqOyAqLyk7XG4gIHZhciByZXQgPSB7IHZhbHVlOiBwYXJ0c1swXSwgcXVhbGl0eTogMSwgcGFyYW1zOiB7fSB9XG5cbiAgZm9yICh2YXIgaSA9IDE7IGkgPCBwYXJ0cy5sZW5ndGg7ICsraSkge1xuICAgIHZhciBwbXMgPSBwYXJ0c1tpXS5zcGxpdCgvICo9ICovKTtcbiAgICBpZiAoJ3EnID09PSBwbXNbMF0pIHtcbiAgICAgIHJldC5xdWFsaXR5ID0gcGFyc2VGbG9hdChwbXNbMV0pO1xuICAgIH0gZWxzZSB7XG4gICAgICByZXQucGFyYW1zW3Btc1swXV0gPSBwbXNbMV07XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIHJldDtcbn1cblxuLyoqXG4gKiBDb21waWxlIFwiZXRhZ1wiIHZhbHVlIHRvIGZ1bmN0aW9uLlxuICpcbiAqIEBwYXJhbSAge0Jvb2xlYW58U3RyaW5nfEZ1bmN0aW9ufSB2YWxcbiAqIEByZXR1cm4ge0Z1bmN0aW9ufVxuICogQGFwaSBwcml2YXRlXG4gKi9cblxuZXhwb3J0cy5jb21waWxlRVRhZyA9IGZ1bmN0aW9uKHZhbCkge1xuICB2YXIgZm47XG5cbiAgaWYgKHR5cGVvZiB2YWwgPT09ICdmdW5jdGlvbicpIHtcbiAgICByZXR1cm4gdmFsO1xuICB9XG5cbiAgc3dpdGNoICh2YWwpIHtcbiAgICBjYXNlIHRydWU6XG4gICAgY2FzZSAnd2Vhayc6XG4gICAgICBmbiA9IGV4cG9ydHMud2V0YWc7XG4gICAgICBicmVhaztcbiAgICBjYXNlIGZhbHNlOlxuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnc3Ryb25nJzpcbiAgICAgIGZuID0gZXhwb3J0cy5ldGFnO1xuICAgICAgYnJlYWs7XG4gICAgZGVmYXVsdDpcbiAgICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ3Vua25vd24gdmFsdWUgZm9yIGV0YWcgZnVuY3Rpb246ICcgKyB2YWwpO1xuICB9XG5cbiAgcmV0dXJuIGZuO1xufVxuXG4vKipcbiAqIENvbXBpbGUgXCJxdWVyeSBwYXJzZXJcIiB2YWx1ZSB0byBmdW5jdGlvbi5cbiAqXG4gKiBAcGFyYW0gIHtTdHJpbmd8RnVuY3Rpb259IHZhbFxuICogQHJldHVybiB7RnVuY3Rpb259XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLmNvbXBpbGVRdWVyeVBhcnNlciA9IGZ1bmN0aW9uIGNvbXBpbGVRdWVyeVBhcnNlcih2YWwpIHtcbiAgdmFyIGZuO1xuXG4gIGlmICh0eXBlb2YgdmFsID09PSAnZnVuY3Rpb24nKSB7XG4gICAgcmV0dXJuIHZhbDtcbiAgfVxuXG4gIHN3aXRjaCAodmFsKSB7XG4gICAgY2FzZSB0cnVlOlxuICAgIGNhc2UgJ3NpbXBsZSc6XG4gICAgICBmbiA9IHF1ZXJ5c3RyaW5nLnBhcnNlO1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSBmYWxzZTpcbiAgICAgIGZuID0gbmV3T2JqZWN0O1xuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnZXh0ZW5kZWQnOlxuICAgICAgZm4gPSBwYXJzZUV4dGVuZGVkUXVlcnlTdHJpbmc7XG4gICAgICBicmVhaztcbiAgICBkZWZhdWx0OlxuICAgICAgdGhyb3cgbmV3IFR5cGVFcnJvcigndW5rbm93biB2YWx1ZSBmb3IgcXVlcnkgcGFyc2VyIGZ1bmN0aW9uOiAnICsgdmFsKTtcbiAgfVxuXG4gIHJldHVybiBmbjtcbn1cblxuLyoqXG4gKiBDb21waWxlIFwicHJveHkgdHJ1c3RcIiB2YWx1ZSB0byBmdW5jdGlvbi5cbiAqXG4gKiBAcGFyYW0gIHtCb29sZWFufFN0cmluZ3xOdW1iZXJ8QXJyYXl8RnVuY3Rpb259IHZhbFxuICogQHJldHVybiB7RnVuY3Rpb259XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5leHBvcnRzLmNvbXBpbGVUcnVzdCA9IGZ1bmN0aW9uKHZhbCkge1xuICBpZiAodHlwZW9mIHZhbCA9PT0gJ2Z1bmN0aW9uJykgcmV0dXJuIHZhbDtcblxuICBpZiAodmFsID09PSB0cnVlKSB7XG4gICAgLy8gU3VwcG9ydCBwbGFpbiB0cnVlL2ZhbHNlXG4gICAgcmV0dXJuIGZ1bmN0aW9uKCl7IHJldHVybiB0cnVlIH07XG4gIH1cblxuICBpZiAodHlwZW9mIHZhbCA9PT0gJ251bWJlcicpIHtcbiAgICAvLyBTdXBwb3J0IHRydXN0aW5nIGhvcCBjb3VudFxuICAgIHJldHVybiBmdW5jdGlvbihhLCBpKXsgcmV0dXJuIGkgPCB2YWwgfTtcbiAgfVxuXG4gIGlmICh0eXBlb2YgdmFsID09PSAnc3RyaW5nJykge1xuICAgIC8vIFN1cHBvcnQgY29tbWEtc2VwYXJhdGVkIHZhbHVlc1xuICAgIHZhbCA9IHZhbC5zcGxpdCgnLCcpXG4gICAgICAubWFwKGZ1bmN0aW9uICh2KSB7IHJldHVybiB2LnRyaW0oKSB9KVxuICB9XG5cbiAgcmV0dXJuIHByb3h5YWRkci5jb21waWxlKHZhbCB8fCBbXSk7XG59XG5cbi8qKlxuICogU2V0IHRoZSBjaGFyc2V0IGluIGEgZ2l2ZW4gQ29udGVudC1UeXBlIHN0cmluZy5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gdHlwZVxuICogQHBhcmFtIHtTdHJpbmd9IGNoYXJzZXRcbiAqIEByZXR1cm4ge1N0cmluZ31cbiAqIEBhcGkgcHJpdmF0ZVxuICovXG5cbmV4cG9ydHMuc2V0Q2hhcnNldCA9IGZ1bmN0aW9uIHNldENoYXJzZXQodHlwZSwgY2hhcnNldCkge1xuICBpZiAoIXR5cGUgfHwgIWNoYXJzZXQpIHtcbiAgICByZXR1cm4gdHlwZTtcbiAgfVxuXG4gIC8vIHBhcnNlIHR5cGVcbiAgdmFyIHBhcnNlZCA9IGNvbnRlbnRUeXBlLnBhcnNlKHR5cGUpO1xuXG4gIC8vIHNldCBjaGFyc2V0XG4gIHBhcnNlZC5wYXJhbWV0ZXJzLmNoYXJzZXQgPSBjaGFyc2V0O1xuXG4gIC8vIGZvcm1hdCB0eXBlXG4gIHJldHVybiBjb250ZW50VHlwZS5mb3JtYXQocGFyc2VkKTtcbn07XG5cbi8qKlxuICogQ3JlYXRlIGFuIEVUYWcgZ2VuZXJhdG9yIGZ1bmN0aW9uLCBnZW5lcmF0aW5nIEVUYWdzIHdpdGhcbiAqIHRoZSBnaXZlbiBvcHRpb25zLlxuICpcbiAqIEBwYXJhbSB7b2JqZWN0fSBvcHRpb25zXG4gKiBAcmV0dXJuIHtmdW5jdGlvbn1cbiAqIEBwcml2YXRlXG4gKi9cblxuZnVuY3Rpb24gY3JlYXRlRVRhZ0dlbmVyYXRvciAob3B0aW9ucykge1xuICByZXR1cm4gZnVuY3Rpb24gZ2VuZXJhdGVFVGFnIChib2R5LCBlbmNvZGluZykge1xuICAgIHZhciBidWYgPSAhQnVmZmVyLmlzQnVmZmVyKGJvZHkpXG4gICAgICA/IEJ1ZmZlci5mcm9tKGJvZHksIGVuY29kaW5nKVxuICAgICAgOiBib2R5XG5cbiAgICByZXR1cm4gZXRhZyhidWYsIG9wdGlvbnMpXG4gIH1cbn1cblxuLyoqXG4gKiBQYXJzZSBhbiBleHRlbmRlZCBxdWVyeSBzdHJpbmcgd2l0aCBxcy5cbiAqXG4gKiBAcGFyYW0ge1N0cmluZ30gc3RyXG4gKiBAcmV0dXJuIHtPYmplY3R9XG4gKiBAcHJpdmF0ZVxuICovXG5cbmZ1bmN0aW9uIHBhcnNlRXh0ZW5kZWRRdWVyeVN0cmluZyhzdHIpIHtcbiAgcmV0dXJuIHFzLnBhcnNlKHN0ciwge1xuICAgIGFsbG93UHJvdG90eXBlczogdHJ1ZVxuICB9KTtcbn1cblxuLyoqXG4gKiBSZXR1cm4gbmV3IGVtcHR5IG9iamVjdC5cbiAqXG4gKiBAcmV0dXJuIHtPYmplY3R9XG4gKiBAYXBpIHByaXZhdGVcbiAqL1xuXG5mdW5jdGlvbiBuZXdPYmplY3QoKSB7XG4gIHJldHVybiB7fTtcbn1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/utils.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/view.js":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/view.js ***!
  \****************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * express\n * Copyright(c) 2009-2013 TJ Holowaychuk\n * Copyright(c) 2013 Roman Shtylman\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar debug = __webpack_require__(/*! debug */ \"(rsc)/./node_modules/.pnpm/debug@2.6.9/node_modules/debug/src/index.js\")('express:view');\nvar path = __webpack_require__(/*! path */ \"path\");\nvar fs = __webpack_require__(/*! fs */ \"fs\");\n\n/**\n * Module variables.\n * @private\n */\n\nvar dirname = path.dirname;\nvar basename = path.basename;\nvar extname = path.extname;\nvar join = path.join;\nvar resolve = path.resolve;\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = View;\n\n/**\n * Initialize a new `View` with the given `name`.\n *\n * Options:\n *\n *   - `defaultEngine` the default template engine name\n *   - `engines` template engine require() cache\n *   - `root` root path for view lookup\n *\n * @param {string} name\n * @param {object} options\n * @public\n */\n\nfunction View(name, options) {\n  var opts = options || {};\n\n  this.defaultEngine = opts.defaultEngine;\n  this.ext = extname(name);\n  this.name = name;\n  this.root = opts.root;\n\n  if (!this.ext && !this.defaultEngine) {\n    throw new Error('No default engine was specified and no extension was provided.');\n  }\n\n  var fileName = name;\n\n  if (!this.ext) {\n    // get extension from default engine name\n    this.ext = this.defaultEngine[0] !== '.'\n      ? '.' + this.defaultEngine\n      : this.defaultEngine;\n\n    fileName += this.ext;\n  }\n\n  if (!opts.engines[this.ext]) {\n    // load engine\n    var mod = this.ext.slice(1)\n    debug('require \"%s\"', mod)\n\n    // default engine export\n    var fn = __webpack_require__(\"(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib sync recursive\")(mod).__express\n\n    if (typeof fn !== 'function') {\n      throw new Error('Module \"' + mod + '\" does not provide a view engine.')\n    }\n\n    opts.engines[this.ext] = fn\n  }\n\n  // store loaded engine\n  this.engine = opts.engines[this.ext];\n\n  // lookup path\n  this.path = this.lookup(fileName);\n}\n\n/**\n * Lookup view by the given `name`\n *\n * @param {string} name\n * @private\n */\n\nView.prototype.lookup = function lookup(name) {\n  var path;\n  var roots = [].concat(this.root);\n\n  debug('lookup \"%s\"', name);\n\n  for (var i = 0; i < roots.length && !path; i++) {\n    var root = roots[i];\n\n    // resolve the path\n    var loc = resolve(root, name);\n    var dir = dirname(loc);\n    var file = basename(loc);\n\n    // resolve the file\n    path = this.resolve(dir, file);\n  }\n\n  return path;\n};\n\n/**\n * Render with the given options.\n *\n * @param {object} options\n * @param {function} callback\n * @private\n */\n\nView.prototype.render = function render(options, callback) {\n  debug('render \"%s\"', this.path);\n  this.engine(this.path, options, callback);\n};\n\n/**\n * Resolve the file within the given directory.\n *\n * @param {string} dir\n * @param {string} file\n * @private\n */\n\nView.prototype.resolve = function resolve(dir, file) {\n  var ext = this.ext;\n\n  // <path>.<ext>\n  var path = join(dir, file);\n  var stat = tryStat(path);\n\n  if (stat && stat.isFile()) {\n    return path;\n  }\n\n  // <path>/index.<ext>\n  path = join(dir, basename(file, ext), 'index' + ext);\n  stat = tryStat(path);\n\n  if (stat && stat.isFile()) {\n    return path;\n  }\n};\n\n/**\n * Return a stat, maybe.\n *\n * @param {string} path\n * @return {fs.Stats}\n * @private\n */\n\nfunction tryStat(path) {\n  debug('stat \"%s\"', path);\n\n  try {\n    return fs.statSync(path);\n  } catch (e) {\n    return undefined;\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/express@4.21.2/node_modules/express/lib/view.js\n");

/***/ })

};
;