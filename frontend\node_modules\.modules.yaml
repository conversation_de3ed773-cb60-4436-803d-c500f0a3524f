hoistPattern:
  - '*'
hoistedDependencies:
  '@ai-sdk/provider-utils@2.2.0(zod@3.24.2)':
    '@ai-sdk/provider-utils': private
  '@ai-sdk/provider@1.1.0':
    '@ai-sdk/provider': private
  '@ai-sdk/ui-utils@0.0.5(zod@3.24.2)':
    '@ai-sdk/ui-utils': private
  '@alloc/quick-lru@5.2.0':
    '@alloc/quick-lru': private
  '@ariakit/core@0.4.14':
    '@ariakit/core': private
  '@ariakit/react-core@0.4.15(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@ariakit/react-core': private
  '@babel/runtime@7.26.10':
    '@babel/runtime': private
  '@cfworker/json-schema@4.1.1':
    '@cfworker/json-schema': private
  '@cspotcode/source-map-support@0.8.1':
    '@cspotcode/source-map-support': private
  '@dnd-kit/accessibility@3.1.1(react@18.2.0)':
    '@dnd-kit/accessibility': private
  '@effect/platform@0.72.0(effect@3.12.0)':
    '@effect/platform': private
  '@emoji-mart/data@1.2.1':
    '@emoji-mart/data': private
  '@eslint-community/eslint-utils@4.5.1(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@excalidraw/excalidraw@0.16.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@excalidraw/excalidraw': private
  '@floating-ui/core@1.6.9':
    '@floating-ui/core': private
  '@floating-ui/dom@1.6.13':
    '@floating-ui/dom': private
  '@floating-ui/react-dom@2.1.2(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@floating-ui/react-dom': private
  '@floating-ui/react@0.26.28(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@floating-ui/react': private
  '@floating-ui/utils@0.2.9':
    '@floating-ui/utils': private
  '@fortawesome/fontawesome-common-types@6.7.2':
    '@fortawesome/fontawesome-common-types': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@isaacs/cliui@8.0.2':
    '@isaacs/cliui': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.9':
    '@jridgewell/trace-mapping': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@langchain/textsplitters@0.1.0(@langchain/core@0.3.36(openai@4.89.0(zod@3.24.2)))':
    '@langchain/textsplitters': private
  '@next/env@14.2.23':
    '@next/env': private
  '@next/eslint-plugin-next@14.2.25':
    '@next/eslint-plugin-next': private
  '@next/swc-win32-x64-msvc@14.2.23':
    '@next/swc-win32-x64-msvc': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@nolyfill/is-core-module@1.0.39':
    '@nolyfill/is-core-module': private
  '@opentelemetry/api@1.9.0':
    '@opentelemetry/api': private
  '@pkgjs/parseargs@0.11.0':
    '@pkgjs/parseargs': private
  '@prisma/debug@5.22.0':
    '@prisma/debug': private
  '@prisma/engines-version@5.22.0-44.605197351a3c8bdd595af2d2a9bc3025bca48ea2':
    '@prisma/engines-version': private
  '@prisma/engines@5.22.0':
    '@prisma/engines': private
  '@prisma/fetch-engine@5.22.0':
    '@prisma/fetch-engine': private
  '@prisma/get-platform@5.22.0':
    '@prisma/get-platform': private
  '@radix-ui/number@1.1.0':
    '@radix-ui/number': private
  '@radix-ui/primitive@1.1.1':
    '@radix-ui/primitive': private
  '@radix-ui/react-arrow@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-arrow': private
  '@radix-ui/react-collection@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-collection': private
  '@radix-ui/react-compose-refs@1.1.1(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-compose-refs': private
  '@radix-ui/react-context@1.1.1(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-context': private
  '@radix-ui/react-direction@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-direction': private
  '@radix-ui/react-dismissable-layer@1.1.5(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-dismissable-layer': private
  '@radix-ui/react-focus-guards@1.1.1(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-focus-guards': private
  '@radix-ui/react-focus-scope@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-focus-scope': private
  '@radix-ui/react-id@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-id': private
  '@radix-ui/react-menu@2.1.6(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-menu': private
  '@radix-ui/react-popper@1.2.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-popper': private
  '@radix-ui/react-portal@1.1.4(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-portal': private
  '@radix-ui/react-presence@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-presence': private
  '@radix-ui/react-primitive@2.0.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-primitive': private
  '@radix-ui/react-roving-focus@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-roving-focus': private
  '@radix-ui/react-use-callback-ref@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-callback-ref': private
  '@radix-ui/react-use-controllable-state@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-controllable-state': private
  '@radix-ui/react-use-escape-keydown@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-escape-keydown': private
  '@radix-ui/react-use-layout-effect@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-layout-effect': private
  '@radix-ui/react-use-previous@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-previous': private
  '@radix-ui/react-use-rect@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-rect': private
  '@radix-ui/react-use-size@1.1.0(@types/react@18.3.20)(react@18.2.0)':
    '@radix-ui/react-use-size': private
  '@radix-ui/react-visually-hidden@1.1.2(@types/react-dom@18.3.5(@types/react@18.3.20))(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@radix-ui/react-visually-hidden': private
  '@radix-ui/rect@1.1.0':
    '@radix-ui/rect': private
  '@react-dnd/asap@5.0.2':
    '@react-dnd/asap': private
  '@react-dnd/invariant@4.0.2':
    '@react-dnd/invariant': private
  '@react-dnd/shallowequal@4.0.2':
    '@react-dnd/shallowequal': private
  '@rtsao/scc@1.1.0':
    '@rtsao/scc': private
  '@rushstack/eslint-patch@1.11.0':
    '@rushstack/eslint-patch': private
  '@standard-schema/spec@1.0.0-beta.4':
    '@standard-schema/spec': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/helpers@0.5.5':
    '@swc/helpers': private
  '@t3-oss/env-core@0.10.1(typescript@5.8.2)(zod@3.24.2)':
    '@t3-oss/env-core': private
  '@tanstack/query-core@5.69.0':
    '@tanstack/query-core': private
  '@tsconfig/node10@1.0.11':
    '@tsconfig/node10': private
  '@tsconfig/node12@1.0.11':
    '@tsconfig/node12': private
  '@tsconfig/node14@1.0.3':
    '@tsconfig/node14': private
  '@tsconfig/node16@1.0.4':
    '@tsconfig/node16': private
  '@types/body-parser@1.19.6':
    '@types/body-parser': private
  '@types/connect@3.4.38':
    '@types/connect': private
  '@types/cors@2.8.19':
    '@types/cors': private
  '@types/d3-array@3.2.1':
    '@types/d3-array': private
  '@types/d3-color@3.1.3':
    '@types/d3-color': private
  '@types/d3-ease@3.0.2':
    '@types/d3-ease': private
  '@types/d3-interpolate@3.0.4':
    '@types/d3-interpolate': private
  '@types/d3-path@3.1.1':
    '@types/d3-path': private
  '@types/d3-scale@4.0.9':
    '@types/d3-scale': private
  '@types/d3-shape@3.1.7':
    '@types/d3-shape': private
  '@types/d3-time@3.0.4':
    '@types/d3-time': private
  '@types/d3-timer@3.0.2':
    '@types/d3-timer': private
  '@types/debug@4.1.12':
    '@types/debug': private
  '@types/diff-match-patch@1.0.36':
    '@types/diff-match-patch': private
  '@types/eslint-scope@3.7.7':
    '@types/eslint-scope': private
  '@types/estree@1.0.7':
    '@types/estree': private
  '@types/express-serve-static-core@5.0.6':
    '@types/express-serve-static-core': private
  '@types/express@5.0.3':
    '@types/express': private
  '@types/http-errors@2.0.5':
    '@types/http-errors': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/json5@0.0.29':
    '@types/json5': private
  '@types/linkify-it@5.0.0':
    '@types/linkify-it': private
  '@types/lodash@4.17.16':
    '@types/lodash': private
  '@types/markdown-it@14.1.2':
    '@types/markdown-it': private
  '@types/mdast@4.0.4':
    '@types/mdast': private
  '@types/mdurl@2.0.0':
    '@types/mdurl': private
  '@types/mime@1.3.5':
    '@types/mime': private
  '@types/ms@2.1.0':
    '@types/ms': private
  '@types/node-fetch@2.6.12':
    '@types/node-fetch': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  '@types/qs@6.14.0':
    '@types/qs': private
  '@types/range-parser@1.2.7':
    '@types/range-parser': private
  '@types/retry@0.12.0':
    '@types/retry': private
  '@types/send@0.17.5':
    '@types/send': private
  '@types/serve-static@1.15.8':
    '@types/serve-static': private
  '@types/unist@3.0.3':
    '@types/unist': private
  '@types/uuid@10.0.0':
    '@types/uuid': private
  '@typescript-eslint/scope-manager@7.18.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/type-utils@7.18.0(eslint@8.57.1)(typescript@5.8.2)':
    '@typescript-eslint/type-utils': private
  '@typescript-eslint/types@7.18.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@7.18.0(typescript@5.8.2)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@7.18.0(eslint@8.57.1)(typescript@5.8.2)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@7.18.0':
    '@typescript-eslint/visitor-keys': private
  '@udecode/plate-csv@41.0.9(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-csv': private
  '@udecode/plate-diff@41.0.0(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-diff': private
  '@udecode/plate-docx@41.0.10(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-docx': private
  '@udecode/plate-find-replace@41.0.0(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-find-replace': private
  '@udecode/plate-normalizers@41.0.0(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-normalizers': private
  '@udecode/plate-suggestion@41.0.0(@udecode/plate-common@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0))(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-suggestion': private
  '@udecode/plate-utils@41.0.13(@types/react@18.3.20)(immer@10.1.1)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(slate-dom@0.112.2(slate@0.103.0))(slate-history@0.110.3(slate@0.103.0))(slate-hyperscript@0.100.0(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/plate-utils': private
  '@udecode/react-hotkeys@37.0.0(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@udecode/react-hotkeys': private
  '@udecode/react-utils@40.2.8(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)':
    '@udecode/react-utils': private
  '@udecode/slate-react@41.0.5(@types/react@18.3.20)(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate-history@0.110.3(slate@0.103.0))(slate-react@0.110.3(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(slate@0.103.0))(slate@0.103.0)':
    '@udecode/slate-react': private
  '@udecode/slate-utils@41.0.0(slate-history@0.110.3(slate@0.103.0))(slate@0.103.0)':
    '@udecode/slate-utils': private
  '@udecode/slate@41.0.0(slate-history@0.110.3(slate@0.103.0))(slate@0.103.0)':
    '@udecode/slate': private
  '@udecode/utils@37.0.0':
    '@udecode/utils': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@unrs/rspack-resolver-binding-win32-x64-msvc@1.2.2':
    '@unrs/rspack-resolver-binding-win32-x64-msvc': private
  '@uploadthing/mime-types@0.3.4':
    '@uploadthing/mime-types': private
  '@uploadthing/shared@7.1.7':
    '@uploadthing/shared': private
  '@webassemblyjs/ast@1.14.1':
    '@webassemblyjs/ast': private
  '@webassemblyjs/floating-point-hex-parser@1.13.2':
    '@webassemblyjs/floating-point-hex-parser': private
  '@webassemblyjs/helper-api-error@1.13.2':
    '@webassemblyjs/helper-api-error': private
  '@webassemblyjs/helper-buffer@1.14.1':
    '@webassemblyjs/helper-buffer': private
  '@webassemblyjs/helper-numbers@1.13.2':
    '@webassemblyjs/helper-numbers': private
  '@webassemblyjs/helper-wasm-bytecode@1.13.2':
    '@webassemblyjs/helper-wasm-bytecode': private
  '@webassemblyjs/helper-wasm-section@1.14.1':
    '@webassemblyjs/helper-wasm-section': private
  '@webassemblyjs/ieee754@1.13.2':
    '@webassemblyjs/ieee754': private
  '@webassemblyjs/leb128@1.13.2':
    '@webassemblyjs/leb128': private
  '@webassemblyjs/utf8@1.13.2':
    '@webassemblyjs/utf8': private
  '@webassemblyjs/wasm-edit@1.14.1':
    '@webassemblyjs/wasm-edit': private
  '@webassemblyjs/wasm-gen@1.14.1':
    '@webassemblyjs/wasm-gen': private
  '@webassemblyjs/wasm-opt@1.14.1':
    '@webassemblyjs/wasm-opt': private
  '@webassemblyjs/wasm-parser@1.14.1':
    '@webassemblyjs/wasm-parser': private
  '@webassemblyjs/wast-printer@1.14.1':
    '@webassemblyjs/wast-printer': private
  '@xtuc/ieee754@1.2.0':
    '@xtuc/ieee754': private
  '@xtuc/long@4.2.2':
    '@xtuc/long': private
  abort-controller@3.0.0:
    abort-controller: private
  accepts@1.3.8:
    accepts: private
  acorn-jsx@5.3.2(acorn@8.14.1):
    acorn-jsx: private
  acorn-walk@8.3.4:
    acorn-walk: private
  acorn@8.14.1:
    acorn: private
  agentkeepalive@4.6.0:
    agentkeepalive: private
  ajv-formats@2.1.1(ajv@8.17.1):
    ajv-formats: private
  ajv-keywords@5.1.0(ajv@8.17.1):
    ajv-keywords: private
  ajv@6.12.6:
    ajv: private
  ansi-colors@4.1.3:
    ansi-colors: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@5.2.0:
    ansi-styles: private
  any-promise@1.3.0:
    any-promise: private
  anymatch@3.1.3:
    anymatch: private
  arg@5.0.2:
    arg: private
  argparse@2.0.1:
    argparse: private
  aria-hidden@1.2.4:
    aria-hidden: private
  aria-query@5.3.2:
    aria-query: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-flatten@1.1.1:
    array-flatten: private
  array-includes@3.1.8:
    array-includes: private
  array-union@2.1.0:
    array-union: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.findlastindex@1.2.6:
    array.prototype.findlastindex: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  ast-types-flow@0.0.8:
    ast-types-flow: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  attr-accept@2.2.5:
    attr-accept: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  axe-core@4.10.3:
    axe-core: private
  axobject-query@4.1.0:
    axobject-query: private
  bail@2.0.2:
    bail: private
  balanced-match@1.0.2:
    balanced-match: private
  base64-js@1.5.1:
    base64-js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  body-parser@2.2.0:
    body-parser: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  browserslist@4.24.4:
    browserslist: private
  buffer-from@1.1.2:
    buffer-from: private
  busboy@1.6.0:
    busboy: private
  bytes@3.1.2:
    bytes: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.4:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  camelcase-css@2.0.1:
    camelcase-css: private
  camelcase@6.3.0:
    camelcase: private
  caniuse-lite@1.0.30001707:
    caniuse-lite: private
  ccount@2.0.1:
    ccount: private
  chalk@4.1.2:
    chalk: private
  character-entities@2.0.2:
    character-entities: private
  cheerio-select@1.6.0:
    cheerio-select: private
  cheerio@1.0.0-rc.10:
    cheerio: private
  chokidar@3.6.0:
    chokidar: private
  chrome-trace-event@1.0.4:
    chrome-trace-event: private
  client-only@0.0.1:
    client-only: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@6.2.1:
    commander: private
  compute-scroll-into-view@3.1.1:
    compute-scroll-into-view: private
  concat-map@0.0.1:
    concat-map: private
  console-table-printer@2.12.1:
    console-table-printer: private
  content-disposition@0.5.4:
    content-disposition: private
  content-type@1.0.5:
    content-type: private
  cookie-signature@1.0.6:
    cookie-signature: private
  cookie@0.7.1:
    cookie: private
  copy-to-clipboard@3.3.3:
    copy-to-clipboard: private
  cors@2.8.5:
    cors: private
  create-require@1.1.1:
    create-require: private
  cross-spawn@7.0.6:
    cross-spawn: private
  css-select@4.3.0:
    css-select: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  d3-array@3.2.4:
    d3-array: private
  d3-color@3.1.0:
    d3-color: private
  d3-ease@3.0.1:
    d3-ease: private
  d3-format@3.1.0:
    d3-format: private
  d3-interpolate@3.0.1:
    d3-interpolate: private
  d3-path@3.1.0:
    d3-path: private
  d3-scale@4.0.2:
    d3-scale: private
  d3-shape@3.2.0:
    d3-shape: private
  d3-time-format@4.1.0:
    d3-time-format: private
  d3-time@3.1.0:
    d3-time: private
  d3-timer@3.0.1:
    d3-timer: private
  damerau-levenshtein@1.0.8:
    damerau-levenshtein: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  decamelize@1.2.0:
    decamelize: private
  decimal.js-light@2.5.1:
    decimal.js-light: private
  decode-named-character-reference@1.1.0:
    decode-named-character-reference: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  depd@2.0.0:
    depd: private
  dequal@2.0.3:
    dequal: private
  destroy@1.2.0:
    destroy: private
  detect-node-es@1.1.0:
    detect-node-es: private
  devlop@1.1.0:
    devlop: private
  didyoumean@1.2.2:
    didyoumean: private
  diff-match-patch-ts@0.6.0:
    diff-match-patch-ts: private
  diff-match-patch@1.0.5:
    diff-match-patch: private
  diff@4.0.2:
    diff: private
  dir-glob@3.0.1:
    dir-glob: private
  direction@1.0.4:
    direction: private
  dlv@1.1.3:
    dlv: private
  dnd-core@16.0.1:
    dnd-core: private
  doctrine@3.0.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dunder-proto@1.0.1:
    dunder-proto: private
  eastasianwidth@0.2.0:
    eastasianwidth: private
  ee-first@1.1.1:
    ee-first: private
  effect@3.12.0:
    effect: private
  electron-to-chromium@1.5.123:
    electron-to-chromium: private
  embla-carousel-reactive-utils@8.5.2(embla-carousel@8.5.2):
    embla-carousel-reactive-utils: private
  embla-carousel@8.5.2:
    embla-carousel: private
  emoji-regex@9.2.2:
    emoji-regex: private
  encodeurl@2.0.0:
    encodeurl: private
  enhanced-resolve@5.18.1:
    enhanced-resolve: private
  entities@4.5.0:
    entities: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-module-lexer@1.6.0:
    es-module-lexer: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  escalade@3.2.0:
    escalade: private
  escape-goat@3.0.0:
    escape-goat: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-import-resolver-node@0.3.9:
    eslint-import-resolver-node: private
  eslint-import-resolver-typescript@3.9.1(eslint-plugin-import@2.31.0)(eslint@8.57.1):
    eslint-import-resolver-typescript: private
  eslint-module-utils@2.12.0(@typescript-eslint/parser@7.18.0(eslint@8.57.1)(typescript@5.8.2))(eslint-import-resolver-node@0.3.9)(eslint-import-resolver-typescript@3.9.1)(eslint@8.57.1):
    eslint-module-utils: private
  eslint-plugin-import@2.31.0(@typescript-eslint/parser@7.18.0(eslint@8.57.1)(typescript@5.8.2))(eslint-import-resolver-typescript@3.9.1)(eslint@8.57.1):
    eslint-plugin-import: private
  eslint-plugin-jsx-a11y@6.10.2(eslint@8.57.1):
    eslint-plugin-jsx-a11y: private
  eslint-plugin-react-hooks@5.0.0-canary-7118f5dd7-20230705(eslint@8.57.1):
    eslint-plugin-react-hooks: private
  eslint-plugin-react@7.37.4(eslint@8.57.1):
    eslint-plugin-react: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  event-target-shim@5.0.1:
    event-target-shim: private
  eventemitter3@4.0.7:
    eventemitter3: private
  events@3.3.0:
    events: private
  eventsource-parser@3.0.0:
    eventsource-parser: private
  express@4.21.2:
    express: private
  extend@3.0.2:
    extend: private
  fast-check@3.23.2:
    fast-check: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-equals@5.2.2:
    fast-equals: private
  fast-glob@3.3.3:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fast-uri@3.0.6:
    fast-uri: private
  fastq@1.19.1:
    fastq: private
  fdir@6.4.3(picomatch@4.0.2):
    fdir: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  file-selector@0.6.0:
    file-selector: private
  fill-range@7.1.1:
    fill-range: private
  finalhandler@1.3.1:
    finalhandler: private
  find-my-way-ts@0.1.5:
    find-my-way-ts: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  for-each@0.3.5:
    for-each: private
  foreground-child@3.3.1:
    foreground-child: private
  form-data-encoder@1.7.2:
    form-data-encoder: private
  form-data@4.0.2:
    form-data: private
  formdata-node@4.4.1:
    formdata-node: private
  forwarded@0.2.0:
    forwarded: private
  fresh@0.5.2:
    fresh: private
  fs.realpath@1.0.0:
    fs.realpath: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-nonce@1.0.1:
    get-nonce: private
  get-proto@1.0.1:
    get-proto: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  get-tsconfig@4.10.0:
    get-tsconfig: private
  glob-parent@6.0.2:
    glob-parent: private
  glob-to-regexp@0.4.1:
    glob-to-regexp: private
  glob@10.3.10:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.2.0:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  hoist-non-react-statics@3.3.2:
    hoist-non-react-statics: private
  html-entities@2.5.3:
    html-entities: private
  htmlparser2@6.1.0:
    htmlparser2: private
  http-errors@2.0.0:
    http-errors: private
  humanize-ms@1.2.1:
    humanize-ms: private
  iconv-lite@0.6.3:
    iconv-lite: private
  ignore@5.3.2:
    ignore: private
  immer@10.1.1:
    immer: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.1.0:
    internal-slot: private
  internmap@2.0.3:
    internmap: private
  ipaddr.js@1.9.1:
    ipaddr.js: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-bun-module@1.3.0:
    is-bun-module: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-hotkey@0.2.0:
    is-hotkey: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-number@7.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@4.1.0:
    is-plain-obj: private
  is-plain-object@5.0.0:
    is-plain-object: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  jackspeak@2.3.6:
    jackspeak: private
  jest-worker@27.5.1:
    jest-worker: private
  jiti@1.21.7:
    jiti: private
  jotai-optics@0.4.0(jotai@2.8.4(@types/react@18.3.20)(react@18.2.0))(optics-ts@2.4.1):
    jotai-optics: private
  jotai-x@1.2.4(@types/react@18.3.20)(jotai@2.8.4(@types/react@18.3.20)(react@18.2.0))(react@18.2.0):
    jotai-x: private
  jotai@2.8.4(@types/react@18.3.20)(react@18.2.0):
    jotai: private
  js-tiktoken@1.0.19:
    js-tiktoken: private
  js-tokens@4.0.0:
    js-tokens: private
  js-video-url-parser@0.5.1:
    js-video-url-parser: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-parse-even-better-errors@2.3.1:
    json-parse-even-better-errors: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-schema@0.4.0:
    json-schema: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsondiffpatch@0.6.0:
    jsondiffpatch: private
  jsonpointer@5.0.1:
    jsonpointer: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  juice@8.1.0:
    juice: private
  katex@0.16.11:
    katex: private
  keyv@4.5.4:
    keyv: private
  langsmith@0.3.14(openai@4.89.0(zod@3.24.2)):
    langsmith: private
  language-subtag-registry@0.3.23:
    language-subtag-registry: private
  language-tags@1.0.9:
    language-tags: private
  levn@0.4.1:
    levn: private
  lilconfig@3.1.3:
    lilconfig: private
  lines-and-columns@1.2.4:
    lines-and-columns: private
  linkify-it@5.0.0:
    linkify-it: private
  loader-runner@4.3.0:
    loader-runner: private
  locate-path@6.0.0:
    locate-path: private
  lodash.castarray@4.4.0:
    lodash.castarray: private
  lodash.isplainobject@4.0.6:
    lodash.isplainobject: private
  lodash.mapvalues@4.6.0:
    lodash.mapvalues: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  longest-streak@3.1.0:
    longest-streak: private
  loose-envify@1.4.0:
    loose-envify: private
  lru-cache@10.4.3:
    lru-cache: private
  make-error@1.3.6:
    make-error: private
  markdown-it@14.1.0:
    markdown-it: private
  markdown-table@3.0.4:
    markdown-table: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mdast-util-find-and-replace@3.0.2:
    mdast-util-find-and-replace: private
  mdast-util-from-markdown@2.0.2:
    mdast-util-from-markdown: private
  mdast-util-gfm-autolink-literal@2.0.1:
    mdast-util-gfm-autolink-literal: private
  mdast-util-gfm-footnote@2.1.0:
    mdast-util-gfm-footnote: private
  mdast-util-gfm-strikethrough@2.0.0:
    mdast-util-gfm-strikethrough: private
  mdast-util-gfm-table@2.0.0:
    mdast-util-gfm-table: private
  mdast-util-gfm-task-list-item@2.0.0:
    mdast-util-gfm-task-list-item: private
  mdast-util-gfm@3.1.0:
    mdast-util-gfm: private
  mdast-util-phrasing@4.1.0:
    mdast-util-phrasing: private
  mdast-util-to-markdown@2.1.2:
    mdast-util-to-markdown: private
  mdast-util-to-string@4.0.0:
    mdast-util-to-string: private
  mdurl@2.0.0:
    mdurl: private
  media-typer@0.3.0:
    media-typer: private
  mensch@0.3.4:
    mensch: private
  merge-descriptors@1.0.3:
    merge-descriptors: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  methods@1.1.2:
    methods: private
  micromark-core-commonmark@2.0.3:
    micromark-core-commonmark: private
  micromark-extension-gfm-autolink-literal@2.1.0:
    micromark-extension-gfm-autolink-literal: private
  micromark-extension-gfm-footnote@2.1.0:
    micromark-extension-gfm-footnote: private
  micromark-extension-gfm-strikethrough@2.1.0:
    micromark-extension-gfm-strikethrough: private
  micromark-extension-gfm-table@2.1.1:
    micromark-extension-gfm-table: private
  micromark-extension-gfm-tagfilter@2.0.0:
    micromark-extension-gfm-tagfilter: private
  micromark-extension-gfm-task-list-item@2.1.0:
    micromark-extension-gfm-task-list-item: private
  micromark-extension-gfm@3.0.0:
    micromark-extension-gfm: private
  micromark-factory-destination@2.0.1:
    micromark-factory-destination: private
  micromark-factory-label@2.0.1:
    micromark-factory-label: private
  micromark-factory-space@2.0.1:
    micromark-factory-space: private
  micromark-factory-title@2.0.1:
    micromark-factory-title: private
  micromark-factory-whitespace@2.0.1:
    micromark-factory-whitespace: private
  micromark-util-character@2.1.1:
    micromark-util-character: private
  micromark-util-chunked@2.0.1:
    micromark-util-chunked: private
  micromark-util-classify-character@2.0.1:
    micromark-util-classify-character: private
  micromark-util-combine-extensions@2.0.1:
    micromark-util-combine-extensions: private
  micromark-util-decode-numeric-character-reference@2.0.2:
    micromark-util-decode-numeric-character-reference: private
  micromark-util-decode-string@2.0.1:
    micromark-util-decode-string: private
  micromark-util-encode@2.0.1:
    micromark-util-encode: private
  micromark-util-html-tag-name@2.0.1:
    micromark-util-html-tag-name: private
  micromark-util-normalize-identifier@2.0.1:
    micromark-util-normalize-identifier: private
  micromark-util-resolve-all@2.0.1:
    micromark-util-resolve-all: private
  micromark-util-sanitize-uri@2.0.1:
    micromark-util-sanitize-uri: private
  micromark-util-subtokenize@2.1.0:
    micromark-util-subtokenize: private
  micromark-util-symbol@2.0.1:
    micromark-util-symbol: private
  micromark-util-types@2.0.2:
    micromark-util-types: private
  micromark@4.0.2:
    micromark: private
  micromatch@4.0.8:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mime@1.6.0:
    mime: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  minipass@7.1.2:
    minipass: private
  ms@2.1.3:
    ms: private
  multipasta@0.2.5:
    multipasta: private
  mustache@4.2.0:
    mustache: private
  mz@2.7.0:
    mz: private
  natural-compare@1.4.0:
    natural-compare: private
  negotiator@0.6.3:
    negotiator: private
  neo-async@2.6.2:
    neo-async: private
  node-domexception@1.0.0:
    node-domexception: private
  node-fetch@2.7.0:
    node-fetch: private
  node-releases@2.0.19:
    node-releases: private
  normalize-path@3.0.0:
    normalize-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-hash@3.0.0:
    object-hash: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.9:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.groupby@1.0.3:
    object.groupby: private
  object.values@1.2.1:
    object.values: private
  on-finished@2.4.1:
    on-finished: private
  once@1.4.0:
    once: private
  openai@4.89.0(zod@3.24.2):
    openai: private
  openapi-types@12.1.3:
    openapi-types: private
  optics-ts@2.4.1:
    optics-ts: private
  optionator@0.9.4:
    optionator: private
  orderedmap@2.1.1:
    orderedmap: private
  own-keys@1.0.1:
    own-keys: private
  p-finally@1.0.0:
    p-finally: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  p-queue@6.6.2:
    p-queue: private
  p-retry@4.6.2:
    p-retry: private
  p-timeout@3.2.0:
    p-timeout: private
  package-json-from-dist@1.0.1:
    package-json-from-dist: private
  papaparse@5.5.2:
    papaparse: private
  parent-module@1.0.1:
    parent-module: private
  parse5-htmlparser2-tree-adapter@6.0.1:
    parse5-htmlparser2-tree-adapter: private
  parse5@6.0.1:
    parse5: private
  parseurl@1.3.3:
    parseurl: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-scurry@1.11.1:
    path-scurry: private
  path-to-regexp@0.1.12:
    path-to-regexp: private
  path-type@4.0.0:
    path-type: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pify@2.3.0:
    pify: private
  pirates@4.0.6:
    pirates: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss-import@15.1.0(postcss@8.5.3):
    postcss-import: private
  postcss-js@4.0.1(postcss@8.5.3):
    postcss-js: private
  postcss-load-config@4.0.2(postcss@8.5.3)(ts-node@10.9.2(@types/node@20.17.26)(typescript@5.8.2)):
    postcss-load-config: private
  postcss-nested@6.2.0(postcss@8.5.3):
    postcss-nested: private
  postcss-selector-parser@6.0.10:
    postcss-selector-parser: private
  postcss-value-parser@4.2.0:
    postcss-value-parser: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  prosemirror-transform@1.10.3:
    prosemirror-transform: private
  proxy-addr@2.0.7:
    proxy-addr: private
  proxy-compare@2.6.0:
    proxy-compare: private
  punycode.js@2.3.1:
    punycode.js: private
  punycode@2.3.1:
    punycode: private
  pure-rand@6.1.0:
    pure-rand: private
  qs@6.14.0:
    qs: private
  queue-microtask@1.2.3:
    queue-microtask: private
  raf@3.4.1:
    raf: private
  randombytes@2.1.0:
    randombytes: private
  range-parser@1.2.1:
    range-parser: private
  raw-body@3.0.0:
    raw-body: private
  react-is@18.3.1:
    react-is: private
  react-remove-scroll-bar@2.3.8(@types/react@18.3.20)(react@18.2.0):
    react-remove-scroll-bar: private
  react-remove-scroll@2.6.3(@types/react@18.3.20)(react@18.2.0):
    react-remove-scroll: private
  react-smooth@4.0.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    react-smooth: private
  react-style-singleton@2.2.3(@types/react@18.3.20)(react@18.2.0):
    react-style-singleton: private
  react-textarea-autosize@8.5.8(@types/react@18.3.20)(react@18.2.0):
    react-textarea-autosize: private
  react-tracked@1.7.14(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2):
    react-tracked: private
  react-transition-group@4.4.5(react-dom@18.2.0(react@18.2.0))(react@18.2.0):
    react-transition-group: private
  read-cache@1.0.0:
    read-cache: private
  readdirp@3.6.0:
    readdirp: private
  recharts-scale@0.4.5:
    recharts-scale: private
  redux@4.2.1:
    redux: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  remark-gfm@4.0.0:
    remark-gfm: private
  remark-parse@11.0.0:
    remark-parse: private
  remark-stringify@11.0.0:
    remark-stringify: private
  require-from-string@2.0.2:
    require-from-string: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-pkg-maps@1.0.0:
    resolve-pkg-maps: private
  resolve@1.22.10:
    resolve: private
  retry@0.13.1:
    retry: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rope-sequence@1.3.4:
    rope-sequence: private
  rspack-resolver@1.2.2:
    rspack-resolver: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  safer-buffer@2.1.2:
    safer-buffer: private
  scheduler@0.23.2:
    scheduler: private
  schema-utils@4.3.0:
    schema-utils: private
  scroll-into-view-if-needed@3.1.0:
    scroll-into-view-if-needed: private
  secure-json-parse@2.7.0:
    secure-json-parse: private
  semver@7.7.1:
    semver: private
  send@0.19.0:
    send: private
  serialize-javascript@6.0.2:
    serialize-javascript: private
  serve-static@1.16.2:
    serve-static: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  setprototypeof@1.2.0:
    setprototypeof: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  signal-exit@4.1.0:
    signal-exit: private
  simple-wcswidth@1.0.1:
    simple-wcswidth: private
  slash@3.0.0:
    slash: private
  slate-dom@0.112.2(slate@0.103.0):
    slate-dom: private
  slate-history@0.110.3(slate@0.103.0):
    slate-history: private
  slate-hyperscript@0.100.0(slate@0.103.0):
    slate-hyperscript: private
  slick@1.12.2:
    slick: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  sqids@0.3.0:
    sqids: private
  stable-hash@0.0.5:
    stable-hash: private
  statuses@2.0.1:
    statuses: private
  streamsearch@1.1.0:
    streamsearch: private
  string-width@4.2.3:
    string-width-cjs: private
  string-width@5.1.2:
    string-width: private
  string.prototype.includes@2.0.1:
    string.prototype.includes: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-ansi@6.0.1:
    strip-ansi: private
    strip-ansi-cjs: private
  strip-bom@3.0.0:
    strip-bom: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  styled-jsx@5.1.1(react@18.2.0):
    styled-jsx: private
  sucrase@3.35.0:
    sucrase: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  swr@2.2.0(react@18.2.0):
    swr: private
  tabbable@6.2.0:
    tabbable: private
  tapable@2.2.1:
    tapable: private
  terser-webpack-plugin@5.3.14(webpack@5.98.0):
    terser-webpack-plugin: private
  terser@5.39.0:
    terser: private
  text-table@0.2.0:
    text-table: private
  thenify-all@1.6.0:
    thenify-all: private
  thenify@3.3.1:
    thenify: private
  throttleit@2.1.0:
    throttleit: private
  tiny-invariant@1.3.3:
    tiny-invariant: private
  tiny-warning@1.0.3:
    tiny-warning: private
  tinyglobby@0.2.12:
    tinyglobby: private
  to-regex-range@5.0.1:
    to-regex-range: private
  toggle-selection@1.0.6:
    toggle-selection: private
  toidentifier@1.0.1:
    toidentifier: private
  tr46@0.0.3:
    tr46: private
  trough@2.2.0:
    trough: private
  ts-api-utils@1.4.3(typescript@5.8.2):
    ts-api-utils: private
  ts-interface-checker@0.1.13:
    ts-interface-checker: private
  tsconfig-paths@3.15.0:
    tsconfig-paths: private
  tslib@2.8.1:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  type-is@2.0.1:
    type-is: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  uc.micro@2.1.0:
    uc.micro: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.19.8:
    undici-types: private
  unified@11.0.5:
    unified: private
  unist-util-is@6.0.0:
    unist-util-is: private
  unist-util-stringify-position@4.0.0:
    unist-util-stringify-position: private
  unist-util-visit-parents@6.0.1:
    unist-util-visit-parents: private
  unist-util-visit@5.0.0:
    unist-util-visit: private
  unpipe@1.0.0:
    unpipe: private
  update-browserslist-db@1.1.3(browserslist@4.24.4):
    update-browserslist-db: private
  uri-js@4.4.1:
    uri-js: private
  use-callback-ref@1.3.3(@types/react@18.3.20)(react@18.2.0):
    use-callback-ref: private
  use-composed-ref@1.4.0(@types/react@18.3.20)(react@18.2.0):
    use-composed-ref: private
  use-context-selector@1.4.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2):
    use-context-selector: private
  use-deep-compare@1.3.0(react@18.2.0):
    use-deep-compare: private
  use-isomorphic-layout-effect@1.2.0(@types/react@18.3.20)(react@18.2.0):
    use-isomorphic-layout-effect: private
  use-latest@1.3.0(@types/react@18.3.20)(react@18.2.0):
    use-latest: private
  use-sidecar@1.1.3(@types/react@18.3.20)(react@18.2.0):
    use-sidecar: private
  use-sync-external-store@1.4.0(react@18.2.0):
    use-sync-external-store: private
  util-deprecate@1.0.2:
    util-deprecate: private
  utils-merge@1.0.1:
    utils-merge: private
  uuid@11.1.0:
    uuid: private
  v8-compile-cache-lib@3.0.1:
    v8-compile-cache-lib: private
  valid-data-url@3.0.1:
    valid-data-url: private
  validator@13.12.0:
    validator: private
  vary@1.1.2:
    vary: private
  vfile-message@4.0.2:
    vfile-message: private
  vfile@6.0.3:
    vfile: private
  victory-vendor@36.9.2:
    victory-vendor: private
  w3c-keyname@2.2.8:
    w3c-keyname: private
  watchpack@2.4.2:
    watchpack: private
  web-resource-inliner@6.0.1:
    web-resource-inliner: private
  web-streams-polyfill@4.0.0-beta.3:
    web-streams-polyfill: private
  webidl-conversions@3.0.1:
    webidl-conversions: private
  webpack-sources@3.2.3:
    webpack-sources: private
  whatwg-url@5.0.0:
    whatwg-url: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.19:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi-cjs: private
  wrap-ansi@8.1.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  yaml@2.7.0:
    yaml: private
  yn@3.1.1:
    yn: private
  yocto-queue@0.1.0:
    yocto-queue: private
  zod-to-json-schema@3.24.5(zod@3.24.2):
    zod-to-json-schema: private
  zustand-x@3.0.4(react-dom@18.2.0(react@18.2.0))(react@18.2.0)(scheduler@0.23.2)(zustand@4.5.6(@types/react@18.3.20)(immer@10.1.1)(react@18.2.0)):
    zustand-x: private
  zwitch@2.0.4:
    zwitch: private
ignoredBuilds:
  - '@prisma/engines'
  - prisma
  - '@prisma/client'
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.4
pendingBuilds: []
prunedAt: Wed, 02 Jul 2025 06:44:44 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmmirror.com/
skipped:
  - '@emnapi/core@1.3.1'
  - '@emnapi/runtime@1.3.1'
  - '@emnapi/wasi-threads@1.0.1'
  - '@napi-rs/wasm-runtime@0.2.7'
  - '@next/swc-darwin-arm64@14.2.23'
  - '@next/swc-darwin-x64@14.2.23'
  - '@next/swc-linux-arm64-gnu@14.2.23'
  - '@next/swc-linux-arm64-musl@14.2.23'
  - '@next/swc-linux-x64-gnu@14.2.23'
  - '@next/swc-linux-x64-musl@14.2.23'
  - '@next/swc-win32-arm64-msvc@14.2.23'
  - '@next/swc-win32-ia32-msvc@14.2.23'
  - '@tybys/wasm-util@0.9.0'
  - '@unrs/rspack-resolver-binding-darwin-arm64@1.2.2'
  - '@unrs/rspack-resolver-binding-darwin-x64@1.2.2'
  - '@unrs/rspack-resolver-binding-freebsd-x64@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm-gnueabihf@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-gnu@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-arm64-musl@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-x64-gnu@1.2.2'
  - '@unrs/rspack-resolver-binding-linux-x64-musl@1.2.2'
  - '@unrs/rspack-resolver-binding-wasm32-wasi@1.2.2'
  - '@unrs/rspack-resolver-binding-win32-arm64-msvc@1.2.2'
  - fsevents@2.3.3
storeDir: E:\.pnpm-store\v10
virtualStoreDir: E:\MultiAgentPPT\frontend\node_modules\.pnpm
virtualStoreDirMaxLength: 60
