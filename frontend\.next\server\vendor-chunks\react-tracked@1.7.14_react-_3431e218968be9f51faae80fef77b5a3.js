"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-tracked@1.7.14_react-_3431e218968be9f51faae80fef77b5a3";
exports.ids = ["vendor-chunks/react-tracked@1.7.14_react-_3431e218968be9f51faae80fef77b5a3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-tracked@1.7.14_react-_3431e218968be9f51faae80fef77b5a3/node_modules/react-tracked/dist/index.modern.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-tracked@1.7.14_react-_3431e218968be9f51faae80fef77b5a3/node_modules/react-tracked/dist/index.modern.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContainer: () => (/* binding */ createContainer),\n/* harmony export */   createTrackedSelector: () => (/* binding */ createTrackedSelector),\n/* harmony export */   getUntrackedObject: () => (/* reexport safe */ proxy_compare__WEBPACK_IMPORTED_MODULE_1__.getUntracked),\n/* harmony export */   memo: () => (/* binding */ memo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var use_context_selector__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! use-context-selector */ \"(ssr)/./node_modules/.pnpm/use-context-selector@1.4.4__3564d954cbfdda88b4abf4c5ba37d924/node_modules/use-context-selector/dist/index.modern.mjs\");\n/* harmony import */ var proxy_compare__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! proxy-compare */ \"(ssr)/./node_modules/.pnpm/proxy-compare@2.6.0/node_modules/proxy-compare/dist/index.modern.js\");\n\n\n\n\n\nconst useAffectedDebugValue = (state, affected) => {\n  const pathList = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    pathList.current = (0,proxy_compare__WEBPACK_IMPORTED_MODULE_1__.affectedToPathList)(state, affected);\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(state);\n};\n\nconst createTrackedSelector = useSelector => {\n  const useTrackedSelector = () => {\n    const [, forceUpdate] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(c => c + 1, 0);\n    const affected = new WeakMap();\n    const lastAffected = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const prevState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const lastState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n      lastAffected.current = affected;\n      if (prevState.current !== lastState.current && (0,proxy_compare__WEBPACK_IMPORTED_MODULE_1__.isChanged)(prevState.current, lastState.current, affected, new WeakMap())) {\n        prevState.current = lastState.current;\n        forceUpdate();\n      }\n    });\n    const selector = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(nextState => {\n      lastState.current = nextState;\n      if (prevState.current && prevState.current !== nextState && lastAffected.current && !(0,proxy_compare__WEBPACK_IMPORTED_MODULE_1__.isChanged)(prevState.current, nextState, lastAffected.current, new WeakMap())) {\n        // not changed\n        return prevState.current;\n      }\n      prevState.current = nextState;\n      return nextState;\n    }, []);\n    const state = useSelector(selector);\n    if (typeof process === 'object' && \"development\" !== 'production') {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useAffectedDebugValue(state, affected);\n    }\n    const proxyCache = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => new WeakMap(), []); // per-hook proxyCache\n    return (0,proxy_compare__WEBPACK_IMPORTED_MODULE_1__.createProxy)(state, affected, proxyCache);\n  };\n  return useTrackedSelector;\n};\n\n/* eslint react/destructuring-assignment: off */\nconst createContainer = (useValue, options) => {\n  var _options, _options2;\n  if (typeof options === 'boolean') {\n    // eslint-disable-next-line no-console\n    console.warn('boolean option is deprecated, please specify { concurrentMode: true }');\n    options = {\n      concurrentMode: options\n    };\n  }\n  const {\n    stateContextName = 'StateContainer',\n    updateContextName = 'UpdateContainer',\n    concurrentMode\n  } = options || {};\n  const StateContext = (0,use_context_selector__WEBPACK_IMPORTED_MODULE_2__.createContext)((_options = options) == null ? void 0 : _options.defaultState);\n  const UpdateContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)((_options2 = options) == null ? void 0 : _options2.defaultUpdate);\n  StateContext.displayName = stateContextName;\n  UpdateContext.displayName = updateContextName;\n  const Provider = props => {\n    const [state, update] = useValue(props);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(UpdateContext.Provider, {\n      value: update\n    }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(StateContext.Provider, {\n      value: state\n    }, props.children));\n  };\n  const useSelector = selector => {\n    if (typeof process === 'object' && \"development\" !== 'production') {\n      const selectorOrig = selector;\n      selector = state => {\n        if (state === undefined) {\n          throw new Error('Please use <Provider>');\n        }\n        return selectorOrig(state);\n      };\n    }\n    const selected = (0,use_context_selector__WEBPACK_IMPORTED_MODULE_2__.useContextSelector)(StateContext, selector);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useDebugValue)(selected);\n    return selected;\n  };\n  const useTrackedState = createTrackedSelector(useSelector);\n  const useUpdate = concurrentMode ? () => {\n    if (typeof process === 'object' && \"development\" !== 'production' && (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UpdateContext) === undefined) {\n      throw new Error('Please use <Provider>');\n    }\n    const contextUpdate = (0,use_context_selector__WEBPACK_IMPORTED_MODULE_2__.useContextUpdate)(StateContext);\n    const update = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UpdateContext);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args) => {\n      let result;\n      contextUpdate(() => {\n        result = update(...args);\n      });\n      return result;\n    }, [contextUpdate, update]);\n  }\n  // not concurrentMode\n  : () => {\n    if (typeof process === 'object' && \"development\" !== 'production' && (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UpdateContext) === undefined) {\n      throw new Error('Please use <Provider>');\n    }\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UpdateContext);\n  };\n  const useTracked = () => [useTrackedState(), useUpdate()];\n  return {\n    Provider,\n    useTrackedState,\n    useTracked,\n    useUpdate,\n    useSelector\n  };\n};\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction memo(Component, propsAreEqual) {\n  const WrappedComponent = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => {\n    Object.values(props).forEach(proxy_compare__WEBPACK_IMPORTED_MODULE_1__.trackMemo);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Component, _extends({}, props, {\n      ref\n    }));\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.memo)(WrappedComponent, propsAreEqual);\n}\n\n\n//# sourceMappingURL=index.modern.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-tracked@1.7.14_react-_3431e218968be9f51faae80fef77b5a3/node_modules/react-tracked/dist/index.modern.mjs\n");

/***/ })

};
;