"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@dnd-kit+utilities@3.2.2_react@18.2.0";
exports.ids = ["vendor-chunks/@dnd-kit+utilities@3.2.2_react@18.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@18.2.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@18.2.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CSS: () => (/* binding */ CSS),\n/* harmony export */   add: () => (/* binding */ add),\n/* harmony export */   canUseDOM: () => (/* binding */ canUseDOM),\n/* harmony export */   findFirstFocusableNode: () => (/* binding */ findFirstFocusableNode),\n/* harmony export */   getEventCoordinates: () => (/* binding */ getEventCoordinates),\n/* harmony export */   getOwnerDocument: () => (/* binding */ getOwnerDocument),\n/* harmony export */   getWindow: () => (/* binding */ getWindow),\n/* harmony export */   hasViewportRelativeCoordinates: () => (/* binding */ hasViewportRelativeCoordinates),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isHTMLElement: () => (/* binding */ isHTMLElement),\n/* harmony export */   isKeyboardEvent: () => (/* binding */ isKeyboardEvent),\n/* harmony export */   isNode: () => (/* binding */ isNode),\n/* harmony export */   isSVGElement: () => (/* binding */ isSVGElement),\n/* harmony export */   isTouchEvent: () => (/* binding */ isTouchEvent),\n/* harmony export */   isWindow: () => (/* binding */ isWindow),\n/* harmony export */   subtract: () => (/* binding */ subtract),\n/* harmony export */   useCombinedRefs: () => (/* binding */ useCombinedRefs),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useLatestValue: () => (/* binding */ useLatestValue),\n/* harmony export */   useLazyMemo: () => (/* binding */ useLazyMemo),\n/* harmony export */   useNodeRef: () => (/* binding */ useNodeRef),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useUniqueId: () => (/* binding */ useUniqueId)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n\n\nfunction useCombinedRefs() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => node => {\n    refs.forEach(ref => ref(node));\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  refs);\n}\n\n// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nconst canUseDOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\nfunction isWindow(element) {\n  const elementString = Object.prototype.toString.call(element);\n  return elementString === '[object Window]' || // In Electron context the Window object serializes to [object global]\n  elementString === '[object global]';\n}\n\nfunction isNode(node) {\n  return 'nodeType' in node;\n}\n\nfunction getWindow(target) {\n  var _target$ownerDocument, _target$ownerDocument2;\n\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return (_target$ownerDocument = (_target$ownerDocument2 = target.ownerDocument) == null ? void 0 : _target$ownerDocument2.defaultView) != null ? _target$ownerDocument : window;\n}\n\nfunction isDocument(node) {\n  const {\n    Document\n  } = getWindow(node);\n  return node instanceof Document;\n}\n\nfunction isHTMLElement(node) {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n\nfunction isSVGElement(node) {\n  return node instanceof getWindow(node).SVGElement;\n}\n\nfunction getOwnerDocument(target) {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n\n/**\r\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\r\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\r\n */\n\nconst useIsomorphicLayoutEffect = canUseDOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\nfunction useEvent(handler) {\n  const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(handler);\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    return handlerRef.current == null ? void 0 : handlerRef.current(...args);\n  }, []);\n}\n\nfunction useInterval() {\n  const intervalRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((listener, duration) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n  const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n  return [set, clear];\n}\n\nfunction useLatestValue(value, dependencies) {\n  if (dependencies === void 0) {\n    dependencies = [value];\n  }\n\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n  return valueRef;\n}\n\nfunction useLazyMemo(callback, dependencies) {\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const newValue = callback(valueRef.current);\n    valueRef.current = newValue;\n    return newValue;\n  }, // eslint-disable-next-line react-hooks/exhaustive-deps\n  [...dependencies]);\n}\n\nfunction useNodeRef(onChange) {\n  const onChangeHandler = useEvent(onChange);\n  const node = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const setNodeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(element => {\n    if (element !== node.current) {\n      onChangeHandler == null ? void 0 : onChangeHandler(element, node.current);\n    }\n\n    node.current = element;\n  }, //eslint-disable-next-line\n  []);\n  return [node, setNodeRef];\n}\n\nfunction usePrevious(value) {\n  const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    ref.current = value;\n  }, [value]);\n  return ref.current;\n}\n\nlet ids = {};\nfunction useUniqueId(prefix, value) {\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n    return prefix + \"-\" + id;\n  }, [prefix, value]);\n}\n\nfunction createAdjustmentFn(modifier) {\n  return function (object) {\n    for (var _len = arguments.length, adjustments = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      adjustments[_key - 1] = arguments[_key];\n    }\n\n    return adjustments.reduce((accumulator, adjustment) => {\n      const entries = Object.entries(adjustment);\n\n      for (const [key, valueAdjustment] of entries) {\n        const value = accumulator[key];\n\n        if (value != null) {\n          accumulator[key] = value + modifier * valueAdjustment;\n        }\n      }\n\n      return accumulator;\n    }, { ...object\n    });\n  };\n}\n\nconst add = /*#__PURE__*/createAdjustmentFn(1);\nconst subtract = /*#__PURE__*/createAdjustmentFn(-1);\n\nfunction hasViewportRelativeCoordinates(event) {\n  return 'clientX' in event && 'clientY' in event;\n}\n\nfunction isKeyboardEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    KeyboardEvent\n  } = getWindow(event.target);\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n\nfunction isTouchEvent(event) {\n  if (!event) {\n    return false;\n  }\n\n  const {\n    TouchEvent\n  } = getWindow(event.target);\n  return TouchEvent && event instanceof TouchEvent;\n}\n\n/**\r\n * Returns the normalized x and y coordinates for mouse and touch events.\r\n */\n\nfunction getEventCoordinates(event) {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.touches[0];\n      return {\n        x,\n        y\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {\n        clientX: x,\n        clientY: y\n      } = event.changedTouches[0];\n      return {\n        x,\n        y\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n\n  return null;\n}\n\nconst CSS = /*#__PURE__*/Object.freeze({\n  Translate: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        x,\n        y\n      } = transform;\n      return \"translate3d(\" + (x ? Math.round(x) : 0) + \"px, \" + (y ? Math.round(y) : 0) + \"px, 0)\";\n    }\n\n  },\n  Scale: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      const {\n        scaleX,\n        scaleY\n      } = transform;\n      return \"scaleX(\" + scaleX + \") scaleY(\" + scaleY + \")\";\n    }\n\n  },\n  Transform: {\n    toString(transform) {\n      if (!transform) {\n        return;\n      }\n\n      return [CSS.Translate.toString(transform), CSS.Scale.toString(transform)].join(' ');\n    }\n\n  },\n  Transition: {\n    toString(_ref) {\n      let {\n        property,\n        duration,\n        easing\n      } = _ref;\n      return property + \" \" + duration + \"ms \" + easing;\n    }\n\n  }\n});\n\nconst SELECTOR = 'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\nfunction findFirstFocusableNode(element) {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n\n\n//# sourceMappingURL=utilities.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@dnd-kit+utilities@3.2.2_react@18.2.0/node_modules/@dnd-kit/utilities/dist/utilities.esm.js\n");

/***/ })

};
;