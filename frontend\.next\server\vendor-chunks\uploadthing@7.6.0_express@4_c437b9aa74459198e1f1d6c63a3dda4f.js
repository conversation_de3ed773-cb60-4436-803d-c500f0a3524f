"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f";
exports.ids = ["vendor-chunks/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/client/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/client/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadAbortedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError),\n/* harmony export */   UploadPausedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError),\n/* harmony export */   genUploader: () => (/* binding */ genUploader),\n/* harmony export */   generateClientDropzoneAccept: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateClientDropzoneAccept),\n/* harmony export */   generateMimeTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generatePermittedFileTypes),\n/* harmony export */   isValidFileSize: () => (/* binding */ isValidFileSize),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType),\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var effect_Array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Array */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Array.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _dist_internal_deferred_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dist/_internal/deferred.js */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deferred.js\");\n/* harmony import */ var _dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dist/_internal/upload-browser.js */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-browser.js\");\n/* harmony import */ var _dist_internal_ut_reporter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dist/_internal/ut-reporter.js */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/ut-reporter.js\");\n\n\n\n\n\n\n\n\nvar version$1 = \"7.6.0\";\n\nconst version = version$1;\n/**\n * Validate that a file is of a valid type given a route config\n * @public\n */ const isValidFileType = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((type)=>file.type.includes(type)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Validate that a file is of a valid size given a route config\n * @public\n */ const isValidFileSize = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((type)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fileSizeToBytes)(routeConfig[type].maxFileSize)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((maxFileSize)=>file.size <= maxFileSize), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Generate a typed uploader for a given FileRouter\n * @public\n */ const genUploader = (initOpts)=>{\n    const routeRegistry = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.createIdentityProxy)();\n    const controllableUpload = async (slug, opts)=>{\n        const uploads = new Map();\n        const endpoint = typeof slug === \"function\" ? slug(routeRegistry) : slug;\n        const utReporter = (0,_dist_internal_ut_reporter_js__WEBPACK_IMPORTED_MODULE_2__.createUTReporter)({\n            endpoint: String(endpoint),\n            package: initOpts?.package ?? \"uploadthing/client\",\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            headers: opts.headers\n        });\n        const fetchFn = initOpts?.fetch ?? window.fetch;\n        const presigneds = await effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromise(utReporter(\"upload\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            input: \"input\" in opts ? opts.input : null,\n            files: opts.files.map((f)=>({\n                    name: f.name,\n                    size: f.size,\n                    type: f.type,\n                    lastModified: f.lastModified\n                }))\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn)));\n        const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);\n        let totalLoaded = 0;\n        const uploadEffect = (file, presigned)=>(0,_dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__.uploadFile)(file, presigned, {\n                onUploadProgress: (progressEvent)=>{\n                    totalLoaded += progressEvent.delta;\n                    opts.onUploadProgress?.({\n                        ...progressEvent,\n                        file,\n                        progress: Math.round(progressEvent.loaded / file.size * 100),\n                        totalLoaded,\n                        totalProgress: Math.round(totalLoaded / totalSize * 100)\n                    });\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn));\n        for (const [i, p] of presigneds.entries()){\n            const file = opts.files[i];\n            if (!file) continue;\n            const deferred = (0,_dist_internal_deferred_js__WEBPACK_IMPORTED_MODULE_4__.createDeferred)();\n            uploads.set(file, {\n                deferred,\n                presigned: p\n            });\n            void effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(uploadEffect(file, p), {\n                signal: deferred.ac.signal\n            }).then((result)=>{\n                if (result._tag === \"Success\") {\n                    return deferred.resolve(result.value);\n                } else if (result.cause._tag === \"Interrupt\") {\n                    throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError();\n                }\n                throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(result.cause);\n            }).catch((err)=>{\n                if (err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError) return;\n                deferred.reject(err);\n            });\n        }\n        /**\n     * Pause an ongoing upload\n     * @param file The file upload you want to pause. Can be omitted to pause all files\n     */ const pauseUpload = (file)=>{\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) return;\n                if (upload.deferred.ac.signal.aborted) {\n                    // Cancel the upload if it's already been paused\n                    throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n                }\n                upload.deferred.ac.abort();\n            }\n        };\n        /**\n     * Resume a paused upload\n     * @param file The file upload you want to resume. Can be omitted to resume all files\n     */ const resumeUpload = (file)=>{\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) throw \"No upload found\";\n                upload.deferred.ac = new AbortController();\n                void effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(uploadEffect(file, upload.presigned), {\n                    signal: upload.deferred.ac.signal\n                }).then((result)=>{\n                    if (result._tag === \"Success\") {\n                        return upload.deferred.resolve(result.value);\n                    } else if (result.cause._tag === \"Interrupt\") {\n                        throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError();\n                    }\n                    throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(result.cause);\n                }).catch((err)=>{\n                    if (err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError) return;\n                    upload.deferred.reject(err);\n                });\n            }\n        };\n        /**\n     * Wait for an upload to complete\n     * @param file The file upload you want to wait for. Can be omitted to wait for all files\n     */ const done = async (file)=>{\n            const promises = [];\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) throw \"No upload found\";\n                promises.push(upload.deferred.promise);\n            }\n            const results = await Promise.all(promises);\n            return file ? results[0] : results;\n        };\n        return {\n            pauseUpload,\n            resumeUpload,\n            done\n        };\n    };\n    /**\n   * One step upload function that both requests presigned URLs\n   * and then uploads the files to UploadThing\n   */ const typedUploadFiles = (slug, opts)=>{\n        const endpoint = typeof slug === \"function\" ? slug(routeRegistry) : slug;\n        const fetchFn = initOpts?.fetch ?? window.fetch;\n        return (0,_dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__.uploadFilesInternal)(endpoint, {\n            ...opts,\n            skipPolling: {},\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            package: initOpts?.package ?? \"uploadthing/client\",\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            input: opts.input\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn), (effect)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(effect, opts.signal && {\n                signal: opts.signal\n            })).then((exit)=>{\n            if (exit._tag === \"Success\") {\n                return exit.value;\n            } else if (exit.cause._tag === \"Interrupt\") {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n            }\n            throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(exit.cause);\n        });\n    };\n    return {\n        uploadFiles: typedUploadFiles,\n        createUpload: controllableUpload,\n        /**\n     * Identity object that can be used instead of raw strings\n     * that allows \"Go to definition\" in your IDE to bring you\n     * to the backend definition of a route.\n     */ routeRegistry\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deferred.js":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deferred.js ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeferred: () => (/* binding */ createDeferred)\n/* harmony export */ });\nconst createDeferred = ()=>{\n    let resolve;\n    let reject;\n    const ac = new AbortController();\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    return {\n        promise,\n        ac,\n        resolve,\n        reject\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC9kZWZlcnJlZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRTBCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3VwbG9hZHRoaW5nQDcuNi4wX2V4cHJlc3NANF9jNDM3YjlhYTc0NDU5MTk4ZTFmMWQ2YzYzYTNkZGE0Zi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvZGlzdC9faW50ZXJuYWwvZGVmZXJyZWQuanM/OGNkYSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBjcmVhdGVEZWZlcnJlZCA9ICgpPT57XG4gICAgbGV0IHJlc29sdmU7XG4gICAgbGV0IHJlamVjdDtcbiAgICBjb25zdCBhYyA9IG5ldyBBYm9ydENvbnRyb2xsZXIoKTtcbiAgICBjb25zdCBwcm9taXNlID0gbmV3IFByb21pc2UoKHJlcywgcmVqKT0+e1xuICAgICAgICByZXNvbHZlID0gcmVzO1xuICAgICAgICByZWplY3QgPSByZWo7XG4gICAgfSk7XG4gICAgcmV0dXJuIHtcbiAgICAgICAgcHJvbWlzZSxcbiAgICAgICAgYWMsXG4gICAgICAgIHJlc29sdmUsXG4gICAgICAgIHJlamVjdFxuICAgIH07XG59O1xuXG5leHBvcnQgeyBjcmVhdGVEZWZlcnJlZCB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deferred.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logDeprecationWarning: () => (/* binding */ logDeprecationWarning)\n/* harmony export */ });\nconst logDeprecationWarning = (message)=>{\n    // eslint-disable-next-line no-console\n    console.warn(`⚠️ [uploadthing][deprecated] ${message}`);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC9kZXByZWNhdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQSxpREFBaUQsUUFBUTtBQUN6RDs7QUFFaUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC9kZXByZWNhdGlvbnMuanM/Nzk5MSJdLCJzb3VyY2VzQ29udGVudCI6WyJjb25zdCBsb2dEZXByZWNhdGlvbldhcm5pbmcgPSAobWVzc2FnZSk9PntcbiAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tY29uc29sZVxuICAgIGNvbnNvbGUud2Fybihg4pqg77iPIFt1cGxvYWR0aGluZ11bZGVwcmVjYXRlZF0gJHttZXNzYWdlfWApO1xufTtcblxuZXhwb3J0IHsgbG9nRGVwcmVjYXRpb25XYXJuaW5nIH07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-browser.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-browser.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadFile: () => (/* binding */ uploadFile),\n/* harmony export */   uploadFilesInternal: () => (/* binding */ uploadFilesInternal)\n/* harmony export */ });\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Predicate */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _deprecations_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./deprecations.js */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js\");\n/* harmony import */ var _ut_reporter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ut-reporter.js */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/ut-reporter.js\");\n\n\n\n\n\n\n\nvar version = \"7.6.0\";\n\nconst uploadWithProgress = (file, rangeStart, presigned, onUploadProgress)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"PUT\", presigned.url, true);\n        xhr.setRequestHeader(\"Range\", `bytes=${rangeStart}-`);\n        xhr.setRequestHeader(\"x-uploadthing-version\", version);\n        xhr.responseType = \"json\";\n        let previousLoaded = 0;\n        xhr.upload.addEventListener(\"progress\", ({ loaded })=>{\n            const delta = loaded - previousLoaded;\n            onUploadProgress?.({\n                loaded,\n                delta\n            });\n            previousLoaded = loaded;\n        });\n        xhr.addEventListener(\"load\", ()=>{\n            if (xhr.status >= 200 && xhr.status < 300 && (0,effect_Predicate__WEBPACK_IMPORTED_MODULE_1__.isRecord)(xhr.response)) {\n                if ((0,effect_Predicate__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(xhr.response, \"error\")) {\n                    resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                        code: \"UPLOAD_FAILED\",\n                        message: String(xhr.response.error),\n                        data: xhr.response\n                    }));\n                } else {\n                    resume(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.succeed(xhr.response));\n                }\n            } else {\n                resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                    code: \"UPLOAD_FAILED\",\n                    message: `XHR failed ${xhr.status} ${xhr.statusText}`,\n                    data: xhr.response\n                }));\n            }\n        });\n        // Is there a case when the client would throw and\n        // ingest server not knowing about it? idts?\n        xhr.addEventListener(\"error\", ()=>{\n            resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                code: \"UPLOAD_FAILED\"\n            }));\n        });\n        const formData = new FormData();\n        /**\n     * iOS/React Native FormData handling requires special attention:\n     *\n     * Issue: In React Native, iOS crashes with \"attempt to insert nil object\" when appending File directly\n     * to FormData. This happens because iOS tries to create NSDictionary from the file object and expects\n     * specific structure {uri, type, name}.\n     *\n     *\n     * Note: Don't try to use Blob or modify File object - iOS specifically needs plain object\n     * with these properties to create valid NSDictionary.\n     */ if (\"uri\" in file) {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            formData.append(\"file\", {\n                uri: file.uri,\n                type: file.type,\n                name: file.name,\n                ...rangeStart > 0 && {\n                    range: rangeStart\n                }\n            });\n        } else {\n            formData.append(\"file\", rangeStart > 0 ? file.slice(rangeStart) : file);\n        }\n        xhr.send(formData);\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_0__.sync(()=>xhr.abort());\n    });\nconst uploadFile = (file, presigned, opts)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.fetchEff)(presigned.url, {\n        method: \"HEAD\"\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(({ headers })=>parseInt(headers.get(\"x-ut-range-start\") ?? \"0\", 10)), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.tap((start)=>opts.onUploadProgress?.({\n            delta: start,\n            loaded: start\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap((start)=>uploadWithProgress(file, start, presigned, (progressEvent)=>opts.onUploadProgress?.({\n                delta: progressEvent.delta,\n                loaded: progressEvent.loaded + start\n            }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map((uploadResponse)=>({\n            name: file.name,\n            size: file.size,\n            key: presigned.key,\n            lastModified: file.lastModified,\n            serverData: uploadResponse.serverData,\n            get url () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_4__.logDeprecationWarning)(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return uploadResponse.url;\n            },\n            get appUrl () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_4__.logDeprecationWarning)(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return uploadResponse.appUrl;\n            },\n            ufsUrl: uploadResponse.ufsUrl,\n            customId: presigned.customId,\n            type: file.type,\n            fileHash: uploadResponse.fileHash\n        })));\nconst uploadFilesInternal = (endpoint, opts)=>{\n    // classic service right here\n    const reportEventToUT = (0,_ut_reporter_js__WEBPACK_IMPORTED_MODULE_5__.createUTReporter)({\n        endpoint: String(endpoint),\n        package: opts.package,\n        url: opts.url,\n        headers: opts.headers\n    });\n    const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);\n    let totalLoaded = 0;\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap(reportEventToUT(\"upload\", {\n        input: \"input\" in opts ? opts.input : null,\n        files: opts.files.map((f)=>({\n                name: f.name,\n                size: f.size,\n                type: f.type,\n                lastModified: f.lastModified\n            }))\n    }), (presigneds)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.forEach(presigneds, (presigned, i)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.sync(()=>opts.onUploadBegin?.({\n                    file: opts.files[i].name\n                })), ()=>uploadFile(opts.files[i], presigned, {\n                    onUploadProgress: (ev)=>{\n                        totalLoaded += ev.delta;\n                        opts.onUploadProgress?.({\n                            file: opts.files[i],\n                            progress: ev.loaded / opts.files[i].size * 100,\n                            loaded: ev.loaded,\n                            delta: ev.delta,\n                            totalLoaded,\n                            totalProgress: totalLoaded / totalSize\n                        });\n                    }\n                })), {\n            concurrency: 6\n        }));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC91cGxvYWQtYnJvd3Nlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUErQztBQUNUO0FBQ21CO0FBQ1E7QUFDUDtBQUNOOztBQUVwRDs7QUFFQSw0RUFBNEUsK0NBQVc7QUFDdkY7QUFDQTtBQUNBLCtDQUErQyxXQUFXO0FBQzFEO0FBQ0E7QUFDQTtBQUNBLG1EQUFtRCxRQUFRO0FBQzNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsU0FBUztBQUNUO0FBQ0EseURBQXlELDBEQUFRO0FBQ2pFLG9CQUFvQiw2REFBVztBQUMvQiwrQkFBK0IsaUVBQWdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBLHFCQUFxQjtBQUNyQixrQkFBa0I7QUFDbEIsMkJBQTJCLGlEQUFhO0FBQ3hDO0FBQ0EsY0FBYztBQUNkLDJCQUEyQixpRUFBZ0I7QUFDM0M7QUFDQSwyQ0FBMkMsWUFBWSxFQUFFLGVBQWU7QUFDeEU7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCLGlFQUFnQjtBQUN2QztBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLGdCQUFnQjtBQUMzQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0EsZUFBZSw4Q0FBVTtBQUN6QixLQUFLO0FBQ0wsNENBQTRDLDZEQUFRO0FBQ3BEO0FBQ0EsS0FBSyxPQUFPLDZDQUFTLElBQUksU0FBUywwREFBMEQsNkNBQVM7QUFDckc7QUFDQTtBQUNBLFNBQVMsSUFBSSxpREFBYTtBQUMxQjtBQUNBO0FBQ0EsYUFBYSxLQUFLLDZDQUFTLENBQUMseURBQVksR0FBRyw2Q0FBUztBQUNwRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsdUVBQXFCO0FBQ3JDO0FBQ0EsYUFBYTtBQUNiO0FBQ0EsZ0JBQWdCLHVFQUFxQjtBQUNyQztBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsNEJBQTRCLGlFQUFnQjtBQUM1QztBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsV0FBVyxpREFBYTtBQUN4QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsS0FBSyxpQkFBaUIsaURBQWEsNkJBQTZCLGlEQUFhLENBQUMsOENBQVU7QUFDeEY7QUFDQSxpQkFBaUI7QUFDakI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0EsaUJBQWlCO0FBQ2pCO0FBQ0EsU0FBUztBQUNUOztBQUUyQyIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL3VwbG9hZC1icm93c2VyLmpzP2U2ZTYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdW5zYWZlQ29lcmNlIH0gZnJvbSAnZWZmZWN0L0Z1bmN0aW9uJztcbmltcG9ydCAqIGFzIE1pY3JvIGZyb20gJ2VmZmVjdC9NaWNybyc7XG5pbXBvcnQgeyBpc1JlY29yZCwgaGFzUHJvcGVydHkgfSBmcm9tICdlZmZlY3QvUHJlZGljYXRlJztcbmltcG9ydCB7IGZldGNoRWZmLCBVcGxvYWRUaGluZ0Vycm9yIH0gZnJvbSAnQHVwbG9hZHRoaW5nL3NoYXJlZCc7XG5pbXBvcnQgeyBsb2dEZXByZWNhdGlvbldhcm5pbmcgfSBmcm9tICcuL2RlcHJlY2F0aW9ucy5qcyc7XG5pbXBvcnQgeyBjcmVhdGVVVFJlcG9ydGVyIH0gZnJvbSAnLi91dC1yZXBvcnRlci5qcyc7XG5cbnZhciB2ZXJzaW9uID0gXCI3LjYuMFwiO1xuXG5jb25zdCB1cGxvYWRXaXRoUHJvZ3Jlc3MgPSAoZmlsZSwgcmFuZ2VTdGFydCwgcHJlc2lnbmVkLCBvblVwbG9hZFByb2dyZXNzKT0+TWljcm8uYXN5bmMoKHJlc3VtZSk9PntcbiAgICAgICAgY29uc3QgeGhyID0gbmV3IFhNTEh0dHBSZXF1ZXN0KCk7XG4gICAgICAgIHhoci5vcGVuKFwiUFVUXCIsIHByZXNpZ25lZC51cmwsIHRydWUpO1xuICAgICAgICB4aHIuc2V0UmVxdWVzdEhlYWRlcihcIlJhbmdlXCIsIGBieXRlcz0ke3JhbmdlU3RhcnR9LWApO1xuICAgICAgICB4aHIuc2V0UmVxdWVzdEhlYWRlcihcIngtdXBsb2FkdGhpbmctdmVyc2lvblwiLCB2ZXJzaW9uKTtcbiAgICAgICAgeGhyLnJlc3BvbnNlVHlwZSA9IFwianNvblwiO1xuICAgICAgICBsZXQgcHJldmlvdXNMb2FkZWQgPSAwO1xuICAgICAgICB4aHIudXBsb2FkLmFkZEV2ZW50TGlzdGVuZXIoXCJwcm9ncmVzc1wiLCAoeyBsb2FkZWQgfSk9PntcbiAgICAgICAgICAgIGNvbnN0IGRlbHRhID0gbG9hZGVkIC0gcHJldmlvdXNMb2FkZWQ7XG4gICAgICAgICAgICBvblVwbG9hZFByb2dyZXNzPy4oe1xuICAgICAgICAgICAgICAgIGxvYWRlZCxcbiAgICAgICAgICAgICAgICBkZWx0YVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBwcmV2aW91c0xvYWRlZCA9IGxvYWRlZDtcbiAgICAgICAgfSk7XG4gICAgICAgIHhoci5hZGRFdmVudExpc3RlbmVyKFwibG9hZFwiLCAoKT0+e1xuICAgICAgICAgICAgaWYgKHhoci5zdGF0dXMgPj0gMjAwICYmIHhoci5zdGF0dXMgPCAzMDAgJiYgaXNSZWNvcmQoeGhyLnJlc3BvbnNlKSkge1xuICAgICAgICAgICAgICAgIGlmIChoYXNQcm9wZXJ0eSh4aHIucmVzcG9uc2UsIFwiZXJyb3JcIikpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdW1lKG5ldyBVcGxvYWRUaGluZ0Vycm9yKHtcbiAgICAgICAgICAgICAgICAgICAgICAgIGNvZGU6IFwiVVBMT0FEX0ZBSUxFRFwiLFxuICAgICAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogU3RyaW5nKHhoci5yZXNwb25zZS5lcnJvciksXG4gICAgICAgICAgICAgICAgICAgICAgICBkYXRhOiB4aHIucmVzcG9uc2VcbiAgICAgICAgICAgICAgICAgICAgfSkpO1xuICAgICAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIHJlc3VtZShNaWNyby5zdWNjZWVkKHhoci5yZXNwb25zZSkpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICAgICAgcmVzdW1lKG5ldyBVcGxvYWRUaGluZ0Vycm9yKHtcbiAgICAgICAgICAgICAgICAgICAgY29kZTogXCJVUExPQURfRkFJTEVEXCIsXG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGBYSFIgZmFpbGVkICR7eGhyLnN0YXR1c30gJHt4aHIuc3RhdHVzVGV4dH1gLFxuICAgICAgICAgICAgICAgICAgICBkYXRhOiB4aHIucmVzcG9uc2VcbiAgICAgICAgICAgICAgICB9KSk7XG4gICAgICAgICAgICB9XG4gICAgICAgIH0pO1xuICAgICAgICAvLyBJcyB0aGVyZSBhIGNhc2Ugd2hlbiB0aGUgY2xpZW50IHdvdWxkIHRocm93IGFuZFxuICAgICAgICAvLyBpbmdlc3Qgc2VydmVyIG5vdCBrbm93aW5nIGFib3V0IGl0PyBpZHRzP1xuICAgICAgICB4aHIuYWRkRXZlbnRMaXN0ZW5lcihcImVycm9yXCIsICgpPT57XG4gICAgICAgICAgICByZXN1bWUobmV3IFVwbG9hZFRoaW5nRXJyb3Ioe1xuICAgICAgICAgICAgICAgIGNvZGU6IFwiVVBMT0FEX0ZBSUxFRFwiXG4gICAgICAgICAgICB9KSk7XG4gICAgICAgIH0pO1xuICAgICAgICBjb25zdCBmb3JtRGF0YSA9IG5ldyBGb3JtRGF0YSgpO1xuICAgICAgICAvKipcbiAgICAgKiBpT1MvUmVhY3QgTmF0aXZlIEZvcm1EYXRhIGhhbmRsaW5nIHJlcXVpcmVzIHNwZWNpYWwgYXR0ZW50aW9uOlxuICAgICAqXG4gICAgICogSXNzdWU6IEluIFJlYWN0IE5hdGl2ZSwgaU9TIGNyYXNoZXMgd2l0aCBcImF0dGVtcHQgdG8gaW5zZXJ0IG5pbCBvYmplY3RcIiB3aGVuIGFwcGVuZGluZyBGaWxlIGRpcmVjdGx5XG4gICAgICogdG8gRm9ybURhdGEuIFRoaXMgaGFwcGVucyBiZWNhdXNlIGlPUyB0cmllcyB0byBjcmVhdGUgTlNEaWN0aW9uYXJ5IGZyb20gdGhlIGZpbGUgb2JqZWN0IGFuZCBleHBlY3RzXG4gICAgICogc3BlY2lmaWMgc3RydWN0dXJlIHt1cmksIHR5cGUsIG5hbWV9LlxuICAgICAqXG4gICAgICpcbiAgICAgKiBOb3RlOiBEb24ndCB0cnkgdG8gdXNlIEJsb2Igb3IgbW9kaWZ5IEZpbGUgb2JqZWN0IC0gaU9TIHNwZWNpZmljYWxseSBuZWVkcyBwbGFpbiBvYmplY3RcbiAgICAgKiB3aXRoIHRoZXNlIHByb3BlcnRpZXMgdG8gY3JlYXRlIHZhbGlkIE5TRGljdGlvbmFyeS5cbiAgICAgKi8gaWYgKFwidXJpXCIgaW4gZmlsZSkge1xuICAgICAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnNhZmUtYXJndW1lbnRcbiAgICAgICAgICAgIGZvcm1EYXRhLmFwcGVuZChcImZpbGVcIiwge1xuICAgICAgICAgICAgICAgIHVyaTogZmlsZS51cmksXG4gICAgICAgICAgICAgICAgdHlwZTogZmlsZS50eXBlLFxuICAgICAgICAgICAgICAgIG5hbWU6IGZpbGUubmFtZSxcbiAgICAgICAgICAgICAgICAuLi5yYW5nZVN0YXJ0ID4gMCAmJiB7XG4gICAgICAgICAgICAgICAgICAgIHJhbmdlOiByYW5nZVN0YXJ0XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBmb3JtRGF0YS5hcHBlbmQoXCJmaWxlXCIsIHJhbmdlU3RhcnQgPiAwID8gZmlsZS5zbGljZShyYW5nZVN0YXJ0KSA6IGZpbGUpO1xuICAgICAgICB9XG4gICAgICAgIHhoci5zZW5kKGZvcm1EYXRhKTtcbiAgICAgICAgcmV0dXJuIE1pY3JvLnN5bmMoKCk9Pnhoci5hYm9ydCgpKTtcbiAgICB9KTtcbmNvbnN0IHVwbG9hZEZpbGUgPSAoZmlsZSwgcHJlc2lnbmVkLCBvcHRzKT0+ZmV0Y2hFZmYocHJlc2lnbmVkLnVybCwge1xuICAgICAgICBtZXRob2Q6IFwiSEVBRFwiXG4gICAgfSkucGlwZShNaWNyby5tYXAoKHsgaGVhZGVycyB9KT0+cGFyc2VJbnQoaGVhZGVycy5nZXQoXCJ4LXV0LXJhbmdlLXN0YXJ0XCIpID8/IFwiMFwiLCAxMCkpLCBNaWNyby50YXAoKHN0YXJ0KT0+b3B0cy5vblVwbG9hZFByb2dyZXNzPy4oe1xuICAgICAgICAgICAgZGVsdGE6IHN0YXJ0LFxuICAgICAgICAgICAgbG9hZGVkOiBzdGFydFxuICAgICAgICB9KSksIE1pY3JvLmZsYXRNYXAoKHN0YXJ0KT0+dXBsb2FkV2l0aFByb2dyZXNzKGZpbGUsIHN0YXJ0LCBwcmVzaWduZWQsIChwcm9ncmVzc0V2ZW50KT0+b3B0cy5vblVwbG9hZFByb2dyZXNzPy4oe1xuICAgICAgICAgICAgICAgIGRlbHRhOiBwcm9ncmVzc0V2ZW50LmRlbHRhLFxuICAgICAgICAgICAgICAgIGxvYWRlZDogcHJvZ3Jlc3NFdmVudC5sb2FkZWQgKyBzdGFydFxuICAgICAgICAgICAgfSkpKSwgTWljcm8ubWFwKHVuc2FmZUNvZXJjZSksIE1pY3JvLm1hcCgodXBsb2FkUmVzcG9uc2UpPT4oe1xuICAgICAgICAgICAgbmFtZTogZmlsZS5uYW1lLFxuICAgICAgICAgICAgc2l6ZTogZmlsZS5zaXplLFxuICAgICAgICAgICAga2V5OiBwcmVzaWduZWQua2V5LFxuICAgICAgICAgICAgbGFzdE1vZGlmaWVkOiBmaWxlLmxhc3RNb2RpZmllZCxcbiAgICAgICAgICAgIHNlcnZlckRhdGE6IHVwbG9hZFJlc3BvbnNlLnNlcnZlckRhdGEsXG4gICAgICAgICAgICBnZXQgdXJsICgpIHtcbiAgICAgICAgICAgICAgICBsb2dEZXByZWNhdGlvbldhcm5pbmcoXCJgZmlsZS51cmxgIGlzIGRlcHJlY2F0ZWQgYW5kIHdpbGwgYmUgcmVtb3ZlZCBpbiB1cGxvYWR0aGluZyB2OS4gVXNlIGBmaWxlLnVmc1VybGAgaW5zdGVhZC5cIik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHVwbG9hZFJlc3BvbnNlLnVybDtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICBnZXQgYXBwVXJsICgpIHtcbiAgICAgICAgICAgICAgICBsb2dEZXByZWNhdGlvbldhcm5pbmcoXCJgZmlsZS5hcHBVcmxgIGlzIGRlcHJlY2F0ZWQgYW5kIHdpbGwgYmUgcmVtb3ZlZCBpbiB1cGxvYWR0aGluZyB2OS4gVXNlIGBmaWxlLnVmc1VybGAgaW5zdGVhZC5cIik7XG4gICAgICAgICAgICAgICAgcmV0dXJuIHVwbG9hZFJlc3BvbnNlLmFwcFVybDtcbiAgICAgICAgICAgIH0sXG4gICAgICAgICAgICB1ZnNVcmw6IHVwbG9hZFJlc3BvbnNlLnVmc1VybCxcbiAgICAgICAgICAgIGN1c3RvbUlkOiBwcmVzaWduZWQuY3VzdG9tSWQsXG4gICAgICAgICAgICB0eXBlOiBmaWxlLnR5cGUsXG4gICAgICAgICAgICBmaWxlSGFzaDogdXBsb2FkUmVzcG9uc2UuZmlsZUhhc2hcbiAgICAgICAgfSkpKTtcbmNvbnN0IHVwbG9hZEZpbGVzSW50ZXJuYWwgPSAoZW5kcG9pbnQsIG9wdHMpPT57XG4gICAgLy8gY2xhc3NpYyBzZXJ2aWNlIHJpZ2h0IGhlcmVcbiAgICBjb25zdCByZXBvcnRFdmVudFRvVVQgPSBjcmVhdGVVVFJlcG9ydGVyKHtcbiAgICAgICAgZW5kcG9pbnQ6IFN0cmluZyhlbmRwb2ludCksXG4gICAgICAgIHBhY2thZ2U6IG9wdHMucGFja2FnZSxcbiAgICAgICAgdXJsOiBvcHRzLnVybCxcbiAgICAgICAgaGVhZGVyczogb3B0cy5oZWFkZXJzXG4gICAgfSk7XG4gICAgY29uc3QgdG90YWxTaXplID0gb3B0cy5maWxlcy5yZWR1Y2UoKGFjYywgZik9PmFjYyArIGYuc2l6ZSwgMCk7XG4gICAgbGV0IHRvdGFsTG9hZGVkID0gMDtcbiAgICByZXR1cm4gTWljcm8uZmxhdE1hcChyZXBvcnRFdmVudFRvVVQoXCJ1cGxvYWRcIiwge1xuICAgICAgICBpbnB1dDogXCJpbnB1dFwiIGluIG9wdHMgPyBvcHRzLmlucHV0IDogbnVsbCxcbiAgICAgICAgZmlsZXM6IG9wdHMuZmlsZXMubWFwKChmKT0+KHtcbiAgICAgICAgICAgICAgICBuYW1lOiBmLm5hbWUsXG4gICAgICAgICAgICAgICAgc2l6ZTogZi5zaXplLFxuICAgICAgICAgICAgICAgIHR5cGU6IGYudHlwZSxcbiAgICAgICAgICAgICAgICBsYXN0TW9kaWZpZWQ6IGYubGFzdE1vZGlmaWVkXG4gICAgICAgICAgICB9KSlcbiAgICB9KSwgKHByZXNpZ25lZHMpPT5NaWNyby5mb3JFYWNoKHByZXNpZ25lZHMsIChwcmVzaWduZWQsIGkpPT5NaWNyby5mbGF0TWFwKE1pY3JvLnN5bmMoKCk9Pm9wdHMub25VcGxvYWRCZWdpbj8uKHtcbiAgICAgICAgICAgICAgICAgICAgZmlsZTogb3B0cy5maWxlc1tpXS5uYW1lXG4gICAgICAgICAgICAgICAgfSkpLCAoKT0+dXBsb2FkRmlsZShvcHRzLmZpbGVzW2ldLCBwcmVzaWduZWQsIHtcbiAgICAgICAgICAgICAgICAgICAgb25VcGxvYWRQcm9ncmVzczogKGV2KT0+e1xuICAgICAgICAgICAgICAgICAgICAgICAgdG90YWxMb2FkZWQgKz0gZXYuZGVsdGE7XG4gICAgICAgICAgICAgICAgICAgICAgICBvcHRzLm9uVXBsb2FkUHJvZ3Jlc3M/Lih7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgZmlsZTogb3B0cy5maWxlc1tpXSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBwcm9ncmVzczogZXYubG9hZGVkIC8gb3B0cy5maWxlc1tpXS5zaXplICogMTAwLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGxvYWRlZDogZXYubG9hZGVkLFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGRlbHRhOiBldi5kZWx0YSxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3RhbExvYWRlZCxcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB0b3RhbFByb2dyZXNzOiB0b3RhbExvYWRlZCAvIHRvdGFsU2l6ZVxuICAgICAgICAgICAgICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9KSksIHtcbiAgICAgICAgICAgIGNvbmN1cnJlbmN5OiA2XG4gICAgICAgIH0pKTtcbn07XG5cbmV4cG9ydCB7IHVwbG9hZEZpbGUsIHVwbG9hZEZpbGVzSW50ZXJuYWwgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/ut-reporter.js":
/*!************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/ut-reporter.js ***!
  \************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUTReporter: () => (/* binding */ createUTReporter)\n/* harmony export */ });\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n\n\n\n\nvar version = \"7.6.0\";\n\nconst createAPIRequestUrl = (config)=>{\n    const url = new URL(config.url);\n    const queryParams = new URLSearchParams(url.search);\n    queryParams.set(\"actionType\", config.actionType);\n    queryParams.set(\"slug\", config.slug);\n    url.search = queryParams.toString();\n    return url;\n};\n/**\n * Creates a \"client\" for reporting events to the UploadThing server via the user's API endpoint.\n * Events are handled in \"./handler.ts starting at L112\"\n */ const createUTReporter = (cfg)=>(type, payload)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.gen(function*() {\n            const url = createAPIRequestUrl({\n                url: cfg.url,\n                slug: cfg.endpoint,\n                actionType: type\n            });\n            const headers = new Headers((yield* effect_Micro__WEBPACK_IMPORTED_MODULE_0__.promise(async ()=>typeof cfg.headers === \"function\" ? await cfg.headers() : cfg.headers)));\n            headers.set(\"x-uploadthing-package\", cfg.package);\n            headers.set(\"x-uploadthing-version\", version);\n            headers.set(\"Content-Type\", \"application/json\");\n            const response = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.fetchEff)(url, {\n                method: \"POST\",\n                body: JSON.stringify(payload),\n                headers\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.parseResponseJson), /**\n         * We don't _need_ to validate the response here, just cast it for now.\n         * As of now, @effect/schema includes quite a few bytes we cut out by this...\n         * We have \"strong typing\" on the backend that ensures the shape should match.\n         */ effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(effect_Function__WEBPACK_IMPORTED_MODULE_2__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"FetchError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: `Failed to report event \"${type}\" to UploadThing server`,\n                    cause: e\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.getErrorTypeFromStatusCode)(e.status),\n                    message: e.getMessage(),\n                    cause: e.json\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"InvalidJson\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: \"Failed to parse response from UploadThing server\",\n                    cause: e\n                }))));\n            return response;\n        });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC91dC1yZXBvcnRlci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQStDO0FBQ1Q7QUFDMEU7O0FBRWhIOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxREFBcUQsNkNBQVM7QUFDOUQ7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhO0FBQ2IsZ0RBQWdELGlEQUFhO0FBQzdEO0FBQ0E7QUFDQTtBQUNBLG9DQUFvQyw2REFBUTtBQUM1QztBQUNBO0FBQ0E7QUFDQSxhQUFhLE9BQU8saURBQWEsQ0FBQyxrRUFBaUI7QUFDbkQ7QUFDQTtBQUNBO0FBQ0EsWUFBWSw2Q0FBUyxDQUFDLHlEQUFZLEdBQUcsa0RBQWMsb0JBQW9CLDhDQUFVLEtBQUssaUVBQWdCO0FBQ3RHO0FBQ0Esd0RBQXdELEtBQUs7QUFDN0Q7QUFDQSxpQkFBaUIsS0FBSyxrREFBYyx5QkFBeUIsOENBQVUsS0FBSyxpRUFBZ0I7QUFDNUYsMEJBQTBCLCtFQUEwQjtBQUNwRDtBQUNBO0FBQ0EsaUJBQWlCLEtBQUssa0RBQWMscUJBQXFCLDhDQUFVLEtBQUssaUVBQWdCO0FBQ3hGO0FBQ0E7QUFDQTtBQUNBLGlCQUFpQjtBQUNqQjtBQUNBLFNBQVM7O0FBRW1CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3VwbG9hZHRoaW5nQDcuNi4wX2V4cHJlc3NANF9jNDM3YjlhYTc0NDU5MTk4ZTFmMWQ2YzYzYTNkZGE0Zi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvZGlzdC9faW50ZXJuYWwvdXQtcmVwb3J0ZXIuanM/MDBkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyB1bnNhZmVDb2VyY2UgfSBmcm9tICdlZmZlY3QvRnVuY3Rpb24nO1xuaW1wb3J0ICogYXMgTWljcm8gZnJvbSAnZWZmZWN0L01pY3JvJztcbmltcG9ydCB7IGZldGNoRWZmLCBwYXJzZVJlc3BvbnNlSnNvbiwgVXBsb2FkVGhpbmdFcnJvciwgZ2V0RXJyb3JUeXBlRnJvbVN0YXR1c0NvZGUgfSBmcm9tICdAdXBsb2FkdGhpbmcvc2hhcmVkJztcblxudmFyIHZlcnNpb24gPSBcIjcuNi4wXCI7XG5cbmNvbnN0IGNyZWF0ZUFQSVJlcXVlc3RVcmwgPSAoY29uZmlnKT0+e1xuICAgIGNvbnN0IHVybCA9IG5ldyBVUkwoY29uZmlnLnVybCk7XG4gICAgY29uc3QgcXVlcnlQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHVybC5zZWFyY2gpO1xuICAgIHF1ZXJ5UGFyYW1zLnNldChcImFjdGlvblR5cGVcIiwgY29uZmlnLmFjdGlvblR5cGUpO1xuICAgIHF1ZXJ5UGFyYW1zLnNldChcInNsdWdcIiwgY29uZmlnLnNsdWcpO1xuICAgIHVybC5zZWFyY2ggPSBxdWVyeVBhcmFtcy50b1N0cmluZygpO1xuICAgIHJldHVybiB1cmw7XG59O1xuLyoqXG4gKiBDcmVhdGVzIGEgXCJjbGllbnRcIiBmb3IgcmVwb3J0aW5nIGV2ZW50cyB0byB0aGUgVXBsb2FkVGhpbmcgc2VydmVyIHZpYSB0aGUgdXNlcidzIEFQSSBlbmRwb2ludC5cbiAqIEV2ZW50cyBhcmUgaGFuZGxlZCBpbiBcIi4vaGFuZGxlci50cyBzdGFydGluZyBhdCBMMTEyXCJcbiAqLyBjb25zdCBjcmVhdGVVVFJlcG9ydGVyID0gKGNmZyk9Pih0eXBlLCBwYXlsb2FkKT0+TWljcm8uZ2VuKGZ1bmN0aW9uKigpIHtcbiAgICAgICAgICAgIGNvbnN0IHVybCA9IGNyZWF0ZUFQSVJlcXVlc3RVcmwoe1xuICAgICAgICAgICAgICAgIHVybDogY2ZnLnVybCxcbiAgICAgICAgICAgICAgICBzbHVnOiBjZmcuZW5kcG9pbnQsXG4gICAgICAgICAgICAgICAgYWN0aW9uVHlwZTogdHlwZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgICAgICBjb25zdCBoZWFkZXJzID0gbmV3IEhlYWRlcnMoKHlpZWxkKiBNaWNyby5wcm9taXNlKGFzeW5jICgpPT50eXBlb2YgY2ZnLmhlYWRlcnMgPT09IFwiZnVuY3Rpb25cIiA/IGF3YWl0IGNmZy5oZWFkZXJzKCkgOiBjZmcuaGVhZGVycykpKTtcbiAgICAgICAgICAgIGhlYWRlcnMuc2V0KFwieC11cGxvYWR0aGluZy1wYWNrYWdlXCIsIGNmZy5wYWNrYWdlKTtcbiAgICAgICAgICAgIGhlYWRlcnMuc2V0KFwieC11cGxvYWR0aGluZy12ZXJzaW9uXCIsIHZlcnNpb24pO1xuICAgICAgICAgICAgaGVhZGVycy5zZXQoXCJDb250ZW50LVR5cGVcIiwgXCJhcHBsaWNhdGlvbi9qc29uXCIpO1xuICAgICAgICAgICAgY29uc3QgcmVzcG9uc2UgPSB5aWVsZCogZmV0Y2hFZmYodXJsLCB7XG4gICAgICAgICAgICAgICAgbWV0aG9kOiBcIlBPU1RcIixcbiAgICAgICAgICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShwYXlsb2FkKSxcbiAgICAgICAgICAgICAgICBoZWFkZXJzXG4gICAgICAgICAgICB9KS5waXBlKE1pY3JvLmFuZFRoZW4ocGFyc2VSZXNwb25zZUpzb24pLCAvKipcbiAgICAgICAgICogV2UgZG9uJ3QgX25lZWRfIHRvIHZhbGlkYXRlIHRoZSByZXNwb25zZSBoZXJlLCBqdXN0IGNhc3QgaXQgZm9yIG5vdy5cbiAgICAgICAgICogQXMgb2Ygbm93LCBAZWZmZWN0L3NjaGVtYSBpbmNsdWRlcyBxdWl0ZSBhIGZldyBieXRlcyB3ZSBjdXQgb3V0IGJ5IHRoaXMuLi5cbiAgICAgICAgICogV2UgaGF2ZSBcInN0cm9uZyB0eXBpbmdcIiBvbiB0aGUgYmFja2VuZCB0aGF0IGVuc3VyZXMgdGhlIHNoYXBlIHNob3VsZCBtYXRjaC5cbiAgICAgICAgICovIE1pY3JvLm1hcCh1bnNhZmVDb2VyY2UpLCBNaWNyby5jYXRjaFRhZyhcIkZldGNoRXJyb3JcIiwgKGUpPT5NaWNyby5mYWlsKG5ldyBVcGxvYWRUaGluZ0Vycm9yKHtcbiAgICAgICAgICAgICAgICAgICAgY29kZTogXCJJTlRFUk5BTF9DTElFTlRfRVJST1JcIixcbiAgICAgICAgICAgICAgICAgICAgbWVzc2FnZTogYEZhaWxlZCB0byByZXBvcnQgZXZlbnQgXCIke3R5cGV9XCIgdG8gVXBsb2FkVGhpbmcgc2VydmVyYCxcbiAgICAgICAgICAgICAgICAgICAgY2F1c2U6IGVcbiAgICAgICAgICAgICAgICB9KSkpLCBNaWNyby5jYXRjaFRhZyhcIkJhZFJlcXVlc3RFcnJvclwiLCAoZSk9Pk1pY3JvLmZhaWwobmV3IFVwbG9hZFRoaW5nRXJyb3Ioe1xuICAgICAgICAgICAgICAgICAgICBjb2RlOiBnZXRFcnJvclR5cGVGcm9tU3RhdHVzQ29kZShlLnN0YXR1cyksXG4gICAgICAgICAgICAgICAgICAgIG1lc3NhZ2U6IGUuZ2V0TWVzc2FnZSgpLFxuICAgICAgICAgICAgICAgICAgICBjYXVzZTogZS5qc29uXG4gICAgICAgICAgICAgICAgfSkpKSwgTWljcm8uY2F0Y2hUYWcoXCJJbnZhbGlkSnNvblwiLCAoZSk9Pk1pY3JvLmZhaWwobmV3IFVwbG9hZFRoaW5nRXJyb3Ioe1xuICAgICAgICAgICAgICAgICAgICBjb2RlOiBcIklOVEVSTkFMX0NMSUVOVF9FUlJPUlwiLFxuICAgICAgICAgICAgICAgICAgICBtZXNzYWdlOiBcIkZhaWxlZCB0byBwYXJzZSByZXNwb25zZSBmcm9tIFVwbG9hZFRoaW5nIHNlcnZlclwiLFxuICAgICAgICAgICAgICAgICAgICBjYXVzZTogZVxuICAgICAgICAgICAgICAgIH0pKSkpO1xuICAgICAgICAgICAgcmV0dXJuIHJlc3BvbnNlO1xuICAgICAgICB9KTtcblxuZXhwb3J0IHsgY3JlYXRlVVRSZXBvcnRlciB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/ut-reporter.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiUrl: () => (/* binding */ ApiUrl),\n/* harmony export */   IngestUrl: () => (/* binding */ IngestUrl),\n/* harmony export */   IsDevelopment: () => (/* binding */ IsDevelopment),\n/* harmony export */   UPLOADTHING_VERSION: () => (/* binding */ version),\n/* harmony export */   UTToken: () => (/* binding */ UTToken),\n/* harmony export */   UfsHost: () => (/* binding */ UfsHost),\n/* harmony export */   UtfsHost: () => (/* binding */ UtfsHost),\n/* harmony export */   configProvider: () => (/* binding */ configProvider)\n/* harmony export */ });\n/* harmony import */ var effect_Config__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Config */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Config.js\");\n/* harmony import */ var effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/ConfigProvider */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/ConfigProvider.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _shared_schemas_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./shared-schemas.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/shared-schemas.js\");\n\n\n\n\n\n\n\nvar version = \"7.6.0\";\n\n/**\n * Merge in `import.meta.env` to the built-in `process.env` provider\n * Prefix keys with `UPLOADTHING_` so we can reference just the name.\n * @example\n * process.env.UPLOADTHING_TOKEN = \"foo\"\n * Config.string(\"token\"); // Config<\"foo\">\n */ const envProvider = effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.fromEnv().pipe(effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.orElse(()=>effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.fromMap(new Map(Object.entries((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.filterDefinedObjectValues)(// fuck this I give up. import.meta is a mistake, someone else can fix it\n     null ?? {}))), {\n        pathDelim: \"_\"\n    })), effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.nested(\"uploadthing\"), effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.constantCase);\n/**\n * Config provider that merges the options from the object\n * and environment variables prefixed with `UPLOADTHING_`.\n * @remarks Options take precedence over environment variables.\n */ const configProvider = (options)=>effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.fromJson(options ?? {}).pipe(effect_ConfigProvider__WEBPACK_IMPORTED_MODULE_0__.orElse(()=>envProvider));\nconst IsDevelopment = effect_Config__WEBPACK_IMPORTED_MODULE_2__.boolean(\"isDev\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.orElse(()=>effect_Config__WEBPACK_IMPORTED_MODULE_2__.succeed(typeof process !== \"undefined\" ? \"development\" : undefined).pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.map((_)=>_ === \"development\"))), effect_Config__WEBPACK_IMPORTED_MODULE_2__.withDefault(false));\nconst UTToken = effect_Schema__WEBPACK_IMPORTED_MODULE_3__.Config(\"token\", _shared_schemas_js__WEBPACK_IMPORTED_MODULE_4__.UploadThingToken).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTags({\n    ConfigError: (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n            code: e._op === \"InvalidData\" ? \"INVALID_SERVER_CONFIG\" : \"MISSING_ENV\",\n            message: e._op === \"InvalidData\" ? \"Invalid token. A token is a base64 encoded JSON object matching { apiKey: string, appId: string, regions: string[] }.\" : \"Missing token. Please set the `UPLOADTHING_TOKEN` environment variable or provide a token manually through config.\",\n            cause: e\n        })\n}));\nconst ApiUrl = effect_Config__WEBPACK_IMPORTED_MODULE_2__.string(\"apiUrl\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.withDefault(\"https://api.uploadthing.com\"), effect_Config__WEBPACK_IMPORTED_MODULE_2__.mapAttempt((_)=>new URL(_)), effect_Config__WEBPACK_IMPORTED_MODULE_2__.map((url)=>url.href.replace(/\\/$/, \"\")));\nconst IngestUrl = effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(function*() {\n    const { regions, ingestHost } = yield* UTToken;\n    const region = regions[0]; // Currently only support 1 region per app\n    return yield* effect_Config__WEBPACK_IMPORTED_MODULE_2__.string(\"ingestUrl\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.withDefault(`https://${region}.${ingestHost}`), effect_Config__WEBPACK_IMPORTED_MODULE_2__.mapAttempt((_)=>new URL(_)), effect_Config__WEBPACK_IMPORTED_MODULE_2__.map((url)=>url.href.replace(/\\/$/, \"\")));\n});\nconst UtfsHost = effect_Config__WEBPACK_IMPORTED_MODULE_2__.string(\"utfsHost\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.withDefault(\"utfs.io\"));\nconst UfsHost = effect_Config__WEBPACK_IMPORTED_MODULE_2__.string(\"ufsHost\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_2__.withDefault(\"ufs.sh\"));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logDeprecationWarning: () => (/* binding */ logDeprecationWarning)\n/* harmony export */ });\nconst logDeprecationWarning = (message)=>{\n    // eslint-disable-next-line no-console\n    console.warn(`⚠️ [uploadthing][deprecated] ${message}`);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL2RlcHJlY2F0aW9ucy5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBLGlEQUFpRCxRQUFRO0FBQ3pEOztBQUVpQyIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL2RlcHJlY2F0aW9ucy5qcz9iMzA1Il0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxvZ0RlcHJlY2F0aW9uV2FybmluZyA9IChtZXNzYWdlKT0+e1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlXG4gICAgY29uc29sZS53YXJuKGDimqDvuI8gW3VwbG9hZHRoaW5nXVtkZXByZWNhdGVkXSAke21lc3NhZ2V9YCk7XG59O1xuXG5leHBvcnQgeyBsb2dEZXByZWNhdGlvbldhcm5pbmcgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/error-formatter.js":
/*!****************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/error-formatter.js ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultErrorFormatter: () => (/* binding */ defaultErrorFormatter),\n/* harmony export */   formatError: () => (/* binding */ formatError)\n/* harmony export */ });\nfunction defaultErrorFormatter(error) {\n    return {\n        message: error.message\n    };\n}\nfunction formatError(error, router) {\n    const firstSlug = Object.keys(router)[0];\n    const errorFormatter = firstSlug ? router[firstSlug]?.errorFormatter ?? defaultErrorFormatter : defaultErrorFormatter;\n    return errorFormatter(error);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL2Vycm9yLWZvcm1hdHRlci5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU4QyIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL2Vycm9yLWZvcm1hdHRlci5qcz9mMmQzIl0sInNvdXJjZXNDb250ZW50IjpbImZ1bmN0aW9uIGRlZmF1bHRFcnJvckZvcm1hdHRlcihlcnJvcikge1xuICAgIHJldHVybiB7XG4gICAgICAgIG1lc3NhZ2U6IGVycm9yLm1lc3NhZ2VcbiAgICB9O1xufVxuZnVuY3Rpb24gZm9ybWF0RXJyb3IoZXJyb3IsIHJvdXRlcikge1xuICAgIGNvbnN0IGZpcnN0U2x1ZyA9IE9iamVjdC5rZXlzKHJvdXRlcilbMF07XG4gICAgY29uc3QgZXJyb3JGb3JtYXR0ZXIgPSBmaXJzdFNsdWcgPyByb3V0ZXJbZmlyc3RTbHVnXT8uZXJyb3JGb3JtYXR0ZXIgPz8gZGVmYXVsdEVycm9yRm9ybWF0dGVyIDogZGVmYXVsdEVycm9yRm9ybWF0dGVyO1xuICAgIHJldHVybiBlcnJvckZvcm1hdHRlcihlcnJvcik7XG59XG5cbmV4cG9ydCB7IGRlZmF1bHRFcnJvckZvcm1hdHRlciwgZm9ybWF0RXJyb3IgfTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/error-formatter.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/handler.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/handler.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdapterArguments: () => (/* binding */ AdapterArguments),\n/* harmony export */   createRequestHandler: () => (/* binding */ createRequestHandler),\n/* harmony export */   makeAdapterHandler: () => (/* binding */ makeAdapterHandler)\n/* harmony export */ });\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpApp.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpServerResponse.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpServerRequest.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpRouter.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClient.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClientRequest.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpIncomingMessage.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClientResponse.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpBody.js\");\n/* harmony import */ var effect_Config__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! effect/Config */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Config.js\");\n/* harmony import */ var effect_Context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Context */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Context.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Match__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! effect/Match */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Match.js\");\n/* harmony import */ var effect_Redacted__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! effect/Redacted */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Redacted.js\");\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js\");\n/* harmony import */ var _deprecations_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./deprecations.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js\");\n/* harmony import */ var _error_formatter_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./error-formatter.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/error-formatter.js\");\n/* harmony import */ var _jsonl_js__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! ./jsonl.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/jsonl.js\");\n/* harmony import */ var _logger_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./logger.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js\");\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! ./parser.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/parser.js\");\n/* harmony import */ var _route_config_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./route-config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/route-config.js\");\n/* harmony import */ var _runtime_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./runtime.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/runtime.js\");\n/* harmony import */ var _shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./shared-schemas.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/shared-schemas.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! ./types.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar version = \"7.6.0\";\n\nclass AdapterArguments extends effect_Context__WEBPACK_IMPORTED_MODULE_0__.Tag(\"uploadthing/AdapterArguments\")() {\n}\n/**\n * Create a request handler adapter for any framework or server library.\n * Refer to the existing adapters for examples on how to use this function.\n * @public\n *\n * @param makeAdapterArgs - Function that takes the args from your framework and returns an Effect that resolves to the adapter args.\n * These args are passed to the `.middleware`, `.onUploadComplete`, and `.onUploadError` hooks.\n * @param toRequest - Function that takes the args from your framework and returns an Effect that resolves to a web Request object.\n * @param opts - The router config and other options that are normally passed to `createRequestHandler` of official adapters\n * @param beAdapter - [Optional] The adapter name of the adapter, used for telemetry purposes\n * @returns A function that takes the args from your framework and returns a promise that resolves to a Response object.\n */ const makeAdapterHandler = (makeAdapterArgs, toRequest, opts, beAdapter)=>{\n    const managed = (0,_runtime_js__WEBPACK_IMPORTED_MODULE_1__.makeRuntime)(opts.config?.fetch, opts.config);\n    const handle = effect_Effect__WEBPACK_IMPORTED_MODULE_2__.promise(()=>managed.runtime().then(_effect_platform__WEBPACK_IMPORTED_MODULE_3__.toWebHandlerRuntime));\n    const app = (...args)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.map(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.promise(()=>managed.runPromise(createRequestHandler(opts, beAdapter ?? \"custom\"))), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.provideServiceEffect(AdapterArguments, makeAdapterArgs(...args)));\n    return async (...args)=>{\n        const result = await handle.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.ap(app(...args)), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.ap(toRequest(...args)), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"requestHandler\"), managed.runPromise);\n        return result;\n    };\n};\nconst createRequestHandler = (opts, beAdapter)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const isDevelopment = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.IsDevelopment;\n        const routerConfig = yield* (0,_route_config_js__WEBPACK_IMPORTED_MODULE_5__.extractRouterConfig)(opts.router);\n        const handleDaemon = (()=>{\n            if (opts.config?.handleDaemonPromise) {\n                return opts.config.handleDaemonPromise;\n            }\n            return isDevelopment ? \"void\" : \"await\";\n        })();\n        if (isDevelopment && handleDaemon === \"await\") {\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"INVALID_SERVER_CONFIG\",\n                message: 'handleDaemonPromise: \"await\" is forbidden in development.'\n            });\n        }\n        const GET = effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n            return yield* _effect_platform__WEBPACK_IMPORTED_MODULE_7__.json(routerConfig);\n        });\n        const POST = effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n            const { \"uploadthing-hook\": uploadthingHook, \"x-uploadthing-package\": fePackage, \"x-uploadthing-version\": clientVersion } = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.schemaHeaders(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Struct({\n                \"uploadthing-hook\": _shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.UploadThingHook.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.optional),\n                \"x-uploadthing-package\": effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.optionalWith({\n                    default: ()=>\"unknown\"\n                })),\n                \"x-uploadthing-version\": effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.optionalWith({\n                    default: ()=>version\n                }))\n            }));\n            if (clientVersion !== version) {\n                const serverVersion = version;\n                yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logWarning(\"Client version mismatch. Things may not work as expected, please sync your versions to ensure compatibility.\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs({\n                    clientVersion,\n                    serverVersion\n                }));\n            }\n            const { slug, actionType } = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_11__.schemaParams(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Struct({\n                actionType: _shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.ActionType.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.optional),\n                slug: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String\n            }));\n            const uploadable = opts.router[slug];\n            if (!uploadable) {\n                const msg = `No file route found for slug ${slug}`;\n                yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(msg);\n                return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                    code: \"NOT_FOUND\",\n                    message: msg\n                });\n            }\n            const { body, fiber } = yield* effect_Match__WEBPACK_IMPORTED_MODULE_12__.value({\n                actionType,\n                uploadthingHook\n            }).pipe(effect_Match__WEBPACK_IMPORTED_MODULE_12__.when({\n                actionType: \"upload\",\n                uploadthingHook: undefined\n            }, ()=>handleUploadAction({\n                    uploadable,\n                    fePackage,\n                    beAdapter,\n                    slug\n                })), effect_Match__WEBPACK_IMPORTED_MODULE_12__.when({\n                actionType: undefined,\n                uploadthingHook: \"callback\"\n            }, ()=>handleCallbackRequest({\n                    uploadable,\n                    fePackage,\n                    beAdapter\n                })), effect_Match__WEBPACK_IMPORTED_MODULE_12__.when({\n                actionType: undefined,\n                uploadthingHook: \"error\"\n            }, ()=>handleErrorRequest({\n                    uploadable\n                })), effect_Match__WEBPACK_IMPORTED_MODULE_12__.orElse(()=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.succeed({\n                    body: null,\n                    fiber: null\n                })));\n            if (fiber) {\n                yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Running fiber as daemon\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"handleDaemon\", handleDaemon));\n                if (handleDaemon === \"void\") ; else if (handleDaemon === \"await\") {\n                    yield* fiber.await;\n                } else if (typeof handleDaemon === \"function\") {\n                    handleDaemon(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.runPromise(fiber.await));\n                }\n            }\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Sending response\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"body\", body));\n            return yield* _effect_platform__WEBPACK_IMPORTED_MODULE_7__.json(body);\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.catchTags({\n            ParseError: (e)=>_effect_platform__WEBPACK_IMPORTED_MODULE_7__.json((0,_error_formatter_js__WEBPACK_IMPORTED_MODULE_13__.formatError)(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid input\",\n                    cause: e.message\n                }), opts.router), {\n                    status: 400\n                }),\n            UploadThingError: (e)=>// eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n                _effect_platform__WEBPACK_IMPORTED_MODULE_7__.json((0,_error_formatter_js__WEBPACK_IMPORTED_MODULE_13__.formatError)(e, opts.router), {\n                    status: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.getStatusCodeFromError)(e)\n                })\n        }));\n        const appendResponseHeaders = effect_Effect__WEBPACK_IMPORTED_MODULE_2__.map(_effect_platform__WEBPACK_IMPORTED_MODULE_7__.setHeader(\"x-uploadthing-version\", version));\n        return _effect_platform__WEBPACK_IMPORTED_MODULE_11__.empty.pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_11__.get(\"*\", GET), _effect_platform__WEBPACK_IMPORTED_MODULE_11__.post(\"*\", POST), _effect_platform__WEBPACK_IMPORTED_MODULE_11__.use(appendResponseHeaders));\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"createRequestHandler\"));\nconst handleErrorRequest = (opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const { uploadable } = opts;\n        const request = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.HttpServerRequest;\n        const { apiKey } = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.UTToken;\n        const verified = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.verifySignature)((yield* request.text), request.headers[\"x-uploadthing-signature\"] ?? null, apiKey);\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(`Signature verified: ${verified}`);\n        if (!verified) {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(\"Invalid signature\");\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid signature\"\n            });\n        }\n        const requestInput = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.schemaBodyJson(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Struct({\n            fileKey: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String,\n            error: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String\n        }));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Handling error callback request with input:\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"json\", requestInput));\n        const adapterArgs = yield* AdapterArguments;\n        const fiber = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tryPromise({\n            try: async ()=>uploadable.onUploadError({\n                    ...adapterArgs,\n                    error: new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                        code: \"UPLOAD_FAILED\",\n                        message: `Upload failed for ${requestInput.fileKey}: ${requestInput.error}`\n                    }),\n                    fileKey: requestInput.fileKey\n                }),\n            catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to run onUploadError\",\n                    cause: error\n                })\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tapError((error)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(\"Failed to run onUploadError. You probably shouldn't be throwing errors here.\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"error\", error)))).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.ignoreLogged, effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forkDaemon);\n        return {\n            body: null,\n            fiber\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"handleErrorRequest\"));\nconst handleCallbackRequest = (opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const { uploadable, fePackage, beAdapter } = opts;\n        const request = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.HttpServerRequest;\n        const { apiKey } = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.UTToken;\n        const verified = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.verifySignature)((yield* request.text), request.headers[\"x-uploadthing-signature\"] ?? null, apiKey);\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(`Signature verified: ${verified}`);\n        if (!verified) {\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(\"Invalid signature\");\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid signature\"\n            });\n        }\n        const requestInput = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.schemaBodyJson(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Struct({\n            status: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String,\n            file: _shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.UploadedFileData,\n            metadata: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Record({\n                key: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.String,\n                value: effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Unknown\n            })\n        }));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Handling callback request with input:\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"json\", requestInput));\n        /**\n     * Run `.onUploadComplete` as a daemon to prevent the\n     * request from UT to potentially timeout.\n     */ const fiber = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n            const adapterArgs = yield* AdapterArguments;\n            const serverData = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tryPromise({\n                try: async ()=>uploadable.onUploadComplete({\n                        ...adapterArgs,\n                        file: {\n                            ...requestInput.file,\n                            get url () {\n                                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_14__.logDeprecationWarning)(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                                return requestInput.file.url;\n                            },\n                            get appUrl () {\n                                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_14__.logDeprecationWarning)(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                                return requestInput.file.appUrl;\n                            }\n                        },\n                        metadata: requestInput.metadata\n                    }),\n                catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                        code: \"INTERNAL_SERVER_ERROR\",\n                        message: \"Failed to run onUploadComplete. You probably shouldn't be throwing errors here.\",\n                        cause: error\n                    })\n            });\n            const payload = {\n                fileKey: requestInput.file.key,\n                callbackData: serverData ?? null\n            };\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"'onUploadComplete' callback finished. Sending response to UploadThing:\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"callbackData\", payload));\n            const baseUrl = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.IngestUrl;\n            const httpClient = (yield* _effect_platform__WEBPACK_IMPORTED_MODULE_15__.HttpClient).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_15__.filterStatusOk);\n            yield* _effect_platform__WEBPACK_IMPORTED_MODULE_16__.post(`/callback-result`).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_16__.prependUrl(baseUrl), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.setHeaders({\n                \"x-uploadthing-api-key\": effect_Redacted__WEBPACK_IMPORTED_MODULE_17__.value(apiKey),\n                \"x-uploadthing-version\": version,\n                \"x-uploadthing-be-adapter\": beAdapter,\n                \"x-uploadthing-fe-package\": fePackage\n            }), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.bodyJson(payload), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.flatMap(httpClient.execute), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tapError((0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientError)(\"Failed to register callback result\")), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.flatMap(_effect_platform__WEBPACK_IMPORTED_MODULE_19__.schemaBodyJson(_shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.CallbackResultResponse)), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tap(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.log(\"Sent callback result to UploadThing\")), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.scoped);\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.ignoreLogged, effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forkDaemon);\n        return {\n            body: null,\n            fiber\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"handleCallbackRequest\"));\nconst runRouteMiddleware = (opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const { json: { files, input }, uploadable } = opts;\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Running middleware\");\n        const adapterArgs = yield* AdapterArguments;\n        const metadata = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tryPromise({\n            try: async ()=>uploadable.middleware({\n                    ...adapterArgs,\n                    input,\n                    files\n                }),\n            catch: (error)=>error instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError ? error : new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                    code: \"INTERNAL_SERVER_ERROR\",\n                    message: \"Failed to run middleware\",\n                    cause: error\n                })\n        });\n        if (metadata[_types_js__WEBPACK_IMPORTED_MODULE_20__.UTFiles] && metadata[_types_js__WEBPACK_IMPORTED_MODULE_20__.UTFiles].length !== files.length) {\n            const msg = `Expected files override to have the same length as original files, got ${metadata[_types_js__WEBPACK_IMPORTED_MODULE_20__.UTFiles].length} but expected ${files.length}`;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(msg);\n            return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Files override must have the same length as files\",\n                cause: msg\n            });\n        }\n        // Attach customIds from middleware to the files\n        const filesWithCustomIds = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forEach(files, (file, idx)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n                const theirs = metadata[_types_js__WEBPACK_IMPORTED_MODULE_20__.UTFiles]?.[idx];\n                if (theirs && theirs.size !== file.size) {\n                    yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logWarning(\"File size mismatch. Reverting to original size\");\n                }\n                return {\n                    name: theirs?.name ?? file.name,\n                    size: file.size,\n                    type: file.type,\n                    customId: theirs?.customId,\n                    lastModified: theirs?.lastModified ?? Date.now()\n                };\n            }));\n        return {\n            metadata,\n            filesWithCustomIds\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"runRouteMiddleware\"));\nconst handleUploadAction = (opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const httpClient = (yield* _effect_platform__WEBPACK_IMPORTED_MODULE_15__.HttpClient).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_15__.filterStatusOk);\n        const { uploadable, fePackage, beAdapter, slug } = opts;\n        const json = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.schemaBodyJson(_shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.UploadActionPayload);\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Handling upload request\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"json\", json));\n        // validate the input\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Parsing user input\");\n        const parsedInput = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tryPromise({\n            try: ()=>(0,_parser_js__WEBPACK_IMPORTED_MODULE_21__.getParseFn)(uploadable.inputParser)(json.input),\n            catch: (error)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid input\",\n                    cause: error\n                })\n        });\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Input parsed successfully\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"input\", parsedInput));\n        const { metadata, filesWithCustomIds } = yield* runRouteMiddleware({\n            json: {\n                input: parsedInput,\n                files: json.files\n            },\n            uploadable\n        });\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Parsing route config\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"routerConfig\", uploadable.routerConfig));\n        const parsedConfig = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.fillInputRouteConfig)(uploadable.routerConfig).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.catchTag(\"InvalidRouteConfig\", (err)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: \"Invalid route config\",\n                cause: err\n            })));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Route config parsed successfully\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"routeConfig\", parsedConfig));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Validating files meet the config requirements\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"files\", json.files));\n        yield* (0,_route_config_js__WEBPACK_IMPORTED_MODULE_5__.assertFilesMeetConfig)(json.files, parsedConfig).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.mapError((e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n                code: \"BAD_REQUEST\",\n                message: `Invalid config: ${e._tag}`,\n                cause: \"reason\" in e ? e.reason : e.message\n            })));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Files validated.\");\n        const fileUploadRequests = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forEach(filesWithCustomIds, (file)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.map((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.objectKeys)(parsedConfig)), (type)=>({\n                    name: file.name,\n                    size: file.size,\n                    type: file.type || type,\n                    lastModified: file.lastModified,\n                    customId: file.customId,\n                    contentDisposition: parsedConfig[type]?.contentDisposition ?? \"inline\",\n                    acl: parsedConfig[type]?.acl\n                }))).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.catchTags({\n            /** Shouldn't happen since config is validated above so just dying is fine I think */ InvalidFileType: (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.die(e),\n            UnknownFileType: (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.die(e)\n        }));\n        const routeOptions = uploadable.routeOptions;\n        const { apiKey, appId } = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.UTToken;\n        const ingestUrl = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.IngestUrl;\n        const isDev = yield* _config_js__WEBPACK_IMPORTED_MODULE_4__.IsDevelopment;\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logDebug(\"Generating presigned URLs\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"fileUploadRequests\", fileUploadRequests), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"ingestUrl\", ingestUrl));\n        const presignedUrls = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forEach(fileUploadRequests, (file)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n                const key = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.generateKey)(file, appId, routeOptions.getFileHashParts);\n                const url = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.generateSignedURL)(`${ingestUrl}/${key}`, apiKey, {\n                    ttlInSeconds: routeOptions.presignedURLTTL,\n                    data: {\n                        \"x-ut-identifier\": appId,\n                        \"x-ut-file-name\": file.name,\n                        \"x-ut-file-size\": file.size,\n                        \"x-ut-file-type\": file.type,\n                        \"x-ut-slug\": slug,\n                        \"x-ut-custom-id\": file.customId,\n                        \"x-ut-content-disposition\": file.contentDisposition,\n                        \"x-ut-acl\": file.acl\n                    }\n                });\n                return {\n                    url,\n                    key\n                };\n            }), {\n            concurrency: \"unbounded\"\n        });\n        const serverReq = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.HttpServerRequest;\n        const requestUrl = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.toURL(serverReq);\n        const devHookRequest = yield* effect_Config__WEBPACK_IMPORTED_MODULE_22__.string(\"callbackUrl\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_22__.withDefault(requestUrl.origin + requestUrl.pathname), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.map((url)=>_effect_platform__WEBPACK_IMPORTED_MODULE_16__.post(url).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_16__.appendUrlParam(\"slug\", slug))));\n        const metadataRequest = _effect_platform__WEBPACK_IMPORTED_MODULE_16__.post(\"/route-metadata\").pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_16__.prependUrl(ingestUrl), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.setHeaders({\n            \"x-uploadthing-api-key\": effect_Redacted__WEBPACK_IMPORTED_MODULE_17__.value(apiKey),\n            \"x-uploadthing-version\": version,\n            \"x-uploadthing-be-adapter\": beAdapter,\n            \"x-uploadthing-fe-package\": fePackage\n        }), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.bodyJson({\n            fileKeys: presignedUrls.map(({ key })=>key),\n            metadata: metadata,\n            isDev,\n            callbackUrl: devHookRequest.url,\n            callbackSlug: slug,\n            awaitServerData: routeOptions.awaitServerData ?? true\n        }), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.flatMap(httpClient.execute));\n        const handleDevStreamError = effect_Effect__WEBPACK_IMPORTED_MODULE_2__.fn(\"handleDevStreamError\")(function*(err, chunk) {\n            const schema = effect_Schema__WEBPACK_IMPORTED_MODULE_9__.parseJson(effect_Schema__WEBPACK_IMPORTED_MODULE_9__.Struct({\n                file: _shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.UploadedFileData\n            }));\n            const parsedChunk = yield* effect_Schema__WEBPACK_IMPORTED_MODULE_9__.decodeUnknown(schema)(chunk);\n            const key = parsedChunk.file.key;\n            yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logError(\"Failed to forward callback request from dev stream\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs({\n                fileKey: key,\n                error: err.message\n            }));\n            const httpResponse = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_16__.post(\"/callback-result\").pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_16__.prependUrl(ingestUrl), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.setHeaders({\n                \"x-uploadthing-api-key\": effect_Redacted__WEBPACK_IMPORTED_MODULE_17__.value(apiKey),\n                \"x-uploadthing-version\": version,\n                \"x-uploadthing-be-adapter\": beAdapter,\n                \"x-uploadthing-fe-package\": fePackage\n            }), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.bodyJson({\n                fileKey: key,\n                error: `Failed to forward callback request from dev stream: ${err.message}`\n            }), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.flatMap(httpClient.execute));\n            yield* (0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientResponse)(\"Reported callback error to UploadThing\")(httpResponse);\n        });\n        // Send metadata to UT server (non blocking as a daemon)\n        // In dev, keep the stream open and simulate the callback requests as\n        // files complete uploading\n        const fiber = yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__[\"if\"](isDev, {\n            onTrue: ()=>metadataRequest.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tapBoth({\n                    onSuccess: (0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientResponse)(\"Registered metadata\", {\n                        mixin: \"None\"\n                    }),\n                    onFailure: (0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientError)(\"Failed to register metadata\")\n                }), _effect_platform__WEBPACK_IMPORTED_MODULE_23__.stream, (0,_jsonl_js__WEBPACK_IMPORTED_MODULE_24__.handleJsonLineStream)(_shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.MetadataFetchStreamPart, (chunk)=>devHookRequest.pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_16__.setHeaders({\n                        \"uploadthing-hook\": chunk.hook,\n                        \"x-uploadthing-signature\": chunk.signature\n                    }), _effect_platform__WEBPACK_IMPORTED_MODULE_16__.setBody(_effect_platform__WEBPACK_IMPORTED_MODULE_25__.text(chunk.payload, \"application/json\")), httpClient.execute, effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tap((0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientResponse)(\"Successfully forwarded callback request from dev stream\")), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.catchTag(\"ResponseError\", (err)=>handleDevStreamError(err, chunk.payload)), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(chunk), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.asVoid, effect_Effect__WEBPACK_IMPORTED_MODULE_2__.ignoreLogged, effect_Effect__WEBPACK_IMPORTED_MODULE_2__.scoped))),\n            onFalse: ()=>metadataRequest.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.tapBoth({\n                    onSuccess: (0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientResponse)(\"Registered metadata\"),\n                    onFailure: (0,_logger_js__WEBPACK_IMPORTED_MODULE_18__.logHttpClientError)(\"Failed to register metadata\")\n                }), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.flatMap(_effect_platform__WEBPACK_IMPORTED_MODULE_19__.schemaBodyJson(_shared_schemas_js__WEBPACK_IMPORTED_MODULE_10__.MetadataFetchResponse)), effect_Effect__WEBPACK_IMPORTED_MODULE_2__.scoped)\n        }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forkDaemon);\n        const presigneds = presignedUrls.map((p, i)=>({\n                url: p.url,\n                key: p.key,\n                name: fileUploadRequests[i].name,\n                customId: fileUploadRequests[i].customId ?? null\n            }));\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_2__.logInfo(\"Sending presigned URLs to client\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.annotateLogs(\"presignedUrls\", presigneds));\n        return {\n            body: presigneds,\n            fiber\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_2__.withLogSpan(\"handleUploadAction\"));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/handler.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/jsonl.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/jsonl.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handleJsonLineStream: () => (/* binding */ handleJsonLineStream)\n/* harmony export */ });\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n/* harmony import */ var effect_Stream__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Stream */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Stream.js\");\n\n\n\n\nconst handleJsonLineStream = (schema, onChunk)=>(stream)=>{\n        let buf = \"\";\n        return stream.pipe(effect_Stream__WEBPACK_IMPORTED_MODULE_0__.decodeText(), effect_Stream__WEBPACK_IMPORTED_MODULE_0__.mapEffect((chunk)=>effect_Effect__WEBPACK_IMPORTED_MODULE_1__.gen(function*() {\n                buf += chunk;\n                // Scan buffer for newlines\n                const parts = buf.split(\"\\n\");\n                const validChunks = [];\n                for (const part of parts){\n                    try {\n                        // Attempt to parse chunk as JSON\n                        validChunks.push(JSON.parse(part));\n                        // Advance buffer if parsing succeeded\n                        buf = buf.slice(part.length + 1);\n                    } catch  {\n                    //\n                    }\n                }\n                yield* effect_Effect__WEBPACK_IMPORTED_MODULE_1__.logDebug(\"Received chunks\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_1__.annotateLogs(\"chunk\", chunk), effect_Effect__WEBPACK_IMPORTED_MODULE_1__.annotateLogs(\"parsedChunks\", validChunks), effect_Effect__WEBPACK_IMPORTED_MODULE_1__.annotateLogs(\"buf\", buf));\n                return validChunks;\n            })), effect_Stream__WEBPACK_IMPORTED_MODULE_0__.mapEffect(effect_Schema__WEBPACK_IMPORTED_MODULE_2__.decodeUnknown(effect_Schema__WEBPACK_IMPORTED_MODULE_2__.Array(schema))), effect_Stream__WEBPACK_IMPORTED_MODULE_0__.mapEffect(effect_Effect__WEBPACK_IMPORTED_MODULE_1__.forEach((part)=>onChunk(part))), effect_Stream__WEBPACK_IMPORTED_MODULE_0__.runDrain, effect_Effect__WEBPACK_IMPORTED_MODULE_1__.withLogSpan(\"handleJsonLineStream\"));\n    };\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/jsonl.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LogFormat: () => (/* binding */ LogFormat),\n/* harmony export */   logHttpClientError: () => (/* binding */ logHttpClientError),\n/* harmony export */   logHttpClientResponse: () => (/* binding */ logHttpClientResponse),\n/* harmony export */   withLogFormat: () => (/* binding */ withLogFormat),\n/* harmony export */   withMinimalLogLevel: () => (/* binding */ withMinimalLogLevel)\n/* harmony export */ });\n/* harmony import */ var effect_Config__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Config */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Config.js\");\n/* harmony import */ var effect_ConfigError__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/ConfigError */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/ConfigError.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Either__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Either */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Either.js\");\n/* harmony import */ var effect_Layer__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! effect/Layer */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Layer.js\");\n/* harmony import */ var effect_Logger__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Logger */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Logger.js\");\n/* harmony import */ var effect_LogLevel__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/LogLevel */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/LogLevel.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js\");\n\n\n\n\n\n\n\n\n\n\n/**\n * Config.logLevel counter-intuitively accepts LogLevel[\"label\"]\n * instead of a literal, ripping it and changing to accept literal\n * Effect 4.0 will change this to accept a literal and then we can\n * remove this and go back to the built-in validator.\n */ const ConfigLogLevel = (name)=>{\n    const config = effect_Config__WEBPACK_IMPORTED_MODULE_0__.mapOrFail(effect_Config__WEBPACK_IMPORTED_MODULE_0__.string(), (literal)=>{\n        const level = effect_LogLevel__WEBPACK_IMPORTED_MODULE_1__.allLevels.find((level)=>level._tag === literal);\n        return level === undefined ? effect_Either__WEBPACK_IMPORTED_MODULE_2__.left(effect_ConfigError__WEBPACK_IMPORTED_MODULE_3__.InvalidData([], `Expected a log level but received ${literal}`)) : effect_Either__WEBPACK_IMPORTED_MODULE_2__.right(level);\n    });\n    return name === undefined ? config : effect_Config__WEBPACK_IMPORTED_MODULE_0__.nested(config, name);\n};\nconst withMinimalLogLevel = ConfigLogLevel(\"logLevel\").pipe(effect_Config__WEBPACK_IMPORTED_MODULE_0__.withDefault(effect_LogLevel__WEBPACK_IMPORTED_MODULE_1__.Info), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.andThen((level)=>effect_Logger__WEBPACK_IMPORTED_MODULE_5__.minimumLogLevel(level)), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.tapError((e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(\"Invalid log level\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.annotateLogs(\"error\", e))), effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"ConfigError\", (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n        code: \"INVALID_SERVER_CONFIG\",\n        message: \"Invalid server configuration\",\n        cause: e\n    })), effect_Layer__WEBPACK_IMPORTED_MODULE_7__.unwrapEffect);\nconst LogFormat = effect_Config__WEBPACK_IMPORTED_MODULE_0__.literal(\"json\", \"logFmt\", \"structured\", \"pretty\")(\"logFormat\");\nconst withLogFormat = effect_Effect__WEBPACK_IMPORTED_MODULE_4__.gen(function*() {\n    const isDev = yield* _config_js__WEBPACK_IMPORTED_MODULE_8__.IsDevelopment;\n    const logFormat = yield* LogFormat.pipe(effect_Config__WEBPACK_IMPORTED_MODULE_0__.withDefault(isDev ? \"pretty\" : \"json\"));\n    return effect_Logger__WEBPACK_IMPORTED_MODULE_5__[logFormat];\n}).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.catchTag(\"ConfigError\", (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_6__.UploadThingError({\n        code: \"INVALID_SERVER_CONFIG\",\n        message: \"Invalid server configuration\",\n        cause: e\n    })), effect_Layer__WEBPACK_IMPORTED_MODULE_7__.unwrapEffect);\nconst logHttpClientResponse = (message, opts)=>{\n    const mixin = opts?.mixin ?? \"json\";\n    const level = effect_LogLevel__WEBPACK_IMPORTED_MODULE_1__.fromLiteral(opts?.level ?? \"Debug\");\n    return (response)=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.flatMap(mixin !== \"None\" ? response[mixin] : effect_Effect__WEBPACK_IMPORTED_MODULE_4__[\"void\"], ()=>effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logWithLevel(level, `${message} (${response.status})`).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.annotateLogs(\"response\", response)));\n};\nconst logHttpClientError = (message)=>(err)=>err._tag === \"ResponseError\" ? logHttpClientResponse(message, {\n            level: \"Error\"\n        })(err.response) : effect_Effect__WEBPACK_IMPORTED_MODULE_4__.logError(message).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_4__.annotateLogs(\"error\", err));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/parser.js":
/*!*******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/parser.js ***!
  \*******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ParserError: () => (/* binding */ ParserError),\n/* harmony export */   getParseFn: () => (/* binding */ getParseFn)\n/* harmony export */ });\n/* harmony import */ var effect_Cause__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Cause */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Cause.js\");\n/* harmony import */ var effect_Data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Data */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Data.js\");\n/* harmony import */ var effect_Runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Runtime */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Runtime.js\");\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n\n\n\n\n\nclass ParserError extends effect_Data__WEBPACK_IMPORTED_MODULE_0__.TaggedError(\"ParserError\") {\n    constructor(...args){\n        super(...args), this.message = \"Input validation failed. The original error with it's validation issues is in the error cause.\";\n    }\n}\nfunction getParseFn(parser) {\n    if (\"parseAsync\" in parser && typeof parser.parseAsync === \"function\") {\n        /**\n     * Zod\n     * TODO (next major): Consider wrapping ZodError in ParserError\n     */ return parser.parseAsync;\n    }\n    if (effect_Schema__WEBPACK_IMPORTED_MODULE_1__.isSchema(parser)) {\n        /**\n     * Effect Schema\n     */ return (value)=>effect_Schema__WEBPACK_IMPORTED_MODULE_1__.decodeUnknownPromise(parser)(value).catch((error)=>{\n                throw new ParserError({\n                    cause: effect_Cause__WEBPACK_IMPORTED_MODULE_2__.squash(error[effect_Runtime__WEBPACK_IMPORTED_MODULE_3__.FiberFailureCauseId])\n                });\n            });\n    }\n    if (\"~standard\" in parser) {\n        /**\n     * Standard Schema\n     * TODO (next major): Consider moving this to the top of the function\n     */ return async (value)=>{\n            const result = await parser[\"~standard\"].validate(value);\n            if (result.issues) {\n                throw new ParserError({\n                    cause: result.issues\n                });\n            }\n            return result.value;\n        };\n    }\n    throw new Error(\"Invalid parser\");\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/parser.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/route-config.js":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/route-config.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assertFilesMeetConfig: () => (/* binding */ assertFilesMeetConfig),\n/* harmony export */   extractRouterConfig: () => (/* binding */ extractRouterConfig)\n/* harmony export */ });\n/* harmony import */ var effect_Data__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Data */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Data.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n\n\n\n\nclass FileSizeMismatch extends effect_Data__WEBPACK_IMPORTED_MODULE_0__.Error {\n    constructor(type, max, actual){\n        const reason = `You uploaded a ${type} file that was ${(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.bytesToFileSize)(actual)}, but the limit for that type is ${max}`;\n        super({\n            reason\n        }), this._tag = \"FileSizeMismatch\", this.name = \"FileSizeMismatchError\";\n    }\n}\nclass FileCountMismatch extends effect_Data__WEBPACK_IMPORTED_MODULE_0__.Error {\n    constructor(type, boundtype, bound, actual){\n        const reason = `You uploaded ${actual} file(s) of type '${type}', but the ${boundtype} for that type is ${bound}`;\n        super({\n            reason\n        }), this._tag = \"FileCountMismatch\", this.name = \"FileCountMismatchError\";\n    }\n}\n// Verify that the uploaded files doesn't violate the route config,\n// e.g. uploading more videos than allowed, or a file that is larger than allowed.\n// This is double-checked on infra side, but we want to fail early to avoid network latency.\nconst assertFilesMeetConfig = (files, routeConfig)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.gen(function*() {\n        const counts = {};\n        for (const file of files){\n            const type = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.objectKeys)(routeConfig));\n            counts[type] = (counts[type] ?? 0) + 1;\n            const sizeLimit = routeConfig[type]?.maxFileSize;\n            if (!sizeLimit) {\n                return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.InvalidRouteConfigError(type, \"maxFileSize\");\n            }\n            const sizeLimitBytes = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.fileSizeToBytes)(sizeLimit);\n            if (file.size > sizeLimitBytes) {\n                return yield* new FileSizeMismatch(type, sizeLimit, file.size);\n            }\n        }\n        for(const _key in counts){\n            const key = _key;\n            const config = routeConfig[key];\n            if (!config) return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.InvalidRouteConfigError(key);\n            const count = counts[key];\n            const min = config.minFileCount;\n            const max = config.maxFileCount;\n            if (min > max) {\n                return yield* new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"Invalid config during file count - minFileCount > maxFileCount\",\n                    cause: `minFileCount must be less than maxFileCount for key ${key}. got: ${min} > ${max}`\n                });\n            }\n            if (count != null && count < min) {\n                return yield* new FileCountMismatch(key, \"minimum\", min, count);\n            }\n            if (count != null && count > max) {\n                return yield* new FileCountMismatch(key, \"maximum\", max, count);\n            }\n        }\n        return null;\n    });\nconst extractRouterConfig = (router)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.forEach((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.objectKeys)(router), (slug)=>effect_Effect__WEBPACK_IMPORTED_MODULE_2__.map((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.fillInputRouteConfig)(router[slug].routerConfig), (config)=>({\n                slug,\n                config\n            })));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/route-config.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/runtime.js":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/runtime.js ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   makeRuntime: () => (/* binding */ makeRuntime)\n/* harmony export */ });\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/FetchHttpClient.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/Headers.js\");\n/* harmony import */ var effect_FiberRef__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/FiberRef */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/FiberRef.js\");\n/* harmony import */ var effect_Layer__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Layer */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Layer.js\");\n/* harmony import */ var effect_ManagedRuntime__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! effect/ManagedRuntime */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/ManagedRuntime.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js\");\n/* harmony import */ var _logger_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./logger.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js\");\n\n\n\n\n\n\n\nconst makeRuntime = (fetch, config)=>{\n    const fetchHttpClient = effect_Layer__WEBPACK_IMPORTED_MODULE_0__.provideMerge(_effect_platform__WEBPACK_IMPORTED_MODULE_1__.layer, effect_Layer__WEBPACK_IMPORTED_MODULE_0__.succeed(_effect_platform__WEBPACK_IMPORTED_MODULE_1__.Fetch, fetch));\n    const withRedactedHeaders = effect_Layer__WEBPACK_IMPORTED_MODULE_0__.effectDiscard(effect_FiberRef__WEBPACK_IMPORTED_MODULE_2__.update(_effect_platform__WEBPACK_IMPORTED_MODULE_3__.currentRedactedNames, (_)=>_.concat([\n            \"x-uploadthing-api-key\"\n        ])));\n    const layer = effect_Layer__WEBPACK_IMPORTED_MODULE_0__.provide(effect_Layer__WEBPACK_IMPORTED_MODULE_0__.mergeAll(_logger_js__WEBPACK_IMPORTED_MODULE_4__.withLogFormat, _logger_js__WEBPACK_IMPORTED_MODULE_4__.withMinimalLogLevel, fetchHttpClient, withRedactedHeaders), effect_Layer__WEBPACK_IMPORTED_MODULE_0__.setConfigProvider((0,_config_js__WEBPACK_IMPORTED_MODULE_5__.configProvider)(config)));\n    return effect_ManagedRuntime__WEBPACK_IMPORTED_MODULE_6__.make(layer);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/runtime.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/shared-schemas.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/shared-schemas.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ACLSchema: () => (/* binding */ ACLSchema),\n/* harmony export */   ActionType: () => (/* binding */ ActionType),\n/* harmony export */   CallbackResultResponse: () => (/* binding */ CallbackResultResponse),\n/* harmony export */   ContentDispositionSchema: () => (/* binding */ ContentDispositionSchema),\n/* harmony export */   FileUploadData: () => (/* binding */ FileUploadData),\n/* harmony export */   FileUploadDataWithCustomId: () => (/* binding */ FileUploadDataWithCustomId),\n/* harmony export */   MetadataFetchResponse: () => (/* binding */ MetadataFetchResponse),\n/* harmony export */   MetadataFetchStreamPart: () => (/* binding */ MetadataFetchStreamPart),\n/* harmony export */   NewPresignedUrl: () => (/* binding */ NewPresignedUrl),\n/* harmony export */   ParsedToken: () => (/* binding */ ParsedToken),\n/* harmony export */   UploadActionPayload: () => (/* binding */ UploadActionPayload),\n/* harmony export */   UploadThingHook: () => (/* binding */ UploadThingHook),\n/* harmony export */   UploadThingToken: () => (/* binding */ UploadThingToken),\n/* harmony export */   UploadedFileData: () => (/* binding */ UploadedFileData)\n/* harmony export */ });\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n\n\n\nconst ContentDispositionSchema = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Literal(..._uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.ValidContentDispositions);\nconst ACLSchema = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Literal(..._uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.ValidACLs);\n/**\n * Valid options for the `?actionType` query param\n */ const ActionType = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Literal(\"upload\");\n/**\n * Valid options for the `uploadthing-hook` header\n * for requests coming from UT server\n */ const UploadThingHook = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Literal(\"callback\", \"error\");\n/**\n * =============================================================================\n * =========================== Configuration ===================================\n * =============================================================================\n */ const DecodeString = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.transform(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayFromSelf, effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String, {\n    decode: (data)=>new TextDecoder().decode(data),\n    encode: (data)=>new TextEncoder().encode(data)\n});\nconst ParsedToken = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Struct({\n    apiKey: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Redacted(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.startsWith(\"sk_\"))),\n    appId: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    regions: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.NonEmptyArray(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String),\n    ingestHost: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.optionalWith({\n        default: ()=>\"ingest.uploadthing.com\"\n    }))\n});\nconst UploadThingToken = effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Uint8ArrayFromBase64.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.compose(DecodeString), effect_Schema__WEBPACK_IMPORTED_MODULE_0__.compose(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.parseJson(ParsedToken)));\n/**\n * =============================================================================\n * ======================== File Type Hierarchy ===============================\n * =============================================================================\n */ /**\n * Properties from the web File object, this is what the client sends when initiating an upload\n */ class FileUploadData extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"FileUploadData\")({\n    name: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    size: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Number,\n    type: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    lastModified: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Number.pipe(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.optional)\n}) {\n}\n/**\n * `.middleware()` can add a customId to the incoming file data\n */ class FileUploadDataWithCustomId extends FileUploadData.extend(\"FileUploadDataWithCustomId\")({\n    customId: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.NullOr(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String)\n}) {\n}\n/**\n * When files are uploaded, we get back\n * - a key\n * - URLs for the file\n * - the hash (md5-hex) of the uploaded file's contents\n */ class UploadedFileData extends FileUploadDataWithCustomId.extend(\"UploadedFileData\")({\n    key: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    /**\n   * @deprecated\n   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.\n   */ url: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    /**\n   * @deprecated\n   * This field will be removed in uploadthing v9. Use `ufsUrl` instead.\n   */ appUrl: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    ufsUrl: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    fileHash: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String\n}) {\n}\n/**\n * =============================================================================\n * ======================== Server Response Schemas ============================\n * =============================================================================\n */ class NewPresignedUrl extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"NewPresignedUrl\")({\n    url: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    key: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    customId: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.NullOr(effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String),\n    name: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String\n}) {\n}\nclass MetadataFetchStreamPart extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"MetadataFetchStreamPart\")({\n    payload: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    signature: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.String,\n    hook: UploadThingHook\n}) {\n}\nclass MetadataFetchResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"MetadataFetchResponse\")({\n    ok: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Boolean\n}) {\n}\nclass CallbackResultResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"CallbackResultResponse\")({\n    ok: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Boolean\n}) {\n}\n/**\n * =============================================================================\n * ======================== Client Action Payloads ============================\n * =============================================================================\n */ class UploadActionPayload extends effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Class(\"UploadActionPayload\")({\n    files: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Array(FileUploadData),\n    input: effect_Schema__WEBPACK_IMPORTED_MODULE_0__.Unknown\n}) {\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/shared-schemas.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UTFiles: () => (/* binding */ UTFiles)\n/* harmony export */ });\n/**\n * Marker used to append a `customId` to the incoming file data in `.middleware()`\n * @example\n * ```ts\n * .middleware((opts) => {\n *   return {\n *     [UTFiles]: opts.files.map((file) => ({\n *       ...file,\n *       customId: generateId(),\n *     }))\n *   };\n * })\n * ```\n */ const UTFiles = Symbol(\"uploadthing-custom-id-symbol\");\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSxJQUFJO0FBQ0o7QUFDQTs7QUFFbUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9wcmVzZW50YXRpb24vLi9ub2RlX21vZHVsZXMvLnBucG0vdXBsb2FkdGhpbmdANy42LjBfZXhwcmVzc0A0X2M0MzdiOWFhNzQ0NTkxOThlMWYxZDZjNjNhM2RkYTRmL25vZGVfbW9kdWxlcy91cGxvYWR0aGluZy9kaXN0L19pbnRlcm5hbC90eXBlcy5qcz83NWFkIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogTWFya2VyIHVzZWQgdG8gYXBwZW5kIGEgYGN1c3RvbUlkYCB0byB0aGUgaW5jb21pbmcgZmlsZSBkYXRhIGluIGAubWlkZGxld2FyZSgpYFxuICogQGV4YW1wbGVcbiAqIGBgYHRzXG4gKiAubWlkZGxld2FyZSgob3B0cykgPT4ge1xuICogICByZXR1cm4ge1xuICogICAgIFtVVEZpbGVzXTogb3B0cy5maWxlcy5tYXAoKGZpbGUpID0+ICh7XG4gKiAgICAgICAuLi5maWxlLFxuICogICAgICAgY3VzdG9tSWQ6IGdlbmVyYXRlSWQoKSxcbiAqICAgICB9KSlcbiAqICAgfTtcbiAqIH0pXG4gKiBgYGBcbiAqLyBjb25zdCBVVEZpbGVzID0gU3ltYm9sKFwidXBsb2FkdGhpbmctY3VzdG9tLWlkLXN5bWJvbFwiKTtcblxuZXhwb3J0IHsgVVRGaWxlcyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-builder.js":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-builder.js ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createBuilder: () => (/* binding */ createBuilder)\n/* harmony export */ });\n/* harmony import */ var _error_formatter_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./error-formatter.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/error-formatter.js\");\n\n\nfunction internalCreateBuilder(initDef = {}) {\n    const _def = {\n        $types: {},\n        // Default router config\n        routerConfig: {\n            image: {\n                maxFileSize: \"4MB\"\n            }\n        },\n        routeOptions: {\n            awaitServerData: true\n        },\n        inputParser: {\n            parseAsync: ()=>Promise.resolve(undefined),\n            _input: undefined,\n            _output: undefined\n        },\n        middleware: ()=>({}),\n        onUploadError: ()=>{\n        // noop\n        },\n        onUploadComplete: ()=>undefined,\n        errorFormatter: initDef.errorFormatter ?? _error_formatter_js__WEBPACK_IMPORTED_MODULE_0__.defaultErrorFormatter,\n        // Overload with properties passed in\n        ...initDef\n    };\n    return {\n        input (userParser) {\n            return internalCreateBuilder({\n                ..._def,\n                inputParser: userParser\n            });\n        },\n        middleware (userMiddleware) {\n            return internalCreateBuilder({\n                ..._def,\n                middleware: userMiddleware\n            });\n        },\n        onUploadComplete (userUploadComplete) {\n            return {\n                ..._def,\n                onUploadComplete: userUploadComplete\n            };\n        },\n        onUploadError (userOnUploadError) {\n            return internalCreateBuilder({\n                ..._def,\n                onUploadError: userOnUploadError\n            });\n        }\n    };\n}\n/**\n * Create a builder for your backend adapter.\n * Refer to the existing adapters for examples on how to use this function.\n * @public\n *\n * @param opts - Options for the builder\n * @returns A file route builder for making UploadThing file routes\n */ function createBuilder(opts) {\n    return (input, config)=>{\n        return internalCreateBuilder({\n            routerConfig: input,\n            routeOptions: config ?? {},\n            ...opts\n        });\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL2Rpc3QvX2ludGVybmFsL3VwbG9hZC1idWlsZGVyLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTZEOztBQUU3RCwyQ0FBMkM7QUFDM0M7QUFDQSxrQkFBa0I7QUFDbEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULDJCQUEyQjtBQUMzQjtBQUNBO0FBQ0EsU0FBUztBQUNUO0FBQ0Esa0RBQWtELHNFQUFxQjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYixTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0NBQXNDO0FBQ3RDO0FBQ0EsU0FBUztBQUNUO0FBQ0E7O0FBRXlCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL3VwbG9hZHRoaW5nQDcuNi4wX2V4cHJlc3NANF9jNDM3YjlhYTc0NDU5MTk4ZTFmMWQ2YzYzYTNkZGE0Zi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvZGlzdC9faW50ZXJuYWwvdXBsb2FkLWJ1aWxkZXIuanM/MWVmZCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBkZWZhdWx0RXJyb3JGb3JtYXR0ZXIgfSBmcm9tICcuL2Vycm9yLWZvcm1hdHRlci5qcyc7XG5cbmZ1bmN0aW9uIGludGVybmFsQ3JlYXRlQnVpbGRlcihpbml0RGVmID0ge30pIHtcbiAgICBjb25zdCBfZGVmID0ge1xuICAgICAgICAkdHlwZXM6IHt9LFxuICAgICAgICAvLyBEZWZhdWx0IHJvdXRlciBjb25maWdcbiAgICAgICAgcm91dGVyQ29uZmlnOiB7XG4gICAgICAgICAgICBpbWFnZToge1xuICAgICAgICAgICAgICAgIG1heEZpbGVTaXplOiBcIjRNQlwiXG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHJvdXRlT3B0aW9uczoge1xuICAgICAgICAgICAgYXdhaXRTZXJ2ZXJEYXRhOiB0cnVlXG4gICAgICAgIH0sXG4gICAgICAgIGlucHV0UGFyc2VyOiB7XG4gICAgICAgICAgICBwYXJzZUFzeW5jOiAoKT0+UHJvbWlzZS5yZXNvbHZlKHVuZGVmaW5lZCksXG4gICAgICAgICAgICBfaW5wdXQ6IHVuZGVmaW5lZCxcbiAgICAgICAgICAgIF9vdXRwdXQ6IHVuZGVmaW5lZFxuICAgICAgICB9LFxuICAgICAgICBtaWRkbGV3YXJlOiAoKT0+KHt9KSxcbiAgICAgICAgb25VcGxvYWRFcnJvcjogKCk9PntcbiAgICAgICAgLy8gbm9vcFxuICAgICAgICB9LFxuICAgICAgICBvblVwbG9hZENvbXBsZXRlOiAoKT0+dW5kZWZpbmVkLFxuICAgICAgICBlcnJvckZvcm1hdHRlcjogaW5pdERlZi5lcnJvckZvcm1hdHRlciA/PyBkZWZhdWx0RXJyb3JGb3JtYXR0ZXIsXG4gICAgICAgIC8vIE92ZXJsb2FkIHdpdGggcHJvcGVydGllcyBwYXNzZWQgaW5cbiAgICAgICAgLi4uaW5pdERlZlxuICAgIH07XG4gICAgcmV0dXJuIHtcbiAgICAgICAgaW5wdXQgKHVzZXJQYXJzZXIpIHtcbiAgICAgICAgICAgIHJldHVybiBpbnRlcm5hbENyZWF0ZUJ1aWxkZXIoe1xuICAgICAgICAgICAgICAgIC4uLl9kZWYsXG4gICAgICAgICAgICAgICAgaW5wdXRQYXJzZXI6IHVzZXJQYXJzZXJcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9LFxuICAgICAgICBtaWRkbGV3YXJlICh1c2VyTWlkZGxld2FyZSkge1xuICAgICAgICAgICAgcmV0dXJuIGludGVybmFsQ3JlYXRlQnVpbGRlcih7XG4gICAgICAgICAgICAgICAgLi4uX2RlZixcbiAgICAgICAgICAgICAgICBtaWRkbGV3YXJlOiB1c2VyTWlkZGxld2FyZVxuICAgICAgICAgICAgfSk7XG4gICAgICAgIH0sXG4gICAgICAgIG9uVXBsb2FkQ29tcGxldGUgKHVzZXJVcGxvYWRDb21wbGV0ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgICAgICAuLi5fZGVmLFxuICAgICAgICAgICAgICAgIG9uVXBsb2FkQ29tcGxldGU6IHVzZXJVcGxvYWRDb21wbGV0ZVxuICAgICAgICAgICAgfTtcbiAgICAgICAgfSxcbiAgICAgICAgb25VcGxvYWRFcnJvciAodXNlck9uVXBsb2FkRXJyb3IpIHtcbiAgICAgICAgICAgIHJldHVybiBpbnRlcm5hbENyZWF0ZUJ1aWxkZXIoe1xuICAgICAgICAgICAgICAgIC4uLl9kZWYsXG4gICAgICAgICAgICAgICAgb25VcGxvYWRFcnJvcjogdXNlck9uVXBsb2FkRXJyb3JcbiAgICAgICAgICAgIH0pO1xuICAgICAgICB9XG4gICAgfTtcbn1cbi8qKlxuICogQ3JlYXRlIGEgYnVpbGRlciBmb3IgeW91ciBiYWNrZW5kIGFkYXB0ZXIuXG4gKiBSZWZlciB0byB0aGUgZXhpc3RpbmcgYWRhcHRlcnMgZm9yIGV4YW1wbGVzIG9uIGhvdyB0byB1c2UgdGhpcyBmdW5jdGlvbi5cbiAqIEBwdWJsaWNcbiAqXG4gKiBAcGFyYW0gb3B0cyAtIE9wdGlvbnMgZm9yIHRoZSBidWlsZGVyXG4gKiBAcmV0dXJucyBBIGZpbGUgcm91dGUgYnVpbGRlciBmb3IgbWFraW5nIFVwbG9hZFRoaW5nIGZpbGUgcm91dGVzXG4gKi8gZnVuY3Rpb24gY3JlYXRlQnVpbGRlcihvcHRzKSB7XG4gICAgcmV0dXJuIChpbnB1dCwgY29uZmlnKT0+e1xuICAgICAgICByZXR1cm4gaW50ZXJuYWxDcmVhdGVCdWlsZGVyKHtcbiAgICAgICAgICAgIHJvdXRlckNvbmZpZzogaW5wdXQsXG4gICAgICAgICAgICByb3V0ZU9wdGlvbnM6IGNvbmZpZyA/PyB7fSxcbiAgICAgICAgICAgIC4uLm9wdHNcbiAgICAgICAgfSk7XG4gICAgfTtcbn1cblxuZXhwb3J0IHsgY3JlYXRlQnVpbGRlciB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-builder.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-server.js":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-server.js ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadWithoutProgress: () => (/* binding */ uploadWithoutProgress)\n/* harmony export */ });\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClient.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClientRequest.js\");\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Function */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _deprecations_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./deprecations.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/deprecations.js\");\n/* harmony import */ var _logger_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./logger.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js\");\n\n\n\n\n\n\n\nvar version = \"7.6.0\";\n\nconst uploadWithoutProgress = (file, presigned)=>effect_Effect__WEBPACK_IMPORTED_MODULE_0__.gen(function*() {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        const httpClient = (yield* _effect_platform__WEBPACK_IMPORTED_MODULE_1__.HttpClient).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_1__.filterStatusOk);\n        const json = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_2__.put(presigned.url).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_2__.bodyFormData(formData), _effect_platform__WEBPACK_IMPORTED_MODULE_2__.setHeader(\"Range\", \"bytes=0-\"), _effect_platform__WEBPACK_IMPORTED_MODULE_2__.setHeader(\"x-uploadthing-version\", version), httpClient.execute, effect_Effect__WEBPACK_IMPORTED_MODULE_0__.tapError((0,_logger_js__WEBPACK_IMPORTED_MODULE_3__.logHttpClientError)(\"Failed to upload file\")), effect_Effect__WEBPACK_IMPORTED_MODULE_0__.mapError((e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_4__.UploadThingError({\n                code: \"UPLOAD_FAILED\",\n                message: \"Failed to upload file\",\n                cause: e\n            })), effect_Effect__WEBPACK_IMPORTED_MODULE_0__.andThen((_)=>_.json), effect_Effect__WEBPACK_IMPORTED_MODULE_0__.andThen(effect_Function__WEBPACK_IMPORTED_MODULE_5__.unsafeCoerce), effect_Effect__WEBPACK_IMPORTED_MODULE_0__.scoped);\n        yield* effect_Effect__WEBPACK_IMPORTED_MODULE_0__.logDebug(`File ${file.name} uploaded successfully`).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_0__.annotateLogs(\"json\", json));\n        return {\n            ...json,\n            get url () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_6__.logDeprecationWarning)(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return json.url;\n            },\n            get appUrl () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_6__.logDeprecationWarning)(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return json.appUrl;\n            }\n        };\n    });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-server.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/next/index.js":
/*!********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/next/index.js ***!
  \********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UTFiles: () => (/* reexport safe */ _dist_internal_types_js__WEBPACK_IMPORTED_MODULE_0__.UTFiles),\n/* harmony export */   createRouteHandler: () => (/* binding */ createRouteHandler),\n/* harmony export */   createUploadthing: () => (/* binding */ createUploadthing)\n/* harmony export */ });\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var _dist_internal_handler_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dist/_internal/handler.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/handler.js\");\n/* harmony import */ var _dist_internal_upload_builder_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../dist/_internal/upload-builder.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-builder.js\");\n/* harmony import */ var _dist_internal_types_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../dist/_internal/types.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js\");\n\n\n\n\n\nconst createUploadthing = (opts)=>(0,_dist_internal_upload_builder_js__WEBPACK_IMPORTED_MODULE_1__.createBuilder)(opts);\nconst createRouteHandler = (opts)=>{\n    const handler = (0,_dist_internal_handler_js__WEBPACK_IMPORTED_MODULE_2__.makeAdapterHandler)((req)=>effect_Effect__WEBPACK_IMPORTED_MODULE_3__.succeed({\n            req\n        }), (req)=>effect_Effect__WEBPACK_IMPORTED_MODULE_3__.succeed(req), opts, \"nextjs-app\");\n    return {\n        POST: handler,\n        GET: handler\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFjdGlvbi1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL25leHQvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUF3QztBQUMwQjtBQUNFO0FBQ2Y7O0FBRXJELGtDQUFrQywrRUFBYTtBQUMvQztBQUNBLG9CQUFvQiw2RUFBa0IsUUFBUSxrREFBYztBQUM1RDtBQUNBLFNBQVMsVUFBVSxrREFBYztBQUNqQztBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVpRCIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS91cGxvYWR0aGluZ0A3LjYuMF9leHByZXNzQDRfYzQzN2I5YWE3NDQ1OTE5OGUxZjFkNmM2M2EzZGRhNGYvbm9kZV9tb2R1bGVzL3VwbG9hZHRoaW5nL25leHQvaW5kZXguanM/NmMxNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgKiBhcyBFZmZlY3QgZnJvbSAnZWZmZWN0L0VmZmVjdCc7XG5pbXBvcnQgeyBtYWtlQWRhcHRlckhhbmRsZXIgfSBmcm9tICcuLi9kaXN0L19pbnRlcm5hbC9oYW5kbGVyLmpzJztcbmltcG9ydCB7IGNyZWF0ZUJ1aWxkZXIgfSBmcm9tICcuLi9kaXN0L19pbnRlcm5hbC91cGxvYWQtYnVpbGRlci5qcyc7XG5leHBvcnQgeyBVVEZpbGVzIH0gZnJvbSAnLi4vZGlzdC9faW50ZXJuYWwvdHlwZXMuanMnO1xuXG5jb25zdCBjcmVhdGVVcGxvYWR0aGluZyA9IChvcHRzKT0+Y3JlYXRlQnVpbGRlcihvcHRzKTtcbmNvbnN0IGNyZWF0ZVJvdXRlSGFuZGxlciA9IChvcHRzKT0+e1xuICAgIGNvbnN0IGhhbmRsZXIgPSBtYWtlQWRhcHRlckhhbmRsZXIoKHJlcSk9PkVmZmVjdC5zdWNjZWVkKHtcbiAgICAgICAgICAgIHJlcVxuICAgICAgICB9KSwgKHJlcSk9PkVmZmVjdC5zdWNjZWVkKHJlcSksIG9wdHMsIFwibmV4dGpzLWFwcFwiKTtcbiAgICByZXR1cm4ge1xuICAgICAgICBQT1NUOiBoYW5kbGVyLFxuICAgICAgICBHRVQ6IGhhbmRsZXJcbiAgICB9O1xufTtcblxuZXhwb3J0IHsgY3JlYXRlUm91dGVIYW5kbGVyLCBjcmVhdGVVcGxvYWR0aGluZyB9O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/next/index.js\n");

/***/ }),

/***/ "(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/server/index.js":
/*!**********************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/server/index.js ***!
  \**********************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UTApi: () => (/* binding */ UTApi),\n/* harmony export */   UTFile: () => (/* binding */ UTFile),\n/* harmony export */   UTFiles: () => (/* reexport safe */ _dist_internal_types_js__WEBPACK_IMPORTED_MODULE_3__.UTFiles),\n/* harmony export */   UploadThingError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError),\n/* harmony export */   createBuilder: () => (/* reexport safe */ _dist_internal_upload_builder_js__WEBPACK_IMPORTED_MODULE_2__.createBuilder),\n/* harmony export */   createRouteHandler: () => (/* binding */ createRouteHandler),\n/* harmony export */   createUploadthing: () => (/* binding */ createUploadthing),\n/* harmony export */   extractRouterConfig: () => (/* binding */ extractRouterConfig),\n/* harmony export */   makeAdapterHandler: () => (/* reexport safe */ _dist_internal_handler_js__WEBPACK_IMPORTED_MODULE_1__.makeAdapterHandler)\n/* harmony export */ });\n/* harmony import */ var effect_Effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Effect */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Effect.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _dist_internal_handler_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../dist/_internal/handler.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/handler.js\");\n/* harmony import */ var _dist_internal_route_config_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ../dist/_internal/route-config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/route-config.js\");\n/* harmony import */ var _dist_internal_upload_builder_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dist/_internal/upload-builder.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-builder.js\");\n/* harmony import */ var _dist_internal_types_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dist/_internal/types.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/types.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClient.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpClientRequest.js\");\n/* harmony import */ var _effect_platform__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @effect/platform */ \"(action-browser)/./node_modules/.pnpm/@effect+platform@0.72.0_effect@3.12.0/node_modules/@effect/platform/dist/esm/HttpIncomingMessage.js\");\n/* harmony import */ var effect_Array__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! effect/Array */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Array.js\");\n/* harmony import */ var effect_Cause__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! effect/Cause */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Cause.js\");\n/* harmony import */ var effect_Redacted__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! effect/Redacted */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Redacted.js\");\n/* harmony import */ var effect_Schema__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! effect/Schema */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Schema.js\");\n/* harmony import */ var _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../dist/_internal/config.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/config.js\");\n/* harmony import */ var _dist_internal_logger_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../dist/_internal/logger.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/logger.js\");\n/* harmony import */ var _dist_internal_runtime_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../dist/_internal/runtime.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/runtime.js\");\n/* harmony import */ var _uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @uploadthing/mime-types */ \"(action-browser)/./node_modules/.pnpm/@uploadthing+mime-types@0.3.4/node_modules/@uploadthing/mime-types/dist/index.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! effect/Predicate */ \"(action-browser)/./node_modules/.pnpm/effect@3.12.0/node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var _dist_internal_upload_server_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../dist/_internal/upload-server.js */ \"(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/dist/_internal/upload-server.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Extension of the Blob class that simplifies setting the `name` and `customId` properties,\n * similar to the built-in File class from Node > 20.\n */ class UTFile extends Blob {\n    constructor(parts, name, options){\n        const optionsWithDefaults = {\n            ...options,\n            type: options?.type ?? ((0,_uploadthing_mime_types__WEBPACK_IMPORTED_MODULE_4__.lookup)(name) || \"application/octet-stream\"),\n            lastModified: options?.lastModified ?? Date.now()\n        };\n        super(parts, optionsWithDefaults);\n        this.name = name;\n        this.customId = optionsWithDefaults.customId;\n        this.lastModified = optionsWithDefaults.lastModified;\n    }\n}\n\nfunction guardServerOnly() {\n    if (typeof window !== \"undefined\") {\n        throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n            code: \"INTERNAL_SERVER_ERROR\",\n            message: \"The `utapi` can only be used on the server.\"\n        });\n    }\n}\nconst downloadFile = (_url)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(function*() {\n        let url = effect_Predicate__WEBPACK_IMPORTED_MODULE_6__.isRecord(_url) ? _url.url : _url;\n        if (typeof url === \"string\") {\n            // since dataurls will result in name being too long, tell the user\n            // to use uploadFiles instead.\n            if (url.startsWith(\"data:\")) {\n                return yield* effect_Effect__WEBPACK_IMPORTED_MODULE_5__.fail({\n                    code: \"BAD_REQUEST\",\n                    message: \"Please use uploadFiles() for data URLs. uploadFilesFromUrl() is intended for use with remote URLs only.\",\n                    data: undefined\n                });\n            }\n        }\n        url = new URL(url);\n        const { name = url.pathname.split(\"/\").pop() ?? \"unknown-filename\", customId = undefined } = effect_Predicate__WEBPACK_IMPORTED_MODULE_6__.isRecord(_url) ? _url : {};\n        const httpClient = (yield* _effect_platform__WEBPACK_IMPORTED_MODULE_7__.HttpClient).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_7__.filterStatusOk);\n        const arrayBuffer = yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.get(url).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_8__.modify({\n            headers: {}\n        }), httpClient.execute, effect_Effect__WEBPACK_IMPORTED_MODULE_5__.flatMap((_)=>_.arrayBuffer), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.mapError((cause)=>{\n            return {\n                code: \"BAD_REQUEST\",\n                message: `Failed to download requested file: ${cause.message}`,\n                data: cause.toJSON()\n            };\n        }), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.scoped);\n        return new UTFile([\n            arrayBuffer\n        ], name, {\n            customId,\n            lastModified: Date.now()\n        });\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"downloadFile\"));\nconst generatePresignedUrl = (file, cd, acl)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(function*() {\n        const { apiKey, appId } = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.UTToken;\n        const baseUrl = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.IngestUrl;\n        const key = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateKey)(file, appId);\n        const url = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateSignedURL)(`${baseUrl}/${key}`, apiKey, {\n            // ttlInSeconds: routeOptions.presignedURLTTL,\n            data: {\n                \"x-ut-identifier\": appId,\n                \"x-ut-file-name\": file.name,\n                \"x-ut-file-size\": file.size,\n                \"x-ut-file-type\": file.type,\n                \"x-ut-custom-id\": file.customId,\n                \"x-ut-content-disposition\": cd,\n                \"x-ut-acl\": acl\n            }\n        });\n        return {\n            url,\n            key\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"generatePresignedUrl\"));\nconst uploadFile = (file, opts)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(function*() {\n        const presigned = yield* generatePresignedUrl(file, opts.contentDisposition ?? \"inline\", opts.acl).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"UploadThingError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.fail(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError.toObject(e))), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"ConfigError\", ()=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.fail({\n                code: \"INVALID_SERVER_CONFIG\",\n                message: \"Failed to generate presigned URL\"\n            })));\n        const response = yield* (0,_dist_internal_upload_server_js__WEBPACK_IMPORTED_MODULE_10__.uploadWithoutProgress)(file, presigned).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"UploadThingError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.fail(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError.toObject(e))), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"ResponseError\", (e)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.fail({\n                code: \"UPLOAD_FAILED\",\n                message: \"Failed to upload file\",\n                data: e.toJSON()\n            })));\n        return {\n            key: presigned.key,\n            url: response.url,\n            appUrl: response.appUrl,\n            ufsUrl: response.ufsUrl,\n            lastModified: file.lastModified ?? Date.now(),\n            name: file.name,\n            size: file.size,\n            type: file.type,\n            customId: file.customId ?? null,\n            fileHash: response.fileHash\n        };\n    }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"uploadFile\"));\n\nclass UTApi {\n    constructor(opts){\n        this.opts = opts;\n        this.requestUploadThing = (pathname, body, responseSchema)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(this, function*() {\n                const { apiKey } = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.UTToken;\n                const baseUrl = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.ApiUrl;\n                const httpClient = (yield* _effect_platform__WEBPACK_IMPORTED_MODULE_7__.HttpClient).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_7__.filterStatusOk);\n                return yield* _effect_platform__WEBPACK_IMPORTED_MODULE_8__.post(pathname).pipe(_effect_platform__WEBPACK_IMPORTED_MODULE_8__.prependUrl(baseUrl), _effect_platform__WEBPACK_IMPORTED_MODULE_8__.bodyUnsafeJson(body), _effect_platform__WEBPACK_IMPORTED_MODULE_8__.setHeaders({\n                    \"x-uploadthing-version\": _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.UPLOADTHING_VERSION,\n                    \"x-uploadthing-be-adapter\": \"server-sdk\",\n                    \"x-uploadthing-api-key\": effect_Redacted__WEBPACK_IMPORTED_MODULE_11__.value(apiKey)\n                }), httpClient.execute, effect_Effect__WEBPACK_IMPORTED_MODULE_5__.tapBoth({\n                    onSuccess: (0,_dist_internal_logger_js__WEBPACK_IMPORTED_MODULE_12__.logHttpClientResponse)(\"UploadThing API Response\"),\n                    onFailure: (0,_dist_internal_logger_js__WEBPACK_IMPORTED_MODULE_12__.logHttpClientError)(\"Failed to request UploadThing API\")\n                }), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.flatMap(_effect_platform__WEBPACK_IMPORTED_MODULE_13__.schemaBodyJson(responseSchema)), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.scoped);\n            }).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"ConfigError\", (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INVALID_SERVER_CONFIG\",\n                    message: \"There was an error with the server configuration. More info can be found on this error's `cause` property\",\n                    cause: e\n                })), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"utapi.#requestUploadThing\"));\n        this.executeAsync = async (program, signal)=>{\n            const exit = await program.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"utapi.#executeAsync\"), (e)=>this.runtime.runPromiseExit(e, signal ? {\n                    signal\n                } : undefined));\n            if (exit._tag === \"Failure\") {\n                throw effect_Cause__WEBPACK_IMPORTED_MODULE_14__.squash(exit.cause);\n            }\n            return exit.value;\n        };\n        this./**\n   * Request to delete files from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * await deleteFiles(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   *\n   * @example\n   * await deleteFiles([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   *\n   * @example\n   * await deleteFiles(\"myCustomIdentifier\", { keyType: \"customId\" })\n   */ deleteFiles = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class DeleteFileResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"DeleteFileResponse\")({\n                success: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Boolean,\n                deletedCount: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/deleteFiles\", keyType === \"fileKey\" ? {\n                fileKeys: effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(keys)\n            } : {\n                customIds: effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(keys)\n            }, DeleteFileResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"deleteFiles\")));\n        };\n        this./**\n   * Request file URLs from UploadThing storage.\n   * @param {string | string[]} fileKeys\n   *\n   * @example\n   * const data = await getFileUrls(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\");\n   * console.log(data); // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\"}]\n   *\n   * @example\n   * const data = await getFileUrls([\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"])\n   * console.log(data) // [{key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", url: \"https://uploadthing.com/f/2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\" },{key: \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\", url: \"https://uploadthing.com/f/1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\"}]\n   *\n   * @deprecated - See https://docs.uploadthing.com/working-with-files#accessing-files for info how to access files\n   */ getFileUrls = async (keys, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            class GetFileUrlResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"GetFileUrlResponse\")({\n                data: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Array(effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Struct({\n                    key: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String,\n                    url: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/getFileUrl\", keyType === \"fileKey\" ? {\n                fileKeys: effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(keys)\n            } : {\n                customIds: effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(keys)\n            }, GetFileUrlResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"getFileUrls\")));\n        };\n        this./**\n   * Request file list from UploadThing storage.\n   * @param {object} opts\n   * @param {number} opts.limit The maximum number of files to return\n   * @param {number} opts.offset The number of files to skip\n   *\n   * @example\n   * const data = await listFiles({ limit: 1 });\n   * console.log(data); // { key: \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", id: \"2e0fdb64-9957-4262-8e45-f372ba903ac8\" }\n   */ listFiles = async (opts)=>{\n            guardServerOnly();\n            class ListFileResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"ListFileResponse\")({\n                hasMore: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Boolean,\n                files: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Array(effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Struct({\n                    id: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String,\n                    customId: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.NullOr(effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String),\n                    key: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String,\n                    name: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String,\n                    size: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number,\n                    status: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Literal(\"Deletion Pending\", \"Failed\", \"Uploaded\", \"Uploading\"),\n                    uploadedAt: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number\n                }))\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/listFiles\", {\n                ...opts\n            }, ListFileResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"listFiles\")));\n        };\n        this.renameFiles = async (updates)=>{\n            guardServerOnly();\n            class RenameFileResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"RenameFileResponse\")({\n                success: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Boolean\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/renameFiles\", {\n                updates: effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(updates)\n            }, RenameFileResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"renameFiles\")));\n        };\n        this.getUsageInfo = async ()=>{\n            guardServerOnly();\n            class GetUsageInfoResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"GetUsageInfoResponse\")({\n                totalBytes: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number,\n                appTotalBytes: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number,\n                filesUploaded: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number,\n                limitBytes: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Number\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/getUsageInfo\", {}, GetUsageInfoResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"getUsageInfo\")));\n        };\n        this./**\n   * Generate a presigned url for a private file\n   * Unlike {@link getSignedURL}, this method does not make a fetch request to the UploadThing API\n   * and is the recommended way to generate a presigned url for a private file.\n   **/ generateSignedURL = async (key, opts)=>{\n            guardServerOnly();\n            const expiresIn = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseTimeToSeconds)(opts?.expiresIn ?? \"5 minutes\");\n            if (opts?.expiresIn && isNaN(expiresIn)) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds.\"\n                });\n            }\n            if (expiresIn > 86400 * 7) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be less than 7 days (604800 seconds).\"\n                });\n            }\n            const program = effect_Effect__WEBPACK_IMPORTED_MODULE_5__.gen(function*() {\n                const { apiKey, appId } = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.UTToken;\n                const ufsHost = yield* _dist_internal_config_js__WEBPACK_IMPORTED_MODULE_9__.UfsHost;\n                const proto = ufsHost.includes(\"local\") ? \"http\" : \"https\";\n                const ufsUrl = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateSignedURL)(`${proto}://${appId}.${ufsHost}/f/${key}`, apiKey, {\n                    ttlInSeconds: expiresIn\n                });\n                return {\n                    ufsUrl\n                };\n            });\n            return await this.executeAsync(program.pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.catchTag(\"ConfigError\", (e)=>new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"INVALID_SERVER_CONFIG\",\n                    message: \"There was an error with the server configuration. More info can be found on this error's `cause` property\",\n                    cause: e\n                })), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"generateSignedURL\")));\n        };\n        this./**\n   * Request a presigned url for a private file(s)\n   * @remarks This method is no longer recommended as it makes a fetch\n   * request to the UploadThing API which incurs redundant latency. It\n   * will be deprecated in UploadThing v8 and removed in UploadThing v9.\n   *\n   * @see {@link generateSignedURL} for a more efficient way to generate a presigned url\n   **/ getSignedURL = async (key, opts)=>{\n            guardServerOnly();\n            const expiresIn = opts?.expiresIn ? (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.parseTimeToSeconds)(opts.expiresIn) : undefined;\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            if (opts?.expiresIn && isNaN(expiresIn)) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be a valid time string, for example '1d', '2 days', or a number of seconds.\"\n                });\n            }\n            if (expiresIn && expiresIn > 86400 * 7) {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadThingError({\n                    code: \"BAD_REQUEST\",\n                    message: \"expiresIn must be less than 7 days (604800 seconds).\"\n                });\n            }\n            class GetSignedUrlResponse extends effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Class(\"GetSignedUrlResponse\")({\n                url: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String,\n                ufsUrl: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.String\n            }) {\n            }\n            return await this.executeAsync(this.requestUploadThing(\"/v6/requestFileAccess\", keyType === \"fileKey\" ? {\n                fileKey: key,\n                expiresIn\n            } : {\n                customId: key,\n                expiresIn\n            }, GetSignedUrlResponse).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"getSignedURL\")));\n        };\n        this./**\n   * Update the ACL of a file or set of files.\n   *\n   * @example\n   * // Make a single file public\n   * await utapi.updateACL(\"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\", \"public-read\");\n   *\n   * // Make multiple files private\n   * await utapi.updateACL(\n   *   [\n   *     \"2e0fdb64-9957-4262-8e45-f372ba903ac8_image.jpg\",\n   *     \"1649353b-04ea-48a2-9db7-31de7f562c8d_image2.jpg\",\n   *   ],\n   *   \"private\",\n   * );\n   */ updateACL = async (keys, acl, opts)=>{\n            guardServerOnly();\n            const { keyType = this.defaultKeyType } = opts ?? {};\n            const updates = effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(keys).map((key)=>{\n                return keyType === \"fileKey\" ? {\n                    fileKey: key,\n                    acl\n                } : {\n                    customId: key,\n                    acl\n                };\n            });\n            const responseSchema = effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Struct({\n                success: effect_Schema__WEBPACK_IMPORTED_MODULE_15__.Boolean\n            });\n            return await this.executeAsync(this.requestUploadThing(\"/v6/updateACL\", {\n                updates\n            }, responseSchema).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"updateACL\")));\n        };\n        // Assert some stuff\n        guardServerOnly();\n        this.fetch = opts?.fetch ?? globalThis.fetch;\n        this.defaultKeyType = opts?.defaultKeyType ?? \"fileKey\";\n        this.runtime = (0,_dist_internal_runtime_js__WEBPACK_IMPORTED_MODULE_17__.makeRuntime)(this.fetch, this.opts);\n    }\n    uploadFiles(files, opts) {\n        guardServerOnly();\n        const program = effect_Effect__WEBPACK_IMPORTED_MODULE_5__.forEach(effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(files), (file)=>uploadFile(file, opts ?? {}).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.match({\n                onSuccess: (data)=>({\n                        data,\n                        error: null\n                    }),\n                onFailure: (error)=>({\n                        data: null,\n                        error\n                    })\n            }))).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.map((ups)=>Array.isArray(files) ? ups : ups[0]), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.tap((res)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.logDebug(\"Finished uploading\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.annotateLogs(\"uploadResult\", res))), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"uploadFiles\"));\n        return this.executeAsync(program, opts?.signal);\n    }\n    uploadFilesFromUrl(urls, opts) {\n        guardServerOnly();\n        const program = effect_Effect__WEBPACK_IMPORTED_MODULE_5__.forEach(effect_Array__WEBPACK_IMPORTED_MODULE_16__.ensure(urls), (url)=>downloadFile(url).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.flatMap((file)=>uploadFile(file, opts ?? {})), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.match({\n                onSuccess: (data)=>({\n                        data,\n                        error: null\n                    }),\n                onFailure: (error)=>({\n                        data: null,\n                        error\n                    })\n            }))).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.map((ups)=>Array.isArray(urls) ? ups : ups[0]), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.tap((res)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.logDebug(\"Finished uploading\").pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.annotateLogs(\"uploadResult\", res))), effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"uploadFiles\")).pipe(effect_Effect__WEBPACK_IMPORTED_MODULE_5__.withLogSpan(\"uploadFilesFromUrl\"));\n        return this.executeAsync(program, opts?.signal);\n    }\n}\n\nconst createUploadthing = (opts)=>(0,_dist_internal_upload_builder_js__WEBPACK_IMPORTED_MODULE_2__.createBuilder)(opts);\nconst createRouteHandler = (opts)=>{\n    return (0,_dist_internal_handler_js__WEBPACK_IMPORTED_MODULE_1__.makeAdapterHandler)((ev)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.succeed({\n            req: \"request\" in ev ? ev.request : ev\n        }), (ev)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.succeed(\"request\" in ev ? ev.request : ev), opts, \"server\");\n};\nconst extractRouterConfig = (router)=>effect_Effect__WEBPACK_IMPORTED_MODULE_5__.runSync((0,_dist_internal_route_config_js__WEBPACK_IMPORTED_MODULE_18__.extractRouterConfig)(router));\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(action-browser)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/server/index.js\n");

/***/ })

};
;