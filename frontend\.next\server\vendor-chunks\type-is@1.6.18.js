"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/type-is@1.6.18";
exports.ids = ["vendor-chunks/type-is@1.6.18"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/type-is@1.6.18/node_modules/type-is/index.js":
/*!*************************************************************************!*\
  !*** ./node_modules/.pnpm/type-is@1.6.18/node_modules/type-is/index.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * type-is\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2014-2015 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar typer = __webpack_require__(/*! media-typer */ \"(rsc)/./node_modules/.pnpm/media-typer@0.3.0/node_modules/media-typer/index.js\")\nvar mime = __webpack_require__(/*! mime-types */ \"(rsc)/./node_modules/.pnpm/mime-types@2.1.35/node_modules/mime-types/index.js\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = typeofrequest\nmodule.exports.is = typeis\nmodule.exports.hasBody = hasbody\nmodule.exports.normalize = normalize\nmodule.exports.match = mimeMatch\n\n/**\n * Compare a `value` content-type with `types`.\n * Each `type` can be an extension like `html`,\n * a special shortcut like `multipart` or `urlencoded`,\n * or a mime type.\n *\n * If no types match, `false` is returned.\n * Otherwise, the first `type` that matches is returned.\n *\n * @param {String} value\n * @param {Array} types\n * @public\n */\n\nfunction typeis (value, types_) {\n  var i\n  var types = types_\n\n  // remove parameters and normalize\n  var val = tryNormalizeType(value)\n\n  // no type or invalid\n  if (!val) {\n    return false\n  }\n\n  // support flattened arguments\n  if (types && !Array.isArray(types)) {\n    types = new Array(arguments.length - 1)\n    for (i = 0; i < types.length; i++) {\n      types[i] = arguments[i + 1]\n    }\n  }\n\n  // no types, return the content type\n  if (!types || !types.length) {\n    return val\n  }\n\n  var type\n  for (i = 0; i < types.length; i++) {\n    if (mimeMatch(normalize(type = types[i]), val)) {\n      return type[0] === '+' || type.indexOf('*') !== -1\n        ? val\n        : type\n    }\n  }\n\n  // no matches\n  return false\n}\n\n/**\n * Check if a request has a request body.\n * A request with a body __must__ either have `transfer-encoding`\n * or `content-length` headers set.\n * http://www.w3.org/Protocols/rfc2616/rfc2616-sec4.html#sec4.3\n *\n * @param {Object} request\n * @return {Boolean}\n * @public\n */\n\nfunction hasbody (req) {\n  return req.headers['transfer-encoding'] !== undefined ||\n    !isNaN(req.headers['content-length'])\n}\n\n/**\n * Check if the incoming request contains the \"Content-Type\"\n * header field, and it contains any of the give mime `type`s.\n * If there is no request body, `null` is returned.\n * If there is no content type, `false` is returned.\n * Otherwise, it returns the first `type` that matches.\n *\n * Examples:\n *\n *     // With Content-Type: text/html; charset=utf-8\n *     this.is('html'); // => 'html'\n *     this.is('text/html'); // => 'text/html'\n *     this.is('text/*', 'application/json'); // => 'text/html'\n *\n *     // When Content-Type is application/json\n *     this.is('json', 'urlencoded'); // => 'json'\n *     this.is('application/json'); // => 'application/json'\n *     this.is('html', 'application/*'); // => 'application/json'\n *\n *     this.is('html'); // => false\n *\n * @param {String|Array} types...\n * @return {String|false|null}\n * @public\n */\n\nfunction typeofrequest (req, types_) {\n  var types = types_\n\n  // no body\n  if (!hasbody(req)) {\n    return null\n  }\n\n  // support flattened arguments\n  if (arguments.length > 2) {\n    types = new Array(arguments.length - 1)\n    for (var i = 0; i < types.length; i++) {\n      types[i] = arguments[i + 1]\n    }\n  }\n\n  // request content type\n  var value = req.headers['content-type']\n\n  return typeis(value, types)\n}\n\n/**\n * Normalize a mime type.\n * If it's a shorthand, expand it to a valid mime type.\n *\n * In general, you probably want:\n *\n *   var type = is(req, ['urlencoded', 'json', 'multipart']);\n *\n * Then use the appropriate body parsers.\n * These three are the most common request body types\n * and are thus ensured to work.\n *\n * @param {String} type\n * @private\n */\n\nfunction normalize (type) {\n  if (typeof type !== 'string') {\n    // invalid type\n    return false\n  }\n\n  switch (type) {\n    case 'urlencoded':\n      return 'application/x-www-form-urlencoded'\n    case 'multipart':\n      return 'multipart/*'\n  }\n\n  if (type[0] === '+') {\n    // \"+json\" -> \"*/*+json\" expando\n    return '*/*' + type\n  }\n\n  return type.indexOf('/') === -1\n    ? mime.lookup(type)\n    : type\n}\n\n/**\n * Check if `expected` mime type\n * matches `actual` mime type with\n * wildcard and +suffix support.\n *\n * @param {String} expected\n * @param {String} actual\n * @return {Boolean}\n * @private\n */\n\nfunction mimeMatch (expected, actual) {\n  // invalid type\n  if (expected === false) {\n    return false\n  }\n\n  // split types\n  var actualParts = actual.split('/')\n  var expectedParts = expected.split('/')\n\n  // invalid format\n  if (actualParts.length !== 2 || expectedParts.length !== 2) {\n    return false\n  }\n\n  // validate type\n  if (expectedParts[0] !== '*' && expectedParts[0] !== actualParts[0]) {\n    return false\n  }\n\n  // validate suffix wildcard\n  if (expectedParts[1].substr(0, 2) === '*+') {\n    return expectedParts[1].length <= actualParts[1].length + 1 &&\n      expectedParts[1].substr(1) === actualParts[1].substr(1 - expectedParts[1].length)\n  }\n\n  // validate subtype\n  if (expectedParts[1] !== '*' && expectedParts[1] !== actualParts[1]) {\n    return false\n  }\n\n  return true\n}\n\n/**\n * Normalize a type and remove parameters.\n *\n * @param {string} value\n * @return {string}\n * @private\n */\n\nfunction normalizeType (value) {\n  // parse the type\n  var type = typer.parse(value)\n\n  // remove the parameters\n  type.parameters = undefined\n\n  // reformat it\n  return typer.format(type)\n}\n\n/**\n * Try to normalize a type and remove parameters.\n *\n * @param {string} value\n * @return {string}\n * @private\n */\n\nfunction tryNormalizeType (value) {\n  if (!value) {\n    return null\n  }\n\n  try {\n    return normalizeType(value)\n  } catch (err) {\n    return null\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/type-is@1.6.18/node_modules/type-is/index.js\n");

/***/ })

};
;