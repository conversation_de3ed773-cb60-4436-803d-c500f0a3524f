"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/serve-static@1.16.2";
exports.ids = ["vendor-chunks/serve-static@1.16.2"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/serve-static@1.16.2/node_modules/serve-static/index.js":
/*!***********************************************************************************!*\
  !*** ./node_modules/.pnpm/serve-static@1.16.2/node_modules/serve-static/index.js ***!
  \***********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * serve-static\n * Copyright(c) 2010 Sencha Inc.\n * Copyright(c) 2011 TJ Holowaychuk\n * Copyright(c) 2014-2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar encodeUrl = __webpack_require__(/*! encodeurl */ \"(rsc)/./node_modules/.pnpm/encodeurl@2.0.0/node_modules/encodeurl/index.js\")\nvar escapeHtml = __webpack_require__(/*! escape-html */ \"(rsc)/./node_modules/.pnpm/escape-html@1.0.3/node_modules/escape-html/index.js\")\nvar parseUrl = __webpack_require__(/*! parseurl */ \"(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\")\nvar resolve = (__webpack_require__(/*! path */ \"path\").resolve)\nvar send = __webpack_require__(/*! send */ \"(rsc)/./node_modules/.pnpm/send@0.19.0/node_modules/send/index.js\")\nvar url = __webpack_require__(/*! url */ \"url\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = serveStatic\nmodule.exports.mime = send.mime\n\n/**\n * @param {string} root\n * @param {object} [options]\n * @return {function}\n * @public\n */\n\nfunction serveStatic (root, options) {\n  if (!root) {\n    throw new TypeError('root path required')\n  }\n\n  if (typeof root !== 'string') {\n    throw new TypeError('root path must be a string')\n  }\n\n  // copy options object\n  var opts = Object.create(options || null)\n\n  // fall-though\n  var fallthrough = opts.fallthrough !== false\n\n  // default redirect\n  var redirect = opts.redirect !== false\n\n  // headers listener\n  var setHeaders = opts.setHeaders\n\n  if (setHeaders && typeof setHeaders !== 'function') {\n    throw new TypeError('option setHeaders must be function')\n  }\n\n  // setup options for send\n  opts.maxage = opts.maxage || opts.maxAge || 0\n  opts.root = resolve(root)\n\n  // construct directory listener\n  var onDirectory = redirect\n    ? createRedirectDirectoryListener()\n    : createNotFoundDirectoryListener()\n\n  return function serveStatic (req, res, next) {\n    if (req.method !== 'GET' && req.method !== 'HEAD') {\n      if (fallthrough) {\n        return next()\n      }\n\n      // method not allowed\n      res.statusCode = 405\n      res.setHeader('Allow', 'GET, HEAD')\n      res.setHeader('Content-Length', '0')\n      res.end()\n      return\n    }\n\n    var forwardError = !fallthrough\n    var originalUrl = parseUrl.original(req)\n    var path = parseUrl(req).pathname\n\n    // make sure redirect occurs at mount\n    if (path === '/' && originalUrl.pathname.substr(-1) !== '/') {\n      path = ''\n    }\n\n    // create send stream\n    var stream = send(req, path, opts)\n\n    // add directory handler\n    stream.on('directory', onDirectory)\n\n    // add headers listener\n    if (setHeaders) {\n      stream.on('headers', setHeaders)\n    }\n\n    // add file listener for fallthrough\n    if (fallthrough) {\n      stream.on('file', function onFile () {\n        // once file is determined, always forward error\n        forwardError = true\n      })\n    }\n\n    // forward errors\n    stream.on('error', function error (err) {\n      if (forwardError || !(err.statusCode < 500)) {\n        next(err)\n        return\n      }\n\n      next()\n    })\n\n    // pipe\n    stream.pipe(res)\n  }\n}\n\n/**\n * Collapse all leading slashes into a single slash\n * @private\n */\nfunction collapseLeadingSlashes (str) {\n  for (var i = 0; i < str.length; i++) {\n    if (str.charCodeAt(i) !== 0x2f /* / */) {\n      break\n    }\n  }\n\n  return i > 1\n    ? '/' + str.substr(i)\n    : str\n}\n\n/**\n * Create a minimal HTML document.\n *\n * @param {string} title\n * @param {string} body\n * @private\n */\n\nfunction createHtmlDocument (title, body) {\n  return '<!DOCTYPE html>\\n' +\n    '<html lang=\"en\">\\n' +\n    '<head>\\n' +\n    '<meta charset=\"utf-8\">\\n' +\n    '<title>' + title + '</title>\\n' +\n    '</head>\\n' +\n    '<body>\\n' +\n    '<pre>' + body + '</pre>\\n' +\n    '</body>\\n' +\n    '</html>\\n'\n}\n\n/**\n * Create a directory listener that just 404s.\n * @private\n */\n\nfunction createNotFoundDirectoryListener () {\n  return function notFound () {\n    this.error(404)\n  }\n}\n\n/**\n * Create a directory listener that performs a redirect.\n * @private\n */\n\nfunction createRedirectDirectoryListener () {\n  return function redirect (res) {\n    if (this.hasTrailingSlash()) {\n      this.error(404)\n      return\n    }\n\n    // get original URL\n    var originalUrl = parseUrl.original(this.req)\n\n    // append trailing slash\n    originalUrl.path = null\n    originalUrl.pathname = collapseLeadingSlashes(originalUrl.pathname + '/')\n\n    // reformat the URL\n    var loc = encodeUrl(url.format(originalUrl))\n    var doc = createHtmlDocument('Redirecting', 'Redirecting to ' + escapeHtml(loc))\n\n    // send redirect response\n    res.statusCode = 301\n    res.setHeader('Content-Type', 'text/html; charset=UTF-8')\n    res.setHeader('Content-Length', Buffer.byteLength(doc))\n    res.setHeader('Content-Security-Policy', \"default-src 'none'\")\n    res.setHeader('X-Content-Type-Options', 'nosniff')\n    res.setHeader('Location', loc)\n    res.end(doc)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/serve-static@1.16.2/node_modules/serve-static/index.js\n");

/***/ })

};
;