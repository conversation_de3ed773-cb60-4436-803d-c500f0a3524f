"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/statuses@2.0.1";
exports.ids = ["vendor-chunks/statuses@2.0.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * statuses\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar codes = __webpack_require__(/*! ./codes.json */ \"(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/codes.json\")\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = status\n\n// status code to message map\nstatus.message = codes\n\n// status message (lower-case) to code map\nstatus.code = createMessageToStatusCodeMap(codes)\n\n// array of status codes\nstatus.codes = createStatusCodeList(codes)\n\n// status codes for redirects\nstatus.redirect = {\n  300: true,\n  301: true,\n  302: true,\n  303: true,\n  305: true,\n  307: true,\n  308: true\n}\n\n// status codes for empty bodies\nstatus.empty = {\n  204: true,\n  205: true,\n  304: true\n}\n\n// status codes for when you should retry the request\nstatus.retry = {\n  502: true,\n  503: true,\n  504: true\n}\n\n/**\n * Create a map of message to status code.\n * @private\n */\n\nfunction createMessageToStatusCodeMap (codes) {\n  var map = {}\n\n  Object.keys(codes).forEach(function forEachCode (code) {\n    var message = codes[code]\n    var status = Number(code)\n\n    // populate map\n    map[message.toLowerCase()] = status\n  })\n\n  return map\n}\n\n/**\n * Create a list of all status codes.\n * @private\n */\n\nfunction createStatusCodeList (codes) {\n  return Object.keys(codes).map(function mapCode (code) {\n    return Number(code)\n  })\n}\n\n/**\n * Get the status code for given message.\n * @private\n */\n\nfunction getStatusCode (message) {\n  var msg = message.toLowerCase()\n\n  if (!Object.prototype.hasOwnProperty.call(status.code, msg)) {\n    throw new Error('invalid status message: \"' + message + '\"')\n  }\n\n  return status.code[msg]\n}\n\n/**\n * Get the status message for given code.\n * @private\n */\n\nfunction getStatusMessage (code) {\n  if (!Object.prototype.hasOwnProperty.call(status.message, code)) {\n    throw new Error('invalid status code: ' + code)\n  }\n\n  return status.message[code]\n}\n\n/**\n * Get the status code.\n *\n * Given a number, this will throw if it is not a known status\n * code, otherwise the code will be returned. Given a string,\n * the string will be parsed for a number and return the code\n * if valid, otherwise will lookup the code assuming this is\n * the status message.\n *\n * @param {string|number} code\n * @returns {number}\n * @public\n */\n\nfunction status (code) {\n  if (typeof code === 'number') {\n    return getStatusMessage(code)\n  }\n\n  if (typeof code !== 'string') {\n    throw new TypeError('code must be a number or string')\n  }\n\n  // '403'\n  var n = parseInt(code, 10)\n  if (!isNaN(n)) {\n    return getStatusMessage(n)\n  }\n\n  return getStatusCode(code)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/codes.json":
/*!****************************************************************************!*\
  !*** ./node_modules/.pnpm/statuses@2.0.1/node_modules/statuses/codes.json ***!
  \****************************************************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"100":"Continue","101":"Switching Protocols","102":"Processing","103":"Early Hints","200":"OK","201":"Created","202":"Accepted","203":"Non-Authoritative Information","204":"No Content","205":"Reset Content","206":"Partial Content","207":"Multi-Status","208":"Already Reported","226":"IM Used","300":"Multiple Choices","301":"Moved Permanently","302":"Found","303":"See Other","304":"Not Modified","305":"Use Proxy","307":"Temporary Redirect","308":"Permanent Redirect","400":"Bad Request","401":"Unauthorized","402":"Payment Required","403":"Forbidden","404":"Not Found","405":"Method Not Allowed","406":"Not Acceptable","407":"Proxy Authentication Required","408":"Request Timeout","409":"Conflict","410":"Gone","411":"Length Required","412":"Precondition Failed","413":"Payload Too Large","414":"URI Too Long","415":"Unsupported Media Type","416":"Range Not Satisfiable","417":"Expectation Failed","418":"I\'m a Teapot","421":"Misdirected Request","422":"Unprocessable Entity","423":"Locked","424":"Failed Dependency","425":"Too Early","426":"Upgrade Required","428":"Precondition Required","429":"Too Many Requests","431":"Request Header Fields Too Large","451":"Unavailable For Legal Reasons","500":"Internal Server Error","501":"Not Implemented","502":"Bad Gateway","503":"Service Unavailable","504":"Gateway Timeout","505":"HTTP Version Not Supported","506":"Variant Also Negotiates","507":"Insufficient Storage","508":"Loop Detected","509":"Bandwidth Limit Exceeded","510":"Not Extended","511":"Network Authentication Required"}');

/***/ })

};
;