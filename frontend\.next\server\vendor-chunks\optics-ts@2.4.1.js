"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/optics-ts@2.4.1";
exports.ids = ["vendor-chunks/optics-ts@2.4.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   collect: () => (/* binding */ collect),\n/* harmony export */   compose: () => (/* binding */ compose),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   modify: () => (/* binding */ modify),\n/* harmony export */   optic: () => (/* binding */ optic),\n/* harmony export */   optic_: () => (/* binding */ optic_),\n/* harmony export */   pipe: () => (/* reexport safe */ _standalone_pipe_js__WEBPACK_IMPORTED_MODULE_1__.pipe),\n/* harmony export */   preview: () => (/* binding */ preview),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   set: () => (/* binding */ set)\n/* harmony export */ });\n/* harmony import */ var _internals_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./internals.js */ \"(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/internals.js\");\n/* harmony import */ var _standalone_pipe_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./standalone/pipe.js */ \"(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/standalone/pipe.js\");\n/* eslint-disable @typescript-eslint/adjacent-overload-signatures, @typescript-eslint/no-unused-vars */\n// This file is generated, do not edit! See ../scripts/generate-index.ts\n\nfunction compose(optic1, optic2) {\n    return optic1.compose(optic2);\n}\nfunction optic() {\n    return _internals_js__WEBPACK_IMPORTED_MODULE_0__.optic;\n}\nfunction optic_() {\n    return _internals_js__WEBPACK_IMPORTED_MODULE_0__.optic;\n}\nfunction get(optic) {\n    return (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.get(optic._ref, source);\n}\nfunction preview(optic) {\n    return (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.preview(optic._ref, source);\n}\nfunction collect(optic) {\n    return (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.collect(optic._ref, source);\n}\nfunction modify(optic) {\n    return (f) => (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.modify(optic._ref, f, source);\n}\nfunction set(optic) {\n    return (value) => (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.set(optic._ref, value, source);\n}\nfunction remove(optic) {\n    return (source) => _internals_js__WEBPACK_IMPORTED_MODULE_0__.remove(optic._ref, source);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/internals.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/internals.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Optic: () => (/* binding */ Optic),\n/* harmony export */   appendTo: () => (/* binding */ appendTo),\n/* harmony export */   at: () => (/* binding */ at),\n/* harmony export */   atKey: () => (/* binding */ atKey),\n/* harmony export */   chars: () => (/* binding */ chars),\n/* harmony export */   collect: () => (/* binding */ collect),\n/* harmony export */   compositionType: () => (/* binding */ compositionType),\n/* harmony export */   elems: () => (/* binding */ elems),\n/* harmony export */   eq: () => (/* binding */ eq),\n/* harmony export */   filter: () => (/* binding */ filter),\n/* harmony export */   find: () => (/* binding */ find),\n/* harmony export */   get: () => (/* binding */ get),\n/* harmony export */   guard: () => (/* binding */ guard),\n/* harmony export */   id: () => (/* binding */ id),\n/* harmony export */   indexed: () => (/* binding */ indexed),\n/* harmony export */   iso: () => (/* binding */ iso),\n/* harmony export */   lens: () => (/* binding */ lens),\n/* harmony export */   modify: () => (/* binding */ modify),\n/* harmony export */   nth: () => (/* binding */ nth),\n/* harmony export */   optic: () => (/* binding */ optic),\n/* harmony export */   optional: () => (/* binding */ optional),\n/* harmony export */   partsOf: () => (/* binding */ partsOf),\n/* harmony export */   pick: () => (/* binding */ pick),\n/* harmony export */   prependTo: () => (/* binding */ prependTo),\n/* harmony export */   preview: () => (/* binding */ preview),\n/* harmony export */   prop: () => (/* binding */ prop),\n/* harmony export */   remove: () => (/* binding */ remove),\n/* harmony export */   reread: () => (/* binding */ reread),\n/* harmony export */   rewrite: () => (/* binding */ rewrite),\n/* harmony export */   set: () => (/* binding */ set),\n/* harmony export */   to: () => (/* binding */ to),\n/* harmony export */   valueOr: () => (/* binding */ valueOr),\n/* harmony export */   when: () => (/* binding */ when),\n/* harmony export */   words: () => (/* binding */ words)\n/* harmony export */ });\nvar __rest = (undefined && undefined.__rest) || function (s, e) {\n    var t = {};\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n        t[p] = s[p];\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n                t[p[i]] = s[p[i]];\n        }\n    return t;\n};\n/* eslint-disable @typescript-eslint/no-non-null-assertion */\nconst id = (x) => x;\nconst Left = (value) => ({\n    _tag: 'Left',\n    value,\n});\nconst Right = (value) => ({\n    _tag: 'Right',\n    value,\n});\nconst either = (mapLeft, mapRight, e) => (e._tag === 'Left' ? mapLeft(e.value) : mapRight(e.value));\nconst profunctorFn = {\n    dimap: (f, g, fn) => (x) => g(fn(f(x))),\n    first: (f) => ([x, y]) => [f(x), y],\n    right: (f) => (e) => e._tag === 'Left' ? e : Right(f(e.value)),\n    wander: (f) => (xs) => xs.map(f),\n};\nconst monoidFirst = {\n    empty: () => undefined,\n    foldMap: (f, xs) => {\n        for (let i = 0; i < xs.length; i++) {\n            const x = f(xs[i]);\n            if (x != undefined)\n                return x;\n        }\n        return undefined;\n    },\n};\nconst monoidArray = {\n    empty: () => [],\n    foldMap: (f, xs) => {\n        let acc = [];\n        xs.forEach((x) => {\n            acc = acc.concat(f(x));\n        });\n        return acc;\n    },\n};\nconst profunctorConst = (monoid) => ({\n    dimap: (f, _g, toF) => (x) => toF(f(x)),\n    first: (toF) => ([x, _y]) => toF(x),\n    right: (toF) => (e) => e._tag === 'Left' ? monoid.empty() : toF(e.value),\n    wander: (toF) => (xs) => monoid.foldMap(toF, xs),\n});\nconst compositionType = {\n    Equivalence: {\n        Equivalence: 'Equivalence',\n        Iso: 'Iso',\n        Lens: 'Lens',\n        Prism: 'Prism',\n        Traversal: 'Traversal',\n        Getter: 'Getter',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: 'Setter',\n    },\n    Iso: {\n        Equivalence: 'Iso',\n        Iso: 'Iso',\n        Lens: 'Lens',\n        Prism: 'Prism',\n        Traversal: 'Traversal',\n        Getter: 'Getter',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: 'Setter',\n    },\n    Lens: {\n        Equivalence: 'Lens',\n        Iso: 'Lens',\n        Lens: 'Lens',\n        Prism: 'Prism',\n        Traversal: 'Traversal',\n        Getter: 'Getter',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: 'Setter',\n    },\n    Prism: {\n        Equivalence: 'Prism',\n        Iso: 'Prism',\n        Lens: 'Prism',\n        Prism: 'Prism',\n        Traversal: 'Traversal',\n        Getter: 'AffineFold',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: 'Setter',\n    },\n    Traversal: {\n        Equivalence: 'Traversal',\n        Iso: 'Traversal',\n        Lens: 'Traversal',\n        Prism: 'Traversal',\n        Traversal: 'Traversal',\n        Getter: 'Fold',\n        AffineFold: 'Fold',\n        Fold: 'Fold',\n        Setter: 'Setter',\n    },\n    Getter: {\n        Equivalence: 'Getter',\n        Iso: 'Getter',\n        Lens: 'Getter',\n        Prism: 'AffineFold',\n        Traversal: 'Fold',\n        Getter: 'Getter',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: undefined,\n    },\n    AffineFold: {\n        Equivalence: 'AffineFold',\n        Iso: 'AffineFold',\n        Lens: 'AffineFold',\n        Prism: 'AffineFold',\n        Traversal: 'Fold',\n        Getter: 'AffineFold',\n        AffineFold: 'AffineFold',\n        Fold: 'Fold',\n        Setter: undefined,\n    },\n    Fold: {\n        Equivalence: 'Fold',\n        Iso: 'Fold',\n        Lens: 'Fold',\n        Prism: 'Fold',\n        Traversal: 'Fold',\n        Getter: 'Fold',\n        AffineFold: 'Fold',\n        Fold: 'Fold',\n        Setter: undefined,\n    },\n    Setter: {\n        Equivalence: undefined,\n        Iso: undefined,\n        Lens: undefined,\n        Prism: undefined,\n        Traversal: undefined,\n        Getter: undefined,\n        AffineFold: undefined,\n        Fold: undefined,\n        Setter: undefined,\n    },\n};\nconst withTag = (tag, optic) => {\n    const result = optic;\n    result._tag = tag;\n    return result;\n};\nconst removable = (optic) => {\n    optic._removable = true;\n    return optic;\n};\nfunction compose(optic1, optic2, optic3) {\n    switch (arguments.length) {\n        case 2: {\n            const next = (P, optic) => optic1(P, optic2(P, optic));\n            next._tag = compositionType[optic1._tag][optic2._tag];\n            next._removable = optic2._removable || false;\n            return next;\n        }\n        default: {\n            const tag1 = compositionType[optic1._tag][optic2._tag];\n            const next = (P, optic) => optic1(P, optic2(P, optic3(P, optic)));\n            next._tag = compositionType[tag1][optic3._tag];\n            next._removable = optic3._removable || false;\n            return next;\n        }\n    }\n}\nconst eq = /* @__PURE__ */ withTag('Equivalence', (_P, optic) => optic);\nconst iso = (there, back) => withTag('Iso', (P, optic) => P.dimap(there, back, optic));\nconst lens = (view, update) => withTag('Lens', (P, optic) => P.dimap((x) => [view(x), x], update, P.first(optic)));\nconst prism = (match, build) => withTag('Prism', (P, optic) => P.dimap(match, (x) => either(id, build, x), P.right(optic)));\nconst elems = /* @__PURE__ */ withTag('Traversal', (P, optic) => P.dimap(id, id, P.wander(optic)));\nconst to = (fn) => withTag('Getter', (P, optic) => P.dimap(fn, id, optic));\n/////////////////////////////////////////////////////////////////////////////\nconst modify = (optic, fn, source) => optic(profunctorFn, fn)(source);\nconst set = (optic, value, source) => optic(profunctorFn, () => value)(source);\nconst remove = (optic, source) => set(optic, removeMe, source);\nconst get = (optic, source) => optic(profunctorConst({}), id)(source);\nconst preview = (optic, source) => optic(profunctorConst(monoidFirst), id)(source);\nconst collect = (optic, source) => optic(profunctorConst(monoidArray), (x) => [x])(source);\n/////////////////////////////////////////////////////////////////////////////\nconst indexed = /* @__PURE__ */ iso((value) => value.map((v, k) => [k, v]), (value) => {\n    const sorted = [...value].sort((a, b) => a[0] - b[0]);\n    const result = [];\n    for (let i = 0; i < sorted.length; ++i) {\n        if (i === sorted.length - 1 || sorted[i][0] !== sorted[i + 1][0]) {\n            result.push(sorted[i][1]);\n        }\n    }\n    return result;\n});\nconst prop = (key) => lens((source) => source[key], ([value, source]) => (Object.assign(Object.assign({}, source), { [key]: value })));\nconst pick = (keys) => lens((source) => {\n    const value = {};\n    for (const key of keys) {\n        value[key] = source[key];\n    }\n    return value;\n}, ([value, source]) => {\n    const result = Object.assign({}, source);\n    for (const key of keys) {\n        delete result[key];\n    }\n    return Object.assign(result, value);\n});\nconst nth = (n) => lens((value) => value[n], ([value, source]) => {\n    const result = source.slice();\n    result[n] = value;\n    return result;\n});\nconst fst = /* @__PURE__ */ nth(0);\nconst when = (pred) => prism((x) => (pred(x) ? Right(x) : Left(x)), id);\nconst noMatch = /* @__PURE__ */ Symbol('__no_match__');\nconst mustMatch = /* @__PURE__ */ when((source) => source !== noMatch);\nconst removeMe = /* @__PURE__ */ Symbol('__remove_me__');\nconst at = (i) => removable(compose(lens((source) => (0 <= i && i < source.length ? source[i] : noMatch), ([value, source]) => {\n    if (value === noMatch) {\n        return source;\n    }\n    if (value === removeMe) {\n        if (typeof source === 'string') {\n            return source.substring(0, i) + source.substring(i + 1);\n        }\n        else {\n            return [...source.slice(0, i), ...source.slice(i + 1)];\n        }\n    }\n    if (typeof source === 'string') {\n        if (i === 0) {\n            return value + source.substring(1);\n        }\n        if (i === source.length) {\n            return source.substring(0, i - 1) + value;\n        }\n        return source.substring(0, i) + value + source.substring(i + 1);\n    }\n    else {\n        const result = source.slice();\n        result[i] = value;\n        return result;\n    }\n}), mustMatch));\nconst atKey = (key) => removable(compose(lens((source) => {\n    const value = source[key];\n    return value !== undefined ? value : noMatch;\n}, ([value, source]) => {\n    if (value === noMatch) {\n        return source;\n    }\n    if (value === removeMe) {\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _a = source, _b = key, _ = _a[_b], rest = __rest(_a, [typeof _b === \"symbol\" ? _b : _b + \"\"]);\n        return rest;\n    }\n    return Object.assign(Object.assign({}, source), { [key]: value });\n}), mustMatch));\nconst optional = /* @__PURE__ */ prism((source) => (source === undefined ? Left(undefined) : Right(source)), id);\nconst guard = (fn) => prism((source) => (fn(source) ? Right(source) : Left(source)), id);\nconst find = (predicate) => removable(compose(lens((source) => {\n    const index = source.findIndex(predicate);\n    if (index === -1) {\n        return [noMatch, -1];\n    }\n    return [source[index], index];\n}, ([[value, index], source]) => {\n    if (value === noMatch) {\n        return source;\n    }\n    if (value === removeMe) {\n        return [...source.slice(0, index), ...source.slice(index + 1)];\n    }\n    const result = source.slice();\n    result[index] = value;\n    return result;\n}), fst, mustMatch));\nconst filter = (predicate) => compose(lens((source) => {\n    const indexes = source\n        .map((item, index) => (predicate(item) ? index : null))\n        .filter((index) => index != null);\n    return [indexes.map((index) => source[index]), indexes];\n}, ([[values, indexes], source]) => {\n    const sn = source.length, vn = values.length;\n    let si = 0, ii = 0, vi = 0;\n    const result = [];\n    while (si < sn) {\n        if (indexes[ii] === si) {\n            ++ii;\n            if (vi < vn) {\n                result.push(values[vi]);\n                ++vi;\n            }\n        }\n        else {\n            result.push(source[si]);\n        }\n        ++si;\n    }\n    while (vi < vn) {\n        result.push(values[vi++]);\n    }\n    return result;\n}), fst);\nconst valueOr = (defaultValue) => lens((source) => (source === undefined ? defaultValue : source), ([value, _source]) => value);\nconst partsOf = (traversal) => compose(lens((source) => {\n    const value = collect(traversal, source);\n    return [value, value.length];\n}, ([[value, originalLength], source]) => {\n    if (value.length !== originalLength) {\n        throw new Error('cannot add/remove elements through partsOf');\n    }\n    let i = 0;\n    return modify(traversal, () => value[i++], source);\n}), fst);\nconst reread = (fn) => lens((source) => fn(source), ([value, _]) => value);\nconst rewrite = (fn) => lens((source) => source, ([value, _]) => fn(value));\nconst prependTo = /* @__PURE__ */ lens((_source) => undefined, ([value, source]) => {\n    if (value === undefined)\n        return source;\n    return [value, ...source];\n});\nconst appendTo = /* @__PURE__ */ lens((_source) => undefined, ([value, source]) => {\n    if (value === undefined)\n        return source;\n    return [...source, value];\n});\nconst chars = /* @__PURE__ */ compose(iso((s) => s.split(''), (a) => a.join('')), elems);\nconst words = /* @__PURE__ */ compose(iso((s) => s.split(/\\b/), (a) => a.join('')), elems, when((s) => !/\\s+/.test(s)));\n/////////////////////////////////////////////////////////////////////////////\nclass Optic {\n    constructor(_ref) {\n        this._ref = _ref;\n    }\n    get _tag() {\n        return this._ref._tag;\n    }\n    get _removable() {\n        return this._ref._removable;\n    }\n    compose(other) {\n        return new Optic(compose(this._ref, other._ref));\n    }\n    iso(there, back) {\n        return new Optic(compose(this._ref, iso(there, back)));\n    }\n    lens(view, set) {\n        return new Optic(compose(this._ref, lens(view, ([value, source]) => set(source, value))));\n    }\n    indexed() {\n        return new Optic(compose(this._ref, indexed));\n    }\n    prop(key) {\n        return new Optic(compose(this._ref, prop(key)));\n    }\n    path(...keys) {\n        if (keys.length === 1) {\n            keys = keys[0].split('.');\n        }\n        return new Optic(keys.reduce((ref, key) => compose(ref, prop(key)), this._ref));\n    }\n    pick(keys) {\n        return new Optic(compose(this._ref, pick(keys)));\n    }\n    nth(n) {\n        return new Optic(compose(this._ref, nth(n)));\n    }\n    filter(predicate) {\n        return new Optic(compose(this._ref, filter(predicate)));\n    }\n    valueOr(defaultValue) {\n        return new Optic(compose(this._ref, valueOr(defaultValue)));\n    }\n    partsOf(traversalOrFn) {\n        const traversal = typeof traversalOrFn === 'function' ? traversalOrFn(optic) : traversalOrFn;\n        return new Optic(compose(this._ref, partsOf(traversal._ref)));\n    }\n    reread(fn) {\n        return new Optic(compose(this._ref, reread(fn)));\n    }\n    rewrite(fn) {\n        return new Optic(compose(this._ref, rewrite(fn)));\n    }\n    optional() {\n        return new Optic(compose(this._ref, optional));\n    }\n    guard_() {\n        return (fn) => this.guard(fn);\n    }\n    guard(fn) {\n        return new Optic(compose(this._ref, guard(fn)));\n    }\n    at(i) {\n        return new Optic(compose(this._ref, at(i)));\n    }\n    head() {\n        return new Optic(compose(this._ref, at(0)));\n    }\n    index(i) {\n        return new Optic(compose(this._ref, at(i)));\n    }\n    find(predicate) {\n        return new Optic(compose(this._ref, find(predicate)));\n    }\n    elems() {\n        return new Optic(compose(this._ref, elems));\n    }\n    to(fn) {\n        return new Optic(compose(this._ref, to(fn)));\n    }\n    when(predicate) {\n        return new Optic(compose(this._ref, when(predicate)));\n    }\n    chars() {\n        return new Optic(compose(this._ref, chars));\n    }\n    words() {\n        return new Optic(compose(this._ref, words));\n    }\n    prependTo() {\n        return new Optic(compose(this._ref, prependTo));\n    }\n    appendTo() {\n        return new Optic(compose(this._ref, appendTo));\n    }\n}\nconst optic = /* @__PURE__ */ new Optic(eq);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb3B0aWNzLXRzQDIuNC4xL25vZGVfbW9kdWxlcy9vcHRpY3MtdHMvZGlzdC9tanMvaW50ZXJuYWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQSxjQUFjLFNBQUksSUFBSSxTQUFJO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkRBQTZELGNBQWM7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLGVBQWU7QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNBO0FBQ0E7QUFDUDtBQUNPO0FBQ0E7QUFDUDtBQUNPO0FBQ0E7QUFDQTtBQUNBLHVEQUF1RDtBQUN2RDtBQUNBO0FBQ1A7QUFDTztBQUNQO0FBQ0E7QUFDQSxvQkFBb0IsbUJBQW1CO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ00sd0dBQXdHLGFBQWEsY0FBYztBQUNuSTtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0QsbUNBQW1DO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNO0FBQ1A7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUNBQXlDLGFBQWEsY0FBYztBQUNwRSxDQUFDO0FBQ007QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNO0FBQ0E7QUFDUDtBQUNBO0FBQ0EsQ0FBQztBQUNEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDQTtBQUNBO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsQ0FBQztBQUNNO0FBQ0E7QUFDUDtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGljcy10c0AyLjQuMS9ub2RlX21vZHVsZXMvb3B0aWNzLXRzL2Rpc3QvbWpzL2ludGVybmFscy5qcz8wYzUyIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX3Jlc3QgPSAodGhpcyAmJiB0aGlzLl9fcmVzdCkgfHwgZnVuY3Rpb24gKHMsIGUpIHtcbiAgICB2YXIgdCA9IHt9O1xuICAgIGZvciAodmFyIHAgaW4gcykgaWYgKE9iamVjdC5wcm90b3R5cGUuaGFzT3duUHJvcGVydHkuY2FsbChzLCBwKSAmJiBlLmluZGV4T2YocCkgPCAwKVxuICAgICAgICB0W3BdID0gc1twXTtcbiAgICBpZiAocyAhPSBudWxsICYmIHR5cGVvZiBPYmplY3QuZ2V0T3duUHJvcGVydHlTeW1ib2xzID09PSBcImZ1bmN0aW9uXCIpXG4gICAgICAgIGZvciAodmFyIGkgPSAwLCBwID0gT2JqZWN0LmdldE93blByb3BlcnR5U3ltYm9scyhzKTsgaSA8IHAubGVuZ3RoOyBpKyspIHtcbiAgICAgICAgICAgIGlmIChlLmluZGV4T2YocFtpXSkgPCAwICYmIE9iamVjdC5wcm90b3R5cGUucHJvcGVydHlJc0VudW1lcmFibGUuY2FsbChzLCBwW2ldKSlcbiAgICAgICAgICAgICAgICB0W3BbaV1dID0gc1twW2ldXTtcbiAgICAgICAgfVxuICAgIHJldHVybiB0O1xufTtcbi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby1ub24tbnVsbC1hc3NlcnRpb24gKi9cbmV4cG9ydCBjb25zdCBpZCA9ICh4KSA9PiB4O1xuY29uc3QgTGVmdCA9ICh2YWx1ZSkgPT4gKHtcbiAgICBfdGFnOiAnTGVmdCcsXG4gICAgdmFsdWUsXG59KTtcbmNvbnN0IFJpZ2h0ID0gKHZhbHVlKSA9PiAoe1xuICAgIF90YWc6ICdSaWdodCcsXG4gICAgdmFsdWUsXG59KTtcbmNvbnN0IGVpdGhlciA9IChtYXBMZWZ0LCBtYXBSaWdodCwgZSkgPT4gKGUuX3RhZyA9PT0gJ0xlZnQnID8gbWFwTGVmdChlLnZhbHVlKSA6IG1hcFJpZ2h0KGUudmFsdWUpKTtcbmNvbnN0IHByb2Z1bmN0b3JGbiA9IHtcbiAgICBkaW1hcDogKGYsIGcsIGZuKSA9PiAoeCkgPT4gZyhmbihmKHgpKSksXG4gICAgZmlyc3Q6IChmKSA9PiAoW3gsIHldKSA9PiBbZih4KSwgeV0sXG4gICAgcmlnaHQ6IChmKSA9PiAoZSkgPT4gZS5fdGFnID09PSAnTGVmdCcgPyBlIDogUmlnaHQoZihlLnZhbHVlKSksXG4gICAgd2FuZGVyOiAoZikgPT4gKHhzKSA9PiB4cy5tYXAoZiksXG59O1xuY29uc3QgbW9ub2lkRmlyc3QgPSB7XG4gICAgZW1wdHk6ICgpID0+IHVuZGVmaW5lZCxcbiAgICBmb2xkTWFwOiAoZiwgeHMpID0+IHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCB4cy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgICAgY29uc3QgeCA9IGYoeHNbaV0pO1xuICAgICAgICAgICAgaWYgKHggIT0gdW5kZWZpbmVkKVxuICAgICAgICAgICAgICAgIHJldHVybiB4O1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiB1bmRlZmluZWQ7XG4gICAgfSxcbn07XG5jb25zdCBtb25vaWRBcnJheSA9IHtcbiAgICBlbXB0eTogKCkgPT4gW10sXG4gICAgZm9sZE1hcDogKGYsIHhzKSA9PiB7XG4gICAgICAgIGxldCBhY2MgPSBbXTtcbiAgICAgICAgeHMuZm9yRWFjaCgoeCkgPT4ge1xuICAgICAgICAgICAgYWNjID0gYWNjLmNvbmNhdChmKHgpKTtcbiAgICAgICAgfSk7XG4gICAgICAgIHJldHVybiBhY2M7XG4gICAgfSxcbn07XG5jb25zdCBwcm9mdW5jdG9yQ29uc3QgPSAobW9ub2lkKSA9PiAoe1xuICAgIGRpbWFwOiAoZiwgX2csIHRvRikgPT4gKHgpID0+IHRvRihmKHgpKSxcbiAgICBmaXJzdDogKHRvRikgPT4gKFt4LCBfeV0pID0+IHRvRih4KSxcbiAgICByaWdodDogKHRvRikgPT4gKGUpID0+IGUuX3RhZyA9PT0gJ0xlZnQnID8gbW9ub2lkLmVtcHR5KCkgOiB0b0YoZS52YWx1ZSksXG4gICAgd2FuZGVyOiAodG9GKSA9PiAoeHMpID0+IG1vbm9pZC5mb2xkTWFwKHRvRiwgeHMpLFxufSk7XG5leHBvcnQgY29uc3QgY29tcG9zaXRpb25UeXBlID0ge1xuICAgIEVxdWl2YWxlbmNlOiB7XG4gICAgICAgIEVxdWl2YWxlbmNlOiAnRXF1aXZhbGVuY2UnLFxuICAgICAgICBJc286ICdJc28nLFxuICAgICAgICBMZW5zOiAnTGVucycsXG4gICAgICAgIFByaXNtOiAnUHJpc20nLFxuICAgICAgICBUcmF2ZXJzYWw6ICdUcmF2ZXJzYWwnLFxuICAgICAgICBHZXR0ZXI6ICdHZXR0ZXInLFxuICAgICAgICBBZmZpbmVGb2xkOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIEZvbGQ6ICdGb2xkJyxcbiAgICAgICAgU2V0dGVyOiAnU2V0dGVyJyxcbiAgICB9LFxuICAgIElzbzoge1xuICAgICAgICBFcXVpdmFsZW5jZTogJ0lzbycsXG4gICAgICAgIElzbzogJ0lzbycsXG4gICAgICAgIExlbnM6ICdMZW5zJyxcbiAgICAgICAgUHJpc206ICdQcmlzbScsXG4gICAgICAgIFRyYXZlcnNhbDogJ1RyYXZlcnNhbCcsXG4gICAgICAgIEdldHRlcjogJ0dldHRlcicsXG4gICAgICAgIEFmZmluZUZvbGQ6ICdBZmZpbmVGb2xkJyxcbiAgICAgICAgRm9sZDogJ0ZvbGQnLFxuICAgICAgICBTZXR0ZXI6ICdTZXR0ZXInLFxuICAgIH0sXG4gICAgTGVuczoge1xuICAgICAgICBFcXVpdmFsZW5jZTogJ0xlbnMnLFxuICAgICAgICBJc286ICdMZW5zJyxcbiAgICAgICAgTGVuczogJ0xlbnMnLFxuICAgICAgICBQcmlzbTogJ1ByaXNtJyxcbiAgICAgICAgVHJhdmVyc2FsOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgR2V0dGVyOiAnR2V0dGVyJyxcbiAgICAgICAgQWZmaW5lRm9sZDogJ0FmZmluZUZvbGQnLFxuICAgICAgICBGb2xkOiAnRm9sZCcsXG4gICAgICAgIFNldHRlcjogJ1NldHRlcicsXG4gICAgfSxcbiAgICBQcmlzbToge1xuICAgICAgICBFcXVpdmFsZW5jZTogJ1ByaXNtJyxcbiAgICAgICAgSXNvOiAnUHJpc20nLFxuICAgICAgICBMZW5zOiAnUHJpc20nLFxuICAgICAgICBQcmlzbTogJ1ByaXNtJyxcbiAgICAgICAgVHJhdmVyc2FsOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgR2V0dGVyOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIEFmZmluZUZvbGQ6ICdBZmZpbmVGb2xkJyxcbiAgICAgICAgRm9sZDogJ0ZvbGQnLFxuICAgICAgICBTZXR0ZXI6ICdTZXR0ZXInLFxuICAgIH0sXG4gICAgVHJhdmVyc2FsOiB7XG4gICAgICAgIEVxdWl2YWxlbmNlOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgSXNvOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgTGVuczogJ1RyYXZlcnNhbCcsXG4gICAgICAgIFByaXNtOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgVHJhdmVyc2FsOiAnVHJhdmVyc2FsJyxcbiAgICAgICAgR2V0dGVyOiAnRm9sZCcsXG4gICAgICAgIEFmZmluZUZvbGQ6ICdGb2xkJyxcbiAgICAgICAgRm9sZDogJ0ZvbGQnLFxuICAgICAgICBTZXR0ZXI6ICdTZXR0ZXInLFxuICAgIH0sXG4gICAgR2V0dGVyOiB7XG4gICAgICAgIEVxdWl2YWxlbmNlOiAnR2V0dGVyJyxcbiAgICAgICAgSXNvOiAnR2V0dGVyJyxcbiAgICAgICAgTGVuczogJ0dldHRlcicsXG4gICAgICAgIFByaXNtOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIFRyYXZlcnNhbDogJ0ZvbGQnLFxuICAgICAgICBHZXR0ZXI6ICdHZXR0ZXInLFxuICAgICAgICBBZmZpbmVGb2xkOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIEZvbGQ6ICdGb2xkJyxcbiAgICAgICAgU2V0dGVyOiB1bmRlZmluZWQsXG4gICAgfSxcbiAgICBBZmZpbmVGb2xkOiB7XG4gICAgICAgIEVxdWl2YWxlbmNlOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIElzbzogJ0FmZmluZUZvbGQnLFxuICAgICAgICBMZW5zOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIFByaXNtOiAnQWZmaW5lRm9sZCcsXG4gICAgICAgIFRyYXZlcnNhbDogJ0ZvbGQnLFxuICAgICAgICBHZXR0ZXI6ICdBZmZpbmVGb2xkJyxcbiAgICAgICAgQWZmaW5lRm9sZDogJ0FmZmluZUZvbGQnLFxuICAgICAgICBGb2xkOiAnRm9sZCcsXG4gICAgICAgIFNldHRlcjogdW5kZWZpbmVkLFxuICAgIH0sXG4gICAgRm9sZDoge1xuICAgICAgICBFcXVpdmFsZW5jZTogJ0ZvbGQnLFxuICAgICAgICBJc286ICdGb2xkJyxcbiAgICAgICAgTGVuczogJ0ZvbGQnLFxuICAgICAgICBQcmlzbTogJ0ZvbGQnLFxuICAgICAgICBUcmF2ZXJzYWw6ICdGb2xkJyxcbiAgICAgICAgR2V0dGVyOiAnRm9sZCcsXG4gICAgICAgIEFmZmluZUZvbGQ6ICdGb2xkJyxcbiAgICAgICAgRm9sZDogJ0ZvbGQnLFxuICAgICAgICBTZXR0ZXI6IHVuZGVmaW5lZCxcbiAgICB9LFxuICAgIFNldHRlcjoge1xuICAgICAgICBFcXVpdmFsZW5jZTogdW5kZWZpbmVkLFxuICAgICAgICBJc286IHVuZGVmaW5lZCxcbiAgICAgICAgTGVuczogdW5kZWZpbmVkLFxuICAgICAgICBQcmlzbTogdW5kZWZpbmVkLFxuICAgICAgICBUcmF2ZXJzYWw6IHVuZGVmaW5lZCxcbiAgICAgICAgR2V0dGVyOiB1bmRlZmluZWQsXG4gICAgICAgIEFmZmluZUZvbGQ6IHVuZGVmaW5lZCxcbiAgICAgICAgRm9sZDogdW5kZWZpbmVkLFxuICAgICAgICBTZXR0ZXI6IHVuZGVmaW5lZCxcbiAgICB9LFxufTtcbmNvbnN0IHdpdGhUYWcgPSAodGFnLCBvcHRpYykgPT4ge1xuICAgIGNvbnN0IHJlc3VsdCA9IG9wdGljO1xuICAgIHJlc3VsdC5fdGFnID0gdGFnO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xuY29uc3QgcmVtb3ZhYmxlID0gKG9wdGljKSA9PiB7XG4gICAgb3B0aWMuX3JlbW92YWJsZSA9IHRydWU7XG4gICAgcmV0dXJuIG9wdGljO1xufTtcbmZ1bmN0aW9uIGNvbXBvc2Uob3B0aWMxLCBvcHRpYzIsIG9wdGljMykge1xuICAgIHN3aXRjaCAoYXJndW1lbnRzLmxlbmd0aCkge1xuICAgICAgICBjYXNlIDI6IHtcbiAgICAgICAgICAgIGNvbnN0IG5leHQgPSAoUCwgb3B0aWMpID0+IG9wdGljMShQLCBvcHRpYzIoUCwgb3B0aWMpKTtcbiAgICAgICAgICAgIG5leHQuX3RhZyA9IGNvbXBvc2l0aW9uVHlwZVtvcHRpYzEuX3RhZ11bb3B0aWMyLl90YWddO1xuICAgICAgICAgICAgbmV4dC5fcmVtb3ZhYmxlID0gb3B0aWMyLl9yZW1vdmFibGUgfHwgZmFsc2U7XG4gICAgICAgICAgICByZXR1cm4gbmV4dDtcbiAgICAgICAgfVxuICAgICAgICBkZWZhdWx0OiB7XG4gICAgICAgICAgICBjb25zdCB0YWcxID0gY29tcG9zaXRpb25UeXBlW29wdGljMS5fdGFnXVtvcHRpYzIuX3RhZ107XG4gICAgICAgICAgICBjb25zdCBuZXh0ID0gKFAsIG9wdGljKSA9PiBvcHRpYzEoUCwgb3B0aWMyKFAsIG9wdGljMyhQLCBvcHRpYykpKTtcbiAgICAgICAgICAgIG5leHQuX3RhZyA9IGNvbXBvc2l0aW9uVHlwZVt0YWcxXVtvcHRpYzMuX3RhZ107XG4gICAgICAgICAgICBuZXh0Ll9yZW1vdmFibGUgPSBvcHRpYzMuX3JlbW92YWJsZSB8fCBmYWxzZTtcbiAgICAgICAgICAgIHJldHVybiBuZXh0O1xuICAgICAgICB9XG4gICAgfVxufVxuZXhwb3J0IGNvbnN0IGVxID0gLyogQF9fUFVSRV9fICovIHdpdGhUYWcoJ0VxdWl2YWxlbmNlJywgKF9QLCBvcHRpYykgPT4gb3B0aWMpO1xuZXhwb3J0IGNvbnN0IGlzbyA9ICh0aGVyZSwgYmFjaykgPT4gd2l0aFRhZygnSXNvJywgKFAsIG9wdGljKSA9PiBQLmRpbWFwKHRoZXJlLCBiYWNrLCBvcHRpYykpO1xuZXhwb3J0IGNvbnN0IGxlbnMgPSAodmlldywgdXBkYXRlKSA9PiB3aXRoVGFnKCdMZW5zJywgKFAsIG9wdGljKSA9PiBQLmRpbWFwKCh4KSA9PiBbdmlldyh4KSwgeF0sIHVwZGF0ZSwgUC5maXJzdChvcHRpYykpKTtcbmNvbnN0IHByaXNtID0gKG1hdGNoLCBidWlsZCkgPT4gd2l0aFRhZygnUHJpc20nLCAoUCwgb3B0aWMpID0+IFAuZGltYXAobWF0Y2gsICh4KSA9PiBlaXRoZXIoaWQsIGJ1aWxkLCB4KSwgUC5yaWdodChvcHRpYykpKTtcbmV4cG9ydCBjb25zdCBlbGVtcyA9IC8qIEBfX1BVUkVfXyAqLyB3aXRoVGFnKCdUcmF2ZXJzYWwnLCAoUCwgb3B0aWMpID0+IFAuZGltYXAoaWQsIGlkLCBQLndhbmRlcihvcHRpYykpKTtcbmV4cG9ydCBjb25zdCB0byA9IChmbikgPT4gd2l0aFRhZygnR2V0dGVyJywgKFAsIG9wdGljKSA9PiBQLmRpbWFwKGZuLCBpZCwgb3B0aWMpKTtcbi8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vXG5leHBvcnQgY29uc3QgbW9kaWZ5ID0gKG9wdGljLCBmbiwgc291cmNlKSA9PiBvcHRpYyhwcm9mdW5jdG9yRm4sIGZuKShzb3VyY2UpO1xuZXhwb3J0IGNvbnN0IHNldCA9IChvcHRpYywgdmFsdWUsIHNvdXJjZSkgPT4gb3B0aWMocHJvZnVuY3RvckZuLCAoKSA9PiB2YWx1ZSkoc291cmNlKTtcbmV4cG9ydCBjb25zdCByZW1vdmUgPSAob3B0aWMsIHNvdXJjZSkgPT4gc2V0KG9wdGljLCByZW1vdmVNZSwgc291cmNlKTtcbmV4cG9ydCBjb25zdCBnZXQgPSAob3B0aWMsIHNvdXJjZSkgPT4gb3B0aWMocHJvZnVuY3RvckNvbnN0KHt9KSwgaWQpKHNvdXJjZSk7XG5leHBvcnQgY29uc3QgcHJldmlldyA9IChvcHRpYywgc291cmNlKSA9PiBvcHRpYyhwcm9mdW5jdG9yQ29uc3QobW9ub2lkRmlyc3QpLCBpZCkoc291cmNlKTtcbmV4cG9ydCBjb25zdCBjb2xsZWN0ID0gKG9wdGljLCBzb3VyY2UpID0+IG9wdGljKHByb2Z1bmN0b3JDb25zdChtb25vaWRBcnJheSksICh4KSA9PiBbeF0pKHNvdXJjZSk7XG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuZXhwb3J0IGNvbnN0IGluZGV4ZWQgPSAvKiBAX19QVVJFX18gKi8gaXNvKCh2YWx1ZSkgPT4gdmFsdWUubWFwKCh2LCBrKSA9PiBbaywgdl0pLCAodmFsdWUpID0+IHtcbiAgICBjb25zdCBzb3J0ZWQgPSBbLi4udmFsdWVdLnNvcnQoKGEsIGIpID0+IGFbMF0gLSBiWzBdKTtcbiAgICBjb25zdCByZXN1bHQgPSBbXTtcbiAgICBmb3IgKGxldCBpID0gMDsgaSA8IHNvcnRlZC5sZW5ndGg7ICsraSkge1xuICAgICAgICBpZiAoaSA9PT0gc29ydGVkLmxlbmd0aCAtIDEgfHwgc29ydGVkW2ldWzBdICE9PSBzb3J0ZWRbaSArIDFdWzBdKSB7XG4gICAgICAgICAgICByZXN1bHQucHVzaChzb3J0ZWRbaV1bMV0pO1xuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59KTtcbmV4cG9ydCBjb25zdCBwcm9wID0gKGtleSkgPT4gbGVucygoc291cmNlKSA9PiBzb3VyY2Vba2V5XSwgKFt2YWx1ZSwgc291cmNlXSkgPT4gKE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgc291cmNlKSwgeyBba2V5XTogdmFsdWUgfSkpKTtcbmV4cG9ydCBjb25zdCBwaWNrID0gKGtleXMpID0+IGxlbnMoKHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0ge307XG4gICAgZm9yIChjb25zdCBrZXkgb2Yga2V5cykge1xuICAgICAgICB2YWx1ZVtrZXldID0gc291cmNlW2tleV07XG4gICAgfVxuICAgIHJldHVybiB2YWx1ZTtcbn0sIChbdmFsdWUsIHNvdXJjZV0pID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSBPYmplY3QuYXNzaWduKHt9LCBzb3VyY2UpO1xuICAgIGZvciAoY29uc3Qga2V5IG9mIGtleXMpIHtcbiAgICAgICAgZGVsZXRlIHJlc3VsdFtrZXldO1xuICAgIH1cbiAgICByZXR1cm4gT2JqZWN0LmFzc2lnbihyZXN1bHQsIHZhbHVlKTtcbn0pO1xuZXhwb3J0IGNvbnN0IG50aCA9IChuKSA9PiBsZW5zKCh2YWx1ZSkgPT4gdmFsdWVbbl0sIChbdmFsdWUsIHNvdXJjZV0pID0+IHtcbiAgICBjb25zdCByZXN1bHQgPSBzb3VyY2Uuc2xpY2UoKTtcbiAgICByZXN1bHRbbl0gPSB2YWx1ZTtcbiAgICByZXR1cm4gcmVzdWx0O1xufSk7XG5jb25zdCBmc3QgPSAvKiBAX19QVVJFX18gKi8gbnRoKDApO1xuZXhwb3J0IGNvbnN0IHdoZW4gPSAocHJlZCkgPT4gcHJpc20oKHgpID0+IChwcmVkKHgpID8gUmlnaHQoeCkgOiBMZWZ0KHgpKSwgaWQpO1xuY29uc3Qgbm9NYXRjaCA9IC8qIEBfX1BVUkVfXyAqLyBTeW1ib2woJ19fbm9fbWF0Y2hfXycpO1xuY29uc3QgbXVzdE1hdGNoID0gLyogQF9fUFVSRV9fICovIHdoZW4oKHNvdXJjZSkgPT4gc291cmNlICE9PSBub01hdGNoKTtcbmNvbnN0IHJlbW92ZU1lID0gLyogQF9fUFVSRV9fICovIFN5bWJvbCgnX19yZW1vdmVfbWVfXycpO1xuZXhwb3J0IGNvbnN0IGF0ID0gKGkpID0+IHJlbW92YWJsZShjb21wb3NlKGxlbnMoKHNvdXJjZSkgPT4gKDAgPD0gaSAmJiBpIDwgc291cmNlLmxlbmd0aCA/IHNvdXJjZVtpXSA6IG5vTWF0Y2gpLCAoW3ZhbHVlLCBzb3VyY2VdKSA9PiB7XG4gICAgaWYgKHZhbHVlID09PSBub01hdGNoKSB7XG4gICAgICAgIHJldHVybiBzb3VyY2U7XG4gICAgfVxuICAgIGlmICh2YWx1ZSA9PT0gcmVtb3ZlTWUpIHtcbiAgICAgICAgaWYgKHR5cGVvZiBzb3VyY2UgPT09ICdzdHJpbmcnKSB7XG4gICAgICAgICAgICByZXR1cm4gc291cmNlLnN1YnN0cmluZygwLCBpKSArIHNvdXJjZS5zdWJzdHJpbmcoaSArIDEpO1xuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmV0dXJuIFsuLi5zb3VyY2Uuc2xpY2UoMCwgaSksIC4uLnNvdXJjZS5zbGljZShpICsgMSldO1xuICAgICAgICB9XG4gICAgfVxuICAgIGlmICh0eXBlb2Ygc291cmNlID09PSAnc3RyaW5nJykge1xuICAgICAgICBpZiAoaSA9PT0gMCkge1xuICAgICAgICAgICAgcmV0dXJuIHZhbHVlICsgc291cmNlLnN1YnN0cmluZygxKTtcbiAgICAgICAgfVxuICAgICAgICBpZiAoaSA9PT0gc291cmNlLmxlbmd0aCkge1xuICAgICAgICAgICAgcmV0dXJuIHNvdXJjZS5zdWJzdHJpbmcoMCwgaSAtIDEpICsgdmFsdWU7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHNvdXJjZS5zdWJzdHJpbmcoMCwgaSkgKyB2YWx1ZSArIHNvdXJjZS5zdWJzdHJpbmcoaSArIDEpO1xuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgY29uc3QgcmVzdWx0ID0gc291cmNlLnNsaWNlKCk7XG4gICAgICAgIHJlc3VsdFtpXSA9IHZhbHVlO1xuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH1cbn0pLCBtdXN0TWF0Y2gpKTtcbmV4cG9ydCBjb25zdCBhdEtleSA9IChrZXkpID0+IHJlbW92YWJsZShjb21wb3NlKGxlbnMoKHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IHZhbHVlID0gc291cmNlW2tleV07XG4gICAgcmV0dXJuIHZhbHVlICE9PSB1bmRlZmluZWQgPyB2YWx1ZSA6IG5vTWF0Y2g7XG59LCAoW3ZhbHVlLCBzb3VyY2VdKSA9PiB7XG4gICAgaWYgKHZhbHVlID09PSBub01hdGNoKSB7XG4gICAgICAgIHJldHVybiBzb3VyY2U7XG4gICAgfVxuICAgIGlmICh2YWx1ZSA9PT0gcmVtb3ZlTWUpIHtcbiAgICAgICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9uby11bnVzZWQtdmFyc1xuICAgICAgICBjb25zdCBfYSA9IHNvdXJjZSwgX2IgPSBrZXksIF8gPSBfYVtfYl0sIHJlc3QgPSBfX3Jlc3QoX2EsIFt0eXBlb2YgX2IgPT09IFwic3ltYm9sXCIgPyBfYiA6IF9iICsgXCJcIl0pO1xuICAgICAgICByZXR1cm4gcmVzdDtcbiAgICB9XG4gICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oT2JqZWN0LmFzc2lnbih7fSwgc291cmNlKSwgeyBba2V5XTogdmFsdWUgfSk7XG59KSwgbXVzdE1hdGNoKSk7XG5leHBvcnQgY29uc3Qgb3B0aW9uYWwgPSAvKiBAX19QVVJFX18gKi8gcHJpc20oKHNvdXJjZSkgPT4gKHNvdXJjZSA9PT0gdW5kZWZpbmVkID8gTGVmdCh1bmRlZmluZWQpIDogUmlnaHQoc291cmNlKSksIGlkKTtcbmV4cG9ydCBjb25zdCBndWFyZCA9IChmbikgPT4gcHJpc20oKHNvdXJjZSkgPT4gKGZuKHNvdXJjZSkgPyBSaWdodChzb3VyY2UpIDogTGVmdChzb3VyY2UpKSwgaWQpO1xuZXhwb3J0IGNvbnN0IGZpbmQgPSAocHJlZGljYXRlKSA9PiByZW1vdmFibGUoY29tcG9zZShsZW5zKChzb3VyY2UpID0+IHtcbiAgICBjb25zdCBpbmRleCA9IHNvdXJjZS5maW5kSW5kZXgocHJlZGljYXRlKTtcbiAgICBpZiAoaW5kZXggPT09IC0xKSB7XG4gICAgICAgIHJldHVybiBbbm9NYXRjaCwgLTFdO1xuICAgIH1cbiAgICByZXR1cm4gW3NvdXJjZVtpbmRleF0sIGluZGV4XTtcbn0sIChbW3ZhbHVlLCBpbmRleF0sIHNvdXJjZV0pID0+IHtcbiAgICBpZiAodmFsdWUgPT09IG5vTWF0Y2gpIHtcbiAgICAgICAgcmV0dXJuIHNvdXJjZTtcbiAgICB9XG4gICAgaWYgKHZhbHVlID09PSByZW1vdmVNZSkge1xuICAgICAgICByZXR1cm4gWy4uLnNvdXJjZS5zbGljZSgwLCBpbmRleCksIC4uLnNvdXJjZS5zbGljZShpbmRleCArIDEpXTtcbiAgICB9XG4gICAgY29uc3QgcmVzdWx0ID0gc291cmNlLnNsaWNlKCk7XG4gICAgcmVzdWx0W2luZGV4XSA9IHZhbHVlO1xuICAgIHJldHVybiByZXN1bHQ7XG59KSwgZnN0LCBtdXN0TWF0Y2gpKTtcbmV4cG9ydCBjb25zdCBmaWx0ZXIgPSAocHJlZGljYXRlKSA9PiBjb21wb3NlKGxlbnMoKHNvdXJjZSkgPT4ge1xuICAgIGNvbnN0IGluZGV4ZXMgPSBzb3VyY2VcbiAgICAgICAgLm1hcCgoaXRlbSwgaW5kZXgpID0+IChwcmVkaWNhdGUoaXRlbSkgPyBpbmRleCA6IG51bGwpKVxuICAgICAgICAuZmlsdGVyKChpbmRleCkgPT4gaW5kZXggIT0gbnVsbCk7XG4gICAgcmV0dXJuIFtpbmRleGVzLm1hcCgoaW5kZXgpID0+IHNvdXJjZVtpbmRleF0pLCBpbmRleGVzXTtcbn0sIChbW3ZhbHVlcywgaW5kZXhlc10sIHNvdXJjZV0pID0+IHtcbiAgICBjb25zdCBzbiA9IHNvdXJjZS5sZW5ndGgsIHZuID0gdmFsdWVzLmxlbmd0aDtcbiAgICBsZXQgc2kgPSAwLCBpaSA9IDAsIHZpID0gMDtcbiAgICBjb25zdCByZXN1bHQgPSBbXTtcbiAgICB3aGlsZSAoc2kgPCBzbikge1xuICAgICAgICBpZiAoaW5kZXhlc1tpaV0gPT09IHNpKSB7XG4gICAgICAgICAgICArK2lpO1xuICAgICAgICAgICAgaWYgKHZpIDwgdm4pIHtcbiAgICAgICAgICAgICAgICByZXN1bHQucHVzaCh2YWx1ZXNbdmldKTtcbiAgICAgICAgICAgICAgICArK3ZpO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgcmVzdWx0LnB1c2goc291cmNlW3NpXSk7XG4gICAgICAgIH1cbiAgICAgICAgKytzaTtcbiAgICB9XG4gICAgd2hpbGUgKHZpIDwgdm4pIHtcbiAgICAgICAgcmVzdWx0LnB1c2godmFsdWVzW3ZpKytdKTtcbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbn0pLCBmc3QpO1xuZXhwb3J0IGNvbnN0IHZhbHVlT3IgPSAoZGVmYXVsdFZhbHVlKSA9PiBsZW5zKChzb3VyY2UpID0+IChzb3VyY2UgPT09IHVuZGVmaW5lZCA/IGRlZmF1bHRWYWx1ZSA6IHNvdXJjZSksIChbdmFsdWUsIF9zb3VyY2VdKSA9PiB2YWx1ZSk7XG5leHBvcnQgY29uc3QgcGFydHNPZiA9ICh0cmF2ZXJzYWwpID0+IGNvbXBvc2UobGVucygoc291cmNlKSA9PiB7XG4gICAgY29uc3QgdmFsdWUgPSBjb2xsZWN0KHRyYXZlcnNhbCwgc291cmNlKTtcbiAgICByZXR1cm4gW3ZhbHVlLCB2YWx1ZS5sZW5ndGhdO1xufSwgKFtbdmFsdWUsIG9yaWdpbmFsTGVuZ3RoXSwgc291cmNlXSkgPT4ge1xuICAgIGlmICh2YWx1ZS5sZW5ndGggIT09IG9yaWdpbmFsTGVuZ3RoKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcignY2Fubm90IGFkZC9yZW1vdmUgZWxlbWVudHMgdGhyb3VnaCBwYXJ0c09mJyk7XG4gICAgfVxuICAgIGxldCBpID0gMDtcbiAgICByZXR1cm4gbW9kaWZ5KHRyYXZlcnNhbCwgKCkgPT4gdmFsdWVbaSsrXSwgc291cmNlKTtcbn0pLCBmc3QpO1xuZXhwb3J0IGNvbnN0IHJlcmVhZCA9IChmbikgPT4gbGVucygoc291cmNlKSA9PiBmbihzb3VyY2UpLCAoW3ZhbHVlLCBfXSkgPT4gdmFsdWUpO1xuZXhwb3J0IGNvbnN0IHJld3JpdGUgPSAoZm4pID0+IGxlbnMoKHNvdXJjZSkgPT4gc291cmNlLCAoW3ZhbHVlLCBfXSkgPT4gZm4odmFsdWUpKTtcbmV4cG9ydCBjb25zdCBwcmVwZW5kVG8gPSAvKiBAX19QVVJFX18gKi8gbGVucygoX3NvdXJjZSkgPT4gdW5kZWZpbmVkLCAoW3ZhbHVlLCBzb3VyY2VdKSA9PiB7XG4gICAgaWYgKHZhbHVlID09PSB1bmRlZmluZWQpXG4gICAgICAgIHJldHVybiBzb3VyY2U7XG4gICAgcmV0dXJuIFt2YWx1ZSwgLi4uc291cmNlXTtcbn0pO1xuZXhwb3J0IGNvbnN0IGFwcGVuZFRvID0gLyogQF9fUFVSRV9fICovIGxlbnMoKF9zb3VyY2UpID0+IHVuZGVmaW5lZCwgKFt2YWx1ZSwgc291cmNlXSkgPT4ge1xuICAgIGlmICh2YWx1ZSA9PT0gdW5kZWZpbmVkKVxuICAgICAgICByZXR1cm4gc291cmNlO1xuICAgIHJldHVybiBbLi4uc291cmNlLCB2YWx1ZV07XG59KTtcbmV4cG9ydCBjb25zdCBjaGFycyA9IC8qIEBfX1BVUkVfXyAqLyBjb21wb3NlKGlzbygocykgPT4gcy5zcGxpdCgnJyksIChhKSA9PiBhLmpvaW4oJycpKSwgZWxlbXMpO1xuZXhwb3J0IGNvbnN0IHdvcmRzID0gLyogQF9fUFVSRV9fICovIGNvbXBvc2UoaXNvKChzKSA9PiBzLnNwbGl0KC9cXGIvKSwgKGEpID0+IGEuam9pbignJykpLCBlbGVtcywgd2hlbigocykgPT4gIS9cXHMrLy50ZXN0KHMpKSk7XG4vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vLy8vL1xuZXhwb3J0IGNsYXNzIE9wdGljIHtcbiAgICBjb25zdHJ1Y3RvcihfcmVmKSB7XG4gICAgICAgIHRoaXMuX3JlZiA9IF9yZWY7XG4gICAgfVxuICAgIGdldCBfdGFnKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fcmVmLl90YWc7XG4gICAgfVxuICAgIGdldCBfcmVtb3ZhYmxlKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5fcmVmLl9yZW1vdmFibGU7XG4gICAgfVxuICAgIGNvbXBvc2Uob3RoZXIpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgb3RoZXIuX3JlZikpO1xuICAgIH1cbiAgICBpc28odGhlcmUsIGJhY2spIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgaXNvKHRoZXJlLCBiYWNrKSkpO1xuICAgIH1cbiAgICBsZW5zKHZpZXcsIHNldCkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBsZW5zKHZpZXcsIChbdmFsdWUsIHNvdXJjZV0pID0+IHNldChzb3VyY2UsIHZhbHVlKSkpKTtcbiAgICB9XG4gICAgaW5kZXhlZCgpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgaW5kZXhlZCkpO1xuICAgIH1cbiAgICBwcm9wKGtleSkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBwcm9wKGtleSkpKTtcbiAgICB9XG4gICAgcGF0aCguLi5rZXlzKSB7XG4gICAgICAgIGlmIChrZXlzLmxlbmd0aCA9PT0gMSkge1xuICAgICAgICAgICAga2V5cyA9IGtleXNbMF0uc3BsaXQoJy4nKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGtleXMucmVkdWNlKChyZWYsIGtleSkgPT4gY29tcG9zZShyZWYsIHByb3Aoa2V5KSksIHRoaXMuX3JlZikpO1xuICAgIH1cbiAgICBwaWNrKGtleXMpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgcGljayhrZXlzKSkpO1xuICAgIH1cbiAgICBudGgobikge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBudGgobikpKTtcbiAgICB9XG4gICAgZmlsdGVyKHByZWRpY2F0ZSkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBmaWx0ZXIocHJlZGljYXRlKSkpO1xuICAgIH1cbiAgICB2YWx1ZU9yKGRlZmF1bHRWYWx1ZSkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCB2YWx1ZU9yKGRlZmF1bHRWYWx1ZSkpKTtcbiAgICB9XG4gICAgcGFydHNPZih0cmF2ZXJzYWxPckZuKSB7XG4gICAgICAgIGNvbnN0IHRyYXZlcnNhbCA9IHR5cGVvZiB0cmF2ZXJzYWxPckZuID09PSAnZnVuY3Rpb24nID8gdHJhdmVyc2FsT3JGbihvcHRpYykgOiB0cmF2ZXJzYWxPckZuO1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBwYXJ0c09mKHRyYXZlcnNhbC5fcmVmKSkpO1xuICAgIH1cbiAgICByZXJlYWQoZm4pIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgcmVyZWFkKGZuKSkpO1xuICAgIH1cbiAgICByZXdyaXRlKGZuKSB7XG4gICAgICAgIHJldHVybiBuZXcgT3B0aWMoY29tcG9zZSh0aGlzLl9yZWYsIHJld3JpdGUoZm4pKSk7XG4gICAgfVxuICAgIG9wdGlvbmFsKCkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBvcHRpb25hbCkpO1xuICAgIH1cbiAgICBndWFyZF8oKSB7XG4gICAgICAgIHJldHVybiAoZm4pID0+IHRoaXMuZ3VhcmQoZm4pO1xuICAgIH1cbiAgICBndWFyZChmbikge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBndWFyZChmbikpKTtcbiAgICB9XG4gICAgYXQoaSkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBhdChpKSkpO1xuICAgIH1cbiAgICBoZWFkKCkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBhdCgwKSkpO1xuICAgIH1cbiAgICBpbmRleChpKSB7XG4gICAgICAgIHJldHVybiBuZXcgT3B0aWMoY29tcG9zZSh0aGlzLl9yZWYsIGF0KGkpKSk7XG4gICAgfVxuICAgIGZpbmQocHJlZGljYXRlKSB7XG4gICAgICAgIHJldHVybiBuZXcgT3B0aWMoY29tcG9zZSh0aGlzLl9yZWYsIGZpbmQocHJlZGljYXRlKSkpO1xuICAgIH1cbiAgICBlbGVtcygpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgZWxlbXMpKTtcbiAgICB9XG4gICAgdG8oZm4pIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgdG8oZm4pKSk7XG4gICAgfVxuICAgIHdoZW4ocHJlZGljYXRlKSB7XG4gICAgICAgIHJldHVybiBuZXcgT3B0aWMoY29tcG9zZSh0aGlzLl9yZWYsIHdoZW4ocHJlZGljYXRlKSkpO1xuICAgIH1cbiAgICBjaGFycygpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgY2hhcnMpKTtcbiAgICB9XG4gICAgd29yZHMoKSB7XG4gICAgICAgIHJldHVybiBuZXcgT3B0aWMoY29tcG9zZSh0aGlzLl9yZWYsIHdvcmRzKSk7XG4gICAgfVxuICAgIHByZXBlbmRUbygpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBPcHRpYyhjb21wb3NlKHRoaXMuX3JlZiwgcHJlcGVuZFRvKSk7XG4gICAgfVxuICAgIGFwcGVuZFRvKCkge1xuICAgICAgICByZXR1cm4gbmV3IE9wdGljKGNvbXBvc2UodGhpcy5fcmVmLCBhcHBlbmRUbykpO1xuICAgIH1cbn1cbmV4cG9ydCBjb25zdCBvcHRpYyA9IC8qIEBfX1BVUkVfXyAqLyBuZXcgT3B0aWMoZXEpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/internals.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/standalone/pipe.js":
/*!***********************************************************************************************!*\
  !*** ./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/standalone/pipe.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pipe: () => (/* binding */ pipe)\n/* harmony export */ });\n/* eslint-disable @typescript-eslint/no-non-null-assertion, @typescript-eslint/ban-types */\nfunction pipe(a, ab, bc, cd, de, ef, fg, gh, hi, ij) {\n    switch (arguments.length) {\n        case 1:\n            return a;\n        case 2:\n            return ab(a);\n        case 3:\n            return bc(ab(a));\n        case 4:\n            return cd(bc(ab(a)));\n        case 5:\n            return de(cd(bc(ab(a))));\n        case 6:\n            return ef(de(cd(bc(ab(a)))));\n        case 7:\n            return fg(ef(de(cd(bc(ab(a))))));\n        case 8:\n            return gh(fg(ef(de(cd(bc(ab(a)))))));\n        case 9:\n            return hi(gh(fg(ef(de(cd(bc(ab(a))))))));\n        case 10:\n            return ij(hi(gh(fg(ef(de(cd(bc(ab(a)))))))));\n    }\n    return;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb3B0aWNzLXRzQDIuNC4xL25vZGVfbW9kdWxlcy9vcHRpY3MtdHMvZGlzdC9tanMvc3RhbmRhbG9uZS9waXBlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vcHJlc2VudGF0aW9uLy4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGljcy10c0AyLjQuMS9ub2RlX21vZHVsZXMvb3B0aWNzLXRzL2Rpc3QvbWpzL3N0YW5kYWxvbmUvcGlwZS5qcz8xNzg2Il0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby1ub24tbnVsbC1hc3NlcnRpb24sIEB0eXBlc2NyaXB0LWVzbGludC9iYW4tdHlwZXMgKi9cbmV4cG9ydCBmdW5jdGlvbiBwaXBlKGEsIGFiLCBiYywgY2QsIGRlLCBlZiwgZmcsIGdoLCBoaSwgaWopIHtcbiAgICBzd2l0Y2ggKGFyZ3VtZW50cy5sZW5ndGgpIHtcbiAgICAgICAgY2FzZSAxOlxuICAgICAgICAgICAgcmV0dXJuIGE7XG4gICAgICAgIGNhc2UgMjpcbiAgICAgICAgICAgIHJldHVybiBhYihhKTtcbiAgICAgICAgY2FzZSAzOlxuICAgICAgICAgICAgcmV0dXJuIGJjKGFiKGEpKTtcbiAgICAgICAgY2FzZSA0OlxuICAgICAgICAgICAgcmV0dXJuIGNkKGJjKGFiKGEpKSk7XG4gICAgICAgIGNhc2UgNTpcbiAgICAgICAgICAgIHJldHVybiBkZShjZChiYyhhYihhKSkpKTtcbiAgICAgICAgY2FzZSA2OlxuICAgICAgICAgICAgcmV0dXJuIGVmKGRlKGNkKGJjKGFiKGEpKSkpKTtcbiAgICAgICAgY2FzZSA3OlxuICAgICAgICAgICAgcmV0dXJuIGZnKGVmKGRlKGNkKGJjKGFiKGEpKSkpKSk7XG4gICAgICAgIGNhc2UgODpcbiAgICAgICAgICAgIHJldHVybiBnaChmZyhlZihkZShjZChiYyhhYihhKSkpKSkpKTtcbiAgICAgICAgY2FzZSA5OlxuICAgICAgICAgICAgcmV0dXJuIGhpKGdoKGZnKGVmKGRlKGNkKGJjKGFiKGEpKSkpKSkpKTtcbiAgICAgICAgY2FzZSAxMDpcbiAgICAgICAgICAgIHJldHVybiBpaihoaShnaChmZyhlZihkZShjZChiYyhhYihhKSkpKSkpKSkpO1xuICAgIH1cbiAgICByZXR1cm47XG59XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/standalone/pipe.js\n");

/***/ })

};
;