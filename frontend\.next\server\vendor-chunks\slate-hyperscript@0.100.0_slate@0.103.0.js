"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/slate-hyperscript@0.100.0_slate@0.103.0";
exports.ids = ["vendor-chunks/slate-hyperscript@0.100.0_slate@0.103.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/slate-hyperscript@0.100.0_slate@0.103.0/node_modules/slate-hyperscript/dist/index.es.js":
/*!********************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/slate-hyperscript@0.100.0_slate@0.103.0/node_modules/slate-hyperscript/dist/index.es.js ***!
  \********************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEditor: () => (/* binding */ createEditor),\n/* harmony export */   createHyperscript: () => (/* binding */ createHyperscript),\n/* harmony export */   createText: () => (/* binding */ createText),\n/* harmony export */   jsx: () => (/* binding */ jsx)\n/* harmony export */ });\n/* harmony import */ var is_plain_object__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-plain-object */ \"(ssr)/./node_modules/.pnpm/is-plain-object@5.0.0/node_modules/is-plain-object/dist/is-plain-object.mjs\");\n/* harmony import */ var slate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! slate */ \"(ssr)/./node_modules/.pnpm/slate@0.103.0/node_modules/slate/dist/index.es.js\");\n\n\n\nfunction _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\n\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\n\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\n\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\n\n/**\n * A weak map to hold anchor tokens.\n */\nvar ANCHOR = new WeakMap();\n/**\n * A weak map to hold focus tokens.\n */\nvar FOCUS = new WeakMap();\n/**\n * All tokens inherit from a single constructor for `instanceof` checking.\n */\nclass Token {}\n/**\n * Anchor tokens represent the selection's anchor point.\n */\nclass AnchorToken extends Token {\n  constructor() {\n    var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    super();\n    _defineProperty(this, \"offset\", void 0);\n    _defineProperty(this, \"path\", void 0);\n    var {\n      offset,\n      path\n    } = props;\n    this.offset = offset;\n    this.path = path;\n  }\n}\n/**\n * Focus tokens represent the selection's focus point.\n */\nclass FocusToken extends Token {\n  constructor() {\n    var props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    super();\n    _defineProperty(this, \"offset\", void 0);\n    _defineProperty(this, \"path\", void 0);\n    var {\n      offset,\n      path\n    } = props;\n    this.offset = offset;\n    this.path = path;\n  }\n}\n/**\n * Add an anchor token to the end of a text node.\n */\nvar addAnchorToken = (text, token) => {\n  var offset = text.text.length;\n  ANCHOR.set(text, [offset, token]);\n};\n/**\n * Get the offset if a text node has an associated anchor token.\n */\nvar getAnchorOffset = text => {\n  return ANCHOR.get(text);\n};\n/**\n * Add a focus token to the end of a text node.\n */\nvar addFocusToken = (text, token) => {\n  var offset = text.text.length;\n  FOCUS.set(text, [offset, token]);\n};\n/**\n * Get the offset if a text node has an associated focus token.\n */\nvar getFocusOffset = text => {\n  return FOCUS.get(text);\n};\n\nfunction ownKeys$1(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread$1(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys$1(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys$1(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n * Resolve the descedants of a node by normalizing the children that can be\n * passed into a hyperscript creator function.\n */\nvar STRINGS = new WeakSet();\nvar resolveDescendants = children => {\n  var nodes = [];\n  var addChild = child => {\n    if (child == null) {\n      return;\n    }\n    var prev = nodes[nodes.length - 1];\n    if (typeof child === 'string') {\n      var text = {\n        text: child\n      };\n      STRINGS.add(text);\n      child = text;\n    }\n    if (slate__WEBPACK_IMPORTED_MODULE_1__.Text.isText(child)) {\n      var c = child; // HACK: fix typescript complaining\n      if (slate__WEBPACK_IMPORTED_MODULE_1__.Text.isText(prev) && STRINGS.has(prev) && STRINGS.has(c) && slate__WEBPACK_IMPORTED_MODULE_1__.Text.equals(prev, c, {\n        loose: true\n      })) {\n        prev.text += c.text;\n      } else {\n        nodes.push(c);\n      }\n    } else if (slate__WEBPACK_IMPORTED_MODULE_1__.Element.isElement(child)) {\n      nodes.push(child);\n    } else if (child instanceof Token) {\n      var n = nodes[nodes.length - 1];\n      if (!slate__WEBPACK_IMPORTED_MODULE_1__.Text.isText(n)) {\n        addChild('');\n        n = nodes[nodes.length - 1];\n      }\n      if (child instanceof AnchorToken) {\n        addAnchorToken(n, child);\n      } else if (child instanceof FocusToken) {\n        addFocusToken(n, child);\n      }\n    } else {\n      throw new Error(\"Unexpected hyperscript child object: \".concat(child));\n    }\n  };\n  for (var child of children.flat(Infinity)) {\n    addChild(child);\n  }\n  return nodes;\n};\n/**\n * Create an anchor token.\n */\nfunction createAnchor(tagName, attributes, children) {\n  return new AnchorToken(attributes);\n}\n/**\n * Create an anchor and a focus token.\n */\nfunction createCursor(tagName, attributes, children) {\n  return [new AnchorToken(attributes), new FocusToken(attributes)];\n}\n/**\n * Create an `Element` object.\n */\nfunction createElement(tagName, attributes, children) {\n  return _objectSpread$1(_objectSpread$1({}, attributes), {}, {\n    children: resolveDescendants(children)\n  });\n}\n/**\n * Create a focus token.\n */\nfunction createFocus(tagName, attributes, children) {\n  return new FocusToken(attributes);\n}\n/**\n * Create a fragment.\n */\nfunction createFragment(tagName, attributes, children) {\n  return resolveDescendants(children);\n}\n/**\n * Create a `Selection` object.\n */\nfunction createSelection(tagName, attributes, children) {\n  var anchor = children.find(c => c instanceof AnchorToken);\n  var focus = children.find(c => c instanceof FocusToken);\n  if (!anchor || anchor.offset == null || anchor.path == null) {\n    throw new Error(\"The <selection> hyperscript tag must have an <anchor> tag as a child with `path` and `offset` attributes defined.\");\n  }\n  if (!focus || focus.offset == null || focus.path == null) {\n    throw new Error(\"The <selection> hyperscript tag must have a <focus> tag as a child with `path` and `offset` attributes defined.\");\n  }\n  return _objectSpread$1({\n    anchor: {\n      offset: anchor.offset,\n      path: anchor.path\n    },\n    focus: {\n      offset: focus.offset,\n      path: focus.path\n    }\n  }, attributes);\n}\n/**\n * Create a `Text` object.\n */\nfunction createText(tagName, attributes, children) {\n  var nodes = resolveDescendants(children);\n  if (nodes.length > 1) {\n    throw new Error(\"The <text> hyperscript tag must only contain a single node's worth of children.\");\n  }\n  var [node] = nodes;\n  if (node == null) {\n    node = {\n      text: ''\n    };\n  }\n  if (!slate__WEBPACK_IMPORTED_MODULE_1__.Text.isText(node)) {\n    throw new Error(\"\\n    The <text> hyperscript tag can only contain text content as children.\");\n  }\n  // COMPAT: If they used the <text> tag we want to guarantee that it won't be\n  // merge with other string children.\n  STRINGS.delete(node);\n  Object.assign(node, attributes);\n  return node;\n}\n/**\n * Create a top-level `Editor` object.\n */\nvar createEditor = makeEditor => (tagName, attributes, children) => {\n  var otherChildren = [];\n  var selectionChild;\n  for (var child of children) {\n    if (slate__WEBPACK_IMPORTED_MODULE_1__.Range.isRange(child)) {\n      selectionChild = child;\n    } else {\n      otherChildren.push(child);\n    }\n  }\n  var descendants = resolveDescendants(otherChildren);\n  var selection = {};\n  var editor = makeEditor();\n  Object.assign(editor, attributes);\n  editor.children = descendants;\n  // Search the document's texts to see if any of them have tokens associated\n  // that need incorporated into the selection.\n  for (var [node, path] of slate__WEBPACK_IMPORTED_MODULE_1__.Node.texts(editor)) {\n    var anchor = getAnchorOffset(node);\n    var focus = getFocusOffset(node);\n    if (anchor != null) {\n      var [offset] = anchor;\n      selection.anchor = {\n        path,\n        offset\n      };\n    }\n    if (focus != null) {\n      var [_offset] = focus;\n      selection.focus = {\n        path,\n        offset: _offset\n      };\n    }\n  }\n  if (selection.anchor && !selection.focus) {\n    throw new Error(\"Slate hyperscript ranges must have both `<anchor />` and `<focus />` defined if one is defined, but you only defined `<anchor />`. For collapsed selections, use `<cursor />` instead.\");\n  }\n  if (!selection.anchor && selection.focus) {\n    throw new Error(\"Slate hyperscript ranges must have both `<anchor />` and `<focus />` defined if one is defined, but you only defined `<focus />`. For collapsed selections, use `<cursor />` instead.\");\n  }\n  if (selectionChild != null) {\n    editor.selection = selectionChild;\n  } else if (slate__WEBPACK_IMPORTED_MODULE_1__.Range.isRange(selection)) {\n    editor.selection = selection;\n  }\n  return editor;\n};\n\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\n/**\n * The default creators for Slate objects.\n */\nvar DEFAULT_CREATORS = {\n  anchor: createAnchor,\n  cursor: createCursor,\n  editor: createEditor(slate__WEBPACK_IMPORTED_MODULE_1__.createEditor),\n  element: createElement,\n  focus: createFocus,\n  fragment: createFragment,\n  selection: createSelection,\n  text: createText\n};\n/**\n * Create a Slate hyperscript function with `options`.\n */\nvar createHyperscript = function createHyperscript() {\n  var options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  var {\n    elements = {}\n  } = options;\n  var elementCreators = normalizeElements(elements);\n  var creators = _objectSpread(_objectSpread(_objectSpread({}, DEFAULT_CREATORS), elementCreators), options.creators);\n  var jsx = createFactory(creators);\n  return jsx;\n};\n/**\n * Create a Slate hyperscript function with `options`.\n */\nvar createFactory = creators => {\n  var jsx = function jsx(tagName, attributes) {\n    for (var _len = arguments.length, children = new Array(_len > 2 ? _len - 2 : 0), _key = 2; _key < _len; _key++) {\n      children[_key - 2] = arguments[_key];\n    }\n    var creator = creators[tagName];\n    if (!creator) {\n      throw new Error(\"No hyperscript creator found for tag: <\".concat(tagName, \">\"));\n    }\n    if (attributes == null) {\n      attributes = {};\n    }\n    if (!(0,is_plain_object__WEBPACK_IMPORTED_MODULE_0__.isPlainObject)(attributes)) {\n      children = [attributes].concat(children);\n      attributes = {};\n    }\n    children = children.filter(child => Boolean(child)).flat();\n    var ret = creator(tagName, attributes, children);\n    return ret;\n  };\n  return jsx;\n};\n/**\n * Normalize a dictionary of element shorthands into creator functions.\n */\nvar normalizeElements = elements => {\n  var creators = {};\n  var _loop = function _loop() {\n    var props = elements[tagName];\n    if (typeof props !== 'object') {\n      throw new Error(\"Properties specified for a hyperscript shorthand should be an object, but for the custom element <\".concat(tagName, \">  tag you passed: \").concat(props));\n    }\n    creators[tagName] = (tagName, attributes, children) => {\n      return createElement('element', _objectSpread(_objectSpread({}, props), attributes), children);\n    };\n  };\n  for (var tagName in elements) {\n    _loop();\n  }\n  return creators;\n};\n\n/**\n * The default hyperscript factory that ships with Slate, without custom tags.\n */\nvar jsx = createHyperscript();\n\n\n//# sourceMappingURL=index.es.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/slate-hyperscript@0.100.0_slate@0.103.0/node_modules/slate-hyperscript/dist/index.es.js\n");

/***/ })

};
;