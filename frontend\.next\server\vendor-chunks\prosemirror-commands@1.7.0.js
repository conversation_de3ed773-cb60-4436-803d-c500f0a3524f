"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-commands@1.7.0";
exports.ids = ["vendor-chunks/prosemirror-commands@1.7.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-commands@1.7.0/node_modules/prosemirror-commands/dist/index.js":
/*!*******************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-commands@1.7.0/node_modules/prosemirror-commands/dist/index.js ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   autoJoin: () => (/* binding */ autoJoin),\n/* harmony export */   baseKeymap: () => (/* binding */ baseKeymap),\n/* harmony export */   chainCommands: () => (/* binding */ chainCommands),\n/* harmony export */   createParagraphNear: () => (/* binding */ createParagraphNear),\n/* harmony export */   deleteSelection: () => (/* binding */ deleteSelection),\n/* harmony export */   exitCode: () => (/* binding */ exitCode),\n/* harmony export */   joinBackward: () => (/* binding */ joinBackward),\n/* harmony export */   joinDown: () => (/* binding */ joinDown),\n/* harmony export */   joinForward: () => (/* binding */ joinForward),\n/* harmony export */   joinTextblockBackward: () => (/* binding */ joinTextblockBackward),\n/* harmony export */   joinTextblockForward: () => (/* binding */ joinTextblockForward),\n/* harmony export */   joinUp: () => (/* binding */ joinUp),\n/* harmony export */   lift: () => (/* binding */ lift),\n/* harmony export */   liftEmptyBlock: () => (/* binding */ liftEmptyBlock),\n/* harmony export */   macBaseKeymap: () => (/* binding */ macBaseKeymap),\n/* harmony export */   newlineInCode: () => (/* binding */ newlineInCode),\n/* harmony export */   pcBaseKeymap: () => (/* binding */ pcBaseKeymap),\n/* harmony export */   selectAll: () => (/* binding */ selectAll),\n/* harmony export */   selectNodeBackward: () => (/* binding */ selectNodeBackward),\n/* harmony export */   selectNodeForward: () => (/* binding */ selectNodeForward),\n/* harmony export */   selectParentNode: () => (/* binding */ selectParentNode),\n/* harmony export */   selectTextblockEnd: () => (/* binding */ selectTextblockEnd),\n/* harmony export */   selectTextblockStart: () => (/* binding */ selectTextblockStart),\n/* harmony export */   setBlockType: () => (/* binding */ setBlockType),\n/* harmony export */   splitBlock: () => (/* binding */ splitBlock),\n/* harmony export */   splitBlockAs: () => (/* binding */ splitBlockAs),\n/* harmony export */   splitBlockKeepMarks: () => (/* binding */ splitBlockKeepMarks),\n/* harmony export */   toggleMark: () => (/* binding */ toggleMark),\n/* harmony export */   wrapIn: () => (/* binding */ wrapIn)\n/* harmony export */ });\n/* harmony import */ var prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-transform */ \"(ssr)/./node_modules/.pnpm/prosemirror-transform@1.10.3/node_modules/prosemirror-transform/dist/index.js\");\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.0/node_modules/prosemirror-model/dist/index.js\");\n/* harmony import */ var prosemirror_state__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! prosemirror-state */ \"(ssr)/./node_modules/.pnpm/prosemirror-state@1.4.3/node_modules/prosemirror-state/dist/index.js\");\n\n\n\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(depth), $cursor.after(depth), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, beforePos, afterPos, prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(after))) {\n        let delStep = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.replaceStep)(state.doc, $cursor.before(), $cursor.after(), prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection) {\n        if (sel.node.isTextblock || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.joinPoint)(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !(0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection || state.selection instanceof prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canSplit)(tr.doc, splitPos, types.length, types);\n        }\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(conn[i].create(null, wrap));\n            wrap = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.liftTarget)(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new prosemirror_model__WEBPACK_IMPORTED_MODULE_2__.Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && (0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.findWrapping)(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new prosemirror_state__WEBPACK_IMPORTED_MODULE_1__.SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    let dropSpace = !(options && options.includeWhitespace);\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = dropSpace && start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = dropSpace && end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if ((0,prosemirror_transform__WEBPACK_IMPORTED_MODULE_0__.canJoin)(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-commands@1.7.0/node_modules/prosemirror-commands/dist/index.js\n");

/***/ })

};
;