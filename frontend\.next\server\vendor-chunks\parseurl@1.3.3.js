"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parseurl@1.3.3";
exports.ids = ["vendor-chunks/parseurl@1.3.3"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js":
/*!**************************************************************************!*\
  !*** ./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js ***!
  \**************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * parseurl\n * Copyright(c) 2014 Jonathan Ong\n * Copyright(c) 2014-2017 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar url = __webpack_require__(/*! url */ \"url\")\nvar parse = url.parse\nvar Url = url.Url\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = parseurl\nmodule.exports.original = originalurl\n\n/**\n * Parse the `req` url with memoization.\n *\n * @param {ServerRequest} req\n * @return {Object}\n * @public\n */\n\nfunction parseurl (req) {\n  var url = req.url\n\n  if (url === undefined) {\n    // URL is undefined\n    return undefined\n  }\n\n  var parsed = req._parsedUrl\n\n  if (fresh(url, parsed)) {\n    // Return cached URL parse\n    return parsed\n  }\n\n  // Parse the URL\n  parsed = fastparse(url)\n  parsed._raw = url\n\n  return (req._parsedUrl = parsed)\n};\n\n/**\n * Parse the `req` original url with fallback and memoization.\n *\n * @param {ServerRequest} req\n * @return {Object}\n * @public\n */\n\nfunction originalurl (req) {\n  var url = req.originalUrl\n\n  if (typeof url !== 'string') {\n    // Fallback\n    return parseurl(req)\n  }\n\n  var parsed = req._parsedOriginalUrl\n\n  if (fresh(url, parsed)) {\n    // Return cached URL parse\n    return parsed\n  }\n\n  // Parse the URL\n  parsed = fastparse(url)\n  parsed._raw = url\n\n  return (req._parsedOriginalUrl = parsed)\n};\n\n/**\n * Parse the `str` url with fast-path short-cut.\n *\n * @param {string} str\n * @return {Object}\n * @private\n */\n\nfunction fastparse (str) {\n  if (typeof str !== 'string' || str.charCodeAt(0) !== 0x2f /* / */) {\n    return parse(str)\n  }\n\n  var pathname = str\n  var query = null\n  var search = null\n\n  // This takes the regexp from https://github.com/joyent/node/pull/7878\n  // Which is /^(\\/[^?#\\s]*)(\\?[^#\\s]*)?$/\n  // And unrolls it into a for loop\n  for (var i = 1; i < str.length; i++) {\n    switch (str.charCodeAt(i)) {\n      case 0x3f: /* ?  */\n        if (search === null) {\n          pathname = str.substring(0, i)\n          query = str.substring(i + 1)\n          search = str.substring(i)\n        }\n        break\n      case 0x09: /* \\t */\n      case 0x0a: /* \\n */\n      case 0x0c: /* \\f */\n      case 0x0d: /* \\r */\n      case 0x20: /*    */\n      case 0x23: /* #  */\n      case 0xa0:\n      case 0xfeff:\n        return parse(str)\n    }\n  }\n\n  var url = Url !== undefined\n    ? new Url()\n    : {}\n\n  url.path = str\n  url.href = str\n  url.pathname = pathname\n\n  if (search !== null) {\n    url.query = query\n    url.search = search\n  }\n\n  return url\n}\n\n/**\n * Determine if parsed is still fresh for url.\n *\n * @param {string} url\n * @param {object} parsedUrl\n * @return {boolean}\n * @private\n */\n\nfunction fresh (url, parsedUrl) {\n  return typeof parsedUrl === 'object' &&\n    parsedUrl !== null &&\n    (Url === undefined || parsedUrl instanceof Url) &&\n    parsedUrl._raw === url\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/parseurl@1.3.3/node_modules/parseurl/index.js\n");

/***/ })

};
;