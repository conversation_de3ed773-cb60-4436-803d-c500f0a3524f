/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/cookie-signature@1.0.6";
exports.ids = ["vendor-chunks/cookie-signature@1.0.6"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/cookie-signature@1.0.6/node_modules/cookie-signature/index.js":
/*!******************************************************************************************!*\
  !*** ./node_modules/.pnpm/cookie-signature@1.0.6/node_modules/cookie-signature/index.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * Module dependencies.\n */\n\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\");\n\n/**\n * Sign the given `val` with `secret`.\n *\n * @param {String} val\n * @param {String} secret\n * @return {String}\n * @api private\n */\n\nexports.sign = function(val, secret){\n  if ('string' != typeof val) throw new TypeError(\"Cookie value must be provided as a string.\");\n  if ('string' != typeof secret) throw new TypeError(\"Secret string must be provided.\");\n  return val + '.' + crypto\n    .createHmac('sha256', secret)\n    .update(val)\n    .digest('base64')\n    .replace(/\\=+$/, '');\n};\n\n/**\n * Unsign and decode the given `val` with `secret`,\n * returning `false` if the signature is invalid.\n *\n * @param {String} val\n * @param {String} secret\n * @return {String|Boolean}\n * @api private\n */\n\nexports.unsign = function(val, secret){\n  if ('string' != typeof val) throw new TypeError(\"Signed cookie string must be provided.\");\n  if ('string' != typeof secret) throw new TypeError(\"Secret string must be provided.\");\n  var str = val.slice(0, val.lastIndexOf('.'))\n    , mac = exports.sign(str, secret);\n  \n  return sha1(mac) == sha1(val) ? str : false;\n};\n\n/**\n * Private\n */\n\nfunction sha1(str){\n  return crypto.createHash('sha1').update(str).digest('hex');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/cookie-signature@1.0.6/node_modules/cookie-signature/index.js\n");

/***/ })

};
;