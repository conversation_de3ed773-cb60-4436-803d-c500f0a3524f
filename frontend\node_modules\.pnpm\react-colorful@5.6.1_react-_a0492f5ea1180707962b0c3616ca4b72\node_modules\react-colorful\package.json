{"name": "react-colorful", "version": "5.6.1", "description": "🎨 A tiny (2,8 KB) color picker component for React and Preact apps. Fast, well-tested, dependency-free, mobile-friendly and accessible", "source": "src/index.ts", "main": "dist/index.js", "module": "dist/index.module.js", "esmodule": "dist/index.esmodule.js", "umd:main": "dist/index.umd.js", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "umd": "./dist/index.umd.js", "import": "./dist/index.mjs", "require": "./dist/index.js", "default": "./dist/index.module.js"}}, "scripts": {"lint": "eslint src/**/*.{ts,tsx} demo/src/**/*.{ts,tsx}", "size": "npm run build && size-limit", "check-types": "tsc --noEmit true", "test": "jest tests --coverage", "build": "del-cli 'dist/*' && microbundle build --entry src/index.ts --jsx React.createElement --name react-colorful --css inline --tsconfig tsconfig.build.json", "postbuild": "cpy dist/index.module.js dist --rename=index.mjs && cpy dist/index.module.js.map dist --rename=index.mjs.map", "prepublishOnly": "npm run build", "check-release": "npm publish --dry-run", "release": "npm publish", "start-demo": "parcel demo/src/index.html --out-dir demo/dist --open", "build-demo": "del-cli 'demo/dist/*' && parcel build demo/src/index.html --out-dir demo/dist --public-url https://omgovich.github.io/react-colorful/", "deploy-demo": "npm run build-demo && gh-pages -d demo/dist"}, "size-limit": [{"path": "dist/index.module.js", "name": "HexColorPicker", "import": "{ HexColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HslColorPicker", "import": "{ HslColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HslaColorPicker", "import": "{ HslaColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HslStringColorPicker", "import": "{ HslStringColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HslaStringColorPicker", "import": "{ HslaStringColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HsvColorPicker", "import": "{ HsvColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HsvaColorPicker", "import": "{ HsvaColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HsvStringColorPicker", "import": "{ HsvStringColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "HsvaStringColorPicker", "import": "{ HsvaStringColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "RgbColorPicker", "import": "{ RgbColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "RgbaColorPicker", "import": "{ RgbaColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "RgbStringColorPicker", "import": "{ RgbStringColorPicker }", "limit": "3 KB"}, {"path": "dist/index.module.js", "name": "RgbaStringColorPicker", "import": "{ RgbaStringColorPicker }", "limit": "3.1 KB"}, {"path": "dist/index.module.js", "name": "HexColorInput", "import": "{ HexColorInput }", "limit": "3 KB"}], "sideEffects": false, "jest": {"verbose": true, "transform": {"\\.js$": "jest-esm-jsx-transform", "^.+\\.tsx?$": "ts-jest"}, "moduleNameMapper": {"\\.css$": "<rootDir>/tests/__mocks__/styles.css.mock.ts"}}, "files": ["dist/*.{js,mjs,ts,map}", "dist/components/*.ts", "dist/utils/nonce.d.ts", "LICENSE", "ACKNOWLEDGMENTS", "README.md", "package.json"], "repository": "omgovich/react-colorful", "keywords": ["react", "color picker", "react-component", "colorpicker", "tiny", "hex", "color", "front-end", "color-picker", "accessible", "react-color", "accessibility", "aria", "a11y", "wai-aria"], "author": "<PERSON> <om<PERSON><PERSON>@ya.ru>", "license": "MIT", "bugs": {"url": "https://github.com/omgovich/react-colorful/issues"}, "homepage": "https://omgovich.github.io/react-colorful", "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "devDependencies": {"@size-limit/preset-small-lib": "^4.10.1", "@testing-library/react": "^11.2.5", "@types/jest": "^26.0.20", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-frame-component": "^4.1.3", "@typescript-eslint/eslint-plugin": "^4.17.0", "@typescript-eslint/parser": "^4.17.0", "cpy-cli": "^3.1.1", "del-cli": "^3.0.1", "eslint": "^7.21.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-react-hooks": "^4.2.0", "gh-pages": "^3.1.0", "goober": "^2.0.33", "jest": "^26.6.3", "jest-esm-jsx-transform": "^1.0.0", "microbundle": "0.13.0", "parcel-bundler": "1.12.3", "parcel-plugin-css-to-string": "^2.5.2", "prettier": "^2.2.1", "react": "^17.0.2", "react-dom": "^17.0.2", "react-frame-component": "^5.1.0", "size-limit": "^4.10.1", "ts-jest": "^26.5.3", "typescript": "^4.2.3", "use-throttled-effect": "0.0.7"}}