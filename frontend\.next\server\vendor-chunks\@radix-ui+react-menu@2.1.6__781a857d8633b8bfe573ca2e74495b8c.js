"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-menu@2.1.6__781a857d8633b8bfe573ca2e74495b8c";
exports.ids = ["vendor-chunks/@radix-ui+react-menu@2.1.6__781a857d8633b8bfe573ca2e74495b8c"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.6__781a857d8633b8bfe573ca2e74495b8c/node_modules/@radix-ui/react-menu/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-menu@2.1.6__781a857d8633b8bfe573ca2e74495b8c/node_modules/@radix-ui/react-menu/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Menu: () => (/* binding */ Menu),\n/* harmony export */   MenuAnchor: () => (/* binding */ MenuAnchor),\n/* harmony export */   MenuArrow: () => (/* binding */ MenuArrow),\n/* harmony export */   MenuCheckboxItem: () => (/* binding */ MenuCheckboxItem),\n/* harmony export */   MenuContent: () => (/* binding */ MenuContent),\n/* harmony export */   MenuGroup: () => (/* binding */ MenuGroup),\n/* harmony export */   MenuItem: () => (/* binding */ MenuItem),\n/* harmony export */   MenuItemIndicator: () => (/* binding */ MenuItemIndicator),\n/* harmony export */   MenuLabel: () => (/* binding */ MenuLabel),\n/* harmony export */   MenuPortal: () => (/* binding */ MenuPortal),\n/* harmony export */   MenuRadioGroup: () => (/* binding */ MenuRadioGroup),\n/* harmony export */   MenuRadioItem: () => (/* binding */ MenuRadioItem),\n/* harmony export */   MenuSeparator: () => (/* binding */ MenuSeparator),\n/* harmony export */   MenuSub: () => (/* binding */ MenuSub),\n/* harmony export */   MenuSubContent: () => (/* binding */ MenuSubContent),\n/* harmony export */   MenuSubTrigger: () => (/* binding */ MenuSubTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Sub: () => (/* binding */ Sub),\n/* harmony export */   SubContent: () => (/* binding */ SubContent),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger),\n/* harmony export */   createMenuScope: () => (/* binding */ createMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.1/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_7256e412e415cb43171c70cabb64e027/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_f0d8d248d4450f605cbe95a52bc62448/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_42d3dbabb2c0e10ad90dea04084e0f18/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_6d135310b070906503465ecf570ec58b/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_a7c3b12f00f397e0a9115766f9969ef3/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_61d195191520d637c5afa16ba55c7c57/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.0_@types+react@18.3.20_react@18.2.0/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._f86bd25910bc9d814f2a908b3bdd82bd/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._1b28f38417320d44c2781ca5ef3763c8/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._b86981b2f1d44efeb0dfddb8c6d3023e/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_62c082c8d2b4a336e6eb97cc77a43f16/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-roving-focus */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-roving-focu_5c34d9c7aac6dc08a5ed58db70fe6643/node_modules/@radix-ui/react-roving-focus/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@18.3.20_react@18.2.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_9208071cd8f8d57aa885c3fb901f201a/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.4/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.6.3_@types+react@18.3.20_react@18.2.0/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,CheckboxItem,Content,Group,Item,ItemIndicator,Label,Menu,MenuAnchor,MenuArrow,MenuCheckboxItem,MenuContent,MenuGroup,MenuItem,MenuItemIndicator,MenuLabel,MenuPortal,MenuRadioGroup,MenuRadioItem,MenuSeparator,MenuSub,MenuSubContent,MenuSubTrigger,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,createMenuScope auto */ // packages/react/menu/src/menu.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar SELECTION_KEYS = [\n    \"Enter\",\n    \" \"\n];\nvar FIRST_KEYS = [\n    \"ArrowDown\",\n    \"PageUp\",\n    \"Home\"\n];\nvar LAST_KEYS = [\n    \"ArrowUp\",\n    \"PageDown\",\n    \"End\"\n];\nvar FIRST_LAST_KEYS = [\n    ...FIRST_KEYS,\n    ...LAST_KEYS\n];\nvar SUB_OPEN_KEYS = {\n    ltr: [\n        ...SELECTION_KEYS,\n        \"ArrowRight\"\n    ],\n    rtl: [\n        ...SELECTION_KEYS,\n        \"ArrowLeft\"\n    ]\n};\nvar SUB_CLOSE_KEYS = {\n    ltr: [\n        \"ArrowLeft\"\n    ],\n    rtl: [\n        \"ArrowRight\"\n    ]\n};\nvar MENU_NAME = \"Menu\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_2__.createCollection)(MENU_NAME);\nvar [createMenuContext, createMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_3__.createContextScope)(MENU_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope,\n    _radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.createPopperScope)();\nvar useRovingFocusGroupScope = (0,_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.createRovingFocusGroupScope)();\nvar [MenuProvider, useMenuContext] = createMenuContext(MENU_NAME);\nvar [MenuRootProvider, useMenuRootContext] = createMenuContext(MENU_NAME);\nvar Menu = (props)=>{\n    const { __scopeMenu, open = false, children, dir, onOpenChange, modal = true } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const isUsingKeyboardRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_7__.useDirection)(dir);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleKeyDown = ()=>{\n            isUsingKeyboardRef.current = true;\n            document.addEventListener(\"pointerdown\", handlePointer, {\n                capture: true,\n                once: true\n            });\n            document.addEventListener(\"pointermove\", handlePointer, {\n                capture: true,\n                once: true\n            });\n        };\n        const handlePointer = ()=>isUsingKeyboardRef.current = false;\n        document.addEventListener(\"keydown\", handleKeyDown, {\n            capture: true\n        });\n        return ()=>{\n            document.removeEventListener(\"keydown\", handleKeyDown, {\n                capture: true\n            });\n            document.removeEventListener(\"pointerdown\", handlePointer, {\n                capture: true\n            });\n            document.removeEventListener(\"pointermove\", handlePointer, {\n                capture: true\n            });\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootProvider, {\n                scope: __scopeMenu,\n                onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>handleOpenChange(false), [\n                    handleOpenChange\n                ]),\n                isUsingKeyboardRef,\n                dir: direction,\n                modal,\n                children\n            })\n        })\n    });\n};\nMenu.displayName = MENU_NAME;\nvar ANCHOR_NAME = \"MenuAnchor\";\nvar MenuAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...anchorProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nMenuAnchor.displayName = ANCHOR_NAME;\nvar PORTAL_NAME = \"MenuPortal\";\nvar [PortalProvider, usePortalContext] = createMenuContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar MenuPortal = (props)=>{\n    const { __scopeMenu, forceMount, children, container } = props;\n    const context = useMenuContext(PORTAL_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeMenu,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_9__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"MenuContent\";\nvar [MenuContentProvider, useMenuContentContext] = createMenuContext(CONTENT_NAME);\nvar MenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: rootContext.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuRootContentNonModal, {\n                    ...contentProps,\n                    ref: forwardedRef\n                })\n            })\n        })\n    });\n});\nvar MenuRootContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = ref.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_11__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: composedRefs,\n        trapFocus: context.open,\n        disableOutsidePointerEvents: context.open,\n        disableOutsideScroll: true,\n        onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n            checkForDefaultPrevented: false\n        }),\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuRootContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        disableOutsideScroll: false,\n        onDismiss: ()=>context.onOpenChange(false)\n    });\n});\nvar MenuContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, loop = false, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEntryFocus, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, onDismiss, disableOutsideScroll, ...contentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, __scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const getItems = useCollection(__scopeMenu);\n    const [currentItemId, setCurrentItemId] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, contentRef, context.onContentChange);\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const pointerGraceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerGraceIntentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerDirRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"right\");\n    const lastPointerXRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const ScrollLockWrapper = disableOutsideScroll ? react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__[\"default\"] : react__WEBPACK_IMPORTED_MODULE_0__.Fragment;\n    const scrollLockWrapperProps = disableOutsideScroll ? {\n        as: _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.Slot,\n        allowPinchZoom: true\n    } : void 0;\n    const handleTypeaheadSearch = (key)=>{\n        const search = searchRef.current + key;\n        const items = getItems().filter((item)=>!item.disabled);\n        const currentItem = document.activeElement;\n        const currentMatch = items.find((item)=>item.ref.current === currentItem)?.textValue;\n        const values = items.map((item)=>item.textValue);\n        const nextMatch = getNextMatch(values, search, currentMatch);\n        const newItem = items.find((item)=>item.textValue === nextMatch)?.ref.current;\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n        if (newItem) {\n            setTimeout(()=>newItem.focus());\n        }\n    };\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_15__.useFocusGuards)();\n    const isPointerMovingToSubmenu = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        const isMovingTowards = pointerDirRef.current === pointerGraceIntentRef.current?.side;\n        return isMovingTowards && isPointerInGraceArea(event, pointerGraceIntentRef.current?.area);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentProvider, {\n        scope: __scopeMenu,\n        searchRef,\n        onItemEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onItemLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) return;\n            contentRef.current?.focus();\n            setCurrentItemId(null);\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n            if (isPointerMovingToSubmenu(event)) event.preventDefault();\n        }, [\n            isPointerMovingToSubmenu\n        ]),\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((intent)=>{\n            pointerGraceIntentRef.current = intent;\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollLockWrapper, {\n            ...scrollLockWrapperProps,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_16__.FocusScope, {\n                asChild: true,\n                trapped: trapFocus,\n                onMountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onOpenAutoFocus, (event)=>{\n                    event.preventDefault();\n                    contentRef.current?.focus({\n                        preventScroll: true\n                    });\n                }),\n                onUnmountAutoFocus: onCloseAutoFocus,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_17__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside,\n                    onInteractOutside,\n                    onDismiss,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Root, {\n                        asChild: true,\n                        ...rovingFocusGroupScope,\n                        dir: rootContext.dir,\n                        orientation: \"vertical\",\n                        loop,\n                        currentTabStopId: currentItemId,\n                        onCurrentTabStopIdChange: setCurrentItemId,\n                        onEntryFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(onEntryFocus, (event)=>{\n                            if (!rootContext.isUsingKeyboardRef.current) event.preventDefault();\n                        }),\n                        preventScrollOnEntryFocus: true,\n                        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Content, {\n                            role: \"menu\",\n                            \"aria-orientation\": \"vertical\",\n                            \"data-state\": getOpenState(context.open),\n                            \"data-radix-menu-content\": \"\",\n                            dir: rootContext.dir,\n                            ...popperScope,\n                            ...contentProps,\n                            ref: composedRefs,\n                            style: {\n                                outline: \"none\",\n                                ...contentProps.style\n                            },\n                            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                                const target = event.target;\n                                const isKeyDownInside = target.closest(\"[data-radix-menu-content]\") === event.currentTarget;\n                                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                                const isCharacterKey = event.key.length === 1;\n                                if (isKeyDownInside) {\n                                    if (event.key === \"Tab\") event.preventDefault();\n                                    if (!isModifierKey && isCharacterKey) handleTypeaheadSearch(event.key);\n                                }\n                                const content = contentRef.current;\n                                if (event.target !== content) return;\n                                if (!FIRST_LAST_KEYS.includes(event.key)) return;\n                                event.preventDefault();\n                                const items = getItems().filter((item)=>!item.disabled);\n                                const candidateNodes = items.map((item)=>item.ref.current);\n                                if (LAST_KEYS.includes(event.key)) candidateNodes.reverse();\n                                focusFirst(candidateNodes);\n                            }),\n                            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, (event)=>{\n                                if (!event.currentTarget.contains(event.target)) {\n                                    window.clearTimeout(timerRef.current);\n                                    searchRef.current = \"\";\n                                }\n                            }),\n                            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                                const target = event.target;\n                                const pointerXHasChanged = lastPointerXRef.current !== event.clientX;\n                                if (event.currentTarget.contains(target) && pointerXHasChanged) {\n                                    const newDir = event.clientX > lastPointerXRef.current ? \"right\" : \"left\";\n                                    pointerDirRef.current = newDir;\n                                    lastPointerXRef.current = event.clientX;\n                                }\n                            }))\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"MenuGroup\";\nvar MenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...groupProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"group\",\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"MenuLabel\";\nvar MenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...labelProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"MenuItem\";\nvar ITEM_SELECT = \"menu.itemSelect\";\nvar MenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { disabled = false, onSelect, ...itemProps } = props;\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const rootContext = useMenuRootContext(ITEM_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(ITEM_NAME, props.__scopeMenu);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handleSelect = ()=>{\n        const menuItem = ref.current;\n        if (!disabled && menuItem) {\n            const itemSelectEvent = new CustomEvent(ITEM_SELECT, {\n                bubbles: true,\n                cancelable: true\n            });\n            menuItem.addEventListener(ITEM_SELECT, (event)=>onSelect?.(event), {\n                once: true\n            });\n            (0,_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.dispatchDiscreteCustomEvent)(menuItem, itemSelectEvent);\n            if (itemSelectEvent.defaultPrevented) {\n                isPointerDownRef.current = false;\n            } else {\n                rootContext.onClose();\n            }\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n        ...itemProps,\n        ref: composedRefs,\n        disabled,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onClick, handleSelect),\n        onPointerDown: (event)=>{\n            props.onPointerDown?.(event);\n            isPointerDownRef.current = true;\n        },\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerUp, (event)=>{\n            if (!isPointerDownRef.current) event.currentTarget?.click();\n        }),\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n            const isTypingAhead = contentContext.searchRef.current !== \"\";\n            if (disabled || isTypingAhead && event.key === \" \") return;\n            if (SELECTION_KEYS.includes(event.key)) {\n                event.currentTarget.click();\n                event.preventDefault();\n            }\n        })\n    });\n});\nMenuItem.displayName = ITEM_NAME;\nvar MenuItemImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, disabled = false, textValue, ...itemProps } = props;\n    const contentContext = useMenuContentContext(ITEM_NAME, __scopeMenu);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [textContent, setTextContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"\");\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const menuItem = ref.current;\n        if (menuItem) {\n            setTextContent((menuItem.textContent ?? \"\").trim());\n        }\n    }, [\n        itemProps.children\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.ItemSlot, {\n        scope: __scopeMenu,\n        disabled,\n        textValue: textValue ?? textContent,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_roving_focus__WEBPACK_IMPORTED_MODULE_5__.Item, {\n            asChild: true,\n            ...rovingFocusGroupScope,\n            focusable: !disabled,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n                role: \"menuitem\",\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                ...itemProps,\n                ref: composedRefs,\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                    if (disabled) {\n                        contentContext.onItemLeave(event);\n                    } else {\n                        contentContext.onItemEnter(event);\n                        if (!event.defaultPrevented) {\n                            const item = event.currentTarget;\n                            item.focus({\n                                preventScroll: true\n                            });\n                        }\n                    }\n                })),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>contentContext.onItemLeave(event))),\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onBlur, ()=>setIsFocused(false))\n            })\n        })\n    });\n});\nvar CHECKBOX_ITEM_NAME = \"MenuCheckboxItem\";\nvar MenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { checked = false, onCheckedChange, ...checkboxItemProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemcheckbox\",\n            \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n            ...checkboxItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(checkboxItemProps.onSelect, ()=>onCheckedChange?.(isIndeterminate(checked) ? true : !checked), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"MenuRadioGroup\";\nvar [RadioGroupProvider, useRadioGroupContext] = createMenuContext(RADIO_GROUP_NAME, {\n    value: void 0,\n    onValueChange: ()=>{}\n});\nvar MenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, onValueChange, ...groupProps } = props;\n    const handleValueChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onValueChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(RadioGroupProvider, {\n        scope: props.__scopeMenu,\n        value,\n        onValueChange: handleValueChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuGroup, {\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"MenuRadioItem\";\nvar MenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { value, ...radioItemProps } = props;\n    const context = useRadioGroupContext(RADIO_ITEM_NAME, props.__scopeMenu);\n    const checked = value === context.value;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ItemIndicatorProvider, {\n        scope: props.__scopeMenu,\n        checked,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItem, {\n            role: \"menuitemradio\",\n            \"aria-checked\": checked,\n            ...radioItemProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(checked),\n            onSelect: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(radioItemProps.onSelect, ()=>context.onValueChange?.(value), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar ITEM_INDICATOR_NAME = \"MenuItemIndicator\";\nvar [ItemIndicatorProvider, useItemIndicatorContext] = createMenuContext(ITEM_INDICATOR_NAME, {\n    checked: false\n});\nvar MenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, forceMount, ...itemIndicatorProps } = props;\n    const indicatorContext = useItemIndicatorContext(ITEM_INDICATOR_NAME, __scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n        present: forceMount || isIndeterminate(indicatorContext.checked) || indicatorContext.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.span, {\n            ...itemIndicatorProps,\n            ref: forwardedRef,\n            \"data-state\": getCheckedState(indicatorContext.checked)\n        })\n    });\n});\nMenuItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SEPARATOR_NAME = \"MenuSeparator\";\nvar MenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_18__.Primitive.div, {\n        role: \"separator\",\n        \"aria-orientation\": \"horizontal\",\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"MenuArrow\";\nvar MenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeMenu, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nMenuArrow.displayName = ARROW_NAME;\nvar SUB_NAME = \"MenuSub\";\nvar [MenuSubProvider, useMenuSubContext] = createMenuContext(SUB_NAME);\nvar MenuSub = (props)=>{\n    const { __scopeMenu, children, open = false, onOpenChange } = props;\n    const parentMenuContext = useMenuContext(SUB_NAME, __scopeMenu);\n    const popperScope = usePopperScope(__scopeMenu);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const handleOpenChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_6__.useCallbackRef)(onOpenChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (parentMenuContext.open === false) handleOpenChange(false);\n        return ()=>handleOpenChange(false);\n    }, [\n        parentMenuContext.open,\n        handleOpenChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuProvider, {\n            scope: __scopeMenu,\n            open,\n            onOpenChange: handleOpenChange,\n            content,\n            onContentChange: setContent,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuSubProvider, {\n                scope: __scopeMenu,\n                contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_19__.useId)(),\n                trigger,\n                onTriggerChange: setTrigger,\n                children\n            })\n        })\n    });\n};\nMenuSub.displayName = SUB_NAME;\nvar SUB_TRIGGER_NAME = \"MenuSubTrigger\";\nvar MenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useMenuContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const contentContext = useMenuContentContext(SUB_TRIGGER_NAME, props.__scopeMenu);\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const { pointerGraceTimerRef, onPointerGraceIntentChange } = contentContext;\n    const scope = {\n        __scopeMenu: props.__scopeMenu\n    };\n    const clearOpenTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (openTimerRef.current) window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = null;\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>clearOpenTimer, [\n        clearOpenTimer\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const pointerGraceTimer = pointerGraceTimerRef.current;\n        return ()=>{\n            window.clearTimeout(pointerGraceTimer);\n            onPointerGraceIntentChange(null);\n        };\n    }, [\n        pointerGraceTimerRef,\n        onPointerGraceIntentChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuAnchor, {\n        asChild: true,\n        ...scope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuItemImpl, {\n            id: subContext.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": subContext.contentId,\n            \"data-state\": getOpenState(context.open),\n            ...props,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.composeRefs)(forwardedRef, subContext.onTriggerChange),\n            onClick: (event)=>{\n                props.onClick?.(event);\n                if (props.disabled || event.defaultPrevented) return;\n                event.currentTarget.focus();\n                if (!context.open) context.onOpenChange(true);\n            },\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerMove, whenMouse((event)=>{\n                contentContext.onItemEnter(event);\n                if (event.defaultPrevented) return;\n                if (!props.disabled && !context.open && !openTimerRef.current) {\n                    contentContext.onPointerGraceIntentChange(null);\n                    openTimerRef.current = window.setTimeout(()=>{\n                        context.onOpenChange(true);\n                        clearOpenTimer();\n                    }, 100);\n                }\n            })),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onPointerLeave, whenMouse((event)=>{\n                clearOpenTimer();\n                const contentRect = context.content?.getBoundingClientRect();\n                if (contentRect) {\n                    const side = context.content?.dataset.side;\n                    const rightSide = side === \"right\";\n                    const bleed = rightSide ? -5 : 5;\n                    const contentNearEdge = contentRect[rightSide ? \"left\" : \"right\"];\n                    const contentFarEdge = contentRect[rightSide ? \"right\" : \"left\"];\n                    contentContext.onPointerGraceIntentChange({\n                        area: [\n                            // Apply a bleed on clientX to ensure that our exit point is\n                            // consistently within polygon bounds\n                            {\n                                x: event.clientX + bleed,\n                                y: event.clientY\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.top\n                            },\n                            {\n                                x: contentFarEdge,\n                                y: contentRect.bottom\n                            },\n                            {\n                                x: contentNearEdge,\n                                y: contentRect.bottom\n                            }\n                        ],\n                        side\n                    });\n                    window.clearTimeout(pointerGraceTimerRef.current);\n                    pointerGraceTimerRef.current = window.setTimeout(()=>contentContext.onPointerGraceIntentChange(null), 300);\n                } else {\n                    contentContext.onTriggerLeave(event);\n                    if (event.defaultPrevented) return;\n                    contentContext.onPointerGraceIntentChange(null);\n                }\n            })),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                const isTypingAhead = contentContext.searchRef.current !== \"\";\n                if (props.disabled || isTypingAhead && event.key === \" \") return;\n                if (SUB_OPEN_KEYS[rootContext.dir].includes(event.key)) {\n                    context.onOpenChange(true);\n                    context.content?.focus();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"MenuSubContent\";\nvar MenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeMenu);\n    const { forceMount = portalContext.forceMount, ...subContentProps } = props;\n    const context = useMenuContext(CONTENT_NAME, props.__scopeMenu);\n    const rootContext = useMenuRootContext(CONTENT_NAME, props.__scopeMenu);\n    const subContext = useMenuSubContext(SUB_CONTENT_NAME, props.__scopeMenu);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_10__.useComposedRefs)(forwardedRef, ref);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Provider, {\n        scope: props.__scopeMenu,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_8__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Collection.Slot, {\n                scope: props.__scopeMenu,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(MenuContentImpl, {\n                    id: subContext.contentId,\n                    \"aria-labelledby\": subContext.triggerId,\n                    ...subContentProps,\n                    ref: composedRefs,\n                    align: \"start\",\n                    side: rootContext.dir === \"rtl\" ? \"left\" : \"right\",\n                    disableOutsidePointerEvents: false,\n                    disableOutsideScroll: false,\n                    trapFocus: false,\n                    onOpenAutoFocus: (event)=>{\n                        if (rootContext.isUsingKeyboardRef.current) ref.current?.focus();\n                        event.preventDefault();\n                    },\n                    onCloseAutoFocus: (event)=>event.preventDefault(),\n                    onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onFocusOutside, (event)=>{\n                        if (event.target !== subContext.trigger) context.onOpenChange(false);\n                    }),\n                    onEscapeKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onEscapeKeyDown, (event)=>{\n                        rootContext.onClose();\n                        event.preventDefault();\n                    }),\n                    onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_12__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                        const isKeyDownInside = event.currentTarget.contains(event.target);\n                        const isCloseKey = SUB_CLOSE_KEYS[rootContext.dir].includes(event.key);\n                        if (isKeyDownInside && isCloseKey) {\n                            context.onOpenChange(false);\n                            subContext.trigger?.focus();\n                            event.preventDefault();\n                        }\n                    })\n                })\n            })\n        })\n    });\n});\nMenuSubContent.displayName = SUB_CONTENT_NAME;\nfunction getOpenState(open) {\n    return open ? \"open\" : \"closed\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getCheckedState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\nfunction focusFirst(candidates) {\n    const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n    for (const candidate of candidates){\n        if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n        candidate.focus();\n        if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n    }\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nfunction getNextMatch(values, search, currentMatch) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentMatchIndex = currentMatch ? values.indexOf(currentMatch) : -1;\n    let wrappedValues = wrapArray(values, Math.max(currentMatchIndex, 0));\n    const excludeCurrentMatch = normalizedSearch.length === 1;\n    if (excludeCurrentMatch) wrappedValues = wrappedValues.filter((v)=>v !== currentMatch);\n    const nextMatch = wrappedValues.find((value)=>value.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextMatch !== currentMatch ? nextMatch : void 0;\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const xi = polygon[i].x;\n        const yi = polygon[i].y;\n        const xj = polygon[j].x;\n        const yj = polygon[j].y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction isPointerInGraceArea(event, area) {\n    if (!area) return false;\n    const cursorPos = {\n        x: event.clientX,\n        y: event.clientY\n    };\n    return isPointInPolygon(cursorPos, area);\n}\nfunction whenMouse(handler) {\n    return (event)=>event.pointerType === \"mouse\" ? handler(event) : void 0;\n}\nvar Root3 = Menu;\nvar Anchor2 = MenuAnchor;\nvar Portal = MenuPortal;\nvar Content2 = MenuContent;\nvar Group = MenuGroup;\nvar Label = MenuLabel;\nvar Item2 = MenuItem;\nvar CheckboxItem = MenuCheckboxItem;\nvar RadioGroup = MenuRadioGroup;\nvar RadioItem = MenuRadioItem;\nvar ItemIndicator = MenuItemIndicator;\nvar Separator = MenuSeparator;\nvar Arrow2 = MenuArrow;\nvar Sub = MenuSub;\nvar SubTrigger = MenuSubTrigger;\nvar SubContent = MenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.6__781a857d8633b8bfe573ca2e74495b8c/node_modules/@radix-ui/react-menu/dist/index.mjs\n");

/***/ })

};
;