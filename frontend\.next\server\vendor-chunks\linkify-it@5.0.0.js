"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/linkify-it@5.0.0";
exports.ids = ["vendor-chunks/linkify-it@5.0.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/index.mjs":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/index.mjs ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_re_mjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./lib/re.mjs */ \"(ssr)/./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/lib/re.mjs\");\n\n\n//\n// Helpers\n//\n\n// Merge objects\n//\nfunction assign (obj /* from1, from2, from3, ... */) {\n  const sources = Array.prototype.slice.call(arguments, 1)\n\n  sources.forEach(function (source) {\n    if (!source) { return }\n\n    Object.keys(source).forEach(function (key) {\n      obj[key] = source[key]\n    })\n  })\n\n  return obj\n}\n\nfunction _class (obj) { return Object.prototype.toString.call(obj) }\nfunction isString (obj) { return _class(obj) === '[object String]' }\nfunction isObject (obj) { return _class(obj) === '[object Object]' }\nfunction isRegExp (obj) { return _class(obj) === '[object RegExp]' }\nfunction isFunction (obj) { return _class(obj) === '[object Function]' }\n\nfunction escapeRE (str) { return str.replace(/[.?*+^$[\\]\\\\(){}|-]/g, '\\\\$&') }\n\n//\n\nconst defaultOptions = {\n  fuzzyLink: true,\n  fuzzyEmail: true,\n  fuzzyIP: false\n}\n\nfunction isOptionsObj (obj) {\n  return Object.keys(obj || {}).reduce(function (acc, k) {\n    /* eslint-disable-next-line no-prototype-builtins */\n    return acc || defaultOptions.hasOwnProperty(k)\n  }, false)\n}\n\nconst defaultSchemas = {\n  'http:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.http) {\n        // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.http = new RegExp(\n          '^\\\\/\\\\/' + self.re.src_auth + self.re.src_host_port_strict + self.re.src_path, 'i'\n        )\n      }\n      if (self.re.http.test(tail)) {\n        return tail.match(self.re.http)[0].length\n      }\n      return 0\n    }\n  },\n  'https:': 'http:',\n  'ftp:': 'http:',\n  '//': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.no_http) {\n      // compile lazily, because \"host\"-containing variables can change on tlds update.\n        self.re.no_http = new RegExp(\n          '^' +\n          self.re.src_auth +\n          // Don't allow single-level domains, because of false positives like '//test'\n          // with code comments\n          '(?:localhost|(?:(?:' + self.re.src_domain + ')\\\\.)+' + self.re.src_domain_root + ')' +\n          self.re.src_port +\n          self.re.src_host_terminator +\n          self.re.src_path,\n\n          'i'\n        )\n      }\n\n      if (self.re.no_http.test(tail)) {\n        // should not be `://` & `///`, that protects from errors in protocol name\n        if (pos >= 3 && text[pos - 3] === ':') { return 0 }\n        if (pos >= 3 && text[pos - 3] === '/') { return 0 }\n        return tail.match(self.re.no_http)[0].length\n      }\n      return 0\n    }\n  },\n  'mailto:': {\n    validate: function (text, pos, self) {\n      const tail = text.slice(pos)\n\n      if (!self.re.mailto) {\n        self.re.mailto = new RegExp(\n          '^' + self.re.src_email_name + '@' + self.re.src_host_strict, 'i'\n        )\n      }\n      if (self.re.mailto.test(tail)) {\n        return tail.match(self.re.mailto)[0].length\n      }\n      return 0\n    }\n  }\n}\n\n// RE pattern for 2-character tlds (autogenerated by ./support/tlds_2char_gen.js)\n/* eslint-disable-next-line max-len */\nconst tlds_2ch_src_re = 'a[cdefgilmnoqrstuwxz]|b[abdefghijmnorstvwyz]|c[acdfghiklmnoruvwxyz]|d[ejkmoz]|e[cegrstu]|f[ijkmor]|g[abdefghilmnpqrstuwy]|h[kmnrtu]|i[delmnoqrst]|j[emop]|k[eghimnprwyz]|l[abcikrstuvy]|m[acdeghklmnopqrstuvwxyz]|n[acefgilopruz]|om|p[aefghklmnrstwy]|qa|r[eosuw]|s[abcdeghijklmnortuvxyz]|t[cdfghjklmnortvwz]|u[agksyz]|v[aceginu]|w[fs]|y[et]|z[amw]'\n\n// DON'T try to make PRs with changes. Extend TLDs with LinkifyIt.tlds() instead\nconst tlds_default = 'biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф'.split('|')\n\nfunction resetScanCache (self) {\n  self.__index__ = -1\n  self.__text_cache__ = ''\n}\n\nfunction createValidator (re) {\n  return function (text, pos) {\n    const tail = text.slice(pos)\n\n    if (re.test(tail)) {\n      return tail.match(re)[0].length\n    }\n    return 0\n  }\n}\n\nfunction createNormalizer () {\n  return function (match, self) {\n    self.normalize(match)\n  }\n}\n\n// Schemas compiler. Build regexps.\n//\nfunction compile (self) {\n  // Load & clone RE patterns.\n  const re = self.re = (0,_lib_re_mjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(self.__opts__)\n\n  // Define dynamic patterns\n  const tlds = self.__tlds__.slice()\n\n  self.onCompile()\n\n  if (!self.__tlds_replaced__) {\n    tlds.push(tlds_2ch_src_re)\n  }\n  tlds.push(re.src_xn)\n\n  re.src_tlds = tlds.join('|')\n\n  function untpl (tpl) { return tpl.replace('%TLDS%', re.src_tlds) }\n\n  re.email_fuzzy = RegExp(untpl(re.tpl_email_fuzzy), 'i')\n  re.link_fuzzy = RegExp(untpl(re.tpl_link_fuzzy), 'i')\n  re.link_no_ip_fuzzy = RegExp(untpl(re.tpl_link_no_ip_fuzzy), 'i')\n  re.host_fuzzy_test = RegExp(untpl(re.tpl_host_fuzzy_test), 'i')\n\n  //\n  // Compile each schema\n  //\n\n  const aliases = []\n\n  self.__compiled__ = {} // Reset compiled data\n\n  function schemaError (name, val) {\n    throw new Error('(LinkifyIt) Invalid schema \"' + name + '\": ' + val)\n  }\n\n  Object.keys(self.__schemas__).forEach(function (name) {\n    const val = self.__schemas__[name]\n\n    // skip disabled methods\n    if (val === null) { return }\n\n    const compiled = { validate: null, link: null }\n\n    self.__compiled__[name] = compiled\n\n    if (isObject(val)) {\n      if (isRegExp(val.validate)) {\n        compiled.validate = createValidator(val.validate)\n      } else if (isFunction(val.validate)) {\n        compiled.validate = val.validate\n      } else {\n        schemaError(name, val)\n      }\n\n      if (isFunction(val.normalize)) {\n        compiled.normalize = val.normalize\n      } else if (!val.normalize) {\n        compiled.normalize = createNormalizer()\n      } else {\n        schemaError(name, val)\n      }\n\n      return\n    }\n\n    if (isString(val)) {\n      aliases.push(name)\n      return\n    }\n\n    schemaError(name, val)\n  })\n\n  //\n  // Compile postponed aliases\n  //\n\n  aliases.forEach(function (alias) {\n    if (!self.__compiled__[self.__schemas__[alias]]) {\n      // Silently fail on missed schemas to avoid errons on disable.\n      // schemaError(alias, self.__schemas__[alias]);\n      return\n    }\n\n    self.__compiled__[alias].validate =\n      self.__compiled__[self.__schemas__[alias]].validate\n    self.__compiled__[alias].normalize =\n      self.__compiled__[self.__schemas__[alias]].normalize\n  })\n\n  //\n  // Fake record for guessed links\n  //\n  self.__compiled__[''] = { validate: null, normalize: createNormalizer() }\n\n  //\n  // Build schema condition\n  //\n  const slist = Object.keys(self.__compiled__)\n    .filter(function (name) {\n      // Filter disabled & fake schemas\n      return name.length > 0 && self.__compiled__[name]\n    })\n    .map(escapeRE)\n    .join('|')\n  // (?!_) cause 1.5x slowdown\n  self.re.schema_test = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'i')\n  self.re.schema_search = RegExp('(^|(?!_)(?:[><\\uff5c]|' + re.src_ZPCc + '))(' + slist + ')', 'ig')\n  self.re.schema_at_start = RegExp('^' + self.re.schema_search.source, 'i')\n\n  self.re.pretest = RegExp(\n    '(' + self.re.schema_test.source + ')|(' + self.re.host_fuzzy_test.source + ')|@',\n    'i'\n  )\n\n  //\n  // Cleanup\n  //\n\n  resetScanCache(self)\n}\n\n/**\n * class Match\n *\n * Match result. Single element of array, returned by [[LinkifyIt#match]]\n **/\nfunction Match (self, shift) {\n  const start = self.__index__\n  const end = self.__last_index__\n  const text = self.__text_cache__.slice(start, end)\n\n  /**\n   * Match#schema -> String\n   *\n   * Prefix (protocol) for matched string.\n   **/\n  this.schema = self.__schema__.toLowerCase()\n  /**\n   * Match#index -> Number\n   *\n   * First position of matched string.\n   **/\n  this.index = start + shift\n  /**\n   * Match#lastIndex -> Number\n   *\n   * Next position after matched string.\n   **/\n  this.lastIndex = end + shift\n  /**\n   * Match#raw -> String\n   *\n   * Matched string.\n   **/\n  this.raw = text\n  /**\n   * Match#text -> String\n   *\n   * Notmalized text of matched string.\n   **/\n  this.text = text\n  /**\n   * Match#url -> String\n   *\n   * Normalized url of matched string.\n   **/\n  this.url = text\n}\n\nfunction createMatch (self, shift) {\n  const match = new Match(self, shift)\n\n  self.__compiled__[match.schema].normalize(match, self)\n\n  return match\n}\n\n/**\n * class LinkifyIt\n **/\n\n/**\n * new LinkifyIt(schemas, options)\n * - schemas (Object): Optional. Additional schemas to validate (prefix/validator)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Creates new linkifier instance with optional additional schemas.\n * Can be called without `new` keyword for convenience.\n *\n * By default understands:\n *\n * - `http(s)://...` , `ftp://...`, `mailto:...` & `//...` links\n * - \"fuzzy\" links and emails (example.com, <EMAIL>).\n *\n * `schemas` is an object, where each key/value describes protocol/rule:\n *\n * - __key__ - link prefix (usually, protocol name with `:` at the end, `skype:`\n *   for example). `linkify-it` makes shure that prefix is not preceeded with\n *   alphanumeric char and symbols. Only whitespaces and punctuation allowed.\n * - __value__ - rule to check tail after link prefix\n *   - _String_ - just alias to existing rule\n *   - _Object_\n *     - _validate_ - validator function (should return matched length on success),\n *       or `RegExp`.\n *     - _normalize_ - optional function to normalize text & url of matched result\n *       (for example, for @twitter mentions).\n *\n * `options`:\n *\n * - __fuzzyLink__ - recognige URL-s without `http(s):` prefix. Default `true`.\n * - __fuzzyIP__ - allow IPs in fuzzy links above. Can conflict with some texts\n *   like version numbers. Default `false`.\n * - __fuzzyEmail__ - recognize emails without `mailto:` prefix.\n *\n **/\nfunction LinkifyIt (schemas, options) {\n  if (!(this instanceof LinkifyIt)) {\n    return new LinkifyIt(schemas, options)\n  }\n\n  if (!options) {\n    if (isOptionsObj(schemas)) {\n      options = schemas\n      schemas = {}\n    }\n  }\n\n  this.__opts__ = assign({}, defaultOptions, options)\n\n  // Cache last tested result. Used to skip repeating steps on next `match` call.\n  this.__index__ = -1\n  this.__last_index__ = -1 // Next scan position\n  this.__schema__ = ''\n  this.__text_cache__ = ''\n\n  this.__schemas__ = assign({}, defaultSchemas, schemas)\n  this.__compiled__ = {}\n\n  this.__tlds__ = tlds_default\n  this.__tlds_replaced__ = false\n\n  this.re = {}\n\n  compile(this)\n}\n\n/** chainable\n * LinkifyIt#add(schema, definition)\n * - schema (String): rule name (fixed pattern prefix)\n * - definition (String|RegExp|Object): schema definition\n *\n * Add new rule definition. See constructor description for details.\n **/\nLinkifyIt.prototype.add = function add (schema, definition) {\n  this.__schemas__[schema] = definition\n  compile(this)\n  return this\n}\n\n/** chainable\n * LinkifyIt#set(options)\n * - options (Object): { fuzzyLink|fuzzyEmail|fuzzyIP: true|false }\n *\n * Set recognition options for links without schema.\n **/\nLinkifyIt.prototype.set = function set (options) {\n  this.__opts__ = assign(this.__opts__, options)\n  return this\n}\n\n/**\n * LinkifyIt#test(text) -> Boolean\n *\n * Searches linkifiable pattern and returns `true` on success or `false` on fail.\n **/\nLinkifyIt.prototype.test = function test (text) {\n  // Reset scan cache\n  this.__text_cache__ = text\n  this.__index__ = -1\n\n  if (!text.length) { return false }\n\n  let m, ml, me, len, shift, next, re, tld_pos, at_pos\n\n  // try to scan for link with schema - that's the most simple rule\n  if (this.re.schema_test.test(text)) {\n    re = this.re.schema_search\n    re.lastIndex = 0\n    while ((m = re.exec(text)) !== null) {\n      len = this.testSchemaAt(text, m[2], re.lastIndex)\n      if (len) {\n        this.__schema__ = m[2]\n        this.__index__ = m.index + m[1].length\n        this.__last_index__ = m.index + m[0].length + len\n        break\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyLink && this.__compiled__['http:']) {\n    // guess schemaless links\n    tld_pos = text.search(this.re.host_fuzzy_test)\n    if (tld_pos >= 0) {\n      // if tld is located after found link - no need to check fuzzy pattern\n      if (this.__index__ < 0 || tld_pos < this.__index__) {\n        if ((ml = text.match(this.__opts__.fuzzyIP ? this.re.link_fuzzy : this.re.link_no_ip_fuzzy)) !== null) {\n          shift = ml.index + ml[1].length\n\n          if (this.__index__ < 0 || shift < this.__index__) {\n            this.__schema__ = ''\n            this.__index__ = shift\n            this.__last_index__ = ml.index + ml[0].length\n          }\n        }\n      }\n    }\n  }\n\n  if (this.__opts__.fuzzyEmail && this.__compiled__['mailto:']) {\n    // guess schemaless emails\n    at_pos = text.indexOf('@')\n    if (at_pos >= 0) {\n      // We can't skip this check, because this cases are possible:\n      // <EMAIL>, <EMAIL>\n      if ((me = text.match(this.re.email_fuzzy)) !== null) {\n        shift = me.index + me[1].length\n        next = me.index + me[0].length\n\n        if (this.__index__ < 0 || shift < this.__index__ ||\n            (shift === this.__index__ && next > this.__last_index__)) {\n          this.__schema__ = 'mailto:'\n          this.__index__ = shift\n          this.__last_index__ = next\n        }\n      }\n    }\n  }\n\n  return this.__index__ >= 0\n}\n\n/**\n * LinkifyIt#pretest(text) -> Boolean\n *\n * Very quick check, that can give false positives. Returns true if link MAY BE\n * can exists. Can be used for speed optimization, when you need to check that\n * link NOT exists.\n **/\nLinkifyIt.prototype.pretest = function pretest (text) {\n  return this.re.pretest.test(text)\n}\n\n/**\n * LinkifyIt#testSchemaAt(text, name, position) -> Number\n * - text (String): text to scan\n * - name (String): rule (schema) name\n * - position (Number): text offset to check from\n *\n * Similar to [[LinkifyIt#test]] but checks only specific protocol tail exactly\n * at given position. Returns length of found pattern (0 on fail).\n **/\nLinkifyIt.prototype.testSchemaAt = function testSchemaAt (text, schema, pos) {\n  // If not supported schema check requested - terminate\n  if (!this.__compiled__[schema.toLowerCase()]) {\n    return 0\n  }\n  return this.__compiled__[schema.toLowerCase()].validate(text, pos, this)\n}\n\n/**\n * LinkifyIt#match(text) -> Array|null\n *\n * Returns array of found link descriptions or `null` on fail. We strongly\n * recommend to use [[LinkifyIt#test]] first, for best speed.\n *\n * ##### Result match description\n *\n * - __schema__ - link schema, can be empty for fuzzy links, or `//` for\n *   protocol-neutral  links.\n * - __index__ - offset of matched text\n * - __lastIndex__ - index of next char after mathch end\n * - __raw__ - matched text\n * - __text__ - normalized text\n * - __url__ - link, generated from matched text\n **/\nLinkifyIt.prototype.match = function match (text) {\n  const result = []\n  let shift = 0\n\n  // Try to take previous element from cache, if .test() called before\n  if (this.__index__ >= 0 && this.__text_cache__ === text) {\n    result.push(createMatch(this, shift))\n    shift = this.__last_index__\n  }\n\n  // Cut head if cache was used\n  let tail = shift ? text.slice(shift) : text\n\n  // Scan string until end reached\n  while (this.test(tail)) {\n    result.push(createMatch(this, shift))\n\n    tail = tail.slice(this.__last_index__)\n    shift += this.__last_index__\n  }\n\n  if (result.length) {\n    return result\n  }\n\n  return null\n}\n\n/**\n * LinkifyIt#matchAtStart(text) -> Match|null\n *\n * Returns fully-formed (not fuzzy) link if it starts at the beginning\n * of the string, and null otherwise.\n **/\nLinkifyIt.prototype.matchAtStart = function matchAtStart (text) {\n  // Reset scan cache\n  this.__text_cache__ = text\n  this.__index__ = -1\n\n  if (!text.length) return null\n\n  const m = this.re.schema_at_start.exec(text)\n  if (!m) return null\n\n  const len = this.testSchemaAt(text, m[2], m[0].length)\n  if (!len) return null\n\n  this.__schema__ = m[2]\n  this.__index__ = m.index + m[1].length\n  this.__last_index__ = m.index + m[0].length + len\n\n  return createMatch(this, 0)\n}\n\n/** chainable\n * LinkifyIt#tlds(list [, keepOld]) -> this\n * - list (Array): list of tlds\n * - keepOld (Boolean): merge with current list if `true` (`false` by default)\n *\n * Load (or merge) new tlds list. Those are user for fuzzy links (without prefix)\n * to avoid false positives. By default this algorythm used:\n *\n * - hostname with any 2-letter root zones are ok.\n * - biz|com|edu|gov|net|org|pro|web|xxx|aero|asia|coop|info|museum|name|shop|рф\n *   are ok.\n * - encoded (`xn--...`) root zones are ok.\n *\n * If list is replaced, then exact match for 2-chars root zones will be checked.\n **/\nLinkifyIt.prototype.tlds = function tlds (list, keepOld) {\n  list = Array.isArray(list) ? list : [list]\n\n  if (!keepOld) {\n    this.__tlds__ = list.slice()\n    this.__tlds_replaced__ = true\n    compile(this)\n    return this\n  }\n\n  this.__tlds__ = this.__tlds__.concat(list)\n    .sort()\n    .filter(function (el, idx, arr) {\n      return el !== arr[idx - 1]\n    })\n    .reverse()\n\n  compile(this)\n  return this\n}\n\n/**\n * LinkifyIt#normalize(match)\n *\n * Default normalizer (if schema does not define it's own).\n **/\nLinkifyIt.prototype.normalize = function normalize (match) {\n  // Do minimal possible changes by default. Need to collect feedback prior\n  // to move forward https://github.com/markdown-it/linkify-it/issues/1\n\n  if (!match.schema) { match.url = 'http://' + match.url }\n\n  if (match.schema === 'mailto:' && !/^mailto:/i.test(match.url)) {\n    match.url = 'mailto:' + match.url\n  }\n}\n\n/**\n * LinkifyIt#onCompile()\n *\n * Override to modify basic RegExp-s.\n **/\nLinkifyIt.prototype.onCompile = function onCompile () {\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LinkifyIt);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/lib/re.mjs":
/*!********************************************************************************!*\
  !*** ./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/lib/re.mjs ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var uc_micro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! uc.micro */ \"(ssr)/./node_modules/.pnpm/uc.micro@2.1.0/node_modules/uc.micro/index.mjs\");\n\n\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(opts) {\n  const re = {}\n  opts = opts || {}\n\n  re.src_Any = uc_micro__WEBPACK_IMPORTED_MODULE_0__.Any.source\n  re.src_Cc = uc_micro__WEBPACK_IMPORTED_MODULE_0__.Cc.source\n  re.src_Z = uc_micro__WEBPACK_IMPORTED_MODULE_0__.Z.source\n  re.src_P = uc_micro__WEBPACK_IMPORTED_MODULE_0__.P.source\n\n  // \\p{\\Z\\P\\Cc\\CF} (white spaces + control + format + punctuation)\n  re.src_ZPCc = [re.src_Z, re.src_P, re.src_Cc].join('|')\n\n  // \\p{\\Z\\Cc} (white spaces + control)\n  re.src_ZCc = [re.src_Z, re.src_Cc].join('|')\n\n  // Experimental. List of chars, completely prohibited in links\n  // because can separate it from other part of text\n  const text_separators = '[><\\uff5c]'\n\n  // All possible word characters (everything without punctuation, spaces & controls)\n  // Defined via punctuation & spaces to save space\n  // Should be something like \\p{\\L\\N\\S\\M} (\\w but without `_`)\n  re.src_pseudo_letter = '(?:(?!' + text_separators + '|' + re.src_ZPCc + ')' + re.src_Any + ')'\n  // The same as abothe but without [0-9]\n  // var src_pseudo_letter_non_d = '(?:(?![0-9]|' + src_ZPCc + ')' + src_Any + ')';\n\n  re.src_ip4 =\n\n    '(?:(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\\\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)'\n\n  // Prohibit any of \"@/[]()\" in user/pass to avoid wrong domain fetch.\n  re.src_auth = '(?:(?:(?!' + re.src_ZCc + '|[@/\\\\[\\\\]()]).)+@)?'\n\n  re.src_port =\n\n    '(?::(?:6(?:[0-4]\\\\d{3}|5(?:[0-4]\\\\d{2}|5(?:[0-2]\\\\d|3[0-5])))|[1-5]?\\\\d{1,4}))?'\n\n  re.src_host_terminator =\n\n    '(?=$|' + text_separators + '|' + re.src_ZPCc + ')' +\n    '(?!' + (opts['---'] ? '-(?!--)|' : '-|') + '_|:\\\\d|\\\\.-|\\\\.(?!$|' + re.src_ZPCc + '))'\n\n  re.src_path =\n\n    '(?:' +\n      '[/?#]' +\n        '(?:' +\n          '(?!' + re.src_ZCc + '|' + text_separators + '|[()[\\\\]{}.,\"\\'?!\\\\-;]).|' +\n          '\\\\[(?:(?!' + re.src_ZCc + '|\\\\]).)*\\\\]|' +\n          '\\\\((?:(?!' + re.src_ZCc + '|[)]).)*\\\\)|' +\n          '\\\\{(?:(?!' + re.src_ZCc + '|[}]).)*\\\\}|' +\n          '\\\\\"(?:(?!' + re.src_ZCc + '|[\"]).)+\\\\\"|' +\n          \"\\\\'(?:(?!\" + re.src_ZCc + \"|[']).)+\\\\'|\" +\n\n          // allow `I'm_king` if no pair found\n          \"\\\\'(?=\" + re.src_pseudo_letter + '|[-])|' +\n\n          // google has many dots in \"google search\" links (#66, #81).\n          // github has ... in commit range links,\n          // Restrict to\n          // - english\n          // - percent-encoded\n          // - parts of file path\n          // - params separator\n          // until more examples found.\n          '\\\\.{2,}[a-zA-Z0-9%/&]|' +\n\n          '\\\\.(?!' + re.src_ZCc + '|[.]|$)|' +\n          (opts['---']\n            ? '\\\\-(?!--(?:[^-]|$))(?:-*)|' // `---` => long dash, terminate\n            : '\\\\-+|'\n          ) +\n          // allow `,,,` in paths\n          ',(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `;` if not followed by space-like char\n          ';(?!' + re.src_ZCc + '|$)|' +\n\n          // allow `!!!` in paths, but not at the end\n          '\\\\!+(?!' + re.src_ZCc + '|[!]|$)|' +\n\n          '\\\\?(?!' + re.src_ZCc + '|[?]|$)' +\n        ')+' +\n      '|\\\\/' +\n    ')?'\n\n  // Allow anything in markdown spec, forbid quote (\") at the first position\n  // because emails enclosed in quotes are far more common\n  re.src_email_name =\n\n    '[\\\\-;:&=\\\\+\\\\$,\\\\.a-zA-Z0-9_][\\\\-;:&=\\\\+\\\\$,\\\\\"\\\\.a-zA-Z0-9_]*'\n\n  re.src_xn =\n\n    'xn--[a-z0-9\\\\-]{1,59}'\n\n  // More to read about domain names\n  // http://serverfault.com/questions/638260/\n\n  re.src_domain_root =\n\n    // Allow letters & digits (http://test1)\n    '(?:' +\n      re.src_xn +\n      '|' +\n      re.src_pseudo_letter + '{1,63}' +\n    ')'\n\n  re.src_domain =\n\n    '(?:' +\n      re.src_xn +\n      '|' +\n      '(?:' + re.src_pseudo_letter + ')' +\n      '|' +\n      '(?:' + re.src_pseudo_letter + '(?:-|' + re.src_pseudo_letter + '){0,61}' + re.src_pseudo_letter + ')' +\n    ')'\n\n  re.src_host =\n\n    '(?:' +\n    // Don't need IP check, because digits are already allowed in normal domain names\n    //   src_ip4 +\n    // '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)*' + re.src_domain/* _root */ + ')' +\n    ')'\n\n  re.tpl_host_fuzzy =\n\n    '(?:' +\n      re.src_ip4 +\n    '|' +\n      '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))' +\n    ')'\n\n  re.tpl_host_no_ip_fuzzy =\n\n    '(?:(?:(?:' + re.src_domain + ')\\\\.)+(?:%TLDS%))'\n\n  re.src_host_strict =\n\n    re.src_host + re.src_host_terminator\n\n  re.tpl_host_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_host_terminator\n\n  re.src_host_port_strict =\n\n    re.src_host + re.src_port + re.src_host_terminator\n\n  re.tpl_host_port_fuzzy_strict =\n\n    re.tpl_host_fuzzy + re.src_port + re.src_host_terminator\n\n  re.tpl_host_port_no_ip_fuzzy_strict =\n\n    re.tpl_host_no_ip_fuzzy + re.src_port + re.src_host_terminator\n\n  //\n  // Main rules\n  //\n\n  // Rude test fuzzy links by host, for quick deny\n  re.tpl_host_fuzzy_test =\n\n    'localhost|www\\\\.|\\\\.\\\\d{1,3}\\\\.|(?:\\\\.(?:%TLDS%)(?:' + re.src_ZPCc + '|>|$))'\n\n  re.tpl_email_fuzzy =\n\n      '(^|' + text_separators + '|\"|\\\\(|' + re.src_ZCc + ')' +\n      '(' + re.src_email_name + '@' + re.tpl_host_fuzzy_strict + ')'\n\n  re.tpl_link_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_fuzzy_strict + re.src_path + ')'\n\n  re.tpl_link_no_ip_fuzzy =\n      // Fuzzy link can't be prepended with .:/\\- and non punctuation.\n      // but can start with > (markdown blockquote)\n      '(^|(?![.:/\\\\-_@])(?:[$+<=>^`|\\uff5c]|' + re.src_ZPCc + '))' +\n      '((?![$+<=>^`|\\uff5c])' + re.tpl_host_port_no_ip_fuzzy_strict + re.src_path + ')'\n\n  return re\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/linkify-it@5.0.0/node_modules/linkify-it/lib/re.mjs\n");

/***/ })

};
;