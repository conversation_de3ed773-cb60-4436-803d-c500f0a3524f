"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jotai-optics@0.4.0_jotai@2._093caeba7651b6cc95319889db2ee7d8";
exports.ids = ["vendor-chunks/jotai-optics@0.4.0_jotai@2._093caeba7651b6cc95319889db2ee7d8"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jotai-optics@0.4.0_jotai@2._093caeba7651b6cc95319889db2ee7d8/node_modules/jotai-optics/dist/focusAtom.js":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/jotai-optics@0.4.0_jotai@2._093caeba7651b6cc95319889db2ee7d8/node_modules/jotai-optics/dist/focusAtom.js ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   focusAtom: () => (/* binding */ focusAtom)\n/* harmony export */ });\n/* harmony import */ var jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jotai/vanilla */ \"(ssr)/./node_modules/.pnpm/jotai@2.8.4_@types+react@18.3.20_react@18.2.0/node_modules/jotai/esm/vanilla.mjs\");\n/* harmony import */ var optics_ts__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! optics-ts */ \"(ssr)/./node_modules/.pnpm/optics-ts@2.4.1/node_modules/optics-ts/dist/mjs/index.js\");\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\n\nconst getCached = (c, m, k) => (m.has(k) ? m : m.set(k, c())).get(k);\nconst cache1 = new WeakMap();\nconst memo2 = (create, dep1, dep2) => {\n    const cache2 = getCached(() => new WeakMap(), cache1, dep1);\n    return getCached(create, cache2, dep2);\n};\nconst isFunction = (x) => typeof x === 'function';\n// Implementation\nfunction focusAtom(baseAtom, callback) {\n    return memo2(() => {\n        const focus = callback(optics_ts__WEBPACK_IMPORTED_MODULE_0__.optic());\n        const derivedAtom = (0,jotai_vanilla__WEBPACK_IMPORTED_MODULE_1__.atom)((get) => {\n            const base = get(baseAtom);\n            return base instanceof Promise\n                ? base.then((v) => getValueUsingOptic(focus, v))\n                : getValueUsingOptic(focus, base);\n        }, (get, set, update) => {\n            const newValueProducer = isFunction(update)\n                ? optics_ts__WEBPACK_IMPORTED_MODULE_0__.modify(focus)(update)\n                : optics_ts__WEBPACK_IMPORTED_MODULE_0__.set(focus)(update);\n            const base = get(baseAtom);\n            return set(baseAtom, (base instanceof Promise\n                ? base.then(newValueProducer)\n                : newValueProducer(base)));\n        });\n        return derivedAtom;\n    }, baseAtom, callback);\n}\nconst getValueUsingOptic = (focus, bigValue) => {\n    if (focus._tag === 'Traversal') {\n        const values = optics_ts__WEBPACK_IMPORTED_MODULE_0__.collect(focus)(bigValue);\n        return values;\n    }\n    if (focus._tag === 'Prism') {\n        const value = optics_ts__WEBPACK_IMPORTED_MODULE_0__.preview(focus)(bigValue);\n        return value;\n    }\n    const value = optics_ts__WEBPACK_IMPORTED_MODULE_0__.get(focus)(bigValue);\n    return value;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jotai-optics@0.4.0_jotai@2._093caeba7651b6cc95319889db2ee7d8/node_modules/jotai-optics/dist/focusAtom.js\n");

/***/ })

};
;