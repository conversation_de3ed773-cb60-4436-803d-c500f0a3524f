"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/prosemirror-schema-basic@1.2.4";
exports.ids = ["vendor-chunks/prosemirror-schema-basic@1.2.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/prosemirror-schema-basic@1.2.4/node_modules/prosemirror-schema-basic/dist/index.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/prosemirror-schema-basic@1.2.4/node_modules/prosemirror-schema-basic/dist/index.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   marks: () => (/* binding */ marks),\n/* harmony export */   nodes: () => (/* binding */ nodes),\n/* harmony export */   schema: () => (/* binding */ schema)\n/* harmony export */ });\n/* harmony import */ var prosemirror_model__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! prosemirror-model */ \"(ssr)/./node_modules/.pnpm/prosemirror-model@1.25.0/node_modules/prosemirror-model/dist/index.js\");\n\n\nconst pDOM = [\"p\", 0], blockquoteDOM = [\"blockquote\", 0], hrDOM = [\"hr\"], preDOM = [\"pre\", [\"code\", 0]], brDOM = [\"br\"];\n/**\n[Specs](https://prosemirror.net/docs/ref/#model.NodeSpec) for the nodes defined in this schema.\n*/\nconst nodes = {\n    /**\n    NodeSpec The top level document node.\n    */\n    doc: {\n        content: \"block+\"\n    },\n    /**\n    A plain paragraph textblock. Represented in the DOM\n    as a `<p>` element.\n    */\n    paragraph: {\n        content: \"inline*\",\n        group: \"block\",\n        parseDOM: [{ tag: \"p\" }],\n        toDOM() { return pDOM; }\n    },\n    /**\n    A blockquote (`<blockquote>`) wrapping one or more blocks.\n    */\n    blockquote: {\n        content: \"block+\",\n        group: \"block\",\n        defining: true,\n        parseDOM: [{ tag: \"blockquote\" }],\n        toDOM() { return blockquoteDOM; }\n    },\n    /**\n    A horizontal rule (`<hr>`).\n    */\n    horizontal_rule: {\n        group: \"block\",\n        parseDOM: [{ tag: \"hr\" }],\n        toDOM() { return hrDOM; }\n    },\n    /**\n    A heading textblock, with a `level` attribute that\n    should hold the number 1 to 6. Parsed and serialized as `<h1>` to\n    `<h6>` elements.\n    */\n    heading: {\n        attrs: { level: { default: 1, validate: \"number\" } },\n        content: \"inline*\",\n        group: \"block\",\n        defining: true,\n        parseDOM: [{ tag: \"h1\", attrs: { level: 1 } },\n            { tag: \"h2\", attrs: { level: 2 } },\n            { tag: \"h3\", attrs: { level: 3 } },\n            { tag: \"h4\", attrs: { level: 4 } },\n            { tag: \"h5\", attrs: { level: 5 } },\n            { tag: \"h6\", attrs: { level: 6 } }],\n        toDOM(node) { return [\"h\" + node.attrs.level, 0]; }\n    },\n    /**\n    A code listing. Disallows marks or non-text inline\n    nodes by default. Represented as a `<pre>` element with a\n    `<code>` element inside of it.\n    */\n    code_block: {\n        content: \"text*\",\n        marks: \"\",\n        group: \"block\",\n        code: true,\n        defining: true,\n        parseDOM: [{ tag: \"pre\", preserveWhitespace: \"full\" }],\n        toDOM() { return preDOM; }\n    },\n    /**\n    The text node.\n    */\n    text: {\n        group: \"inline\"\n    },\n    /**\n    An inline image (`<img>`) node. Supports `src`,\n    `alt`, and `href` attributes. The latter two default to the empty\n    string.\n    */\n    image: {\n        inline: true,\n        attrs: {\n            src: { validate: \"string\" },\n            alt: { default: null, validate: \"string|null\" },\n            title: { default: null, validate: \"string|null\" }\n        },\n        group: \"inline\",\n        draggable: true,\n        parseDOM: [{ tag: \"img[src]\", getAttrs(dom) {\n                    return {\n                        src: dom.getAttribute(\"src\"),\n                        title: dom.getAttribute(\"title\"),\n                        alt: dom.getAttribute(\"alt\")\n                    };\n                } }],\n        toDOM(node) { let { src, alt, title } = node.attrs; return [\"img\", { src, alt, title }]; }\n    },\n    /**\n    A hard line break, represented in the DOM as `<br>`.\n    */\n    hard_break: {\n        inline: true,\n        group: \"inline\",\n        selectable: false,\n        parseDOM: [{ tag: \"br\" }],\n        toDOM() { return brDOM; }\n    }\n};\nconst emDOM = [\"em\", 0], strongDOM = [\"strong\", 0], codeDOM = [\"code\", 0];\n/**\n[Specs](https://prosemirror.net/docs/ref/#model.MarkSpec) for the marks in the schema.\n*/\nconst marks = {\n    /**\n    A link. Has `href` and `title` attributes. `title`\n    defaults to the empty string. Rendered and parsed as an `<a>`\n    element.\n    */\n    link: {\n        attrs: {\n            href: { validate: \"string\" },\n            title: { default: null, validate: \"string|null\" }\n        },\n        inclusive: false,\n        parseDOM: [{ tag: \"a[href]\", getAttrs(dom) {\n                    return { href: dom.getAttribute(\"href\"), title: dom.getAttribute(\"title\") };\n                } }],\n        toDOM(node) { let { href, title } = node.attrs; return [\"a\", { href, title }, 0]; }\n    },\n    /**\n    An emphasis mark. Rendered as an `<em>` element. Has parse rules\n    that also match `<i>` and `font-style: italic`.\n    */\n    em: {\n        parseDOM: [\n            { tag: \"i\" }, { tag: \"em\" },\n            { style: \"font-style=italic\" },\n            { style: \"font-style=normal\", clearMark: m => m.type.name == \"em\" }\n        ],\n        toDOM() { return emDOM; }\n    },\n    /**\n    A strong mark. Rendered as `<strong>`, parse rules also match\n    `<b>` and `font-weight: bold`.\n    */\n    strong: {\n        parseDOM: [\n            { tag: \"strong\" },\n            // This works around a Google Docs misbehavior where\n            // pasted content will be inexplicably wrapped in `<b>`\n            // tags with a font-weight normal.\n            { tag: \"b\", getAttrs: (node) => node.style.fontWeight != \"normal\" && null },\n            { style: \"font-weight=400\", clearMark: m => m.type.name == \"strong\" },\n            { style: \"font-weight\", getAttrs: (value) => /^(bold(er)?|[5-9]\\d{2,})$/.test(value) && null },\n        ],\n        toDOM() { return strongDOM; }\n    },\n    /**\n    Code font mark. Represented as a `<code>` element.\n    */\n    code: {\n        code: true,\n        parseDOM: [{ tag: \"code\" }],\n        toDOM() { return codeDOM; }\n    }\n};\n/**\nThis schema roughly corresponds to the document schema used by\n[CommonMark](http://commonmark.org/), minus the list elements,\nwhich are defined in the [`prosemirror-schema-list`](https://prosemirror.net/docs/ref/#schema-list)\nmodule.\n\nTo reuse elements from this schema, extend or read from its\n`spec.nodes` and `spec.marks` [properties](https://prosemirror.net/docs/ref/#model.Schema.spec).\n*/\nconst schema = new prosemirror_model__WEBPACK_IMPORTED_MODULE_0__.Schema({ nodes, marks });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/prosemirror-schema-basic@1.2.4/node_modules/prosemirror-schema-basic/dist/index.js\n");

/***/ })

};
;