"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-intersection-observer_79f19ea8133637cdae643aad9bf05255";
exports.ids = ["vendor-chunks/react-intersection-observer_79f19ea8133637cdae643aad9bf05255"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/react-intersection-observer_79f19ea8133637cdae643aad9bf05255/node_modules/react-intersection-observer/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/react-intersection-observer_79f19ea8133637cdae643aad9bf05255/node_modules/react-intersection-observer/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   InView: () => (/* binding */ InView),\n/* harmony export */   defaultFallbackInView: () => (/* binding */ defaultFallbackInView),\n/* harmony export */   observe: () => (/* binding */ observe),\n/* harmony export */   useInView: () => (/* binding */ useInView)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ InView,defaultFallbackInView,observe,useInView auto */ var __defProp = Object.defineProperty;\nvar __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {\n        enumerable: true,\n        configurable: true,\n        writable: true,\n        value\n    }) : obj[key] = value;\nvar __publicField = (obj, key, value)=>__defNormalProp(obj, typeof key !== \"symbol\" ? key + \"\" : key, value);\n// src/InView.tsx\n\n// src/observe.ts\nvar observerMap = /* @__PURE__ */ new Map();\nvar RootIds = /* @__PURE__ */ new WeakMap();\nvar rootId = 0;\nvar unsupportedValue = void 0;\nfunction defaultFallbackInView(inView) {\n    unsupportedValue = inView;\n}\nfunction getRootId(root) {\n    if (!root) return \"0\";\n    if (RootIds.has(root)) return RootIds.get(root);\n    rootId += 1;\n    RootIds.set(root, rootId.toString());\n    return RootIds.get(root);\n}\nfunction optionsToId(options) {\n    return Object.keys(options).sort().filter((key)=>options[key] !== void 0).map((key)=>{\n        return `${key}_${key === \"root\" ? getRootId(options.root) : options[key]}`;\n    }).toString();\n}\nfunction createObserver(options) {\n    const id = optionsToId(options);\n    let instance = observerMap.get(id);\n    if (!instance) {\n        const elements = /* @__PURE__ */ new Map();\n        let thresholds;\n        const observer = new IntersectionObserver((entries)=>{\n            entries.forEach((entry)=>{\n                var _a;\n                const inView = entry.isIntersecting && thresholds.some((threshold)=>entry.intersectionRatio >= threshold);\n                if (options.trackVisibility && typeof entry.isVisible === \"undefined\") {\n                    entry.isVisible = inView;\n                }\n                (_a = elements.get(entry.target)) == null ? void 0 : _a.forEach((callback)=>{\n                    callback(inView, entry);\n                });\n            });\n        }, options);\n        thresholds = observer.thresholds || (Array.isArray(options.threshold) ? options.threshold : [\n            options.threshold || 0\n        ]);\n        instance = {\n            id,\n            observer,\n            elements\n        };\n        observerMap.set(id, instance);\n    }\n    return instance;\n}\nfunction observe(element, callback, options = {}, fallbackInView = unsupportedValue) {\n    if (typeof window.IntersectionObserver === \"undefined\" && fallbackInView !== void 0) {\n        const bounds = element.getBoundingClientRect();\n        callback(fallbackInView, {\n            isIntersecting: fallbackInView,\n            target: element,\n            intersectionRatio: typeof options.threshold === \"number\" ? options.threshold : 0,\n            time: 0,\n            boundingClientRect: bounds,\n            intersectionRect: bounds,\n            rootBounds: bounds\n        });\n        return ()=>{};\n    }\n    const { id, observer, elements } = createObserver(options);\n    const callbacks = elements.get(element) || [];\n    if (!elements.has(element)) {\n        elements.set(element, callbacks);\n    }\n    callbacks.push(callback);\n    observer.observe(element);\n    return function unobserve() {\n        callbacks.splice(callbacks.indexOf(callback), 1);\n        if (callbacks.length === 0) {\n            elements.delete(element);\n            observer.unobserve(element);\n        }\n        if (elements.size === 0) {\n            observer.disconnect();\n            observerMap.delete(id);\n        }\n    };\n}\n// src/InView.tsx\nfunction isPlainChildren(props) {\n    return typeof props.children !== \"function\";\n}\nvar InView = class extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props){\n        super(props);\n        __publicField(this, \"node\", null);\n        __publicField(this, \"_unobserveCb\", null);\n        __publicField(this, \"handleNode\", (node)=>{\n            if (this.node) {\n                this.unobserve();\n                if (!node && !this.props.triggerOnce && !this.props.skip) {\n                    this.setState({\n                        inView: !!this.props.initialInView,\n                        entry: void 0\n                    });\n                }\n            }\n            this.node = node ? node : null;\n            this.observeNode();\n        });\n        __publicField(this, \"handleChange\", (inView, entry)=>{\n            if (inView && this.props.triggerOnce) {\n                this.unobserve();\n            }\n            if (!isPlainChildren(this.props)) {\n                this.setState({\n                    inView,\n                    entry\n                });\n            }\n            if (this.props.onChange) {\n                this.props.onChange(inView, entry);\n            }\n        });\n        this.state = {\n            inView: !!props.initialInView,\n            entry: void 0\n        };\n    }\n    componentDidMount() {\n        this.unobserve();\n        this.observeNode();\n    }\n    componentDidUpdate(prevProps) {\n        if (prevProps.rootMargin !== this.props.rootMargin || prevProps.root !== this.props.root || prevProps.threshold !== this.props.threshold || prevProps.skip !== this.props.skip || prevProps.trackVisibility !== this.props.trackVisibility || prevProps.delay !== this.props.delay) {\n            this.unobserve();\n            this.observeNode();\n        }\n    }\n    componentWillUnmount() {\n        this.unobserve();\n    }\n    observeNode() {\n        if (!this.node || this.props.skip) return;\n        const { threshold, root, rootMargin, trackVisibility, delay, fallbackInView } = this.props;\n        this._unobserveCb = observe(this.node, this.handleChange, {\n            threshold,\n            root,\n            rootMargin,\n            // @ts-ignore\n            trackVisibility,\n            // @ts-ignore\n            delay\n        }, fallbackInView);\n    }\n    unobserve() {\n        if (this._unobserveCb) {\n            this._unobserveCb();\n            this._unobserveCb = null;\n        }\n    }\n    render() {\n        const { children } = this.props;\n        if (typeof children === \"function\") {\n            const { inView, entry } = this.state;\n            return children({\n                inView,\n                entry,\n                ref: this.handleNode\n            });\n        }\n        const { as, triggerOnce, threshold, root, rootMargin, onChange, skip, trackVisibility, delay, initialInView, fallbackInView, ...props } = this.props;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(as || \"div\", {\n            ref: this.handleNode,\n            ...props\n        }, children);\n    }\n};\n// src/useInView.tsx\n\nfunction useInView({ threshold, delay, trackVisibility, rootMargin, root, triggerOnce, skip, initialInView, fallbackInView, onChange } = {}) {\n    var _a;\n    const [ref, setRef] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const callback = react__WEBPACK_IMPORTED_MODULE_0__.useRef(onChange);\n    const [state, setState] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        inView: !!initialInView,\n        entry: void 0\n    });\n    callback.current = onChange;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (skip || !ref) return;\n        let unobserve;\n        unobserve = observe(ref, (inView, entry)=>{\n            setState({\n                inView,\n                entry\n            });\n            if (callback.current) callback.current(inView, entry);\n            if (entry.isIntersecting && triggerOnce && unobserve) {\n                unobserve();\n                unobserve = void 0;\n            }\n        }, {\n            root,\n            rootMargin,\n            threshold,\n            // @ts-ignore\n            trackVisibility,\n            // @ts-ignore\n            delay\n        }, fallbackInView);\n        return ()=>{\n            if (unobserve) {\n                unobserve();\n            }\n        };\n    }, // We break the rule here, because we aren't including the actual `threshold` variable\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        // If the threshold is an array, convert it to a string, so it won't change between renders.\n        Array.isArray(threshold) ? threshold.toString() : threshold,\n        ref,\n        root,\n        rootMargin,\n        triggerOnce,\n        skip,\n        trackVisibility,\n        fallbackInView,\n        delay\n    ]);\n    const entryTarget = (_a = state.entry) == null ? void 0 : _a.target;\n    const previousEntryTarget = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    if (!ref && entryTarget && !triggerOnce && !skip && previousEntryTarget.current !== entryTarget) {\n        previousEntryTarget.current = entryTarget;\n        setState({\n            inView: !!initialInView,\n            entry: void 0\n        });\n    }\n    const result = [\n        setRef,\n        state.inView,\n        state.entry\n    ];\n    result.ref = result[0];\n    result.inView = result[1];\n    result.entry = result[2];\n    return result;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/react-intersection-observer_79f19ea8133637cdae643aad9bf05255/node_modules/react-intersection-observer/dist/index.mjs\n");

/***/ })

};
;