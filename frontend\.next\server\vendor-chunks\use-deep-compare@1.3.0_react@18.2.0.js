"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/use-deep-compare@1.3.0_react@18.2.0";
exports.ids = ["vendor-chunks/use-deep-compare@1.3.0_react@18.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/use-deep-compare@1.3.0_react@18.2.0/node_modules/use-deep-compare/dist-web/index.js":
/*!****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/use-deep-compare@1.3.0_react@18.2.0/node_modules/use-deep-compare/dist-web/index.js ***!
  \****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDeepCompareCallback: () => (/* binding */ useDeepCompareCallback),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDeepCompareImperativeHandle: () => (/* binding */ useDeepCompareImperativeHandle),\n/* harmony export */   useDeepCompareLayoutEffect: () => (/* binding */ useDeepCompareLayoutEffect),\n/* harmony export */   useDeepCompareMemo: () => (/* binding */ useDeepCompareMemo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var dequal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! dequal */ \"(ssr)/./node_modules/.pnpm/dequal@2.0.3/node_modules/dequal/dist/index.mjs\");\n\n\n\nfunction useDeepCompareMemoize(dependencies) {\n  const dependenciesRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(dependencies);\n  const signalRef = react__WEBPACK_IMPORTED_MODULE_0___default().useRef(0);\n\n  if (!(0,dequal__WEBPACK_IMPORTED_MODULE_1__.dequal)(dependencies, dependenciesRef.current)) {\n    dependenciesRef.current = dependencies;\n    signalRef.current += 1;\n  }\n\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(() => dependenciesRef.current, [signalRef.current]);\n}\n\n/**\n * `useDeepCompareCallback` will return a memoized version of the callback that\n * only changes if one of the `dependencies` has changed.\n *\n * Warning: `useDeepCompareCallback` should not be used with dependencies that\n * are all primitive values. Use `React.useCallback` instead.\n *\n * @see {@link https://react.dev/reference/react/useCallback}\n */\n\nfunction useDeepCompareCallback(callback, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useCallback(callback, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * Accepts a function that contains imperative, possibly effectful code.\n *\n * Warning: `useDeepCompareEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useEffect}\n */\n\nfunction useDeepCompareEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareImperativeHandle` customizes the instance value that is exposed to parent components when using `ref`.\n * As always, imperative code using refs should be avoided in most cases.\n *\n * `useDeepCompareImperativeHandle` should be used with `React.forwardRef`.\n *\n * It's similar to `useImperativeHandle`, but uses deep comparison on the dependencies.\n *\n * Warning: `useDeepCompareImperativeHandle` should not be used with dependencies that\n * are all primitive values. Use `React.useImperativeHandle` instead.\n *\n * @see {@link https://react.dev/reference/react/useImperativeHandle}\n */\n\nfunction useDeepCompareImperativeHandle(ref, init, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useImperativeHandle(ref, init, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * The signature is identical to `useDeepCompareEffect`, but it fires synchronously after all DOM mutations.\n * Use this to read layout from the DOM and synchronously re-render. Updates scheduled inside\n * `useDeepCompareLayoutEffect` will be flushed synchronously, before the browser has a chance to paint.\n *\n * Prefer the standard `useDeepCompareEffect` when possible to avoid blocking visual updates.\n *\n * If you’re migrating code from a class component, `useDeepCompareLayoutEffect` fires in the same phase as\n * `componentDidMount` and `componentDidUpdate`.\n *\n * Warning: `useDeepCompareLayoutEffect` should not be used with dependencies that\n * are all primitive values. Use `React.useLayoutEffect` instead.\n *\n * @see {@link https://react.dev/reference/react/useLayoutEffect}\n */\n\nfunction useDeepCompareLayoutEffect(effect, dependencies) {\n  react__WEBPACK_IMPORTED_MODULE_0___default().useLayoutEffect(effect, useDeepCompareMemoize(dependencies));\n}\n\n/**\n * `useDeepCompareMemo` will only recompute the memoized value when one of the\n * `dependencies` has changed.\n *\n * Warning: `useDeepCompareMemo` should not be used with dependencies that\n * are all primitive values. Use `React.useMemo` instead.\n *\n * @see {@link https://react.dev/reference/react/useMemo}\n */\n\nfunction useDeepCompareMemo(factory, dependencies) {\n  return react__WEBPACK_IMPORTED_MODULE_0___default().useMemo(factory, useDeepCompareMemoize(dependencies));\n}\n\n\n//# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/use-deep-compare@1.3.0_react@18.2.0/node_modules/use-deep-compare/dist-web/index.js\n");

/***/ })

};
;