"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@udecode+react-utils@40.2.8_82a5c5987c4e3d62e9a0af27379e8c42";
exports.ids = ["vendor-chunks/@udecode+react-utils@40.2.8_82a5c5987c4e3d62e9a0af27379e8c42"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@udecode+react-utils@40.2.8_82a5c5987c4e3d62e9a0af27379e8c42/node_modules/@udecode/react-utils/dist/index.mjs":
/*!******************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@udecode+react-utils@40.2.8_82a5c5987c4e3d62e9a0af27379e8c42/node_modules/@udecode/react-utils/dist/index.mjs ***!
  \******************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Box: () => (/* binding */ Box),\n/* harmony export */   CAN_USE_DOM: () => (/* binding */ CAN_USE_DOM),\n/* harmony export */   DEFAULT_IGNORE_CLASS: () => (/* binding */ DEFAULT_IGNORE_CLASS),\n/* harmony export */   MemoizedChildren: () => (/* binding */ MemoizedChildren),\n/* harmony export */   PortalBody: () => (/* binding */ PortalBody),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers),\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   createPrimitiveComponent: () => (/* binding */ createPrimitiveComponent),\n/* harmony export */   createPrimitiveElement: () => (/* binding */ createPrimitiveElement),\n/* harmony export */   createSlotComponent: () => (/* binding */ createSlotComponent),\n/* harmony export */   useComposedRef: () => (/* binding */ useComposedRef),\n/* harmony export */   useEffectOnce: () => (/* binding */ useEffectOnce),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useMemoOnce: () => (/* binding */ useMemoOnce),\n/* harmony export */   useOnClickOutside: () => (/* binding */ useOnClickOutside),\n/* harmony export */   useStableMemo: () => (/* binding */ useStableMemo),\n/* harmony export */   withProviders: () => (/* binding */ withProviders),\n/* harmony export */   withRef: () => (/* binding */ withRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.1.2_@types+react@18.3.20_react@18.2.0/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _udecode_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @udecode/utils */ \"(ssr)/./node_modules/.pnpm/@udecode+utils@37.0.0/node_modules/@udecode/utils/dist/index.mjs\");\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/.pnpm/clsx@2.1.1/node_modules/clsx/dist/clsx.mjs\");\nvar __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __objRest = (source, exclude) => {\n  var target = {};\n  for (var prop in source)\n    if (__hasOwnProp.call(source, prop) && exclude.indexOf(prop) < 0)\n      target[prop] = source[prop];\n  if (source != null && __getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(source)) {\n      if (exclude.indexOf(prop) < 0 && __propIsEnum.call(source, prop))\n        target[prop] = source[prop];\n    }\n  return target;\n};\n\n// src/createSlotComponent.tsx\n\n\nvar createSlotComponent = (element) => (\n  // eslint-disable-next-line react/display-name\n  react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((_a2, ref) => {\n    var _b = _a2, { as, asChild = false } = _b, props = __objRest(_b, [\"as\", \"asChild\"]);\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_1__.Slot : as || element;\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Comp, __spreadValues({ ref }, props));\n  })\n);\n\n// src/Box.tsx\nvar Box = createSlotComponent(\"div\");\n\n// src/MemoizedChildren.tsx\n\nvar MemoizedChildren = react__WEBPACK_IMPORTED_MODULE_0__.memo(\n  ({ children }) => {\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n  }\n);\n\n// src/PortalBody.tsx\n\n\nvar PortalBody = ({\n  children,\n  element\n}) => {\n  const container = element || typeof window !== \"undefined\" ? document.body : void 0;\n  if (!container) return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n  return react_dom__WEBPACK_IMPORTED_MODULE_2__.createPortal(children, element || document.body);\n};\n\n// src/Text.tsx\nvar Text = createSlotComponent(\"span\");\n\n// src/composeEventHandlers.ts\nvar composeEventHandlers = (originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) => (event) => {\n  originalEventHandler == null ? void 0 : originalEventHandler(event);\n  if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n    return ourEventHandler == null ? void 0 : ourEventHandler(event);\n  }\n};\n\n// src/createPrimitiveComponent.tsx\n\n\n\n\n// src/useComposedRef.ts\n\nvar setRef = (ref, value) => {\n  if (typeof ref === \"function\") {\n    ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n};\nvar composeRefs = (...refs) => (node) => refs.forEach((ref) => setRef(ref, node));\nvar useComposedRef = (...refs) => {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n};\n\n// src/createPrimitiveComponent.tsx\nvar createPrimitiveComponent = (element) => {\n  const Comp = createSlotComponent(element);\n  return ({\n    propsHook,\n    stateHook\n  } = {}) => {\n    return react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n      (_a2, ref) => {\n        var _b = _a2, {\n          asChild,\n          className: classNameProp,\n          getClassName,\n          options,\n          state: stateProp\n        } = _b, props = __objRest(_b, [\n          \"asChild\",\n          \"className\",\n          \"getClassName\",\n          \"options\",\n          \"state\"\n        ]);\n        var _a3, _b2;\n        const state = (0,_udecode_utils__WEBPACK_IMPORTED_MODULE_4__.isDefined)(stateProp) ? stateProp : stateHook ? stateHook(options) : void 0;\n        const {\n          hidden,\n          props: hookProps,\n          ref: hookRef\n        } = propsHook ? propsHook(state) : { hidden: false, props: {}, ref: null };\n        const _ref = useComposedRef(ref, hookRef);\n        const className = (0,_udecode_utils__WEBPACK_IMPORTED_MODULE_4__.isDefined)(hookProps == null ? void 0 : hookProps.className) || (0,_udecode_utils__WEBPACK_IMPORTED_MODULE_4__.isDefined)(classNameProp) ? (0,clsx__WEBPACK_IMPORTED_MODULE_3__.clsx)(hookProps == null ? void 0 : hookProps.className, classNameProp) : void 0;\n        const style = (hookProps == null ? void 0 : hookProps.style) || props.style ? __spreadValues(__spreadValues({}, hookProps == null ? void 0 : hookProps.style), props.style) : void 0;\n        if (!asChild && hidden) return null;\n        return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\n          Comp,\n          __spreadValues(__spreadValues(__spreadProps(__spreadValues({\n            ref: _ref,\n            asChild\n          }, hookProps), {\n            className,\n            style\n          }), props), (_b2 = (_a3 = props.setProps) == null ? void 0 : _a3.call(props, hookProps != null ? hookProps : {})) != null ? _b2 : {})\n        );\n      }\n    );\n  };\n};\n\n// src/createPrimitiveElement.tsx\n\nfunction createPrimitiveElement(tag) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(\n    function CreateComponent(props, ref) {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createElement(tag, __spreadProps(__spreadValues({}, props), { ref }));\n    }\n  );\n}\n\n// src/useEffectOnce.ts\n\nfunction useEffectOnce(effect, deps) {\n  const initialized = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const prevDepsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(deps);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    const depsChanged = deps.some((dep, i) => dep !== prevDepsRef.current[i]);\n    if (!initialized.current || depsChanged) {\n      initialized.current = true;\n      prevDepsRef.current = deps;\n      effect();\n    }\n  }, deps);\n}\n\n// src/useIsomorphicLayoutEffect.ts\n\nvar _a;\nvar CAN_USE_DOM = typeof window !== \"undefined\" && ((_a = window.document) == null ? void 0 : _a.createElement) !== void 0;\nvar useIsomorphicLayoutEffect = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n// src/useMemoOnce.ts\n\nfunction useMemoOnce(factory, deps) {\n  const initialized = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n  const prevDepsRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(deps);\n  const memoizedValueRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef();\n  if (!initialized.current || deps.some((dep, i) => dep !== prevDepsRef.current[i])) {\n    initialized.current = true;\n    prevDepsRef.current = deps;\n    memoizedValueRef.current = factory();\n  }\n  return memoizedValueRef.current;\n}\n\n// src/useOnClickOutside.ts\n\nvar canUsePassiveEvents = () => {\n  if (typeof window === \"undefined\" || typeof window.addEventListener !== \"function\")\n    return false;\n  let passive = false;\n  const options = Object.defineProperty({}, \"passive\", {\n    // eslint-disable-next-line getter-return\n    get() {\n      passive = true;\n    }\n  });\n  const noop = () => null;\n  window.addEventListener(\"test\", noop, options);\n  window.removeEventListener(\"test\", noop, options);\n  return passive;\n};\nvar DEFAULT_IGNORE_CLASS = \"ignore-onclickoutside\";\nvar checkClass = (el, cl) => {\n  var _a2;\n  return (_a2 = el.classList) == null ? void 0 : _a2.contains(cl);\n};\nvar hasIgnoreClass = (e, ignoreClass) => {\n  let el = e.target || e;\n  while (el) {\n    if (Array.isArray(ignoreClass)) {\n      if (ignoreClass.some((c) => checkClass(el, c))) return true;\n    } else if (checkClass(el, ignoreClass)) {\n      return true;\n    }\n    el = el.parentElement;\n  }\n  return false;\n};\nvar clickedOnScrollbar = (e) => document.documentElement.clientWidth <= e.clientX || document.documentElement.clientHeight <= e.clientY;\nvar getEventOptions = (type) => type.includes(\"touch\") && canUsePassiveEvents() ? { passive: true } : false;\nvar useOnClickOutside = (callback, {\n  detectIFrame = true,\n  disabled,\n  eventTypes = [\"mousedown\", \"touchstart\"],\n  excludeScrollbar,\n  ignoreClass = DEFAULT_IGNORE_CLASS,\n  refs: refsOpt\n} = {}) => {\n  const [refsState, setRefsState] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  callbackRef.current = callback;\n  const ref = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(\n    (el) => setRefsState((prevState) => [...prevState, { current: el }]),\n    []\n  );\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(\n    () => {\n      if (!(refsOpt == null ? void 0 : refsOpt.length) && refsState.length === 0) return;\n      const getEls = () => {\n        const els = [];\n        (refsOpt || refsState).forEach(\n          ({ current }) => current && els.push(current)\n        );\n        return els;\n      };\n      const handler = (e) => {\n        if (!hasIgnoreClass(e, ignoreClass) && !(excludeScrollbar && clickedOnScrollbar(e)) && getEls().every((el) => !el.contains(e.target)))\n          callbackRef.current(e);\n      };\n      const blurHandler = (e) => (\n        // On firefox the iframe becomes document.activeElement in the next event loop\n        setTimeout(() => {\n          const { activeElement } = document;\n          if ((activeElement == null ? void 0 : activeElement.tagName) === \"IFRAME\" && !hasIgnoreClass(activeElement, ignoreClass) && !getEls().includes(activeElement))\n            callbackRef.current(e);\n        }, 0)\n      );\n      const removeEventListener = () => {\n        eventTypes.forEach(\n          (type) => document.removeEventListener(\n            type,\n            handler,\n            getEventOptions(type)\n          )\n        );\n        if (detectIFrame) window.removeEventListener(\"blur\", blurHandler);\n      };\n      if (disabled) {\n        removeEventListener();\n        return;\n      }\n      eventTypes.forEach(\n        (type) => document.addEventListener(type, handler, getEventOptions(type))\n      );\n      if (detectIFrame) window.addEventListener(\"blur\", blurHandler);\n      return () => removeEventListener();\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n      refsState,\n      ignoreClass,\n      excludeScrollbar,\n      disabled,\n      detectIFrame,\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      JSON.stringify(eventTypes)\n    ]\n  );\n  return ref;\n};\n\n// src/useStableMemo.ts\n\nvar useStableMemo = (producer, deps) => {\n  const [value, setValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(producer);\n  react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect(() => {\n    setValue(producer);\n  }, deps);\n  return value;\n};\n\n// src/withProviders.tsx\n\nvar withProviders = (...providers) => (WrappedComponent) => (props) => providers.reduceRight(\n  (acc, prov) => {\n    let Provider = prov;\n    if (Array.isArray(prov)) {\n      [Provider] = prov;\n      return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Provider, __spreadValues({}, prov[1]), acc);\n    }\n    return /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Provider, null, acc);\n  },\n  /* @__PURE__ */ react__WEBPACK_IMPORTED_MODULE_0__.createElement(WrappedComponent, __spreadValues({}, props))\n);\n\n// src/withRef.tsx\n\nfunction withRef(renderFunction) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(renderFunction);\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@udecode+react-utils@40.2.8_82a5c5987c4e3d62e9a0af27379e8c42/node_modules/@udecode/react-utils/dist/index.mjs\n");

/***/ })

};
;