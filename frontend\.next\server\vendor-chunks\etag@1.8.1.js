"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/etag@1.8.1";
exports.ids = ["vendor-chunks/etag@1.8.1"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/etag@1.8.1/node_modules/etag/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/.pnpm/etag@1.8.1/node_modules/etag/index.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * etag\n * Copyright(c) 2014-2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = etag\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar crypto = __webpack_require__(/*! crypto */ \"crypto\")\nvar Stats = (__webpack_require__(/*! fs */ \"fs\").Stats)\n\n/**\n * Module variables.\n * @private\n */\n\nvar toString = Object.prototype.toString\n\n/**\n * Generate an entity tag.\n *\n * @param {Buffer|string} entity\n * @return {string}\n * @private\n */\n\nfunction entitytag (entity) {\n  if (entity.length === 0) {\n    // fast-path empty\n    return '\"0-2jmj7l5rSw0yVb/vlWAYkK/YBwk\"'\n  }\n\n  // compute hash of entity\n  var hash = crypto\n    .createHash('sha1')\n    .update(entity, 'utf8')\n    .digest('base64')\n    .substring(0, 27)\n\n  // compute length of entity\n  var len = typeof entity === 'string'\n    ? Buffer.byteLength(entity, 'utf8')\n    : entity.length\n\n  return '\"' + len.toString(16) + '-' + hash + '\"'\n}\n\n/**\n * Create a simple ETag.\n *\n * @param {string|Buffer|Stats} entity\n * @param {object} [options]\n * @param {boolean} [options.weak]\n * @return {String}\n * @public\n */\n\nfunction etag (entity, options) {\n  if (entity == null) {\n    throw new TypeError('argument entity is required')\n  }\n\n  // support fs.Stats object\n  var isStats = isstats(entity)\n  var weak = options && typeof options.weak === 'boolean'\n    ? options.weak\n    : isStats\n\n  // validate argument\n  if (!isStats && typeof entity !== 'string' && !Buffer.isBuffer(entity)) {\n    throw new TypeError('argument entity must be string, Buffer, or fs.Stats')\n  }\n\n  // generate entity tag\n  var tag = isStats\n    ? stattag(entity)\n    : entitytag(entity)\n\n  return weak\n    ? 'W/' + tag\n    : tag\n}\n\n/**\n * Determine if object is a Stats object.\n *\n * @param {object} obj\n * @return {boolean}\n * @api private\n */\n\nfunction isstats (obj) {\n  // genuine fs.Stats\n  if (typeof Stats === 'function' && obj instanceof Stats) {\n    return true\n  }\n\n  // quack quack\n  return obj && typeof obj === 'object' &&\n    'ctime' in obj && toString.call(obj.ctime) === '[object Date]' &&\n    'mtime' in obj && toString.call(obj.mtime) === '[object Date]' &&\n    'ino' in obj && typeof obj.ino === 'number' &&\n    'size' in obj && typeof obj.size === 'number'\n}\n\n/**\n * Generate a tag for a stat.\n *\n * @param {object} stat\n * @return {string}\n * @private\n */\n\nfunction stattag (stat) {\n  var mtime = stat.mtime.getTime().toString(16)\n  var size = stat.size.toString(16)\n\n  return '\"' + size + '-' + mtime + '\"'\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/etag@1.8.1/node_modules/etag/index.js\n");

/***/ })

};
;