"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/proxy-addr@2.0.7";
exports.ids = ["vendor-chunks/proxy-addr@2.0.7"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/proxy-addr@2.0.7/node_modules/proxy-addr/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/proxy-addr@2.0.7/node_modules/proxy-addr/index.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/*!\n * proxy-addr\n * Copyright(c) 2014-2016 Douglas Christopher Wilson\n * MIT Licensed\n */\n\n\n\n/**\n * Module exports.\n * @public\n */\n\nmodule.exports = proxyaddr\nmodule.exports.all = alladdrs\nmodule.exports.compile = compile\n\n/**\n * Module dependencies.\n * @private\n */\n\nvar forwarded = __webpack_require__(/*! forwarded */ \"(rsc)/./node_modules/.pnpm/forwarded@0.2.0/node_modules/forwarded/index.js\")\nvar ipaddr = __webpack_require__(/*! ipaddr.js */ \"(rsc)/./node_modules/.pnpm/ipaddr.js@1.9.1/node_modules/ipaddr.js/lib/ipaddr.js\")\n\n/**\n * Variables.\n * @private\n */\n\nvar DIGIT_REGEXP = /^[0-9]+$/\nvar isip = ipaddr.isValid\nvar parseip = ipaddr.parse\n\n/**\n * Pre-defined IP ranges.\n * @private\n */\n\nvar IP_RANGES = {\n  linklocal: ['***********/16', 'fe80::/10'],\n  loopback: ['127.0.0.1/8', '::1/128'],\n  uniquelocal: ['10.0.0.0/8', '**********/12', '***********/16', 'fc00::/7']\n}\n\n/**\n * Get all addresses in the request, optionally stopping\n * at the first untrusted.\n *\n * @param {Object} request\n * @param {Function|Array|String} [trust]\n * @public\n */\n\nfunction alladdrs (req, trust) {\n  // get addresses\n  var addrs = forwarded(req)\n\n  if (!trust) {\n    // Return all addresses\n    return addrs\n  }\n\n  if (typeof trust !== 'function') {\n    trust = compile(trust)\n  }\n\n  for (var i = 0; i < addrs.length - 1; i++) {\n    if (trust(addrs[i], i)) continue\n\n    addrs.length = i + 1\n  }\n\n  return addrs\n}\n\n/**\n * Compile argument into trust function.\n *\n * @param {Array|String} val\n * @private\n */\n\nfunction compile (val) {\n  if (!val) {\n    throw new TypeError('argument is required')\n  }\n\n  var trust\n\n  if (typeof val === 'string') {\n    trust = [val]\n  } else if (Array.isArray(val)) {\n    trust = val.slice()\n  } else {\n    throw new TypeError('unsupported trust argument')\n  }\n\n  for (var i = 0; i < trust.length; i++) {\n    val = trust[i]\n\n    if (!Object.prototype.hasOwnProperty.call(IP_RANGES, val)) {\n      continue\n    }\n\n    // Splice in pre-defined range\n    val = IP_RANGES[val]\n    trust.splice.apply(trust, [i, 1].concat(val))\n    i += val.length - 1\n  }\n\n  return compileTrust(compileRangeSubnets(trust))\n}\n\n/**\n * Compile `arr` elements into range subnets.\n *\n * @param {Array} arr\n * @private\n */\n\nfunction compileRangeSubnets (arr) {\n  var rangeSubnets = new Array(arr.length)\n\n  for (var i = 0; i < arr.length; i++) {\n    rangeSubnets[i] = parseipNotation(arr[i])\n  }\n\n  return rangeSubnets\n}\n\n/**\n * Compile range subnet array into trust function.\n *\n * @param {Array} rangeSubnets\n * @private\n */\n\nfunction compileTrust (rangeSubnets) {\n  // Return optimized function based on length\n  var len = rangeSubnets.length\n  return len === 0\n    ? trustNone\n    : len === 1\n      ? trustSingle(rangeSubnets[0])\n      : trustMulti(rangeSubnets)\n}\n\n/**\n * Parse IP notation string into range subnet.\n *\n * @param {String} note\n * @private\n */\n\nfunction parseipNotation (note) {\n  var pos = note.lastIndexOf('/')\n  var str = pos !== -1\n    ? note.substring(0, pos)\n    : note\n\n  if (!isip(str)) {\n    throw new TypeError('invalid IP address: ' + str)\n  }\n\n  var ip = parseip(str)\n\n  if (pos === -1 && ip.kind() === 'ipv6' && ip.isIPv4MappedAddress()) {\n    // Store as IPv4\n    ip = ip.toIPv4Address()\n  }\n\n  var max = ip.kind() === 'ipv6'\n    ? 128\n    : 32\n\n  var range = pos !== -1\n    ? note.substring(pos + 1, note.length)\n    : null\n\n  if (range === null) {\n    range = max\n  } else if (DIGIT_REGEXP.test(range)) {\n    range = parseInt(range, 10)\n  } else if (ip.kind() === 'ipv4' && isip(range)) {\n    range = parseNetmask(range)\n  } else {\n    range = null\n  }\n\n  if (range <= 0 || range > max) {\n    throw new TypeError('invalid range on address: ' + note)\n  }\n\n  return [ip, range]\n}\n\n/**\n * Parse netmask string into CIDR range.\n *\n * @param {String} netmask\n * @private\n */\n\nfunction parseNetmask (netmask) {\n  var ip = parseip(netmask)\n  var kind = ip.kind()\n\n  return kind === 'ipv4'\n    ? ip.prefixLengthFromSubnetMask()\n    : null\n}\n\n/**\n * Determine address of proxied request.\n *\n * @param {Object} request\n * @param {Function|Array|String} trust\n * @public\n */\n\nfunction proxyaddr (req, trust) {\n  if (!req) {\n    throw new TypeError('req argument is required')\n  }\n\n  if (!trust) {\n    throw new TypeError('trust argument is required')\n  }\n\n  var addrs = alladdrs(req, trust)\n  var addr = addrs[addrs.length - 1]\n\n  return addr\n}\n\n/**\n * Static trust function to trust nothing.\n *\n * @private\n */\n\nfunction trustNone () {\n  return false\n}\n\n/**\n * Compile trust function for multiple subnets.\n *\n * @param {Array} subnets\n * @private\n */\n\nfunction trustMulti (subnets) {\n  return function trust (addr) {\n    if (!isip(addr)) return false\n\n    var ip = parseip(addr)\n    var ipconv\n    var kind = ip.kind()\n\n    for (var i = 0; i < subnets.length; i++) {\n      var subnet = subnets[i]\n      var subnetip = subnet[0]\n      var subnetkind = subnetip.kind()\n      var subnetrange = subnet[1]\n      var trusted = ip\n\n      if (kind !== subnetkind) {\n        if (subnetkind === 'ipv4' && !ip.isIPv4MappedAddress()) {\n          // Incompatible IP addresses\n          continue\n        }\n\n        if (!ipconv) {\n          // Convert IP to match subnet IP kind\n          ipconv = subnetkind === 'ipv4'\n            ? ip.toIPv4Address()\n            : ip.toIPv4MappedAddress()\n        }\n\n        trusted = ipconv\n      }\n\n      if (trusted.match(subnetip, subnetrange)) {\n        return true\n      }\n    }\n\n    return false\n  }\n}\n\n/**\n * Compile trust function for single subnet.\n *\n * @param {Object} subnet\n * @private\n */\n\nfunction trustSingle (subnet) {\n  var subnetip = subnet[0]\n  var subnetkind = subnetip.kind()\n  var subnetisipv4 = subnetkind === 'ipv4'\n  var subnetrange = subnet[1]\n\n  return function trust (addr) {\n    if (!isip(addr)) return false\n\n    var ip = parseip(addr)\n    var kind = ip.kind()\n\n    if (kind !== subnetkind) {\n      if (subnetisipv4 && !ip.isIPv4MappedAddress()) {\n        // Incompatible IP addresses\n        return false\n      }\n\n      // Convert IP to match subnet IP kind\n      ip = subnetisipv4\n        ? ip.toIPv4Address()\n        : ip.toIPv4MappedAddress()\n    }\n\n    return ip.match(subnetip, subnetrange)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/proxy-addr@2.0.7/node_modules/proxy-addr/index.js\n");

/***/ })

};
;