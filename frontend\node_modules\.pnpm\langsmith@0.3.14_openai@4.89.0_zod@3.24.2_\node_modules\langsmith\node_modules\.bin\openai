#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules/openai/bin/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules/openai/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules/openai/bin/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules/openai/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/openai@4.89.0_zod@3.24.2/node_modules:/mnt/e/MultiAgentPPT/frontend/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../openai@4.89.0_zod@3.24.2/node_modules/openai/bin/cli" "$@"
else
  exec node  "$basedir/../../../../../openai@4.89.0_zod@3.24.2/node_modules/openai/bin/cli" "$@"
fi
