"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@ai-sdk+provider@1.1.0";
exports.ids = ["vendor-chunks/@ai-sdk+provider@1.1.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AISDKError: () => (/* binding */ AISDKError),\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoSuchModelError: () => (/* binding */ NoSuchModelError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isJSONArray: () => (/* binding */ isJSONArray),\n/* harmony export */   isJSONObject: () => (/* binding */ isJSONObject),\n/* harmony export */   isJSONValue: () => (/* binding */ isJSONValue)\n/* harmony export */ });\n// src/errors/ai-sdk-error.ts\nvar marker = \"vercel.ai.error\";\nvar symbol = Symbol.for(marker);\nvar _a;\nvar _AISDKError = class _AISDKError extends Error {\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name: name14,\n    message,\n    cause\n  }) {\n    super(message);\n    this[_a] = true;\n    this.name = name14;\n    this.cause = cause;\n  }\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error) {\n    return _AISDKError.hasMarker(error, marker);\n  }\n  static hasMarker(error, marker15) {\n    const markerSymbol = Symbol.for(marker15);\n    return error != null && typeof error === \"object\" && markerSymbol in error && typeof error[markerSymbol] === \"boolean\" && error[markerSymbol] === true;\n  }\n};\n_a = symbol;\nvar AISDKError = _AISDKError;\n\n// src/errors/api-call-error.ts\nvar name = \"AI_APICallError\";\nvar marker2 = `vercel.ai.error.${name}`;\nvar symbol2 = Symbol.for(marker2);\nvar _a2;\nvar APICallError = class extends AISDKError {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super({ name, message, cause });\n    this[_a2] = true;\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker2);\n  }\n};\n_a2 = symbol2;\n\n// src/errors/empty-response-body-error.ts\nvar name2 = \"AI_EmptyResponseBodyError\";\nvar marker3 = `vercel.ai.error.${name2}`;\nvar symbol3 = Symbol.for(marker3);\nvar _a3;\nvar EmptyResponseBodyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message = \"Empty response body\" } = {}) {\n    super({ name: name2, message });\n    this[_a3] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker3);\n  }\n};\n_a3 = symbol3;\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-argument-error.ts\nvar name3 = \"AI_InvalidArgumentError\";\nvar marker4 = `vercel.ai.error.${name3}`;\nvar symbol4 = Symbol.for(marker4);\nvar _a4;\nvar InvalidArgumentError = class extends AISDKError {\n  constructor({\n    message,\n    cause,\n    argument\n  }) {\n    super({ name: name3, message, cause });\n    this[_a4] = true;\n    this.argument = argument;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker4);\n  }\n};\n_a4 = symbol4;\n\n// src/errors/invalid-prompt-error.ts\nvar name4 = \"AI_InvalidPromptError\";\nvar marker5 = `vercel.ai.error.${name4}`;\nvar symbol5 = Symbol.for(marker5);\nvar _a5;\nvar InvalidPromptError = class extends AISDKError {\n  constructor({\n    prompt,\n    message,\n    cause\n  }) {\n    super({ name: name4, message: `Invalid prompt: ${message}`, cause });\n    this[_a5] = true;\n    this.prompt = prompt;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker5);\n  }\n};\n_a5 = symbol5;\n\n// src/errors/invalid-response-data-error.ts\nvar name5 = \"AI_InvalidResponseDataError\";\nvar marker6 = `vercel.ai.error.${name5}`;\nvar symbol6 = Symbol.for(marker6);\nvar _a6;\nvar InvalidResponseDataError = class extends AISDKError {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super({ name: name5, message });\n    this[_a6] = true;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker6);\n  }\n};\n_a6 = symbol6;\n\n// src/errors/json-parse-error.ts\nvar name6 = \"AI_JSONParseError\";\nvar marker7 = `vercel.ai.error.${name6}`;\nvar symbol7 = Symbol.for(marker7);\nvar _a7;\nvar JSONParseError = class extends AISDKError {\n  constructor({ text, cause }) {\n    super({\n      name: name6,\n      message: `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a7] = true;\n    this.text = text;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker7);\n  }\n};\n_a7 = symbol7;\n\n// src/errors/load-api-key-error.ts\nvar name7 = \"AI_LoadAPIKeyError\";\nvar marker8 = `vercel.ai.error.${name7}`;\nvar symbol8 = Symbol.for(marker8);\nvar _a8;\nvar LoadAPIKeyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name7, message });\n    this[_a8] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker8);\n  }\n};\n_a8 = symbol8;\n\n// src/errors/load-setting-error.ts\nvar name8 = \"AI_LoadSettingError\";\nvar marker9 = `vercel.ai.error.${name8}`;\nvar symbol9 = Symbol.for(marker9);\nvar _a9;\nvar LoadSettingError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name8, message });\n    this[_a9] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker9);\n  }\n};\n_a9 = symbol9;\n\n// src/errors/no-content-generated-error.ts\nvar name9 = \"AI_NoContentGeneratedError\";\nvar marker10 = `vercel.ai.error.${name9}`;\nvar symbol10 = Symbol.for(marker10);\nvar _a10;\nvar NoContentGeneratedError = class extends AISDKError {\n  // used in isInstance\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super({ name: name9, message });\n    this[_a10] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker10);\n  }\n};\n_a10 = symbol10;\n\n// src/errors/no-such-model-error.ts\nvar name10 = \"AI_NoSuchModelError\";\nvar marker11 = `vercel.ai.error.${name10}`;\nvar symbol11 = Symbol.for(marker11);\nvar _a11;\nvar NoSuchModelError = class extends AISDKError {\n  constructor({\n    errorName = name10,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`\n  }) {\n    super({ name: errorName, message });\n    this[_a11] = true;\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker11);\n  }\n};\n_a11 = symbol11;\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar name11 = \"AI_TooManyEmbeddingValuesForCallError\";\nvar marker12 = `vercel.ai.error.${name11}`;\nvar symbol12 = Symbol.for(marker12);\nvar _a12;\nvar TooManyEmbeddingValuesForCallError = class extends AISDKError {\n  constructor(options) {\n    super({\n      name: name11,\n      message: `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    });\n    this[_a12] = true;\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker12);\n  }\n};\n_a12 = symbol12;\n\n// src/errors/type-validation-error.ts\nvar name12 = \"AI_TypeValidationError\";\nvar marker13 = `vercel.ai.error.${name12}`;\nvar symbol13 = Symbol.for(marker13);\nvar _a13;\nvar _TypeValidationError = class _TypeValidationError extends AISDKError {\n  constructor({ value, cause }) {\n    super({\n      name: name12,\n      message: `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a13] = true;\n    this.value = value;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker13);\n  }\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause\n  }) {\n    return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({ value, cause });\n  }\n};\n_a13 = symbol13;\nvar TypeValidationError = _TypeValidationError;\n\n// src/errors/unsupported-functionality-error.ts\nvar name13 = \"AI_UnsupportedFunctionalityError\";\nvar marker14 = `vercel.ai.error.${name13}`;\nvar symbol14 = Symbol.for(marker14);\nvar _a14;\nvar UnsupportedFunctionalityError = class extends AISDKError {\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`\n  }) {\n    super({ name: name13, message });\n    this[_a14] = true;\n    this.functionality = functionality;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker14);\n  }\n};\n_a14 = symbol14;\n\n// src/json-value/is-json.ts\nfunction isJSONValue(value) {\n  if (value === null || typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n    return true;\n  }\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n    );\n  }\n  return false;\n}\nfunction isJSONArray(value) {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\nfunction isJSONObject(value) {\n  return value != null && typeof value === \"object\" && Object.entries(value).every(\n    ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs":
/*!************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AISDKError: () => (/* binding */ AISDKError),\n/* harmony export */   APICallError: () => (/* binding */ APICallError),\n/* harmony export */   EmptyResponseBodyError: () => (/* binding */ EmptyResponseBodyError),\n/* harmony export */   InvalidArgumentError: () => (/* binding */ InvalidArgumentError),\n/* harmony export */   InvalidPromptError: () => (/* binding */ InvalidPromptError),\n/* harmony export */   InvalidResponseDataError: () => (/* binding */ InvalidResponseDataError),\n/* harmony export */   JSONParseError: () => (/* binding */ JSONParseError),\n/* harmony export */   LoadAPIKeyError: () => (/* binding */ LoadAPIKeyError),\n/* harmony export */   LoadSettingError: () => (/* binding */ LoadSettingError),\n/* harmony export */   NoContentGeneratedError: () => (/* binding */ NoContentGeneratedError),\n/* harmony export */   NoSuchModelError: () => (/* binding */ NoSuchModelError),\n/* harmony export */   TooManyEmbeddingValuesForCallError: () => (/* binding */ TooManyEmbeddingValuesForCallError),\n/* harmony export */   TypeValidationError: () => (/* binding */ TypeValidationError),\n/* harmony export */   UnsupportedFunctionalityError: () => (/* binding */ UnsupportedFunctionalityError),\n/* harmony export */   getErrorMessage: () => (/* binding */ getErrorMessage),\n/* harmony export */   isJSONArray: () => (/* binding */ isJSONArray),\n/* harmony export */   isJSONObject: () => (/* binding */ isJSONObject),\n/* harmony export */   isJSONValue: () => (/* binding */ isJSONValue)\n/* harmony export */ });\n// src/errors/ai-sdk-error.ts\nvar marker = \"vercel.ai.error\";\nvar symbol = Symbol.for(marker);\nvar _a;\nvar _AISDKError = class _AISDKError extends Error {\n  /**\n   * Creates an AI SDK Error.\n   *\n   * @param {Object} params - The parameters for creating the error.\n   * @param {string} params.name - The name of the error.\n   * @param {string} params.message - The error message.\n   * @param {unknown} [params.cause] - The underlying cause of the error.\n   */\n  constructor({\n    name: name14,\n    message,\n    cause\n  }) {\n    super(message);\n    this[_a] = true;\n    this.name = name14;\n    this.cause = cause;\n  }\n  /**\n   * Checks if the given error is an AI SDK Error.\n   * @param {unknown} error - The error to check.\n   * @returns {boolean} True if the error is an AI SDK Error, false otherwise.\n   */\n  static isInstance(error) {\n    return _AISDKError.hasMarker(error, marker);\n  }\n  static hasMarker(error, marker15) {\n    const markerSymbol = Symbol.for(marker15);\n    return error != null && typeof error === \"object\" && markerSymbol in error && typeof error[markerSymbol] === \"boolean\" && error[markerSymbol] === true;\n  }\n};\n_a = symbol;\nvar AISDKError = _AISDKError;\n\n// src/errors/api-call-error.ts\nvar name = \"AI_APICallError\";\nvar marker2 = `vercel.ai.error.${name}`;\nvar symbol2 = Symbol.for(marker2);\nvar _a2;\nvar APICallError = class extends AISDKError {\n  constructor({\n    message,\n    url,\n    requestBodyValues,\n    statusCode,\n    responseHeaders,\n    responseBody,\n    cause,\n    isRetryable = statusCode != null && (statusCode === 408 || // request timeout\n    statusCode === 409 || // conflict\n    statusCode === 429 || // too many requests\n    statusCode >= 500),\n    // server error\n    data\n  }) {\n    super({ name, message, cause });\n    this[_a2] = true;\n    this.url = url;\n    this.requestBodyValues = requestBodyValues;\n    this.statusCode = statusCode;\n    this.responseHeaders = responseHeaders;\n    this.responseBody = responseBody;\n    this.isRetryable = isRetryable;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker2);\n  }\n};\n_a2 = symbol2;\n\n// src/errors/empty-response-body-error.ts\nvar name2 = \"AI_EmptyResponseBodyError\";\nvar marker3 = `vercel.ai.error.${name2}`;\nvar symbol3 = Symbol.for(marker3);\nvar _a3;\nvar EmptyResponseBodyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message = \"Empty response body\" } = {}) {\n    super({ name: name2, message });\n    this[_a3] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker3);\n  }\n};\n_a3 = symbol3;\n\n// src/errors/get-error-message.ts\nfunction getErrorMessage(error) {\n  if (error == null) {\n    return \"unknown error\";\n  }\n  if (typeof error === \"string\") {\n    return error;\n  }\n  if (error instanceof Error) {\n    return error.message;\n  }\n  return JSON.stringify(error);\n}\n\n// src/errors/invalid-argument-error.ts\nvar name3 = \"AI_InvalidArgumentError\";\nvar marker4 = `vercel.ai.error.${name3}`;\nvar symbol4 = Symbol.for(marker4);\nvar _a4;\nvar InvalidArgumentError = class extends AISDKError {\n  constructor({\n    message,\n    cause,\n    argument\n  }) {\n    super({ name: name3, message, cause });\n    this[_a4] = true;\n    this.argument = argument;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker4);\n  }\n};\n_a4 = symbol4;\n\n// src/errors/invalid-prompt-error.ts\nvar name4 = \"AI_InvalidPromptError\";\nvar marker5 = `vercel.ai.error.${name4}`;\nvar symbol5 = Symbol.for(marker5);\nvar _a5;\nvar InvalidPromptError = class extends AISDKError {\n  constructor({\n    prompt,\n    message,\n    cause\n  }) {\n    super({ name: name4, message: `Invalid prompt: ${message}`, cause });\n    this[_a5] = true;\n    this.prompt = prompt;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker5);\n  }\n};\n_a5 = symbol5;\n\n// src/errors/invalid-response-data-error.ts\nvar name5 = \"AI_InvalidResponseDataError\";\nvar marker6 = `vercel.ai.error.${name5}`;\nvar symbol6 = Symbol.for(marker6);\nvar _a6;\nvar InvalidResponseDataError = class extends AISDKError {\n  constructor({\n    data,\n    message = `Invalid response data: ${JSON.stringify(data)}.`\n  }) {\n    super({ name: name5, message });\n    this[_a6] = true;\n    this.data = data;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker6);\n  }\n};\n_a6 = symbol6;\n\n// src/errors/json-parse-error.ts\nvar name6 = \"AI_JSONParseError\";\nvar marker7 = `vercel.ai.error.${name6}`;\nvar symbol7 = Symbol.for(marker7);\nvar _a7;\nvar JSONParseError = class extends AISDKError {\n  constructor({ text, cause }) {\n    super({\n      name: name6,\n      message: `JSON parsing failed: Text: ${text}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a7] = true;\n    this.text = text;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker7);\n  }\n};\n_a7 = symbol7;\n\n// src/errors/load-api-key-error.ts\nvar name7 = \"AI_LoadAPIKeyError\";\nvar marker8 = `vercel.ai.error.${name7}`;\nvar symbol8 = Symbol.for(marker8);\nvar _a8;\nvar LoadAPIKeyError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name7, message });\n    this[_a8] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker8);\n  }\n};\n_a8 = symbol8;\n\n// src/errors/load-setting-error.ts\nvar name8 = \"AI_LoadSettingError\";\nvar marker9 = `vercel.ai.error.${name8}`;\nvar symbol9 = Symbol.for(marker9);\nvar _a9;\nvar LoadSettingError = class extends AISDKError {\n  // used in isInstance\n  constructor({ message }) {\n    super({ name: name8, message });\n    this[_a9] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker9);\n  }\n};\n_a9 = symbol9;\n\n// src/errors/no-content-generated-error.ts\nvar name9 = \"AI_NoContentGeneratedError\";\nvar marker10 = `vercel.ai.error.${name9}`;\nvar symbol10 = Symbol.for(marker10);\nvar _a10;\nvar NoContentGeneratedError = class extends AISDKError {\n  // used in isInstance\n  constructor({\n    message = \"No content generated.\"\n  } = {}) {\n    super({ name: name9, message });\n    this[_a10] = true;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker10);\n  }\n};\n_a10 = symbol10;\n\n// src/errors/no-such-model-error.ts\nvar name10 = \"AI_NoSuchModelError\";\nvar marker11 = `vercel.ai.error.${name10}`;\nvar symbol11 = Symbol.for(marker11);\nvar _a11;\nvar NoSuchModelError = class extends AISDKError {\n  constructor({\n    errorName = name10,\n    modelId,\n    modelType,\n    message = `No such ${modelType}: ${modelId}`\n  }) {\n    super({ name: errorName, message });\n    this[_a11] = true;\n    this.modelId = modelId;\n    this.modelType = modelType;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker11);\n  }\n};\n_a11 = symbol11;\n\n// src/errors/too-many-embedding-values-for-call-error.ts\nvar name11 = \"AI_TooManyEmbeddingValuesForCallError\";\nvar marker12 = `vercel.ai.error.${name11}`;\nvar symbol12 = Symbol.for(marker12);\nvar _a12;\nvar TooManyEmbeddingValuesForCallError = class extends AISDKError {\n  constructor(options) {\n    super({\n      name: name11,\n      message: `Too many values for a single embedding call. The ${options.provider} model \"${options.modelId}\" can only embed up to ${options.maxEmbeddingsPerCall} values per call, but ${options.values.length} values were provided.`\n    });\n    this[_a12] = true;\n    this.provider = options.provider;\n    this.modelId = options.modelId;\n    this.maxEmbeddingsPerCall = options.maxEmbeddingsPerCall;\n    this.values = options.values;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker12);\n  }\n};\n_a12 = symbol12;\n\n// src/errors/type-validation-error.ts\nvar name12 = \"AI_TypeValidationError\";\nvar marker13 = `vercel.ai.error.${name12}`;\nvar symbol13 = Symbol.for(marker13);\nvar _a13;\nvar _TypeValidationError = class _TypeValidationError extends AISDKError {\n  constructor({ value, cause }) {\n    super({\n      name: name12,\n      message: `Type validation failed: Value: ${JSON.stringify(value)}.\nError message: ${getErrorMessage(cause)}`,\n      cause\n    });\n    this[_a13] = true;\n    this.value = value;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker13);\n  }\n  /**\n   * Wraps an error into a TypeValidationError.\n   * If the cause is already a TypeValidationError with the same value, it returns the cause.\n   * Otherwise, it creates a new TypeValidationError.\n   *\n   * @param {Object} params - The parameters for wrapping the error.\n   * @param {unknown} params.value - The value that failed validation.\n   * @param {unknown} params.cause - The original error or cause of the validation failure.\n   * @returns {TypeValidationError} A TypeValidationError instance.\n   */\n  static wrap({\n    value,\n    cause\n  }) {\n    return _TypeValidationError.isInstance(cause) && cause.value === value ? cause : new _TypeValidationError({ value, cause });\n  }\n};\n_a13 = symbol13;\nvar TypeValidationError = _TypeValidationError;\n\n// src/errors/unsupported-functionality-error.ts\nvar name13 = \"AI_UnsupportedFunctionalityError\";\nvar marker14 = `vercel.ai.error.${name13}`;\nvar symbol14 = Symbol.for(marker14);\nvar _a14;\nvar UnsupportedFunctionalityError = class extends AISDKError {\n  constructor({\n    functionality,\n    message = `'${functionality}' functionality not supported.`\n  }) {\n    super({ name: name13, message });\n    this[_a14] = true;\n    this.functionality = functionality;\n  }\n  static isInstance(error) {\n    return AISDKError.hasMarker(error, marker14);\n  }\n};\n_a14 = symbol14;\n\n// src/json-value/is-json.ts\nfunction isJSONValue(value) {\n  if (value === null || typeof value === \"string\" || typeof value === \"number\" || typeof value === \"boolean\") {\n    return true;\n  }\n  if (Array.isArray(value)) {\n    return value.every(isJSONValue);\n  }\n  if (typeof value === \"object\") {\n    return Object.entries(value).every(\n      ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n    );\n  }\n  return false;\n}\nfunction isJSONArray(value) {\n  return Array.isArray(value) && value.every(isJSONValue);\n}\nfunction isJSONObject(value) {\n  return value != null && typeof value === \"object\" && Object.entries(value).every(\n    ([key, val]) => typeof key === \"string\" && isJSONValue(val)\n  );\n}\n\n//# sourceMappingURL=index.mjs.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvLnBucG0vQGFpLXNkaytwcm92aWRlckAxLjEuMC9ub2RlX21vZHVsZXMvQGFpLXNkay9wcm92aWRlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYSxRQUFRO0FBQ3JCLGFBQWEsUUFBUTtBQUNyQixhQUFhLFFBQVE7QUFDckIsYUFBYSxTQUFTO0FBQ3RCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFNBQVM7QUFDdEIsZUFBZSxTQUFTO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlDQUFpQyxLQUFLO0FBQ3RDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0gsWUFBWSxzQkFBc0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0Isa0NBQWtDLElBQUk7QUFDdEQsWUFBWSxzQkFBc0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlDQUFpQyxNQUFNO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILFlBQVksNkJBQTZCO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGlDQUFpQyxNQUFNO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNILFlBQVkseUNBQXlDLFFBQVEsVUFBVTtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxpQ0FBaUMsTUFBTTtBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0NBQXdDLHFCQUFxQjtBQUM3RCxHQUFHO0FBQ0gsWUFBWSxzQkFBc0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTtBQUNBO0FBQ0EsZ0JBQWdCLGFBQWE7QUFDN0I7QUFDQTtBQUNBLDZDQUE2QyxLQUFLO0FBQ2xELGlCQUFpQix1QkFBdUI7QUFDeEM7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsU0FBUztBQUN6QixZQUFZLHNCQUFzQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0EsaUNBQWlDLE1BQU07QUFDdkM7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsU0FBUztBQUN6QixZQUFZLHNCQUFzQjtBQUNsQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0NBQWtDLE1BQU07QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSxJQUFJO0FBQ1IsWUFBWSxzQkFBc0I7QUFDbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtDQUFrQyxPQUFPO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLFVBQVUsSUFBSSxRQUFRO0FBQy9DLEdBQUc7QUFDSCxZQUFZLDBCQUEwQjtBQUN0QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLGtDQUFrQyxPQUFPO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1FQUFtRSxrQkFBa0IsU0FBUyxnQkFBZ0IseUJBQXlCLDhCQUE4Qix1QkFBdUIsdUJBQXVCO0FBQ25OLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxrQ0FBa0MsT0FBTztBQUN6QztBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsY0FBYztBQUM5QjtBQUNBO0FBQ0EsaURBQWlELHNCQUFzQjtBQUN2RSxpQkFBaUIsdUJBQXVCO0FBQ3hDO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxhQUFhLFFBQVE7QUFDckIsYUFBYSxTQUFTO0FBQ3RCLGFBQWEsU0FBUztBQUN0QixlQUFlLHFCQUFxQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUc7QUFDSCxnSEFBZ0gsY0FBYztBQUM5SDtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0Esa0NBQWtDLE9BQU87QUFDekM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixjQUFjO0FBQ2hDLEdBQUc7QUFDSCxZQUFZLHVCQUF1QjtBQUNuQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFvQkU7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3ByZXNlbnRhdGlvbi8uL25vZGVfbW9kdWxlcy8ucG5wbS9AYWktc2RrK3Byb3ZpZGVyQDEuMS4wL25vZGVfbW9kdWxlcy9AYWktc2RrL3Byb3ZpZGVyL2Rpc3QvaW5kZXgubWpzPzA1NDQiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL2Vycm9ycy9haS1zZGstZXJyb3IudHNcbnZhciBtYXJrZXIgPSBcInZlcmNlbC5haS5lcnJvclwiO1xudmFyIHN5bWJvbCA9IFN5bWJvbC5mb3IobWFya2VyKTtcbnZhciBfYTtcbnZhciBfQUlTREtFcnJvciA9IGNsYXNzIF9BSVNES0Vycm9yIGV4dGVuZHMgRXJyb3Ige1xuICAvKipcbiAgICogQ3JlYXRlcyBhbiBBSSBTREsgRXJyb3IuXG4gICAqXG4gICAqIEBwYXJhbSB7T2JqZWN0fSBwYXJhbXMgLSBUaGUgcGFyYW1ldGVycyBmb3IgY3JlYXRpbmcgdGhlIGVycm9yLlxuICAgKiBAcGFyYW0ge3N0cmluZ30gcGFyYW1zLm5hbWUgLSBUaGUgbmFtZSBvZiB0aGUgZXJyb3IuXG4gICAqIEBwYXJhbSB7c3RyaW5nfSBwYXJhbXMubWVzc2FnZSAtIFRoZSBlcnJvciBtZXNzYWdlLlxuICAgKiBAcGFyYW0ge3Vua25vd259IFtwYXJhbXMuY2F1c2VdIC0gVGhlIHVuZGVybHlpbmcgY2F1c2Ugb2YgdGhlIGVycm9yLlxuICAgKi9cbiAgY29uc3RydWN0b3Ioe1xuICAgIG5hbWU6IG5hbWUxNCxcbiAgICBtZXNzYWdlLFxuICAgIGNhdXNlXG4gIH0pIHtcbiAgICBzdXBlcihtZXNzYWdlKTtcbiAgICB0aGlzW19hXSA9IHRydWU7XG4gICAgdGhpcy5uYW1lID0gbmFtZTE0O1xuICAgIHRoaXMuY2F1c2UgPSBjYXVzZTtcbiAgfVxuICAvKipcbiAgICogQ2hlY2tzIGlmIHRoZSBnaXZlbiBlcnJvciBpcyBhbiBBSSBTREsgRXJyb3IuXG4gICAqIEBwYXJhbSB7dW5rbm93bn0gZXJyb3IgLSBUaGUgZXJyb3IgdG8gY2hlY2suXG4gICAqIEByZXR1cm5zIHtib29sZWFufSBUcnVlIGlmIHRoZSBlcnJvciBpcyBhbiBBSSBTREsgRXJyb3IsIGZhbHNlIG90aGVyd2lzZS5cbiAgICovXG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIF9BSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyKTtcbiAgfVxuICBzdGF0aWMgaGFzTWFya2VyKGVycm9yLCBtYXJrZXIxNSkge1xuICAgIGNvbnN0IG1hcmtlclN5bWJvbCA9IFN5bWJvbC5mb3IobWFya2VyMTUpO1xuICAgIHJldHVybiBlcnJvciAhPSBudWxsICYmIHR5cGVvZiBlcnJvciA9PT0gXCJvYmplY3RcIiAmJiBtYXJrZXJTeW1ib2wgaW4gZXJyb3IgJiYgdHlwZW9mIGVycm9yW21hcmtlclN5bWJvbF0gPT09IFwiYm9vbGVhblwiICYmIGVycm9yW21hcmtlclN5bWJvbF0gPT09IHRydWU7XG4gIH1cbn07XG5fYSA9IHN5bWJvbDtcbnZhciBBSVNES0Vycm9yID0gX0FJU0RLRXJyb3I7XG5cbi8vIHNyYy9lcnJvcnMvYXBpLWNhbGwtZXJyb3IudHNcbnZhciBuYW1lID0gXCJBSV9BUElDYWxsRXJyb3JcIjtcbnZhciBtYXJrZXIyID0gYHZlcmNlbC5haS5lcnJvci4ke25hbWV9YDtcbnZhciBzeW1ib2wyID0gU3ltYm9sLmZvcihtYXJrZXIyKTtcbnZhciBfYTI7XG52YXIgQVBJQ2FsbEVycm9yID0gY2xhc3MgZXh0ZW5kcyBBSVNES0Vycm9yIHtcbiAgY29uc3RydWN0b3Ioe1xuICAgIG1lc3NhZ2UsXG4gICAgdXJsLFxuICAgIHJlcXVlc3RCb2R5VmFsdWVzLFxuICAgIHN0YXR1c0NvZGUsXG4gICAgcmVzcG9uc2VIZWFkZXJzLFxuICAgIHJlc3BvbnNlQm9keSxcbiAgICBjYXVzZSxcbiAgICBpc1JldHJ5YWJsZSA9IHN0YXR1c0NvZGUgIT0gbnVsbCAmJiAoc3RhdHVzQ29kZSA9PT0gNDA4IHx8IC8vIHJlcXVlc3QgdGltZW91dFxuICAgIHN0YXR1c0NvZGUgPT09IDQwOSB8fCAvLyBjb25mbGljdFxuICAgIHN0YXR1c0NvZGUgPT09IDQyOSB8fCAvLyB0b28gbWFueSByZXF1ZXN0c1xuICAgIHN0YXR1c0NvZGUgPj0gNTAwKSxcbiAgICAvLyBzZXJ2ZXIgZXJyb3JcbiAgICBkYXRhXG4gIH0pIHtcbiAgICBzdXBlcih7IG5hbWUsIG1lc3NhZ2UsIGNhdXNlIH0pO1xuICAgIHRoaXNbX2EyXSA9IHRydWU7XG4gICAgdGhpcy51cmwgPSB1cmw7XG4gICAgdGhpcy5yZXF1ZXN0Qm9keVZhbHVlcyA9IHJlcXVlc3RCb2R5VmFsdWVzO1xuICAgIHRoaXMuc3RhdHVzQ29kZSA9IHN0YXR1c0NvZGU7XG4gICAgdGhpcy5yZXNwb25zZUhlYWRlcnMgPSByZXNwb25zZUhlYWRlcnM7XG4gICAgdGhpcy5yZXNwb25zZUJvZHkgPSByZXNwb25zZUJvZHk7XG4gICAgdGhpcy5pc1JldHJ5YWJsZSA9IGlzUmV0cnlhYmxlO1xuICAgIHRoaXMuZGF0YSA9IGRhdGE7XG4gIH1cbiAgc3RhdGljIGlzSW5zdGFuY2UoZXJyb3IpIHtcbiAgICByZXR1cm4gQUlTREtFcnJvci5oYXNNYXJrZXIoZXJyb3IsIG1hcmtlcjIpO1xuICB9XG59O1xuX2EyID0gc3ltYm9sMjtcblxuLy8gc3JjL2Vycm9ycy9lbXB0eS1yZXNwb25zZS1ib2R5LWVycm9yLnRzXG52YXIgbmFtZTIgPSBcIkFJX0VtcHR5UmVzcG9uc2VCb2R5RXJyb3JcIjtcbnZhciBtYXJrZXIzID0gYHZlcmNlbC5haS5lcnJvci4ke25hbWUyfWA7XG52YXIgc3ltYm9sMyA9IFN5bWJvbC5mb3IobWFya2VyMyk7XG52YXIgX2EzO1xudmFyIEVtcHR5UmVzcG9uc2VCb2R5RXJyb3IgPSBjbGFzcyBleHRlbmRzIEFJU0RLRXJyb3Ige1xuICAvLyB1c2VkIGluIGlzSW5zdGFuY2VcbiAgY29uc3RydWN0b3IoeyBtZXNzYWdlID0gXCJFbXB0eSByZXNwb25zZSBib2R5XCIgfSA9IHt9KSB7XG4gICAgc3VwZXIoeyBuYW1lOiBuYW1lMiwgbWVzc2FnZSB9KTtcbiAgICB0aGlzW19hM10gPSB0cnVlO1xuICB9XG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIEFJU0RLRXJyb3IuaGFzTWFya2VyKGVycm9yLCBtYXJrZXIzKTtcbiAgfVxufTtcbl9hMyA9IHN5bWJvbDM7XG5cbi8vIHNyYy9lcnJvcnMvZ2V0LWVycm9yLW1lc3NhZ2UudHNcbmZ1bmN0aW9uIGdldEVycm9yTWVzc2FnZShlcnJvcikge1xuICBpZiAoZXJyb3IgPT0gbnVsbCkge1xuICAgIHJldHVybiBcInVua25vd24gZXJyb3JcIjtcbiAgfVxuICBpZiAodHlwZW9mIGVycm9yID09PSBcInN0cmluZ1wiKSB7XG4gICAgcmV0dXJuIGVycm9yO1xuICB9XG4gIGlmIChlcnJvciBpbnN0YW5jZW9mIEVycm9yKSB7XG4gICAgcmV0dXJuIGVycm9yLm1lc3NhZ2U7XG4gIH1cbiAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KGVycm9yKTtcbn1cblxuLy8gc3JjL2Vycm9ycy9pbnZhbGlkLWFyZ3VtZW50LWVycm9yLnRzXG52YXIgbmFtZTMgPSBcIkFJX0ludmFsaWRBcmd1bWVudEVycm9yXCI7XG52YXIgbWFya2VyNCA9IGB2ZXJjZWwuYWkuZXJyb3IuJHtuYW1lM31gO1xudmFyIHN5bWJvbDQgPSBTeW1ib2wuZm9yKG1hcmtlcjQpO1xudmFyIF9hNDtcbnZhciBJbnZhbGlkQXJndW1lbnRFcnJvciA9IGNsYXNzIGV4dGVuZHMgQUlTREtFcnJvciB7XG4gIGNvbnN0cnVjdG9yKHtcbiAgICBtZXNzYWdlLFxuICAgIGNhdXNlLFxuICAgIGFyZ3VtZW50XG4gIH0pIHtcbiAgICBzdXBlcih7IG5hbWU6IG5hbWUzLCBtZXNzYWdlLCBjYXVzZSB9KTtcbiAgICB0aGlzW19hNF0gPSB0cnVlO1xuICAgIHRoaXMuYXJndW1lbnQgPSBhcmd1bWVudDtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyNCk7XG4gIH1cbn07XG5fYTQgPSBzeW1ib2w0O1xuXG4vLyBzcmMvZXJyb3JzL2ludmFsaWQtcHJvbXB0LWVycm9yLnRzXG52YXIgbmFtZTQgPSBcIkFJX0ludmFsaWRQcm9tcHRFcnJvclwiO1xudmFyIG1hcmtlcjUgPSBgdmVyY2VsLmFpLmVycm9yLiR7bmFtZTR9YDtcbnZhciBzeW1ib2w1ID0gU3ltYm9sLmZvcihtYXJrZXI1KTtcbnZhciBfYTU7XG52YXIgSW52YWxpZFByb21wdEVycm9yID0gY2xhc3MgZXh0ZW5kcyBBSVNES0Vycm9yIHtcbiAgY29uc3RydWN0b3Ioe1xuICAgIHByb21wdCxcbiAgICBtZXNzYWdlLFxuICAgIGNhdXNlXG4gIH0pIHtcbiAgICBzdXBlcih7IG5hbWU6IG5hbWU0LCBtZXNzYWdlOiBgSW52YWxpZCBwcm9tcHQ6ICR7bWVzc2FnZX1gLCBjYXVzZSB9KTtcbiAgICB0aGlzW19hNV0gPSB0cnVlO1xuICAgIHRoaXMucHJvbXB0ID0gcHJvbXB0O1xuICB9XG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIEFJU0RLRXJyb3IuaGFzTWFya2VyKGVycm9yLCBtYXJrZXI1KTtcbiAgfVxufTtcbl9hNSA9IHN5bWJvbDU7XG5cbi8vIHNyYy9lcnJvcnMvaW52YWxpZC1yZXNwb25zZS1kYXRhLWVycm9yLnRzXG52YXIgbmFtZTUgPSBcIkFJX0ludmFsaWRSZXNwb25zZURhdGFFcnJvclwiO1xudmFyIG1hcmtlcjYgPSBgdmVyY2VsLmFpLmVycm9yLiR7bmFtZTV9YDtcbnZhciBzeW1ib2w2ID0gU3ltYm9sLmZvcihtYXJrZXI2KTtcbnZhciBfYTY7XG52YXIgSW52YWxpZFJlc3BvbnNlRGF0YUVycm9yID0gY2xhc3MgZXh0ZW5kcyBBSVNES0Vycm9yIHtcbiAgY29uc3RydWN0b3Ioe1xuICAgIGRhdGEsXG4gICAgbWVzc2FnZSA9IGBJbnZhbGlkIHJlc3BvbnNlIGRhdGE6ICR7SlNPTi5zdHJpbmdpZnkoZGF0YSl9LmBcbiAgfSkge1xuICAgIHN1cGVyKHsgbmFtZTogbmFtZTUsIG1lc3NhZ2UgfSk7XG4gICAgdGhpc1tfYTZdID0gdHJ1ZTtcbiAgICB0aGlzLmRhdGEgPSBkYXRhO1xuICB9XG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIEFJU0RLRXJyb3IuaGFzTWFya2VyKGVycm9yLCBtYXJrZXI2KTtcbiAgfVxufTtcbl9hNiA9IHN5bWJvbDY7XG5cbi8vIHNyYy9lcnJvcnMvanNvbi1wYXJzZS1lcnJvci50c1xudmFyIG5hbWU2ID0gXCJBSV9KU09OUGFyc2VFcnJvclwiO1xudmFyIG1hcmtlcjcgPSBgdmVyY2VsLmFpLmVycm9yLiR7bmFtZTZ9YDtcbnZhciBzeW1ib2w3ID0gU3ltYm9sLmZvcihtYXJrZXI3KTtcbnZhciBfYTc7XG52YXIgSlNPTlBhcnNlRXJyb3IgPSBjbGFzcyBleHRlbmRzIEFJU0RLRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcih7IHRleHQsIGNhdXNlIH0pIHtcbiAgICBzdXBlcih7XG4gICAgICBuYW1lOiBuYW1lNixcbiAgICAgIG1lc3NhZ2U6IGBKU09OIHBhcnNpbmcgZmFpbGVkOiBUZXh0OiAke3RleHR9LlxuRXJyb3IgbWVzc2FnZTogJHtnZXRFcnJvck1lc3NhZ2UoY2F1c2UpfWAsXG4gICAgICBjYXVzZVxuICAgIH0pO1xuICAgIHRoaXNbX2E3XSA9IHRydWU7XG4gICAgdGhpcy50ZXh0ID0gdGV4dDtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyNyk7XG4gIH1cbn07XG5fYTcgPSBzeW1ib2w3O1xuXG4vLyBzcmMvZXJyb3JzL2xvYWQtYXBpLWtleS1lcnJvci50c1xudmFyIG5hbWU3ID0gXCJBSV9Mb2FkQVBJS2V5RXJyb3JcIjtcbnZhciBtYXJrZXI4ID0gYHZlcmNlbC5haS5lcnJvci4ke25hbWU3fWA7XG52YXIgc3ltYm9sOCA9IFN5bWJvbC5mb3IobWFya2VyOCk7XG52YXIgX2E4O1xudmFyIExvYWRBUElLZXlFcnJvciA9IGNsYXNzIGV4dGVuZHMgQUlTREtFcnJvciB7XG4gIC8vIHVzZWQgaW4gaXNJbnN0YW5jZVxuICBjb25zdHJ1Y3Rvcih7IG1lc3NhZ2UgfSkge1xuICAgIHN1cGVyKHsgbmFtZTogbmFtZTcsIG1lc3NhZ2UgfSk7XG4gICAgdGhpc1tfYThdID0gdHJ1ZTtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyOCk7XG4gIH1cbn07XG5fYTggPSBzeW1ib2w4O1xuXG4vLyBzcmMvZXJyb3JzL2xvYWQtc2V0dGluZy1lcnJvci50c1xudmFyIG5hbWU4ID0gXCJBSV9Mb2FkU2V0dGluZ0Vycm9yXCI7XG52YXIgbWFya2VyOSA9IGB2ZXJjZWwuYWkuZXJyb3IuJHtuYW1lOH1gO1xudmFyIHN5bWJvbDkgPSBTeW1ib2wuZm9yKG1hcmtlcjkpO1xudmFyIF9hOTtcbnZhciBMb2FkU2V0dGluZ0Vycm9yID0gY2xhc3MgZXh0ZW5kcyBBSVNES0Vycm9yIHtcbiAgLy8gdXNlZCBpbiBpc0luc3RhbmNlXG4gIGNvbnN0cnVjdG9yKHsgbWVzc2FnZSB9KSB7XG4gICAgc3VwZXIoeyBuYW1lOiBuYW1lOCwgbWVzc2FnZSB9KTtcbiAgICB0aGlzW19hOV0gPSB0cnVlO1xuICB9XG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIEFJU0RLRXJyb3IuaGFzTWFya2VyKGVycm9yLCBtYXJrZXI5KTtcbiAgfVxufTtcbl9hOSA9IHN5bWJvbDk7XG5cbi8vIHNyYy9lcnJvcnMvbm8tY29udGVudC1nZW5lcmF0ZWQtZXJyb3IudHNcbnZhciBuYW1lOSA9IFwiQUlfTm9Db250ZW50R2VuZXJhdGVkRXJyb3JcIjtcbnZhciBtYXJrZXIxMCA9IGB2ZXJjZWwuYWkuZXJyb3IuJHtuYW1lOX1gO1xudmFyIHN5bWJvbDEwID0gU3ltYm9sLmZvcihtYXJrZXIxMCk7XG52YXIgX2ExMDtcbnZhciBOb0NvbnRlbnRHZW5lcmF0ZWRFcnJvciA9IGNsYXNzIGV4dGVuZHMgQUlTREtFcnJvciB7XG4gIC8vIHVzZWQgaW4gaXNJbnN0YW5jZVxuICBjb25zdHJ1Y3Rvcih7XG4gICAgbWVzc2FnZSA9IFwiTm8gY29udGVudCBnZW5lcmF0ZWQuXCJcbiAgfSA9IHt9KSB7XG4gICAgc3VwZXIoeyBuYW1lOiBuYW1lOSwgbWVzc2FnZSB9KTtcbiAgICB0aGlzW19hMTBdID0gdHJ1ZTtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyMTApO1xuICB9XG59O1xuX2ExMCA9IHN5bWJvbDEwO1xuXG4vLyBzcmMvZXJyb3JzL25vLXN1Y2gtbW9kZWwtZXJyb3IudHNcbnZhciBuYW1lMTAgPSBcIkFJX05vU3VjaE1vZGVsRXJyb3JcIjtcbnZhciBtYXJrZXIxMSA9IGB2ZXJjZWwuYWkuZXJyb3IuJHtuYW1lMTB9YDtcbnZhciBzeW1ib2wxMSA9IFN5bWJvbC5mb3IobWFya2VyMTEpO1xudmFyIF9hMTE7XG52YXIgTm9TdWNoTW9kZWxFcnJvciA9IGNsYXNzIGV4dGVuZHMgQUlTREtFcnJvciB7XG4gIGNvbnN0cnVjdG9yKHtcbiAgICBlcnJvck5hbWUgPSBuYW1lMTAsXG4gICAgbW9kZWxJZCxcbiAgICBtb2RlbFR5cGUsXG4gICAgbWVzc2FnZSA9IGBObyBzdWNoICR7bW9kZWxUeXBlfTogJHttb2RlbElkfWBcbiAgfSkge1xuICAgIHN1cGVyKHsgbmFtZTogZXJyb3JOYW1lLCBtZXNzYWdlIH0pO1xuICAgIHRoaXNbX2ExMV0gPSB0cnVlO1xuICAgIHRoaXMubW9kZWxJZCA9IG1vZGVsSWQ7XG4gICAgdGhpcy5tb2RlbFR5cGUgPSBtb2RlbFR5cGU7XG4gIH1cbiAgc3RhdGljIGlzSW5zdGFuY2UoZXJyb3IpIHtcbiAgICByZXR1cm4gQUlTREtFcnJvci5oYXNNYXJrZXIoZXJyb3IsIG1hcmtlcjExKTtcbiAgfVxufTtcbl9hMTEgPSBzeW1ib2wxMTtcblxuLy8gc3JjL2Vycm9ycy90b28tbWFueS1lbWJlZGRpbmctdmFsdWVzLWZvci1jYWxsLWVycm9yLnRzXG52YXIgbmFtZTExID0gXCJBSV9Ub29NYW55RW1iZWRkaW5nVmFsdWVzRm9yQ2FsbEVycm9yXCI7XG52YXIgbWFya2VyMTIgPSBgdmVyY2VsLmFpLmVycm9yLiR7bmFtZTExfWA7XG52YXIgc3ltYm9sMTIgPSBTeW1ib2wuZm9yKG1hcmtlcjEyKTtcbnZhciBfYTEyO1xudmFyIFRvb01hbnlFbWJlZGRpbmdWYWx1ZXNGb3JDYWxsRXJyb3IgPSBjbGFzcyBleHRlbmRzIEFJU0RLRXJyb3Ige1xuICBjb25zdHJ1Y3RvcihvcHRpb25zKSB7XG4gICAgc3VwZXIoe1xuICAgICAgbmFtZTogbmFtZTExLFxuICAgICAgbWVzc2FnZTogYFRvbyBtYW55IHZhbHVlcyBmb3IgYSBzaW5nbGUgZW1iZWRkaW5nIGNhbGwuIFRoZSAke29wdGlvbnMucHJvdmlkZXJ9IG1vZGVsIFwiJHtvcHRpb25zLm1vZGVsSWR9XCIgY2FuIG9ubHkgZW1iZWQgdXAgdG8gJHtvcHRpb25zLm1heEVtYmVkZGluZ3NQZXJDYWxsfSB2YWx1ZXMgcGVyIGNhbGwsIGJ1dCAke29wdGlvbnMudmFsdWVzLmxlbmd0aH0gdmFsdWVzIHdlcmUgcHJvdmlkZWQuYFxuICAgIH0pO1xuICAgIHRoaXNbX2ExMl0gPSB0cnVlO1xuICAgIHRoaXMucHJvdmlkZXIgPSBvcHRpb25zLnByb3ZpZGVyO1xuICAgIHRoaXMubW9kZWxJZCA9IG9wdGlvbnMubW9kZWxJZDtcbiAgICB0aGlzLm1heEVtYmVkZGluZ3NQZXJDYWxsID0gb3B0aW9ucy5tYXhFbWJlZGRpbmdzUGVyQ2FsbDtcbiAgICB0aGlzLnZhbHVlcyA9IG9wdGlvbnMudmFsdWVzO1xuICB9XG4gIHN0YXRpYyBpc0luc3RhbmNlKGVycm9yKSB7XG4gICAgcmV0dXJuIEFJU0RLRXJyb3IuaGFzTWFya2VyKGVycm9yLCBtYXJrZXIxMik7XG4gIH1cbn07XG5fYTEyID0gc3ltYm9sMTI7XG5cbi8vIHNyYy9lcnJvcnMvdHlwZS12YWxpZGF0aW9uLWVycm9yLnRzXG52YXIgbmFtZTEyID0gXCJBSV9UeXBlVmFsaWRhdGlvbkVycm9yXCI7XG52YXIgbWFya2VyMTMgPSBgdmVyY2VsLmFpLmVycm9yLiR7bmFtZTEyfWA7XG52YXIgc3ltYm9sMTMgPSBTeW1ib2wuZm9yKG1hcmtlcjEzKTtcbnZhciBfYTEzO1xudmFyIF9UeXBlVmFsaWRhdGlvbkVycm9yID0gY2xhc3MgX1R5cGVWYWxpZGF0aW9uRXJyb3IgZXh0ZW5kcyBBSVNES0Vycm9yIHtcbiAgY29uc3RydWN0b3IoeyB2YWx1ZSwgY2F1c2UgfSkge1xuICAgIHN1cGVyKHtcbiAgICAgIG5hbWU6IG5hbWUxMixcbiAgICAgIG1lc3NhZ2U6IGBUeXBlIHZhbGlkYXRpb24gZmFpbGVkOiBWYWx1ZTogJHtKU09OLnN0cmluZ2lmeSh2YWx1ZSl9LlxuRXJyb3IgbWVzc2FnZTogJHtnZXRFcnJvck1lc3NhZ2UoY2F1c2UpfWAsXG4gICAgICBjYXVzZVxuICAgIH0pO1xuICAgIHRoaXNbX2ExM10gPSB0cnVlO1xuICAgIHRoaXMudmFsdWUgPSB2YWx1ZTtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyMTMpO1xuICB9XG4gIC8qKlxuICAgKiBXcmFwcyBhbiBlcnJvciBpbnRvIGEgVHlwZVZhbGlkYXRpb25FcnJvci5cbiAgICogSWYgdGhlIGNhdXNlIGlzIGFscmVhZHkgYSBUeXBlVmFsaWRhdGlvbkVycm9yIHdpdGggdGhlIHNhbWUgdmFsdWUsIGl0IHJldHVybnMgdGhlIGNhdXNlLlxuICAgKiBPdGhlcndpc2UsIGl0IGNyZWF0ZXMgYSBuZXcgVHlwZVZhbGlkYXRpb25FcnJvci5cbiAgICpcbiAgICogQHBhcmFtIHtPYmplY3R9IHBhcmFtcyAtIFRoZSBwYXJhbWV0ZXJzIGZvciB3cmFwcGluZyB0aGUgZXJyb3IuXG4gICAqIEBwYXJhbSB7dW5rbm93bn0gcGFyYW1zLnZhbHVlIC0gVGhlIHZhbHVlIHRoYXQgZmFpbGVkIHZhbGlkYXRpb24uXG4gICAqIEBwYXJhbSB7dW5rbm93bn0gcGFyYW1zLmNhdXNlIC0gVGhlIG9yaWdpbmFsIGVycm9yIG9yIGNhdXNlIG9mIHRoZSB2YWxpZGF0aW9uIGZhaWx1cmUuXG4gICAqIEByZXR1cm5zIHtUeXBlVmFsaWRhdGlvbkVycm9yfSBBIFR5cGVWYWxpZGF0aW9uRXJyb3IgaW5zdGFuY2UuXG4gICAqL1xuICBzdGF0aWMgd3JhcCh7XG4gICAgdmFsdWUsXG4gICAgY2F1c2VcbiAgfSkge1xuICAgIHJldHVybiBfVHlwZVZhbGlkYXRpb25FcnJvci5pc0luc3RhbmNlKGNhdXNlKSAmJiBjYXVzZS52YWx1ZSA9PT0gdmFsdWUgPyBjYXVzZSA6IG5ldyBfVHlwZVZhbGlkYXRpb25FcnJvcih7IHZhbHVlLCBjYXVzZSB9KTtcbiAgfVxufTtcbl9hMTMgPSBzeW1ib2wxMztcbnZhciBUeXBlVmFsaWRhdGlvbkVycm9yID0gX1R5cGVWYWxpZGF0aW9uRXJyb3I7XG5cbi8vIHNyYy9lcnJvcnMvdW5zdXBwb3J0ZWQtZnVuY3Rpb25hbGl0eS1lcnJvci50c1xudmFyIG5hbWUxMyA9IFwiQUlfVW5zdXBwb3J0ZWRGdW5jdGlvbmFsaXR5RXJyb3JcIjtcbnZhciBtYXJrZXIxNCA9IGB2ZXJjZWwuYWkuZXJyb3IuJHtuYW1lMTN9YDtcbnZhciBzeW1ib2wxNCA9IFN5bWJvbC5mb3IobWFya2VyMTQpO1xudmFyIF9hMTQ7XG52YXIgVW5zdXBwb3J0ZWRGdW5jdGlvbmFsaXR5RXJyb3IgPSBjbGFzcyBleHRlbmRzIEFJU0RLRXJyb3Ige1xuICBjb25zdHJ1Y3Rvcih7XG4gICAgZnVuY3Rpb25hbGl0eSxcbiAgICBtZXNzYWdlID0gYCcke2Z1bmN0aW9uYWxpdHl9JyBmdW5jdGlvbmFsaXR5IG5vdCBzdXBwb3J0ZWQuYFxuICB9KSB7XG4gICAgc3VwZXIoeyBuYW1lOiBuYW1lMTMsIG1lc3NhZ2UgfSk7XG4gICAgdGhpc1tfYTE0XSA9IHRydWU7XG4gICAgdGhpcy5mdW5jdGlvbmFsaXR5ID0gZnVuY3Rpb25hbGl0eTtcbiAgfVxuICBzdGF0aWMgaXNJbnN0YW5jZShlcnJvcikge1xuICAgIHJldHVybiBBSVNES0Vycm9yLmhhc01hcmtlcihlcnJvciwgbWFya2VyMTQpO1xuICB9XG59O1xuX2ExNCA9IHN5bWJvbDE0O1xuXG4vLyBzcmMvanNvbi12YWx1ZS9pcy1qc29uLnRzXG5mdW5jdGlvbiBpc0pTT05WYWx1ZSh2YWx1ZSkge1xuICBpZiAodmFsdWUgPT09IG51bGwgfHwgdHlwZW9mIHZhbHVlID09PSBcInN0cmluZ1wiIHx8IHR5cGVvZiB2YWx1ZSA9PT0gXCJudW1iZXJcIiB8fCB0eXBlb2YgdmFsdWUgPT09IFwiYm9vbGVhblwiKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbiAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgcmV0dXJuIHZhbHVlLmV2ZXJ5KGlzSlNPTlZhbHVlKTtcbiAgfVxuICBpZiAodHlwZW9mIHZhbHVlID09PSBcIm9iamVjdFwiKSB7XG4gICAgcmV0dXJuIE9iamVjdC5lbnRyaWVzKHZhbHVlKS5ldmVyeShcbiAgICAgIChba2V5LCB2YWxdKSA9PiB0eXBlb2Yga2V5ID09PSBcInN0cmluZ1wiICYmIGlzSlNPTlZhbHVlKHZhbClcbiAgICApO1xuICB9XG4gIHJldHVybiBmYWxzZTtcbn1cbmZ1bmN0aW9uIGlzSlNPTkFycmF5KHZhbHVlKSB7XG4gIHJldHVybiBBcnJheS5pc0FycmF5KHZhbHVlKSAmJiB2YWx1ZS5ldmVyeShpc0pTT05WYWx1ZSk7XG59XG5mdW5jdGlvbiBpc0pTT05PYmplY3QodmFsdWUpIHtcbiAgcmV0dXJuIHZhbHVlICE9IG51bGwgJiYgdHlwZW9mIHZhbHVlID09PSBcIm9iamVjdFwiICYmIE9iamVjdC5lbnRyaWVzKHZhbHVlKS5ldmVyeShcbiAgICAoW2tleSwgdmFsXSkgPT4gdHlwZW9mIGtleSA9PT0gXCJzdHJpbmdcIiAmJiBpc0pTT05WYWx1ZSh2YWwpXG4gICk7XG59XG5leHBvcnQge1xuICBBSVNES0Vycm9yLFxuICBBUElDYWxsRXJyb3IsXG4gIEVtcHR5UmVzcG9uc2VCb2R5RXJyb3IsXG4gIEludmFsaWRBcmd1bWVudEVycm9yLFxuICBJbnZhbGlkUHJvbXB0RXJyb3IsXG4gIEludmFsaWRSZXNwb25zZURhdGFFcnJvcixcbiAgSlNPTlBhcnNlRXJyb3IsXG4gIExvYWRBUElLZXlFcnJvcixcbiAgTG9hZFNldHRpbmdFcnJvcixcbiAgTm9Db250ZW50R2VuZXJhdGVkRXJyb3IsXG4gIE5vU3VjaE1vZGVsRXJyb3IsXG4gIFRvb01hbnlFbWJlZGRpbmdWYWx1ZXNGb3JDYWxsRXJyb3IsXG4gIFR5cGVWYWxpZGF0aW9uRXJyb3IsXG4gIFVuc3VwcG9ydGVkRnVuY3Rpb25hbGl0eUVycm9yLFxuICBnZXRFcnJvck1lc3NhZ2UsXG4gIGlzSlNPTkFycmF5LFxuICBpc0pTT05PYmplY3QsXG4gIGlzSlNPTlZhbHVlXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/@ai-sdk+provider@1.1.0/node_modules/@ai-sdk/provider/dist/index.mjs\n");

/***/ })

};
;