"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@uploadthing+react@7.3.0_ne_f0a3ae338aeeec2f9af2dd930ab51e2f";
exports.ids = ["vendor-chunks/@uploadthing+react@7.3.0_ne_f0a3ae338aeeec2f9af2dd930ab51e2f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@uploadthing+react@7.3.0_ne_f0a3ae338aeeec2f9af2dd930ab51e2f/node_modules/@uploadthing/react/dist/button-client-DaYiwH1a.js":
/*!********************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@uploadthing+react@7.3.0_ne_f0a3ae338aeeec2f9af2dd930ab51e2f/node_modules/@uploadthing/react/dist/button-client-DaYiwH1a.js ***!
  \********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   C: () => (/* binding */ Cancel),\n/* harmony export */   S: () => (/* binding */ Spinner),\n/* harmony export */   U: () => (/* binding */ UploadButton),\n/* harmony export */   _: () => (/* binding */ __useUploadThingInternal),\n/* harmony export */   g: () => (/* binding */ generateReactHelpers),\n/* harmony export */   p: () => (/* binding */ peerDependencies),\n/* harmony export */   u: () => (/* binding */ usePaste)\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/.pnpm/@uploadthing+shared@7.1.7/node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var uploadthing_client__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! uploadthing/client */ \"(ssr)/./node_modules/.pnpm/uploadthing@7.6.0_express@4_c437b9aa74459198e1f1d6c63a3dda4f/node_modules/uploadthing/client/index.js\");\n/* __next_internal_client_entry_do_not_use__ C,S,U,_,g,p,u auto */ \n\n\n\nvar peerDependencies = {\n    next: \"*\",\n    react: \"^17.0.2 || ^18.0.0 || ^19.0.0\",\n    uploadthing: \"^7.2.0\"\n};\n// Ripped from https://github.com/scottrippey/react-use-event-hook\nconst noop = ()=>void 0;\n/**\n * Suppress the warning when using useLayoutEffect with SSR. (https://reactjs.org/link/uselayouteffect-ssr)\n * Make use of useInsertionEffect if available.\n */ const useInsertionEffect =  false ? 0 : noop;\n/**\n * Similar to useCallback, with a few subtle differences:\n * - The returned function is a stable reference, and will always be the same between renders\n * - No dependency lists required\n * - Properties or state accessed within the callback will always be \"current\"\n */ function useEvent(callback) {\n    // Keep track of the latest callback:\n    const latestRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef(useEvent_shouldNotBeInvokedBeforeMount);\n    useInsertionEffect(()=>{\n        latestRef.current = callback;\n    }, [\n        callback\n    ]);\n    // Create a stable callback that always calls the latest callback:\n    // using useRef instead of useCallback avoids creating and empty array on every render\n    const stableRef = react__WEBPACK_IMPORTED_MODULE_1__.useRef();\n    if (!stableRef.current) {\n        stableRef.current = function() {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-return, prefer-rest-params, @typescript-eslint/no-unsafe-argument\n            return latestRef.current.apply(this, arguments);\n        };\n    }\n    return stableRef.current;\n}\n/**\n * Render methods should be pure, especially when concurrency is used,\n * so we will throw this error if the callback is called while rendering.\n */ function useEvent_shouldNotBeInvokedBeforeMount() {\n    throw new Error(\"INVALID_USEEVENT_INVOCATION: the callback from useEvent cannot be invoked before the component has mounted.\");\n}\n// Ripped from https://usehooks-ts.com/react-hook/use-fetch\nfunction useFetch(fetch, url, options) {\n    const cache = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)({});\n    // Used to prevent state update if the component is unmounted\n    const cancelRequest = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(false);\n    const initialState = {\n        error: undefined,\n        data: undefined\n    };\n    // Keep state logic separated\n    const fetchReducer = (state, action)=>{\n        switch(action.type){\n            case \"loading\":\n                return {\n                    ...initialState\n                };\n            case \"fetched\":\n                return {\n                    ...initialState,\n                    data: action.payload\n                };\n            case \"error\":\n                return {\n                    ...initialState,\n                    error: action.payload\n                };\n            default:\n                return state;\n        }\n    };\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)(fetchReducer, initialState);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Do nothing if the url is not given\n        if (!url) return;\n        cancelRequest.current = false;\n        const fetchData = async ()=>{\n            dispatch({\n                type: \"loading\"\n            });\n            // If a cache exists for this url, return it\n            if (cache.current[url]) {\n                dispatch({\n                    type: \"fetched\",\n                    payload: cache.current[url]\n                });\n                return;\n            }\n            try {\n                const response = await fetch(url, options);\n                if (!response.ok) {\n                    throw new Error(response.statusText);\n                }\n                const dataOrError = await (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.safeParseJSON)(response);\n                if (dataOrError instanceof Error) {\n                    throw dataOrError;\n                }\n                cache.current[url] = dataOrError;\n                if (cancelRequest.current) return;\n                dispatch({\n                    type: \"fetched\",\n                    payload: dataOrError\n                });\n            } catch (error) {\n                if (cancelRequest.current) return;\n                dispatch({\n                    type: \"error\",\n                    payload: error\n                });\n            }\n        };\n        void fetchData();\n        // Use the cleanup function for avoiding a possibly...\n        // ...state update after the component was unmounted\n        return ()=>{\n            cancelRequest.current = true;\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        url\n    ]);\n    return state;\n}\nconst useRouteConfig = (fetch, url, endpoint)=>{\n    const maybeServerData = globalThis.__UPLOADTHING;\n    const { data } = useFetch(fetch, maybeServerData ? undefined : url.href);\n    return (maybeServerData ?? data)?.find((x)=>x.slug === endpoint)?.config;\n};\n/**\n * @internal - This is an internal function. Use `generateReactHelpers` instead.\n * The actual hook we export for public usage is generated from `generateReactHelpers`\n * which has the URL and FileRouter generic pre-bound.\n */ function useUploadThingInternal(url, endpoint, fetch, opts) {\n    const progressGranularity = opts?.uploadProgressGranularity ?? \"coarse\";\n    const { uploadFiles, routeRegistry } = (0,uploadthing_client__WEBPACK_IMPORTED_MODULE_3__.genUploader)({\n        fetch,\n        url,\n        package: \"@uploadthing/react\"\n    });\n    const [isUploading, setUploading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const uploadProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(0);\n    const fileProgress = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new Map());\n    const startUpload = useEvent(async (...args)=>{\n        const files = await opts?.onBeforeUploadBegin?.(args[0]) ?? args[0];\n        const input = args[1];\n        setUploading(true);\n        files.forEach((f)=>fileProgress.current.set(f, 0));\n        opts?.onUploadProgress?.(0);\n        try {\n            const res = await uploadFiles(endpoint, {\n                signal: opts?.signal,\n                headers: opts?.headers,\n                files,\n                onUploadProgress: (progress)=>{\n                    if (!opts?.onUploadProgress) return;\n                    fileProgress.current.set(progress.file, progress.progress);\n                    let sum = 0;\n                    fileProgress.current.forEach((p)=>{\n                        sum += p;\n                    });\n                    const averageProgress = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.roundProgress)(Math.min(100, sum / fileProgress.current.size), progressGranularity);\n                    if (averageProgress !== uploadProgress.current) {\n                        opts.onUploadProgress(averageProgress);\n                        uploadProgress.current = averageProgress;\n                    }\n                },\n                onUploadBegin ({ file }) {\n                    if (!opts?.onUploadBegin) return;\n                    opts.onUploadBegin(file);\n                },\n                // @ts-expect-error - input may not be defined on the type\n                input\n            });\n            await opts?.onClientUploadComplete?.(res);\n            return res;\n        } catch (e) {\n            /**\n       * This is the only way to introduce this as a non-breaking change\n       * TODO: Consider refactoring API in the next major version\n       */ if (e instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadAbortedError) throw e;\n            let error;\n            if (e instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError) {\n                error = e;\n            } else {\n                error = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.INTERNAL_DO_NOT_USE__fatalClientError)(e);\n                // eslint-disable-next-line no-console\n                console.error(\"Something went wrong. Please contact UploadThing and provide the following cause:\", error.cause instanceof Error ? error.cause.toString() : error.cause);\n            }\n            await opts?.onUploadError?.(error);\n        } finally{\n            setUploading(false);\n            fileProgress.current = new Map();\n            uploadProgress.current = 0;\n        }\n    });\n    const _endpoint = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.unwrap)(endpoint, routeRegistry);\n    const routeConfig = useRouteConfig(fetch, url, _endpoint);\n    return {\n        startUpload,\n        isUploading,\n        routeConfig\n    };\n}\n/** @internal - This is an internal function. Use `generateReactHelpers` instead. */ const __useUploadThingInternal = useUploadThingInternal;\nconst generateReactHelpers = (initOpts)=>{\n    (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.warnIfInvalidPeerDependency)(\"@uploadthing/react\", peerDependencies.uploadthing, uploadthing_client__WEBPACK_IMPORTED_MODULE_3__.version);\n    const fetch = initOpts?.fetch ?? globalThis.fetch;\n    const url = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.resolveMaybeUrlArg)(initOpts?.url);\n    const clientHelpers = (0,uploadthing_client__WEBPACK_IMPORTED_MODULE_3__.genUploader)({\n        fetch,\n        url,\n        package: \"@uploadthing/react\"\n    });\n    function useUploadThing(endpoint, opts) {\n        return __useUploadThingInternal(url, endpoint, fetch, opts);\n    }\n    function getRouteConfig(slug) {\n        const maybeServerData = globalThis.__UPLOADTHING;\n        const endpoint = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.unwrap)(slug, clientHelpers.routeRegistry);\n        const config = maybeServerData?.find((x)=>x.slug === endpoint)?.config;\n        if (!config) {\n            throw new Error(`No config found for endpoint \"${endpoint.toString()}\". Please make sure to use the NextSSRPlugin in your Next.js app.`);\n        }\n        return config;\n    }\n    return {\n        useUploadThing,\n        ...clientHelpers,\n        /**\n     * Get the config for a given endpoint outside of React context.\n     * @remarks Can only be used if the NextSSRPlugin is used in the app.\n     */ getRouteConfig\n    };\n};\nconst usePaste = (callback)=>{\n    const stableCallback = useEvent(callback);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const controller = new AbortController();\n        window.addEventListener(\"paste\", stableCallback, {\n            signal: controller.signal\n        });\n        return ()=>{\n            controller.abort();\n        };\n    }, [\n        stableCallback\n    ]);\n};\nfunction Spinner() {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"svg\", {\n        className: \"z-10 block h-5 w-5 animate-spin align-middle text-white\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 576 512\",\n        children: /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n            fill: \"currentColor\",\n            d: \"M256 32C256 14.33 270.3 0 288 0C429.4 0 544 114.6 544 256C544 302.6 531.5 346.4 509.7 384C500.9 399.3 481.3 404.6 465.1 395.7C450.7 386.9 445.5 367.3 454.3 351.1C470.6 323.8 480 291 480 255.1C480 149.1 394 63.1 288 63.1C270.3 63.1 256 49.67 256 31.1V32z\"\n        })\n    });\n}\nfunction Cancel({ className, cn, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"svg\", {\n        xmlns: \"http://www.w3.org/2000/svg\",\n        viewBox: \"0 0 24 24\",\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        className: cn(\"fill-none stroke-current stroke-2\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"circle\", {\n                cx: \"12\",\n                cy: \"12\",\n                r: \"10\"\n            }),\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"path\", {\n                d: \"m4.9 4.9 14.2 14.2\"\n            })\n        ]\n    });\n}\n/**\n * @remarks It is not recommended using this directly as it requires manually binding generics. Instead, use `createUploadButton`.\n * @example\n * <UploadButton<OurFileRouter, \"someEndpoint\">\n *   endpoint=\"someEndpoint\"\n *   onUploadComplete={(res) => console.log(res)}\n *   onUploadError={(err) => console.log(err)}\n * />\n */ function UploadButton(props) {\n    // Cast back to UploadthingComponentProps<TRouter> to get the correct type\n    // since the ErrorMessage messes it up otherwise\n    const $props = props;\n    const { mode = \"auto\", appendOnPaste = false, cn = _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.defaultClassListMerger } = $props.config ?? {};\n    const acRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(new AbortController());\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [uploadProgress, setUploadProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)($props.__internal_upload_progress ?? 0);\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const { startUpload, isUploading, routeConfig } = __useUploadThingInternal((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.resolveMaybeUrlArg)($props.url), $props.endpoint, $props.fetch ?? globalThis.fetch, {\n        signal: acRef.current.signal,\n        headers: $props.headers,\n        onClientUploadComplete: (res)=>{\n            if (fileInputRef.current) {\n                fileInputRef.current.value = \"\";\n            }\n            setFiles([]);\n            void $props.onClientUploadComplete?.(res);\n            setUploadProgress(0);\n        },\n        uploadProgressGranularity: $props.uploadProgressGranularity,\n        onUploadProgress: (p)=>{\n            setUploadProgress(p);\n            $props.onUploadProgress?.(p);\n        },\n        onUploadError: $props.onUploadError,\n        onUploadBegin: $props.onUploadBegin,\n        onBeforeUploadBegin: $props.onBeforeUploadBegin\n    });\n    const { fileTypes, multiple } = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.generatePermittedFileTypes)(routeConfig);\n    const disabled = !!($props.__internal_button_disabled ?? $props.disabled);\n    const state = (()=>{\n        const ready = $props.__internal_state === \"ready\" || fileTypes.length > 0;\n        if ($props.__internal_state) return $props.__internal_state;\n        if (disabled) return \"disabled\";\n        if (!ready) return \"readying\";\n        if (!isUploading) return \"ready\";\n        return \"uploading\";\n    })();\n    const uploadFiles = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((files)=>{\n        const input = \"input\" in $props ? $props.input : undefined;\n        startUpload(files, input).catch((e)=>{\n            if (e instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadAbortedError) {\n                void $props.onUploadAborted?.();\n            } else {\n                throw e;\n            }\n        });\n    }, [\n        $props,\n        startUpload\n    ]);\n    const onUploadClick = (e)=>{\n        if (state === \"uploading\") {\n            e.preventDefault();\n            e.stopPropagation();\n            acRef.current.abort();\n            acRef.current = new AbortController();\n            return;\n        }\n        if (mode === \"manual\" && files.length > 0) {\n            e.preventDefault();\n            e.stopPropagation();\n            uploadFiles(files);\n        }\n    };\n    const inputProps = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            type: \"file\",\n            ref: fileInputRef,\n            multiple,\n            accept: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.generateMimeTypes)(fileTypes).join(\", \"),\n            onChange: (e)=>{\n                if (!e.target.files) return;\n                const selectedFiles = Array.from(e.target.files);\n                $props.onChange?.(selectedFiles);\n                if (mode === \"manual\") {\n                    setFiles(selectedFiles);\n                    return;\n                }\n                uploadFiles(selectedFiles);\n            },\n            disabled,\n            tabIndex: disabled ? -1 : 0\n        }), [\n        $props,\n        disabled,\n        fileTypes,\n        mode,\n        multiple,\n        uploadFiles\n    ]);\n    usePaste((event)=>{\n        if (!appendOnPaste) return;\n        if (document.activeElement !== fileInputRef.current) return;\n        const pastedFiles = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.getFilesFromClipboardEvent)(event);\n        if (!pastedFiles) return;\n        let filesToUpload = pastedFiles;\n        setFiles((prev)=>{\n            filesToUpload = [\n                ...prev,\n                ...pastedFiles\n            ];\n            $props.onChange?.(filesToUpload);\n            return filesToUpload;\n        });\n        if (mode === \"auto\") uploadFiles(files);\n    });\n    const styleFieldArg = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            ready: state !== \"readying\",\n            isUploading: state === \"uploading\",\n            uploadProgress,\n            fileTypes,\n            files\n        }), [\n        fileTypes,\n        files,\n        state,\n        uploadProgress\n    ]);\n    const renderButton = ()=>{\n        const customContent = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.contentFieldToContent)($props.content?.button, styleFieldArg);\n        if (customContent) return customContent;\n        switch(state){\n            case \"readying\":\n                {\n                    return \"Loading...\";\n                }\n            case \"uploading\":\n                {\n                    if (uploadProgress >= 100) return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Spinner, {});\n                    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                        className: \"z-50\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"span\", {\n                                className: \"block group-hover:hidden\",\n                                children: [\n                                    Math.round(uploadProgress),\n                                    \"%\"\n                                ]\n                            }),\n                            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Cancel, {\n                                cn: cn,\n                                className: \"hidden size-4 group-hover:block\"\n                            })\n                        ]\n                    });\n                }\n            case \"disabled\":\n            case \"ready\":\n            default:\n                {\n                    if (mode === \"manual\" && files.length > 0) {\n                        return `Upload ${files.length} file${files.length === 1 ? \"\" : \"s\"}`;\n                    }\n                    return `Choose File${inputProps.multiple ? `(s)` : ``}`;\n                }\n        }\n    };\n    const renderClearButton = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n            onClick: ()=>{\n                setFiles([]);\n                if (fileInputRef.current) {\n                    fileInputRef.current.value = \"\";\n                }\n                $props.onChange?.([]);\n            },\n            className: cn(\"h-[1.25rem] cursor-pointer rounded border-none bg-transparent text-gray-500 transition-colors hover:bg-slate-200 hover:text-gray-600\", (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToClassName)($props.appearance?.clearBtn, styleFieldArg)),\n            style: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToCssObject)($props.appearance?.clearBtn, styleFieldArg),\n            \"data-state\": state,\n            \"data-ut-element\": \"clear-btn\",\n            children: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.contentFieldToContent)($props.content?.clearBtn, styleFieldArg) ?? \"Clear\"\n        });\n    const renderAllowedContent = ()=>/*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            className: cn(\"h-[1.25rem] text-xs leading-5 text-gray-600\", (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToClassName)($props.appearance?.allowedContent, styleFieldArg)),\n            style: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToCssObject)($props.appearance?.allowedContent, styleFieldArg),\n            \"data-state\": state,\n            \"data-ut-element\": \"allowed-content\",\n            children: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.contentFieldToContent)($props.content?.allowedContent, styleFieldArg) ?? (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.allowedContentTextLabelGenerator)(routeConfig)\n        });\n    return /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: cn(\"flex flex-col items-center justify-center gap-1\", $props.className, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToClassName)($props.appearance?.container, styleFieldArg)),\n        style: {\n            \"--progress-width\": `${uploadProgress}%`,\n            ...(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToCssObject)($props.appearance?.container, styleFieldArg)\n        },\n        \"data-state\": state,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"label\", {\n                className: cn(\"group relative flex h-10 w-36 cursor-pointer items-center justify-center overflow-hidden rounded-md text-white after:transition-[width] after:duration-500 focus-within:ring-2 focus-within:ring-blue-600 focus-within:ring-offset-2\", \"disabled:pointer-events-none\", \"data-[state=disabled]:cursor-not-allowed data-[state=readying]:cursor-not-allowed\", \"data-[state=disabled]:bg-blue-400 data-[state=ready]:bg-blue-600 data-[state=readying]:bg-blue-400 data-[state=uploading]:bg-blue-400\", \"after:absolute after:left-0 after:h-full after:w-[var(--progress-width)] after:content-[''] data-[state=uploading]:after:bg-blue-600\", (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToClassName)($props.appearance?.button, styleFieldArg)),\n                style: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.styleFieldToCssObject)($props.appearance?.button, styleFieldArg),\n                \"data-state\": state,\n                \"data-ut-element\": \"button\",\n                onClick: onUploadClick,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"input\", {\n                        ...inputProps,\n                        className: \"sr-only\"\n                    }),\n                    renderButton()\n                ]\n            }),\n            mode === \"manual\" && files.length > 0 ? renderClearButton() : renderAllowedContent()\n        ]\n    });\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@uploadthing+react@7.3.0_ne_f0a3ae338aeeec2f9af2dd930ab51e2f/node_modules/@uploadthing/react/dist/button-client-DaYiwH1a.js\n");

/***/ })

};
;