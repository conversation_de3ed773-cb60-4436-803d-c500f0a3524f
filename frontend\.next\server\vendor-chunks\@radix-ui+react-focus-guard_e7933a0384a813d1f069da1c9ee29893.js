"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893/node_modules/@radix-ui/react-focus-guards/dist/index.mjs":
/*!**************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893/node_modules/@radix-ui/react-focus-guards/dist/index.mjs ***!
  \**************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusGuards: () => (/* binding */ FocusGuards),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   useFocusGuards: () => (/* binding */ useFocusGuards)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.23_@opentelemetry_cd181414dbba96b19b68b89ec7792c28/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ FocusGuards,Root,useFocusGuards auto */ // packages/react/focus-guards/src/FocusGuards.tsx\n\nvar count = 0;\nfunction FocusGuards(props) {\n    useFocusGuards();\n    return props.children;\n}\nfunction useFocusGuards() {\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const edgeGuards = document.querySelectorAll(\"[data-radix-focus-guard]\");\n        document.body.insertAdjacentElement(\"afterbegin\", edgeGuards[0] ?? createFocusGuard());\n        document.body.insertAdjacentElement(\"beforeend\", edgeGuards[1] ?? createFocusGuard());\n        count++;\n        return ()=>{\n            if (count === 1) {\n                document.querySelectorAll(\"[data-radix-focus-guard]\").forEach((node)=>node.remove());\n            }\n            count--;\n        };\n    }, []);\n}\nfunction createFocusGuard() {\n    const element = document.createElement(\"span\");\n    element.setAttribute(\"data-radix-focus-guard\", \"\");\n    element.tabIndex = 0;\n    element.style.outline = \"none\";\n    element.style.opacity = \"0\";\n    element.style.position = \"fixed\";\n    element.style.pointerEvents = \"none\";\n    return element;\n}\nvar Root = FocusGuards;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_e7933a0384a813d1f069da1c9ee29893/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\n");

/***/ })

};
;