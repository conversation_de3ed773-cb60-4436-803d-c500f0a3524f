{"name": "react-file-picker", "version": "0.0.6", "description": "A simple wrapper around the native file input", "main": "lib/index.js", "module": "lib/index.js", "scripts": {"__internal__prettier": "prettier 'src/**/*.js*'", "build": "webpack --config config/webpack.js && cp README.md lib/README.md", "clean": "rm -rf index.* lib/", "demo": "parcel demo/index.html", "format": "npm run __internal__prettier -- --write", "format:check": "npm run __internal__prettier -- --list-different", "lint": "eslint src/**/*.js", "prepublish": "npm run build", "test": "NODE_ENV=test jest", "test:watch": "npm run test -- --watch", "test:coverage": "npm run test -- --coverage", "test:coverage:report": "npm run test:coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js"}, "repository": {"type": "git", "url": "https://github.com/meinstein/react-file-picker.git"}, "keywords": ["react", "file", "input"], "author": "<PERSON><PERSON><PERSON>", "license": "MIT", "devDependencies": {"@babel/core": "7.1.2", "@babel/plugin-transform-runtime": "^7.4.0", "@babel/preset-env": "7.1.0", "@babel/preset-react": "7.0.0", "@babel/runtime": "^7.4.0", "babel-eslint": "^10.0.1", "babel-loader": "^8.0.5", "coveralls": "^2.13.1", "enzyme": "^2.8.2", "eslint": "^4.0.0", "eslint-plugin-jest": "^20.0.3", "eslint-plugin-react": "^7.1.0", "jest": "^24.0.0", "parcel": "^1.12.3", "prettier": "^1.16.4", "prop-types": "^15.5.10", "react": "^15.6.1", "react-dom": "^15.6.1", "react-test-renderer": "^15.6.1", "webpack": "4.23.1", "webpack-cli": "^3.3.0"}, "babel": {"presets": ["@babel/preset-env", "@babel/preset-react"], "plugins": ["@babel/plugin-transform-runtime"]}, "prettier": {"trailingComma": "none", "tabWidth": 2, "semi": false, "singleQuote": true, "printWidth": 86}, "jest": {"testRegex": "(/src/.*test\\.js)$", "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/config/__mocks__/fileMock.js", "\\.(css|scss)$": "<rootDir>/config/__mocks__/styleMock.js"}, "modulePaths": ["<rootDir>/src"], "coveragePathIgnorePatterns": ["/node_modules/"]}, "eslintConfig": {"env": {"browser": true, "es6": true, "jest": true, "node": false}, "extends": ["eslint:recommended", "plugin:react/recommended"], "parser": "babel-es<PERSON>", "parserOptions": {"ecmaFeatures": {"jsx": true}, "ecmaVersion": 2018, "sourceType": "module"}, "plugins": ["react"], "rules": {"no-console": 0, "react/prop-types": 0, "react/display-name": 0}}}